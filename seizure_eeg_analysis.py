#!/usr/bin/env python3
"""
癫痫EEG地形图分析脚本
专门针对PN00-1.edf癫痫发作数据
"""

import numpy as np
import matplotlib.pyplot as plt
import mne
from mne.viz import plot_topomap
import os
import warnings
from datetime import datetime, timedelta

warnings.filterwarnings('ignore')
mne.set_log_level('ERROR')

def parse_time(time_str):
    """解析时间字符串 HH.MM.SS"""
    h, m, s = map(int, time_str.split('.'))
    return timedelta(hours=h, minutes=m, seconds=s)

def prepare_seizure_eeg_data(file_path):
    """
    准备癫痫EEG数据，正确映射通道
    """
    print("=== 加载癫痫EEG数据 ===")
    print(f"文件: {file_path}")
    
    # 加载数据
    raw = mne.io.read_raw_edf(file_path, preload=True, verbose=False)
    
    print(f"原始通道数: {len(raw.ch_names)}")
    print("原始通道名称:")
    for i, ch in enumerate(raw.ch_names):
        print(f"  {i+1:2d}. {ch}")
    
    # 根据实际文件内容创建正确的通道映射
    correct_channel_mapping = {
        'EEG Fp1': 'Fp1',    # Channel 1
        'EEG F3': 'F3',      # Channel 2
        'EEG C3': 'C3',      # Channel 3
        'EEG P3': 'P3',      # Channel 4
        'EEG O1': 'O1',      # Channel 5 (实际上文件中是EEG O1)
        'EEG F7': 'F7',      # Channel 6
        'EEG T3': 'T7',      # Channel 7 (T3->T7)
        'EEG T5': 'P7',      # Channel 8 (T5->P7)
        'EEG Fc1': 'FC1',    # Channel 9
        'EEG Fc5': 'FC5',    # Channel 10
        'EEG Cp1': 'CP1',    # Channel 11
        'EEG Cp5': 'CP5',    # Channel 12
        'EEG F9': 'F9',      # Channel 13
        'EEG Fz': 'Fz',      # Channel 14
        'EEG Cz': 'Cz',      # Channel 15
        'EEG Pz': 'Pz',      # Channel 16
        'EEG Fp2': 'Fp2',    # Channel 17
        'EEG F4': 'F4',      # Channel 18
        'EEG C4': 'C4',      # Channel 19
        'EEG P4': 'P4',      # Channel 20
        'EEG O2': 'O2',      # Channel 21
        'EEG F8': 'F8',      # Channel 22
        'EEG T4': 'T8',      # Channel 23 (T4->T8)
        'EEG T6': 'P8',      # Channel 24 (T6->P8)
        'EEG Fc2': 'FC2',    # Channel 25
        'EEG Fc6': 'FC6',    # Channel 26
        'EEG Cp2': 'CP2',    # Channel 27
        'EEG Cp6': 'CP6',    # Channel 28
        'EEG F10': 'F10',    # Channel 29
    }
    
    # 识别EEG通道
    eeg_channels = []
    for ch in raw.ch_names:
        if ch in correct_channel_mapping:
            eeg_channels.append(ch)
    
    print(f"\n识别的EEG通道: {len(eeg_channels)}个")
    
    # 只保留EEG通道
    raw.pick_channels(eeg_channels)
    
    # 设置通道类型
    raw.set_channel_types({ch: 'eeg' for ch in raw.ch_names})
    
    # 重命名通道
    raw.rename_channels(correct_channel_mapping)
    
    # 设置标准montage
    montage = mne.channels.make_standard_montage('standard_1020')
    raw.set_montage(montage, match_case=False, on_missing='ignore')
    
    print(f"最终EEG通道: {len(raw.ch_names)}个")
    print(f"通道名称: {raw.ch_names}")
    
    return raw

def analyze_seizure_periods(raw):
    """
    分析癫痫发作期间的EEG活动
    """
    print("\n=== 癫痫发作时间分析 ===")
    
    # 时间信息（根据提供的数据）
    start_time = parse_time("19.39.33")
    end_time = parse_time("20.22.58") 
    seizure_start = parse_time("19.58.36")
    seizure_end = parse_time("19.59.46")
    
    # 计算相对时间（秒）
    total_duration = (end_time - start_time).total_seconds()
    seizure_start_sec = (seizure_start - start_time).total_seconds()
    seizure_end_sec = (seizure_end - start_time).total_seconds()
    
    print(f"记录总时长: {total_duration:.0f}秒 ({total_duration/60:.1f}分钟)")
    print(f"癫痫发作开始: {seizure_start_sec:.0f}秒")
    print(f"癫痫发作结束: {seizure_end_sec:.0f}秒")
    print(f"癫痫发作持续: {seizure_end_sec - seizure_start_sec:.0f}秒")
    
    # 找到对应的数据索引
    sfreq = raw.info['sfreq']
    seizure_start_idx = int(seizure_start_sec * sfreq)
    seizure_end_idx = int(seizure_end_sec * sfreq)
    
    # 定义分析时段
    periods = {
        'pre_seizure': (max(0, seizure_start_idx - int(60*sfreq)), seizure_start_idx),
        'seizure': (seizure_start_idx, seizure_end_idx),
        'post_seizure': (seizure_end_idx, min(len(raw.times), seizure_end_idx + int(60*sfreq)))
    }
    
    return periods

def create_seizure_topomaps(raw, periods):
    """
    创建癫痫发作相关的地形图
    """
    print("\n=== 创建癫痫发作地形图 ===")
    
    os.makedirs('seizure_analysis', exist_ok=True)
    
    data = raw.get_data()
    
    # 1. 发作前、发作中、发作后对比
    period_names = ['发作前', '发作中', '发作后']
    period_keys = ['pre_seizure', 'seizure', 'post_seizure']
    
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    fig.suptitle('癫痫发作期EEG地形图对比', fontsize=16, fontweight='bold')
    
    for i, (period_name, period_key) in enumerate(zip(period_names, period_keys)):
        start_idx, end_idx = periods[period_key]
        
        # 计算该时段的RMS功率
        period_data = data[:, start_idx:end_idx]
        rms_power = np.sqrt(np.mean(period_data**2, axis=1))
        
        # 创建地形图
        ax = axes[i]
        try:
            im, _ = plot_topomap(rms_power, raw.info,
                               ch_type='eeg',
                               contours=8,
                               cmap='RdBu_r',
                               axes=ax,
                               show=False,
                               sphere='auto')
            ax.set_title(f'{period_name}\nRMS功率', fontweight='bold')
        except Exception as e:
            ax.text(0.5, 0.5, f'{period_name}\n映射错误', 
                   ha='center', va='center', transform=ax.transAxes)
    
    plt.tight_layout()
    plt.savefig('seizure_analysis/seizure_periods_comparison.png', 
                dpi=300, bbox_inches='tight', facecolor='white')
    print("保存: seizure_analysis/seizure_periods_comparison.png")
    plt.close(fig)

def create_seizure_frequency_analysis(raw, periods):
    """
    创建癫痫发作的频段分析
    """
    print("=== 癫痫发作频段分析 ===")
    
    # 重点分析癫痫相关频段
    seizure_freq_bands = {
        'Delta': (0.5, 4),
        'Theta': (4, 8),
        'Alpha': (8, 13),
        'Beta': (13, 30),
        'Gamma': (30, 50),
        'High_Gamma': (50, 100)  # 癫痫发作时常见高频活动
    }
    
    # 分析发作期间的频段活动
    start_idx, end_idx = periods['seizure']
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('癫痫发作期频段分析', fontsize=16, fontweight='bold')
    
    for i, (band_name, (low_freq, high_freq)) in enumerate(seizure_freq_bands.items()):
        ax = axes[i//3, i%3]
        
        # 滤波
        raw_filtered = raw.copy()
        raw_filtered.filter(low_freq, high_freq, fir_design='firwin', verbose=False)
        
        # 获取发作期间的数据
        seizure_data = raw_filtered.get_data()[:, start_idx:end_idx]
        power = np.sqrt(np.mean(seizure_data**2, axis=1))
        
        try:
            im, _ = plot_topomap(power, raw_filtered.info,
                               ch_type='eeg',
                               contours=6,
                               cmap='RdBu_r',
                               axes=ax,
                               show=False,
                               sphere='auto')
            ax.set_title(f'{band_name}\n({low_freq}-{high_freq} Hz)', fontweight='bold')
        except:
            ax.text(0.5, 0.5, f'{band_name}\n映射错误', 
                   ha='center', va='center', transform=ax.transAxes)
    
    plt.tight_layout()
    plt.savefig('seizure_analysis/seizure_frequency_analysis.png', 
                dpi=300, bbox_inches='tight', facecolor='white')
    print("保存: seizure_analysis/seizure_frequency_analysis.png")
    plt.close(fig)

def create_seizure_evolution(raw, periods):
    """
    创建癫痫发作演化过程地形图
    """
    print("=== 癫痫发作演化分析 ===")
    
    start_idx, end_idx = periods['seizure']
    seizure_duration = end_idx - start_idx
    
    # 将发作期分为5个时间段
    num_segments = 5
    segment_length = seizure_duration // num_segments
    
    fig, axes = plt.subplots(1, 5, figsize=(25, 5))
    fig.suptitle('癫痫发作演化过程 (70秒发作期)', fontsize=16, fontweight='bold')
    
    data = raw.get_data()
    
    for i in range(num_segments):
        seg_start = start_idx + i * segment_length
        seg_end = start_idx + (i + 1) * segment_length
        
        # 计算该时间段的功率
        seg_data = data[:, seg_start:seg_end]
        power = np.sqrt(np.mean(seg_data**2, axis=1))
        
        ax = axes[i]
        try:
            im, _ = plot_topomap(power, raw.info,
                               ch_type='eeg',
                               contours=6,
                               cmap='RdBu_r',
                               axes=ax,
                               show=False,
                               sphere='auto')
            
            time_point = i * (70 / num_segments)  # 70秒发作期
            ax.set_title(f'T+{time_point:.0f}s', fontweight='bold')
        except:
            ax.text(0.5, 0.5, f'T+{i*14}s\n错误', 
                   ha='center', va='center', transform=ax.transAxes)
    
    plt.tight_layout()
    plt.savefig('seizure_analysis/seizure_evolution.png', 
                dpi=300, bbox_inches='tight', facecolor='white')
    print("保存: seizure_analysis/seizure_evolution.png")
    plt.close(fig)

def main():
    """
    主分析函数
    """
    file_path = "PN00-1.edf"
    
    if not os.path.exists(file_path):
        print(f"错误: 文件 {file_path} 不存在!")
        return
    
    print("=== 癫痫EEG地形图分析 ===")
    print("患者: PN00")
    print("癫痫发作 #1")
    print("记录时间: 19:39:33 - 20:22:58")
    print("发作时间: 19:58:36 - 19:59:46 (70秒)")
    
    # 准备数据
    raw = prepare_seizure_eeg_data(file_path)
    
    # 分析癫痫时间段
    periods = analyze_seizure_periods(raw)
    
    # 创建基础RMS地形图
    print("\n=== 创建基础地形图 ===")
    os.makedirs('seizure_analysis', exist_ok=True)
    data = raw.get_data()
    rms_data = np.sqrt(np.mean(data**2, axis=1))
    
    fig, ax = plt.subplots(figsize=(10, 8))
    im, _ = plot_topomap(rms_data, raw.info,
                        ch_type='eeg',
                        contours=8,
                        cmap='RdBu_r',
                        axes=ax,
                        show=False,
                        sphere='auto')
    ax.set_title('29通道癫痫EEG整体RMS活动', fontsize=16, fontweight='bold')
    cbar = plt.colorbar(im, ax=ax, shrink=0.8)
    cbar.set_label('幅度 (µV)', rotation=270, labelpad=20)
    plt.tight_layout()
    plt.savefig('seizure_analysis/overall_rms_topomap.png', 
                dpi=300, bbox_inches='tight', facecolor='white')
    print("保存: seizure_analysis/overall_rms_topomap.png")
    plt.close(fig)
    
    # 创建癫痫相关分析
    create_seizure_topomaps(raw, periods)
    create_seizure_frequency_analysis(raw, periods)
    create_seizure_evolution(raw, periods)
    
    print("\n=== 分析完成 ===")
    print("生成的癫痫分析文件:")
    print("- seizure_analysis/overall_rms_topomap.png: 整体活动")
    print("- seizure_analysis/seizure_periods_comparison.png: 发作期对比")
    print("- seizure_analysis/seizure_frequency_analysis.png: 频段分析")
    print("- seizure_analysis/seizure_evolution.png: 发作演化过程")
    print(f"分析的EEG通道数: {len(raw.ch_names)} (29通道系统)")

if __name__ == "__main__":
    main()
