{"test_summary": {"total_subjects_tested": 3, "successful_loads": 3, "success_rate": 1.0, "test_timestamp": "2025-07-31 16:44:36"}, "detailed_results": [{"subject_id": 1, "status": "success", "loading_time": 0.010564088821411133, "file_size_mb": 1.4584312438964844, "total_columns": 36, "available_eeg_channels": 0, "eeg_channels": [], "estimated_samples": 10000, "signal_std": 0, "signal_mean": 0, "signal_range": 0}, {"subject_id": 2, "status": "success", "loading_time": 0.001245260238647461, "file_size_mb": 1.5211305618286133, "total_columns": 36, "available_eeg_channels": 0, "eeg_channels": [], "estimated_samples": 10000, "signal_std": 0, "signal_mean": 0, "signal_range": 0}, {"subject_id": 3, "status": "success", "loading_time": 0.0008149147033691406, "file_size_mb": 1.5376091003417969, "total_columns": 36, "available_eeg_channels": 0, "eeg_channels": [], "estimated_samples": 10000, "signal_std": 0, "signal_mean": 0, "signal_range": 0}]}