#!/usr/bin/env python3
"""
Comprehensive Hyperparameter Optimization for Epilepsy Lesion Localization
Addresses fundamental training issues and systematically optimizes performance

This script implements:
1. Loss function analysis and scaling
2. Learning rate optimization and scheduler tuning
3. Architecture-specific hyperparameter search
4. Data preprocessing validation
5. Progressive training strategies
6. Automated hyperparameter search with Optuna
7. Performance monitoring and debugging
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json
import time
import warnings
from typing import Dict, List, Tuple, Optional
from tqdm import tqdm
import optuna
from optuna.visualization import plot_optimization_history, plot_param_importances
warnings.filterwarnings('ignore')

# Import our modules
from training_pipeline import EpilepsyLocalizationModel, create_data_loaders
from epilepsy_localization_network import BoundingBoxLoss

# Set random seeds for reproducibility
torch.manual_seed(42)
np.random.seed(42)

class LossAnalyzer:
    """Analyze and fix loss function scaling issues"""
    
    def __init__(self):
        self.loss_history = []
        self.component_history = []
    
    def analyze_loss_components(self, model, data_loader, device):
        """Analyze individual loss components to identify scaling issues"""
        print("Analyzing loss components...")
        
        model.eval()
        bbox_loss = BoundingBoxLoss()
        
        dice_losses = []
        iou_losses = []
        focal_losses = []
        total_losses = []
        
        with torch.no_grad():
            for batch_idx, batch in enumerate(data_loader):
                if batch_idx >= 10:  # Analyze first 10 batches
                    break
                
                eeg_data = batch['eeg_data'].to(device)
                temporal_sequence = batch['temporal_sequence'].to(device)
                lesion_masks = batch['lesion_mask'].to(device)
                
                # Forward pass
                outputs = model(eeg_data, temporal_sequence)
                bbox_params = outputs['bbox_params']
                
                # Compute losses
                loss_results = bbox_loss(bbox_params, lesion_masks)
                
                dice_losses.append(loss_results['dice_loss'].item())
                iou_losses.append(loss_results['iou_loss'].item())
                focal_losses.append(loss_results['focal_loss'].item())
                total_losses.append(loss_results['total_loss'].item())
        
        # Analyze loss statistics
        analysis = {
            'dice_loss': {'mean': np.mean(dice_losses), 'std': np.std(dice_losses), 'range': [np.min(dice_losses), np.max(dice_losses)]},
            'iou_loss': {'mean': np.mean(iou_losses), 'std': np.std(iou_losses), 'range': [np.min(iou_losses), np.max(iou_losses)]},
            'focal_loss': {'mean': np.mean(focal_losses), 'std': np.std(focal_losses), 'range': [np.min(focal_losses), np.max(focal_losses)]},
            'total_loss': {'mean': np.mean(total_losses), 'std': np.std(total_losses), 'range': [np.min(total_losses), np.max(total_losses)]}
        }
        
        print("Loss Component Analysis:")
        for loss_name, stats in analysis.items():
            print(f"  {loss_name}:")
            print(f"    Mean: {stats['mean']:.6f}")
            print(f"    Std:  {stats['std']:.6f}")
            print(f"    Range: [{stats['range'][0]:.6f}, {stats['range'][1]:.6f}]")
        
        return analysis
    
    def create_balanced_loss_function(self, analysis):
        """Create properly balanced loss function based on analysis"""
        
        # Calculate scaling factors to normalize loss components to [0, 1] range
        dice_scale = 1.0 / max(analysis['dice_loss']['mean'], 1e-6)
        iou_scale = 1.0 / max(analysis['iou_loss']['mean'], 1e-6)
        focal_scale = 1.0 / max(analysis['focal_loss']['mean'], 1e-6)
        
        print(f"Calculated scaling factors:")
        print(f"  Dice scale: {dice_scale:.6f}")
        print(f"  IoU scale: {iou_scale:.6f}")
        print(f"  Focal scale: {focal_scale:.6f}")
        
        class BalancedBoundingBoxLoss(BoundingBoxLoss):
            def __init__(self, dice_weight=0.4, iou_weight=0.4, focal_weight=0.2,
                        dice_scale=1.0, iou_scale=1.0, focal_scale=1.0):
                super().__init__(dice_weight, iou_weight, focal_weight)
                self.dice_scale = dice_scale
                self.iou_scale = iou_scale
                self.focal_scale = focal_scale
            
            def forward(self, box_params, target_masks):
                result = super().forward(box_params, target_masks)
                
                # Apply scaling
                scaled_dice = result['dice_loss'] * self.dice_scale
                scaled_iou = result['iou_loss'] * self.iou_scale
                scaled_focal = result['focal_loss'] * self.focal_scale
                
                # Recompute total loss with scaled components
                total_loss = (self.dice_weight * scaled_dice + 
                             self.iou_weight * scaled_iou + 
                             self.focal_weight * scaled_focal)
                
                return {
                    'total_loss': total_loss,
                    'dice_loss': scaled_dice,
                    'iou_loss': scaled_iou,
                    'focal_loss': scaled_focal,
                    'pred_masks': result['pred_masks']
                }
        
        return BalancedBoundingBoxLoss(
            dice_scale=dice_scale,
            iou_scale=iou_scale,
            focal_scale=focal_scale
        )

class LearningRateFinder:
    """Find optimal learning rate using learning rate range test"""
    
    def __init__(self, model, optimizer, criterion, device):
        self.model = model
        self.optimizer = optimizer
        self.criterion = criterion
        self.device = device
        
    def range_test(self, data_loader, start_lr=1e-7, end_lr=1e-1, num_iter=100):
        """Perform learning rate range test"""
        print("Performing learning rate range test...")

        # Store original state
        original_state = self.model.state_dict()

        # Ensure model is in training mode and gradients are enabled
        self.model.train()
        for param in self.model.parameters():
            param.requires_grad = True

        # Initialize
        lr_mult = (end_lr / start_lr) ** (1.0 / num_iter)
        lr = start_lr
        self.optimizer.param_groups[0]['lr'] = lr

        lrs = []
        losses = []

        data_iter = iter(data_loader)

        for i in tqdm(range(num_iter), desc="LR Range Test"):
            try:
                batch = next(data_iter)
            except StopIteration:
                data_iter = iter(data_loader)
                batch = next(data_iter)

            try:
                # Forward pass
                eeg_data = batch['eeg_data'].to(self.device)
                temporal_sequence = batch['temporal_sequence'].to(self.device)
                lesion_masks = batch['lesion_mask'].to(self.device)

                self.optimizer.zero_grad()
                outputs = self.model(eeg_data, temporal_sequence)
                loss_results = self.criterion(outputs['bbox_params'], lesion_masks)
                loss = loss_results['total_loss']

                # Check for invalid loss
                if torch.isnan(loss) or torch.isinf(loss):
                    print(f"Invalid loss at iteration {i}: {loss.item()}")
                    break

                # Backward pass
                loss.backward()

                # Gradient clipping
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)

                self.optimizer.step()

                # Record
                lrs.append(lr)
                losses.append(loss.item())

                # Update learning rate
                lr *= lr_mult
                self.optimizer.param_groups[0]['lr'] = lr

                # Stop if loss explodes
                if i > 10 and losses[-1] > 4 * losses[0]:
                    print(f"Loss explosion detected at iteration {i}")
                    break

            except Exception as e:
                print(f"Error at iteration {i}: {e}")
                break

        # Restore original state
        self.model.load_state_dict(original_state)

        return lrs, losses
    
    def plot_lr_finder(self, lrs, losses, save_path="lr_finder_plot.png"):
        """Plot learning rate finder results"""
        plt.figure(figsize=(10, 6))
        plt.semilogx(lrs, losses)
        plt.xlabel('Learning Rate')
        plt.ylabel('Loss')
        plt.title('Learning Rate Range Test')
        plt.grid(True, alpha=0.3)
        
        # Find optimal LR (steepest descent)
        gradients = np.gradient(losses)
        optimal_idx = np.argmin(gradients)
        optimal_lr = lrs[optimal_idx]
        
        plt.axvline(optimal_lr, color='red', linestyle='--', 
                   label=f'Optimal LR: {optimal_lr:.2e}')
        plt.legend()
        
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"Optimal learning rate: {optimal_lr:.2e}")
        return optimal_lr

class DataValidator:
    """Validate data preprocessing and loading"""
    
    def __init__(self):
        pass
    
    def validate_eeg_data(self, data_loader):
        """Validate EEG data ranges and preprocessing"""
        print("Validating EEG data preprocessing...")
        
        eeg_stats = {
            'min_vals': [],
            'max_vals': [],
            'mean_vals': [],
            'std_vals': []
        }
        
        for batch_idx, batch in enumerate(data_loader):
            if batch_idx >= 10:  # Check first 10 batches
                break
            
            eeg_data = batch['eeg_data']
            
            eeg_stats['min_vals'].append(eeg_data.min().item())
            eeg_stats['max_vals'].append(eeg_data.max().item())
            eeg_stats['mean_vals'].append(eeg_data.mean().item())
            eeg_stats['std_vals'].append(eeg_data.std().item())
        
        print("EEG Data Statistics:")
        print(f"  Value range: [{np.min(eeg_stats['min_vals']):.3f}, {np.max(eeg_stats['max_vals']):.3f}]")
        print(f"  Mean: {np.mean(eeg_stats['mean_vals']):.3f} ± {np.std(eeg_stats['mean_vals']):.3f}")
        print(f"  Std: {np.mean(eeg_stats['std_vals']):.3f} ± {np.std(eeg_stats['std_vals']):.3f}")
        
        # Check for potential issues
        if np.max(eeg_stats['max_vals']) > 1000:
            print("⚠️  WARNING: EEG values are very large - consider normalization")
        if np.min(eeg_stats['min_vals']) < -1000:
            print("⚠️  WARNING: EEG values are very negative - check preprocessing")
        if np.mean(eeg_stats['std_vals']) < 0.1:
            print("⚠️  WARNING: EEG data has very low variance - check scaling")
        
        return eeg_stats
    
    def validate_lesion_masks(self, data_loader):
        """Validate lesion mask preprocessing"""
        print("Validating lesion mask preprocessing...")
        
        mask_stats = {
            'volumes': [],
            'centers': [],
            'non_zero_counts': []
        }
        
        for batch_idx, batch in enumerate(data_loader):
            if batch_idx >= 10:
                break
            
            lesion_masks = batch['lesion_mask']
            
            for i in range(lesion_masks.shape[0]):
                mask = lesion_masks[i]
                volume = torch.sum(mask > 0).item()
                
                if volume > 0:
                    coords = torch.nonzero(mask, as_tuple=False)
                    center = torch.mean(coords.float(), dim=0)
                    mask_stats['centers'].append(center.numpy())
                    mask_stats['volumes'].append(volume)
                    mask_stats['non_zero_counts'].append(1)
                else:
                    mask_stats['non_zero_counts'].append(0)
        
        print("Lesion Mask Statistics:")
        print(f"  Non-zero masks: {sum(mask_stats['non_zero_counts'])}/{len(mask_stats['non_zero_counts'])}")
        if mask_stats['volumes']:
            print(f"  Volume range: [{np.min(mask_stats['volumes'])}, {np.max(mask_stats['volumes'])}] voxels")
            print(f"  Mean volume: {np.mean(mask_stats['volumes']):.1f} ± {np.std(mask_stats['volumes']):.1f}")
        
        return mask_stats

class ProgressiveTrainer:
    """Implement progressive training strategy"""
    
    def __init__(self, model, device):
        self.model = model
        self.device = device
        
    def train_stage1_autoencoder(self, data_loader, num_epochs=5):
        """Stage 1: Train autoencoder branch only"""
        print("Stage 1: Training autoencoder branch...")

        # Store original requires_grad states
        original_grad_states = {}
        for name, param in self.model.named_parameters():
            original_grad_states[name] = param.requires_grad
            if 'attention_autoencoder' not in name:
                param.requires_grad = False

        # Get trainable parameters
        trainable_params = [p for p in self.model.parameters() if p.requires_grad]
        if not trainable_params:
            print("  No trainable parameters found for autoencoder!")
            return

        optimizer = optim.Adam(trainable_params, lr=1e-3)
        criterion = nn.MSELoss()

        self.model.train()

        for epoch in range(num_epochs):
            total_loss = 0
            num_batches = 0

            for batch_idx, batch in enumerate(tqdm(data_loader, desc=f"Epoch {epoch+1}/{num_epochs}")):
                if batch_idx >= 10:  # Limit for speed
                    break

                eeg_data = batch['eeg_data'].to(self.device)
                temporal_sequence = batch['temporal_sequence'].to(self.device)

                try:
                    optimizer.zero_grad()
                    outputs = self.model(eeg_data, temporal_sequence, return_autoencoder_loss=True)

                    if 'reconstruction' in outputs:
                        loss = criterion(outputs['reconstruction'], eeg_data)
                        if not torch.isnan(loss) and not torch.isinf(loss):
                            loss.backward()
                            torch.nn.utils.clip_grad_norm_(trainable_params, max_norm=1.0)
                            optimizer.step()

                            total_loss += loss.item()
                            num_batches += 1
                except Exception as e:
                    print(f"  Error in batch {batch_idx}: {e}")
                    continue

            avg_loss = total_loss / max(num_batches, 1)
            print(f"  Epoch {epoch+1}: Autoencoder Loss = {avg_loss:.6f}")

        # Restore original grad states
        for name, param in self.model.named_parameters():
            param.requires_grad = original_grad_states[name]
    
    def train_stage2_features(self, data_loader, num_epochs=5):
        """Stage 2: Train feature extraction without bounding box"""
        print("Stage 2: Training feature extraction...")
        
        # Freeze bounding box predictor
        for name, param in self.model.named_parameters():
            if 'bbox_predictor' in name:
                param.requires_grad = False
        
        optimizer = optim.Adam(
            filter(lambda p: p.requires_grad, self.model.parameters()),
            lr=1e-4
        )
        
        # Simple contrastive loss for feature learning
        for epoch in range(num_epochs):
            total_loss = 0
            num_batches = 0
            
            for batch in tqdm(data_loader, desc=f"Epoch {epoch+1}/{num_epochs}"):
                eeg_data = batch['eeg_data'].to(self.device)
                temporal_sequence = batch['temporal_sequence'].to(self.device)
                
                optimizer.zero_grad()
                outputs = self.model(eeg_data, temporal_sequence)
                
                # Simple regularization loss on features
                fused_features = outputs['fused_features']
                loss = torch.mean(torch.abs(fused_features)) * 0.01  # L1 regularization
                
                loss.backward()
                optimizer.step()
                
                total_loss += loss.item()
                num_batches += 1
            
            avg_loss = total_loss / max(num_batches, 1)
            print(f"  Epoch {epoch+1}: Feature Loss = {avg_loss:.6f}")
        
        # Unfreeze all parameters
        for param in self.model.parameters():
            param.requires_grad = True

class HyperparameterOptimizer:
    """Automated hyperparameter optimization using Optuna"""
    
    def __init__(self, train_loader, val_loader, device):
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.device = device
        
    def objective(self, trial):
        """Optuna objective function"""
        
        # Suggest hyperparameters
        learning_rate = trial.suggest_float('learning_rate', 1e-6, 1e-2, log=True)
        batch_size = trial.suggest_categorical('batch_size', [1, 2, 4])
        feature_dim = trial.suggest_categorical('feature_dim', [256, 512, 1024])
        fused_dim = trial.suggest_categorical('fused_dim', [512, 1024, 2048])
        
        dice_weight = trial.suggest_float('dice_weight', 0.1, 0.8)
        iou_weight = trial.suggest_float('iou_weight', 0.1, 0.8)
        focal_weight = 1.0 - dice_weight - iou_weight
        
        if focal_weight < 0.1:
            focal_weight = 0.1
            dice_weight = (1.0 - focal_weight) * dice_weight / (dice_weight + iou_weight)
            iou_weight = 1.0 - dice_weight - focal_weight
        
        dropout_rate = trial.suggest_float('dropout_rate', 0.1, 0.5)
        
        try:
            # Create model with suggested parameters
            model = EpilepsyLocalizationModel(
                n_channels=14,
                feature_dim=feature_dim,
                fused_dim=fused_dim,
                volume_size=256
            ).to(self.device)
            
            # Create loss function
            criterion = BoundingBoxLoss(
                dice_weight=dice_weight,
                iou_weight=iou_weight,
                focal_weight=focal_weight
            )
            
            # Create optimizer
            optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=1e-5)
            
            # Train for a few epochs
            model.train()
            for epoch in range(3):  # Quick evaluation
                total_loss = 0
                num_batches = 0
                
                for batch_idx, batch in enumerate(self.train_loader):
                    if batch_idx >= 10:  # Limit batches for speed
                        break
                    
                    eeg_data = batch['eeg_data'].to(self.device)
                    temporal_sequence = batch['temporal_sequence'].to(self.device)
                    lesion_masks = batch['lesion_mask'].to(self.device)
                    
                    optimizer.zero_grad()
                    outputs = model(eeg_data, temporal_sequence)
                    loss_results = criterion(outputs['bbox_params'], lesion_masks)
                    loss = loss_results['total_loss']
                    
                    if torch.isnan(loss) or torch.isinf(loss):
                        return float('inf')
                    
                    loss.backward()
                    torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                    optimizer.step()
                    
                    total_loss += loss.item()
                    num_batches += 1
                
                if num_batches == 0:
                    return float('inf')
            
            # Evaluate on validation set
            model.eval()
            val_dice_scores = []
            val_iou_scores = []
            
            with torch.no_grad():
                for batch_idx, batch in enumerate(self.val_loader):
                    if batch_idx >= 5:  # Limit for speed
                        break
                    
                    eeg_data = batch['eeg_data'].to(self.device)
                    temporal_sequence = batch['temporal_sequence'].to(self.device)
                    lesion_masks = batch['lesion_mask'].to(self.device)
                    
                    outputs = model(eeg_data, temporal_sequence)
                    loss_results = criterion(outputs['bbox_params'], lesion_masks)
                    
                    # Compute metrics
                    pred_masks = loss_results['pred_masks']
                    for i in range(pred_masks.shape[0]):
                        pred_mask = pred_masks[i].cpu().numpy()
                        target_mask = lesion_masks[i].cpu().numpy()
                        
                        # Dice score
                        intersection = np.sum(pred_mask * target_mask)
                        union = np.sum(pred_mask) + np.sum(target_mask)
                        dice = (2.0 * intersection) / (union + 1e-6)
                        val_dice_scores.append(dice)
                        
                        # IoU score
                        iou = intersection / (union - intersection + 1e-6)
                        val_iou_scores.append(iou)
            
            if not val_dice_scores:
                return float('inf')
            
            # Return negative of mean dice score (Optuna minimizes)
            mean_dice = np.mean(val_dice_scores)
            return -mean_dice
            
        except Exception as e:
            print(f"Trial failed: {e}")
            return float('inf')
    
    def optimize(self, n_trials=50):
        """Run hyperparameter optimization"""
        print(f"Starting hyperparameter optimization with {n_trials} trials...")
        
        study = optuna.create_study(direction='minimize')
        study.optimize(self.objective, n_trials=n_trials)
        
        print("Optimization completed!")
        print(f"Best trial: {study.best_trial.number}")
        print(f"Best value: {-study.best_value:.4f}")
        print("Best parameters:")
        for key, value in study.best_params.items():
            print(f"  {key}: {value}")
        
        return study

def main():
    """Main optimization function"""
    print("Comprehensive Hyperparameter Optimization")
    print("="*60)
    
    # Check device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Create data loaders
    try:
        train_loader, val_loader, test_loader = create_data_loaders(
            train_file="eeg_lesion_training_dataset/train_pairings.pkl",
            val_file="eeg_lesion_training_dataset/validation_pairings.pkl",
            test_file="eeg_lesion_training_dataset/test_pairings.pkl",
            batch_size=2,
            num_workers=0
        )
        print("✅ Data loaders created successfully")
    except Exception as e:
        print(f"❌ Error creating data loaders: {e}")
        return
    
    # 1. Data Validation
    print("\n" + "="*60)
    print("STEP 1: DATA VALIDATION")
    print("="*60)
    
    validator = DataValidator()
    eeg_stats = validator.validate_eeg_data(train_loader)
    mask_stats = validator.validate_lesion_masks(train_loader)
    
    # 2. Loss Analysis
    print("\n" + "="*60)
    print("STEP 2: LOSS FUNCTION ANALYSIS")
    print("="*60)
    
    # Create initial model for analysis
    model = EpilepsyLocalizationModel().to(device)
    loss_analyzer = LossAnalyzer()
    
    analysis = loss_analyzer.analyze_loss_components(model, train_loader, device)
    balanced_loss = loss_analyzer.create_balanced_loss_function(analysis)
    
    # 3. Learning Rate Finding
    print("\n" + "="*60)
    print("STEP 3: LEARNING RATE OPTIMIZATION")
    print("="*60)
    
    optimizer = optim.AdamW(model.parameters(), lr=1e-4)
    lr_finder = LearningRateFinder(model, optimizer, balanced_loss, device)
    
    lrs, losses = lr_finder.range_test(train_loader, num_iter=50)
    optimal_lr = lr_finder.plot_lr_finder(lrs, losses)
    
    # 4. Progressive Training Test
    print("\n" + "="*60)
    print("STEP 4: PROGRESSIVE TRAINING TEST")
    print("="*60)
    
    progressive_trainer = ProgressiveTrainer(model, device)
    progressive_trainer.train_stage1_autoencoder(train_loader, num_epochs=2)
    progressive_trainer.train_stage2_features(train_loader, num_epochs=2)
    
    # 5. Hyperparameter Optimization
    print("\n" + "="*60)
    print("STEP 5: AUTOMATED HYPERPARAMETER OPTIMIZATION")
    print("="*60)
    
    optimizer_engine = HyperparameterOptimizer(train_loader, val_loader, device)
    study = optimizer_engine.optimize(n_trials=20)  # Reduced for demo
    
    # Save results
    results = {
        'optimal_lr': optimal_lr,
        'best_params': study.best_params,
        'best_score': -study.best_value,
        'loss_analysis': analysis
    }
    
    with open('optimization_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print("\n" + "="*60)
    print("OPTIMIZATION COMPLETED")
    print("="*60)
    print("✅ Results saved to optimization_results.json")
    print("✅ Learning rate plot saved to lr_finder_plot.png")
    print(f"✅ Recommended learning rate: {optimal_lr:.2e}")
    print(f"✅ Best validation Dice score: {-study.best_value:.4f}")

if __name__ == "__main__":
    main()
