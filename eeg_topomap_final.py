#!/usr/bin/env python3
"""
Final EEG Topographic Mapping Script
Specifically designed for the PN00-1.edf file
"""

import numpy as np
import matplotlib.pyplot as plt
import mne
from mne.viz import plot_topomap
import os
import warnings

# Suppress warnings
warnings.filterwarnings('ignore')
mne.set_log_level('ERROR')

def prepare_eeg_data(file_path):
    """
    Load and prepare EEG data with proper electrode positioning
    """
    print(f"Loading EEG file: {file_path}")
    
    # Load the EDF file
    raw = mne.io.read_raw_edf(file_path, preload=True, verbose=False)
    
    # Define EEG channels (exclude non-EEG channels)
    eeg_channels = [ch for ch in raw.ch_names if ch.startswith('EEG')]
    
    print(f"Found {len(eeg_channels)} EEG channels out of {len(raw.ch_names)} total channels")
    
    # Pick only EEG channels
    raw.pick_channels(eeg_channels)
    
    # Set channel types
    raw.set_channel_types({ch: 'eeg' for ch in raw.ch_names})
    
    # Create channel name mapping (remove 'EEG ' prefix)
    ch_mapping = {}
    for ch in raw.ch_names:
        clean_name = ch.replace('EEG ', '').strip()
        # Handle case variations
        if clean_name.lower() == 'fc1':
            clean_name = 'FC1'
        elif clean_name.lower() == 'fc2':
            clean_name = 'FC2'
        elif clean_name.lower() == 'fc5':
            clean_name = 'FC5'
        elif clean_name.lower() == 'fc6':
            clean_name = 'FC6'
        elif clean_name.lower() == 'cp1':
            clean_name = 'CP1'
        elif clean_name.lower() == 'cp2':
            clean_name = 'CP2'
        elif clean_name.lower() == 'cp5':
            clean_name = 'CP5'
        elif clean_name.lower() == 'cp6':
            clean_name = 'CP6'
        ch_mapping[ch] = clean_name
    
    # Rename channels
    raw.rename_channels(ch_mapping)
    
    # Set standard montage
    montage = mne.channels.make_standard_montage('standard_1020')
    raw.set_montage(montage, match_case=False, on_missing='ignore')
    
    print(f"Applied standard 10-20 montage to {len(raw.ch_names)} channels")
    print(f"Channel names: {raw.ch_names}")
    
    return raw

def create_single_topomap(data, info, title, save_path=None, cmap='RdBu_r'):
    """
    Create a single topographic map
    """
    fig, ax = plt.subplots(figsize=(10, 8))
    
    # Create topographic map
    im, _ = plot_topomap(data, info, 
                        ch_type='eeg',
                        contours=8,
                        cmap=cmap,
                        axes=ax,
                        show=False,
                        sphere='auto',
                        size=8)
    
    # Customize the plot
    ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
    
    # Add colorbar
    cbar = plt.colorbar(im, ax=ax, shrink=0.8, aspect=20)
    cbar.set_label('Amplitude (µV)', rotation=270, labelpad=20, fontsize=12)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        print(f"Saved: {save_path}")
        plt.close(fig)
    else:
        plt.show()
    
    return fig

def create_frequency_topomaps(raw):
    """
    Create topographic maps for different frequency bands
    """
    print("\n=== Creating Frequency Band Topomaps ===")
    
    # Define frequency bands
    freq_bands = {
        'Delta': (0.5, 4),
        'Theta': (4, 8), 
        'Alpha': (8, 13),
        'Beta': (13, 30),
        'Gamma': (30, 50)
    }
    
    os.makedirs('frequency_maps', exist_ok=True)
    
    for band_name, (low_freq, high_freq) in freq_bands.items():
        print(f"Processing {band_name} band ({low_freq}-{high_freq} Hz)...")
        
        # Filter data
        raw_filtered = raw.copy()
        raw_filtered.filter(low_freq, high_freq, fir_design='firwin', verbose=False)
        
        # Compute power (RMS)
        data = raw_filtered.get_data()
        power = np.sqrt(np.mean(data**2, axis=1))
        
        # Create topomap
        title = f'{band_name} Band Power ({low_freq}-{high_freq} Hz)'
        save_path = f'frequency_maps/{band_name.lower()}_topomap.png'
        
        create_single_topomap(power, raw_filtered.info, title, save_path)

def create_time_topomaps(raw, num_points=5):
    """
    Create topographic maps at different time points
    """
    print(f"\n=== Creating Time Evolution Topomaps ({num_points} time points) ===")
    
    os.makedirs('time_maps', exist_ok=True)
    
    duration = raw.times[-1]
    time_points = np.linspace(0, duration, num_points)
    
    for i, time_point in enumerate(time_points):
        print(f"Processing time point {i+1}/{num_points}: {time_point:.1f}s")
        
        # Get data at specific time
        time_idx = np.argmin(np.abs(raw.times - time_point))
        data = raw.get_data()[:, time_idx]
        
        # Create topomap
        title = f'EEG Activity at {time_point:.1f}s'
        save_path = f'time_maps/time_{time_point:.1f}s_topomap.png'
        
        create_single_topomap(data, raw.info, title, save_path)

def create_summary_figure(raw):
    """
    Create a comprehensive summary figure
    """
    print("\n=== Creating Summary Figure ===")
    
    # Create 2x3 subplot
    fig, axes = plt.subplots(2, 3, figsize=(20, 12))
    fig.suptitle('EEG Topographic Analysis - PN00-1.edf', fontsize=20, fontweight='bold')
    
    # Frequency bands
    freq_bands = [
        ('Delta', (0.5, 4)),
        ('Theta', (4, 8)),
        ('Alpha', (8, 13)),
        ('Beta', (13, 30)),
        ('Gamma', (30, 50))
    ]
    
    # Create frequency band maps
    for i, (band_name, (low_freq, high_freq)) in enumerate(freq_bands):
        ax = axes[i//3, i%3]
        
        # Filter and compute power
        raw_filtered = raw.copy()
        raw_filtered.filter(low_freq, high_freq, fir_design='firwin', verbose=False)
        data = raw_filtered.get_data()
        power = np.sqrt(np.mean(data**2, axis=1))
        
        # Create topomap
        im, _ = plot_topomap(power, raw_filtered.info,
                           ch_type='eeg',
                           contours=6,
                           cmap='RdBu_r',
                           axes=ax,
                           show=False,
                           sphere='auto')
        
        ax.set_title(f'{band_name}\n({low_freq}-{high_freq} Hz)', fontweight='bold')
    
    # Add overall activity map
    ax = axes[1, 2]
    data = raw.get_data()
    rms_data = np.sqrt(np.mean(data**2, axis=1))
    
    im, _ = plot_topomap(rms_data, raw.info,
                       ch_type='eeg',
                       contours=6,
                       cmap='RdBu_r',
                       axes=ax,
                       show=False,
                       sphere='auto')
    
    ax.set_title('Overall RMS\nActivity', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('eeg_summary_analysis.png', dpi=300, bbox_inches='tight', facecolor='white')
    print("Saved: eeg_summary_analysis.png")
    plt.close(fig)

def main():
    """
    Main analysis function
    """
    file_path = "PN00-1.edf"
    
    if not os.path.exists(file_path):
        print(f"Error: File {file_path} not found!")
        return
    
    print("=== EEG Topographic Mapping Analysis ===")
    print("Analyzing file: PN00-1.edf")
    
    # Load and prepare data
    raw = prepare_eeg_data(file_path)
    
    # Create basic RMS topomap
    print("\n=== Creating Basic RMS Topomap ===")
    data = raw.get_data()
    rms_data = np.sqrt(np.mean(data**2, axis=1))
    create_single_topomap(rms_data, raw.info, 
                         'EEG RMS Activity (Broadband)', 
                         'basic_rms_topomap.png')
    
    # Create frequency band topomaps
    create_frequency_topomaps(raw)
    
    # Create time evolution topomaps
    create_time_topomaps(raw)
    
    # Create summary figure
    create_summary_figure(raw)
    
    print("\n=== Analysis Complete ===")
    print("Generated files:")
    print("- basic_rms_topomap.png: Basic RMS activity map")
    print("- frequency_maps/: Frequency band topomaps")
    print("- time_maps/: Time evolution topomaps") 
    print("- eeg_summary_analysis.png: Comprehensive summary")
    print(f"\nTotal EEG channels analyzed: {len(raw.ch_names)}")

if __name__ == "__main__":
    main()
