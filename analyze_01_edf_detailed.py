#!/usr/bin/env python3
"""
详细分析01.edf文件 - 参考电极系统
"""

import numpy as np
import matplotlib.pyplot as plt
import mne
import os
import warnings

warnings.filterwarnings('ignore')
mne.set_log_level('ERROR')

def analyze_reference_system():
    """
    分析参考电极系统的EEG数据
    """
    print("=== 01.edf 参考电极系统分析 ===")
    
    # 加载文件
    raw = mne.io.read_raw_edf('01.edf', preload=True, verbose=False, encoding='latin1')
    
    print(f"文件信息:")
    print(f"  通道数: {raw.info['nchan']}")
    print(f"  采样频率: {raw.info['sfreq']} Hz")
    print(f"  记录时长: {raw.times[-1]:.1f}秒 ({raw.times[-1]/60:.1f}分钟)")
    
    # 识别EEG通道（参考电极系统）
    eeg_ref_channels = [ch for ch in raw.ch_names if ch.startswith('EEG') and '-Ref' in ch]
    
    print(f"\n参考电极EEG通道 ({len(eeg_ref_channels)}个):")
    for ch in eeg_ref_channels:
        print(f"  {ch}")
    
    return raw, eeg_ref_channels

def create_reference_electrode_mapping(eeg_channels):
    """
    创建参考电极到标准命名的映射
    """
    print(f"\n=== 创建电极映射 ===")
    
    mapping = {}
    for ch in eeg_channels:
        # 移除'-Ref'后缀
        clean_name = ch.replace('EEG ', '').replace('-Ref', '').strip()
        
        # 处理旧命名法
        if clean_name == 'T3':
            clean_name = 'T7'
        elif clean_name == 'T4':
            clean_name = 'T8'
        elif clean_name == 'T5':
            clean_name = 'P7'
        elif clean_name == 'T6':
            clean_name = 'P8'
        
        mapping[ch] = clean_name
    
    print("电极映射:")
    for orig, mapped in mapping.items():
        print(f"  {orig} -> {mapped}")
    
    return mapping

def create_reference_topomap(raw, eeg_channels, output_dir="01_edf_analysis"):
    """
    创建参考电极系统的地形图
    """
    print(f"\n=== 创建参考电极地形图 ===")
    
    os.makedirs(output_dir, exist_ok=True)
    
    # 处理EEG数据
    raw_eeg = raw.copy()
    raw_eeg.pick_channels(eeg_channels)
    raw_eeg.set_channel_types({ch: 'eeg' for ch in raw_eeg.ch_names})
    
    # 创建映射并重命名
    mapping = create_reference_electrode_mapping(eeg_channels)
    raw_eeg.rename_channels(mapping)
    
    # 移除A1, A2参考电极（如果存在）
    channels_to_keep = [ch for ch in raw_eeg.ch_names if ch not in ['A1', 'A2']]
    if len(channels_to_keep) < len(raw_eeg.ch_names):
        raw_eeg.pick_channels(channels_to_keep)
        print(f"移除参考电极A1/A2，保留{len(channels_to_keep)}个通道")
    
    try:
        # 设置标准montage
        montage = mne.channels.make_standard_montage('standard_1020')
        raw_eeg.set_montage(montage, match_case=False, on_missing='ignore')
        
        print(f"成功设置montage，最终通道数: {len(raw_eeg.ch_names)}")
        print(f"最终通道: {raw_eeg.ch_names}")
        
        # 创建RMS地形图
        data = raw_eeg.get_data()
        rms_data = np.sqrt(np.mean(data**2, axis=1))
        
        fig, ax = plt.subplots(figsize=(12, 10))
        
        from mne.viz import plot_topomap
        im, _ = plot_topomap(rms_data, raw_eeg.info,
                           ch_type='eeg',
                           contours=8,
                           cmap='RdBu_r',
                           axes=ax,
                           show=False,
                           sphere='auto')
        
        ax.set_title(f'01.edf - 参考电极系统EEG地形图\n({len(raw_eeg.ch_names)}通道, 参考电极系统)', 
                    fontsize=16, fontweight='bold')
        
        cbar = plt.colorbar(im, ax=ax, shrink=0.8)
        cbar.set_label('RMS幅度 (µV)', rotation=270, labelpad=20)
        
        plt.tight_layout()
        save_path = f'{output_dir}/01_edf_reference_topomap.png'
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        print(f"✅ 保存地形图: {save_path}")
        plt.close(fig)
        
        return raw_eeg, True
        
    except Exception as e:
        print(f"❌ 创建地形图失败: {e}")
        return raw_eeg, False

def create_frequency_analysis(raw_eeg, output_dir="01_edf_analysis"):
    """
    创建频段分析
    """
    print(f"\n=== 频段分析 ===")
    
    freq_bands = {
        'Delta': (0.5, 4),
        'Theta': (4, 8),
        'Alpha': (8, 13),
        'Beta': (13, 30),
        'Gamma': (30, 50)
    }
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('01.edf - 频段分析 (参考电极系统)', fontsize=16, fontweight='bold')
    
    for i, (band_name, (low_freq, high_freq)) in enumerate(freq_bands.items()):
        if i >= 5:  # 只有5个频段
            break
            
        ax = axes[i//3, i%3]
        
        try:
            # 滤波
            raw_filtered = raw_eeg.copy()
            raw_filtered.filter(low_freq, high_freq, fir_design='firwin', verbose=False)
            
            # 计算功率
            data = raw_filtered.get_data()
            power = np.sqrt(np.mean(data**2, axis=1))
            
            # 创建地形图
            from mne.viz import plot_topomap
            im, _ = plot_topomap(power, raw_filtered.info,
                               ch_type='eeg',
                               contours=6,
                               cmap='RdBu_r',
                               axes=ax,
                               show=False,
                               sphere='auto')
            
            ax.set_title(f'{band_name}\n({low_freq}-{high_freq} Hz)', fontweight='bold')
            
        except Exception as e:
            ax.text(0.5, 0.5, f'{band_name}\n映射错误\n{str(e)[:30]}...', 
                   ha='center', va='center', transform=ax.transAxes, fontsize=8)
    
    # 添加整体RMS
    ax = axes[1, 2]
    try:
        data = raw_eeg.get_data()
        rms_data = np.sqrt(np.mean(data**2, axis=1))
        
        im, _ = plot_topomap(rms_data, raw_eeg.info,
                           ch_type='eeg',
                           contours=6,
                           cmap='RdBu_r',
                           axes=ax,
                           show=False,
                           sphere='auto')
        ax.set_title('整体RMS', fontweight='bold')
    except:
        ax.text(0.5, 0.5, 'RMS\n映射错误', 
               ha='center', va='center', transform=ax.transAxes)
    
    plt.tight_layout()
    save_path = f'{output_dir}/01_edf_frequency_analysis.png'
    plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"✅ 保存频段分析: {save_path}")
    plt.close(fig)

def analyze_seizure_detection(raw, eeg_channels):
    """
    分析可能的癫痫活动
    """
    print(f"\n=== 癫痫活动详细分析 ===")
    
    # 处理EEG数据
    raw_eeg = raw.copy()
    raw_eeg.pick_channels(eeg_channels)
    
    # 移除A1, A2
    channels_to_keep = [ch for ch in raw_eeg.ch_names if not any(x in ch for x in ['A1', 'A2'])]
    raw_eeg.pick_channels(channels_to_keep)
    
    data = raw_eeg.get_data()
    
    # 更详细的异常检测
    window_size = int(5 * raw.info['sfreq'])  # 5秒窗口
    n_windows = data.shape[1] // window_size
    
    print(f"分析参数:")
    print(f"  窗口大小: 5秒 ({window_size}个采样点)")
    print(f"  总窗口数: {n_windows}")
    
    # 计算每个窗口的多种指标
    window_metrics = {
        'rms': [],
        'max_amplitude': [],
        'variance': [],
        'high_freq_power': []
    }
    
    for i in range(n_windows):
        start_idx = i * window_size
        end_idx = (i + 1) * window_size
        window_data = data[:, start_idx:end_idx]
        
        # RMS功率
        rms = np.sqrt(np.mean(window_data**2))
        window_metrics['rms'].append(rms)
        
        # 最大幅度
        max_amp = np.max(np.abs(window_data))
        window_metrics['max_amplitude'].append(max_amp)
        
        # 方差
        var = np.var(window_data)
        window_metrics['variance'].append(var)
        
        # 高频功率（简单估计）
        diff_data = np.diff(window_data, axis=1)
        high_freq = np.sqrt(np.mean(diff_data**2))
        window_metrics['high_freq_power'].append(high_freq)
    
    # 检测异常
    print(f"\n异常活动检测:")
    for metric_name, values in window_metrics.items():
        values = np.array(values)
        mean_val = np.mean(values)
        std_val = np.std(values)
        threshold = mean_val + 3 * std_val
        
        anomalies = np.where(values > threshold)[0]
        
        print(f"  {metric_name}:")
        print(f"    平均值: {mean_val:.2e}")
        print(f"    标准差: {std_val:.2e}")
        print(f"    异常窗口: {len(anomalies)}个")
        
        if len(anomalies) > 0:
            print(f"    异常时间段:")
            for window_idx in anomalies[:5]:  # 只显示前5个
                time_start = window_idx * 5
                time_end = (window_idx + 1) * 5
                print(f"      {time_start}-{time_end}秒: {values[window_idx]:.2e}")

def compare_systems():
    """
    对比两种EEG系统
    """
    print(f"\n=== EEG系统对比 ===")
    
    print("PN00-1.edf vs 01.edf:")
    print("┌─────────────────┬─────────────────┬─────────────────┐")
    print("│     特征        │    PN00-1.edf   │     01.edf      │")
    print("├─────────────────┼─────────────────┼─────────────────┤")
    print("│ 总通道数        │       35        │       43        │")
    print("│ EEG通道数       │       29        │       21        │")
    print("│ 采样频率        │     512 Hz      │     500 Hz      │")
    print("│ 记录时长        │    43.7分钟     │    25.6分钟     │")
    print("│ 电极系统        │   标准命名      │   参考电极      │")
    print("│ 命名格式        │   EEG Fp1       │  EEG Fp1-Ref    │")
    print("│ 参考电极        │     无          │   A1, A2        │")
    print("│ 其他信号        │  EKG, SPO2等    │  POL系列信号    │")
    print("│ 数据类型        │   癫痫发作      │    常规EEG      │")
    print("└─────────────────┴─────────────────┴─────────────────┘")

def main():
    """
    主分析函数
    """
    print("=== 01.edf 详细分析 ===")
    
    # 基础分析
    raw, eeg_channels = analyze_reference_system()
    
    # 创建地形图
    raw_eeg, success = create_reference_topomap(raw, eeg_channels)
    
    if success:
        # 频段分析
        create_frequency_analysis(raw_eeg)
    
    # 癫痫活动分析
    analyze_seizure_detection(raw, eeg_channels)
    
    # 系统对比
    compare_systems()
    
    print(f"\n=== 分析完成 ===")
    print("生成的文件:")
    print("- 01_edf_analysis/01_edf_reference_topomap.png: 参考电极地形图")
    if success:
        print("- 01_edf_analysis/01_edf_frequency_analysis.png: 频段分析")

if __name__ == "__main__":
    main()
