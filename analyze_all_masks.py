#!/usr/bin/env python3
"""
分析masks-2文件夹中所有MaskInRawData的位置分布
"""

import numpy as np
import matplotlib.pyplot as plt
import nibabel as nib
import os
import glob
from collections import defaultdict
import warnings

warnings.filterwarnings('ignore')

def scan_all_masks():
    """
    扫描所有掩码文件并获取基本信息
    """
    print("=== 扫描所有掩码文件 ===")
    
    # 查找所有MaskInRawData文件
    mask_files = glob.glob("masks-2/*/[0-9]*_MaskInRawData.nii.gz")
    mask_files.sort(key=lambda x: int(os.path.basename(os.path.dirname(x))))
    
    print(f"找到 {len(mask_files)} 个MaskInRawData文件")
    
    mask_info = []
    valid_masks = []
    
    for i, mask_file in enumerate(mask_files):
        try:
            # 提取掩码编号
            mask_id = int(os.path.basename(os.path.dirname(mask_file)))
            
            # 加载掩码
            img = nib.load(mask_file)
            data = img.get_fdata()
            
            # 检查是否为空掩码
            if np.sum(data > 0) == 0:
                continue
            
            # 计算基本信息
            mask_binary = data > 0
            coords = np.where(mask_binary)
            
            if len(coords[0]) == 0:
                continue
            
            centroid = [np.mean(coords[0]), np.mean(coords[1]), np.mean(coords[2])]
            volume = np.sum(mask_binary)
            
            # 边界框
            bbox = {
                'x_min': coords[0].min(), 'x_max': coords[0].max(),
                'y_min': coords[1].min(), 'y_max': coords[1].max(),
                'z_min': coords[2].min(), 'z_max': coords[2].max()
            }
            
            mask_info.append({
                'id': mask_id,
                'file': mask_file,
                'centroid': centroid,
                'volume': volume,
                'bbox': bbox,
                'shape': data.shape
            })
            
            valid_masks.append(mask_file)
            
            if (i + 1) % 50 == 0:
                print(f"已处理 {i + 1}/{len(mask_files)} 个文件...")
                
        except Exception as e:
            print(f"处理文件 {mask_file} 时出错: {e}")
            continue
    
    print(f"成功加载 {len(mask_info)} 个有效掩码")
    return mask_info, valid_masks

def analyze_spatial_distribution(mask_info):
    """
    分析空间分布特征
    """
    print("\n=== 分析空间分布 ===")
    
    # 提取质心坐标
    centroids = np.array([info['centroid'] for info in mask_info])
    volumes = np.array([info['volume'] for info in mask_info])
    
    print(f"质心坐标统计:")
    print(f"  X轴: {centroids[:, 0].min():.1f} - {centroids[:, 0].max():.1f} (平均: {centroids[:, 0].mean():.1f})")
    print(f"  Y轴: {centroids[:, 1].min():.1f} - {centroids[:, 1].max():.1f} (平均: {centroids[:, 1].mean():.1f})")
    print(f"  Z轴: {centroids[:, 2].min():.1f} - {centroids[:, 2].max():.1f} (平均: {centroids[:, 2].mean():.1f})")
    
    print(f"\n体积统计:")
    print(f"  最小体积: {volumes.min():,} 体素")
    print(f"  最大体积: {volumes.max():,} 体素")
    print(f"  平均体积: {volumes.mean():.0f} 体素")
    print(f"  中位体积: {np.median(volumes):.0f} 体素")
    
    return centroids, volumes

def create_spatial_distribution_plots(mask_info, centroids, volumes):
    """
    创建空间分布图
    """
    print("\n=== 创建空间分布可视化 ===")
    
    fig, axes = plt.subplots(2, 3, figsize=(20, 12))
    fig.suptitle('所有病灶掩码的空间分布分析', fontsize=16, fontweight='bold')
    
    # 1. XY平面分布 (轴位面视角)
    ax = axes[0, 0]
    scatter = ax.scatter(centroids[:, 0], centroids[:, 1], 
                        c=volumes, cmap='viridis', alpha=0.7, s=30)
    ax.set_xlabel('X坐标')
    ax.set_ylabel('Y坐标')
    ax.set_title('XY平面分布 (轴位面视角)')
    ax.grid(True, alpha=0.3)
    plt.colorbar(scatter, ax=ax, label='体积 (体素)')
    
    # 2. XZ平面分布 (冠状面视角)
    ax = axes[0, 1]
    scatter = ax.scatter(centroids[:, 0], centroids[:, 2], 
                        c=volumes, cmap='viridis', alpha=0.7, s=30)
    ax.set_xlabel('X坐标')
    ax.set_ylabel('Z坐标')
    ax.set_title('XZ平面分布 (冠状面视角)')
    ax.grid(True, alpha=0.3)
    plt.colorbar(scatter, ax=ax, label='体积 (体素)')
    
    # 3. YZ平面分布 (矢状面视角)
    ax = axes[0, 2]
    scatter = ax.scatter(centroids[:, 1], centroids[:, 2], 
                        c=volumes, cmap='viridis', alpha=0.7, s=30)
    ax.set_xlabel('Y坐标')
    ax.set_ylabel('Z坐标')
    ax.set_title('YZ平面分布 (矢状面视角)')
    ax.grid(True, alpha=0.3)
    plt.colorbar(scatter, ax=ax, label='体积 (体素)')
    
    # 4. 体积分布直方图
    ax = axes[1, 0]
    ax.hist(volumes, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    ax.set_xlabel('体积 (体素)')
    ax.set_ylabel('频次')
    ax.set_title('病灶体积分布')
    ax.set_yscale('log')
    ax.grid(True, alpha=0.3)
    
    # 5. 左右半球分布
    ax = axes[1, 1]
    brain_center_x = 128  # 假设256的一半是脑部中线
    left_hemisphere = centroids[:, 0] < brain_center_x
    right_hemisphere = centroids[:, 0] >= brain_center_x
    
    hemisphere_counts = [np.sum(left_hemisphere), np.sum(right_hemisphere)]
    hemisphere_labels = ['左半球', '右半球']
    colors = ['lightcoral', 'lightblue']
    
    bars = ax.bar(hemisphere_labels, hemisphere_counts, color=colors, alpha=0.7)
    ax.set_ylabel('病灶数量')
    ax.set_title('左右半球分布')
    
    # 添加数值标签
    for bar, count in zip(bars, hemisphere_counts):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
               f'{count}', ha='center', va='bottom', fontweight='bold')
    
    # 6. 前后分布
    ax = axes[1, 2]
    brain_center_y = 114  # 假设228的一半是前后中线
    anterior = centroids[:, 1] < brain_center_y
    posterior = centroids[:, 1] >= brain_center_y
    
    ap_counts = [np.sum(anterior), np.sum(posterior)]
    ap_labels = ['前部', '后部']
    colors = ['lightgreen', 'lightyellow']
    
    bars = ax.bar(ap_labels, ap_counts, color=colors, alpha=0.7)
    ax.set_ylabel('病灶数量')
    ax.set_title('前后分布')
    
    # 添加数值标签
    for bar, count in zip(bars, ap_counts):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
               f'{count}', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('all_masks_spatial_distribution.png', dpi=300, bbox_inches='tight')
    print("保存: all_masks_spatial_distribution.png")
    plt.close(fig)

def create_density_maps(mask_info):
    """
    创建病灶密度图
    """
    print("\n=== 创建病灶密度图 ===")
    
    # 假设标准脑空间大小
    brain_shape = (256, 228, 256)
    
    # 创建密度图
    density_map = np.zeros(brain_shape)
    
    print("计算病灶密度...")
    for i, info in enumerate(mask_info):
        try:
            # 加载掩码数据
            img = nib.load(info['file'])
            data = img.get_fdata()
            
            # 确保形状匹配
            if data.shape == brain_shape:
                density_map += (data > 0).astype(int)
            else:
                # 如果形状不匹配，裁剪到公共区域
                min_shape = [min(density_map.shape[i], data.shape[i]) for i in range(3)]
                density_map[:min_shape[0], :min_shape[1], :min_shape[2]] += \
                    (data[:min_shape[0], :min_shape[1], :min_shape[2]] > 0).astype(int)
            
            if (i + 1) % 50 == 0:
                print(f"已处理 {i + 1}/{len(mask_info)} 个掩码...")
                
        except Exception as e:
            print(f"处理掩码 {info['id']} 时出错: {e}")
            continue
    
    print(f"密度图最大值: {density_map.max()}")
    
    # 创建密度图可视化
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('病灶密度分布图', fontsize=16, fontweight='bold')
    
    # 选择代表性切片
    center_x = brain_shape[0] // 2
    center_y = brain_shape[1] // 2
    center_z = brain_shape[2] // 2
    
    # 轴位面密度图
    ax = axes[0, 0]
    im1 = ax.imshow(density_map[:, :, center_z].T, cmap='hot', origin='lower')
    ax.set_title(f'轴位面密度 (Z={center_z})')
    ax.set_xlabel('X坐标')
    ax.set_ylabel('Y坐标')
    plt.colorbar(im1, ax=ax, label='重叠病灶数')
    
    # 冠状面密度图
    ax = axes[0, 1]
    im2 = ax.imshow(density_map[:, center_y, :].T, cmap='hot', origin='lower')
    ax.set_title(f'冠状面密度 (Y={center_y})')
    ax.set_xlabel('X坐标')
    ax.set_ylabel('Z坐标')
    plt.colorbar(im2, ax=ax, label='重叠病灶数')
    
    # 矢状面密度图
    ax = axes[0, 2]
    im3 = ax.imshow(density_map[center_x, :, :].T, cmap='hot', origin='lower')
    ax.set_title(f'矢状面密度 (X={center_x})')
    ax.set_xlabel('Y坐标')
    ax.set_ylabel('Z坐标')
    plt.colorbar(im3, ax=ax, label='重叠病灶数')
    
    # 密度分布直方图
    ax = axes[1, 0]
    density_values = density_map[density_map > 0]
    ax.hist(density_values, bins=range(1, int(density_values.max()) + 2), 
            alpha=0.7, color='orange', edgecolor='black')
    ax.set_xlabel('重叠病灶数')
    ax.set_ylabel('体素数')
    ax.set_title('密度分布直方图')
    ax.set_yscale('log')
    ax.grid(True, alpha=0.3)
    
    # 最高密度区域
    ax = axes[1, 1]
    max_density = density_map.max()
    high_density_threshold = max_density * 0.8
    high_density_coords = np.where(density_map >= high_density_threshold)
    
    if len(high_density_coords[0]) > 0:
        ax.scatter(high_density_coords[0], high_density_coords[1], 
                  c=density_map[high_density_coords], cmap='hot', s=20)
        ax.set_xlabel('X坐标')
        ax.set_ylabel('Y坐标')
        ax.set_title(f'高密度区域 (≥{high_density_threshold:.0f})')
        ax.grid(True, alpha=0.3)
    
    # 统计信息
    ax = axes[1, 2]
    stats_text = f"""
    病灶密度统计:
    
    • 总病灶数: {len(mask_info)}
    • 最大重叠数: {max_density:.0f}
    • 有病灶的体素: {np.sum(density_map > 0):,}
    • 平均密度: {density_map[density_map > 0].mean():.2f}
    
    高密度区域:
    • 阈值: ≥{high_density_threshold:.0f}
    • 体素数: {len(high_density_coords[0]):,}
    • 占比: {len(high_density_coords[0])/np.sum(density_map > 0)*100:.1f}%
    """
    
    ax.text(0.05, 0.95, stats_text, transform=ax.transAxes,
            fontsize=11, verticalalignment='top',
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow"))
    ax.set_title('统计信息')
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('lesion_density_maps.png', dpi=300, bbox_inches='tight')
    print("保存: lesion_density_maps.png")
    plt.close(fig)
    
    return density_map

def create_summary_statistics(mask_info, centroids, volumes):
    """
    创建总结统计图
    """
    print("\n=== 创建总结统计 ===")
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('病灶掩码总结统计', fontsize=16, fontweight='bold')
    
    # 1. 病灶ID vs 体积
    ax = axes[0, 0]
    mask_ids = [info['id'] for info in mask_info]
    ax.scatter(mask_ids, volumes, alpha=0.6, s=20)
    ax.set_xlabel('病灶ID')
    ax.set_ylabel('体积 (体素)')
    ax.set_title('病灶ID vs 体积')
    ax.set_yscale('log')
    ax.grid(True, alpha=0.3)
    
    # 2. 体积分布箱线图（按半球）
    ax = axes[0, 1]
    brain_center_x = 128
    left_volumes = volumes[centroids[:, 0] < brain_center_x]
    right_volumes = volumes[centroids[:, 0] >= brain_center_x]
    
    box_data = [left_volumes, right_volumes]
    box_labels = ['左半球', '右半球']
    bp = ax.boxplot(box_data, labels=box_labels, patch_artist=True)
    bp['boxes'][0].set_facecolor('lightcoral')
    bp['boxes'][1].set_facecolor('lightblue')
    ax.set_ylabel('体积 (体素)')
    ax.set_title('左右半球体积分布')
    ax.set_yscale('log')
    ax.grid(True, alpha=0.3)
    
    # 3. 空间分布热图 (XY投影)
    ax = axes[1, 0]
    # 创建2D直方图
    hist, xedges, yedges = np.histogram2d(centroids[:, 0], centroids[:, 1], bins=20)
    extent = [xedges[0], xedges[-1], yedges[0], yedges[-1]]
    im = ax.imshow(hist.T, extent=extent, origin='lower', cmap='hot', aspect='auto')
    ax.set_xlabel('X坐标')
    ax.set_ylabel('Y坐标')
    ax.set_title('病灶分布热图 (XY平面)')
    plt.colorbar(im, ax=ax, label='病灶数量')
    
    # 4. 详细统计信息
    ax = axes[1, 1]
    
    # 计算更多统计信息
    brain_center_x = 128
    brain_center_y = 114
    brain_center_z = 128
    
    left_count = np.sum(centroids[:, 0] < brain_center_x)
    right_count = np.sum(centroids[:, 0] >= brain_center_x)
    anterior_count = np.sum(centroids[:, 1] < brain_center_y)
    posterior_count = np.sum(centroids[:, 1] >= brain_center_y)
    superior_count = np.sum(centroids[:, 2] >= brain_center_z)
    inferior_count = np.sum(centroids[:, 2] < brain_center_z)
    
    stats_text = f"""
    病灶分布统计:
    
    总病灶数: {len(mask_info)}
    
    半球分布:
    • 左半球: {left_count} ({left_count/len(mask_info)*100:.1f}%)
    • 右半球: {right_count} ({right_count/len(mask_info)*100:.1f}%)
    
    前后分布:
    • 前部: {anterior_count} ({anterior_count/len(mask_info)*100:.1f}%)
    • 后部: {posterior_count} ({posterior_count/len(mask_info)*100:.1f}%)
    
    上下分布:
    • 上部: {superior_count} ({superior_count/len(mask_info)*100:.1f}%)
    • 下部: {inferior_count} ({inferior_count/len(mask_info)*100:.1f}%)
    
    体积统计:
    • 最小: {volumes.min():,} 体素
    • 最大: {volumes.max():,} 体素
    • 平均: {volumes.mean():.0f} 体素
    • 中位: {np.median(volumes):.0f} 体素
    """
    
    ax.text(0.05, 0.95, stats_text, transform=ax.transAxes,
            fontsize=11, verticalalignment='top',
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))
    ax.set_title('详细统计信息')
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('lesion_summary_statistics.png', dpi=300, bbox_inches='tight')
    print("保存: lesion_summary_statistics.png")
    plt.close(fig)

def main():
    """
    主分析函数
    """
    print("=== 分析masks-2中所有MaskInRawData ===")
    
    # 检查文件夹是否存在
    if not os.path.exists("masks-2"):
        print("错误: 找不到masks-2文件夹!")
        return
    
    # 扫描所有掩码
    mask_info, valid_masks = scan_all_masks()
    
    if len(mask_info) == 0:
        print("错误: 没有找到有效的掩码文件!")
        return
    
    # 分析空间分布
    centroids, volumes = analyze_spatial_distribution(mask_info)
    
    # 创建可视化
    create_spatial_distribution_plots(mask_info, centroids, volumes)
    create_summary_statistics(mask_info, centroids, volumes)
    
    # 创建密度图（可能需要较长时间）
    print("\n注意: 密度图计算可能需要几分钟时间...")
    density_map = create_density_maps(mask_info)
    
    print(f"\n=== 分析完成 ===")
    print("生成的文件:")
    print("- all_masks_spatial_distribution.png: 空间分布分析")
    print("- lesion_summary_statistics.png: 总结统计")
    print("- lesion_density_maps.png: 病灶密度图")
    
    print(f"\n=== 主要发现 ===")
    print(f"• 总共分析了 {len(mask_info)} 个有效病灶")
    print(f"• 病灶体积范围: {volumes.min():,} - {volumes.max():,} 体素")
    print(f"• 平均病灶体积: {volumes.mean():.0f} 体素")
    
    # 半球分布
    brain_center_x = 128
    left_count = np.sum(centroids[:, 0] < brain_center_x)
    right_count = np.sum(centroids[:, 0] >= brain_center_x)
    print(f"• 左半球病灶: {left_count} ({left_count/len(mask_info)*100:.1f}%)")
    print(f"• 右半球病灶: {right_count} ({right_count/len(mask_info)*100:.1f}%)")

if __name__ == "__main__":
    main()
