"""
EEG源定位系统 - 边界元法(BEM)建模系统

该模块实现高精度的BEM边界条件建模，确保与真实头部结构完全一致。
为EEG源定位提供准确的正向模型，支持3层和5层BEM模型。

主要功能：
1. 高质量三角网格生成
2. 多层BEM模型构建
3. 导电率参数优化
4. 边界条件精确设置
5. 数值精度控制
"""

import numpy as np
import nibabel as nib
from scipy import spatial, linalg
from skimage import measure, morphology
from typing import Dict, List, Optional, Tuple, Union
import logging
import trimesh
from pathlib import Path
import yaml

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BEMModeler:
    """BEM建模器主类"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化BEM建模器
        
        参数:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.bem_config = self.config['bem_modeling']
        
        # 初始化子模块
        self.mesh_generator = MeshGenerator(self.config)
        self.conductivity_manager = ConductivityManager(self.config)
        self.boundary_calculator = BoundaryCalculator(self.config)
        self.geometry_validator = GeometryValidator(self.config)
        
        logger.info(f"BEM建模器初始化完成，模型类型: {self.bem_config['model_type']}")
        
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            raise
            
    def build_bem_model(self, tissue_masks: Dict[str, np.ndarray], 
                       voxel_size: Tuple[float, float, float]) -> Dict:
        """
        构建完整的BEM模型
        
        参数:
            tissue_masks: 组织分割结果
            voxel_size: 体素尺寸
            
        返回:
            Dict: 完整的BEM模型
        """
        try:
            logger.info("开始构建BEM模型")
            
            # 步骤1: 生成高质量三角网格
            meshes = self.mesh_generator.generate_meshes(tissue_masks, voxel_size)
            
            # 步骤2: 验证几何结构
            geometry_valid = self.geometry_validator.validate_geometry(meshes)
            if not geometry_valid:
                logger.warning("几何结构验证失败，尝试修复")
                meshes = self.geometry_validator.repair_geometry(meshes)
                
            # 步骤3: 设置导电率参数
            conductivities = self.conductivity_manager.get_conductivities()
            
            # 步骤4: 计算BEM边界条件
            bem_matrices = self.boundary_calculator.calculate_bem_matrices(
                meshes, conductivities, voxel_size
            )
            
            # 步骤5: 构建完整BEM模型
            bem_model = {
                'meshes': meshes,
                'conductivities': conductivities,
                'bem_matrices': bem_matrices,
                'model_info': {
                    'model_type': self.bem_config['model_type'],
                    'num_layers': len(meshes),
                    'total_vertices': sum(len(mesh['vertices']) for mesh in meshes.values()),
                    'total_faces': sum(len(mesh['faces']) for mesh in meshes.values()),
                    'numerical_accuracy': self.bem_config['numerical_accuracy']
                }
            }
            
            # 步骤6: 最终验证
            validation_results = self._validate_bem_model(bem_model)
            bem_model['validation'] = validation_results
            
            logger.info(f"BEM模型构建完成，层数: {len(meshes)}, "
                       f"总顶点数: {bem_model['model_info']['total_vertices']}")
            
            return bem_model
            
        except Exception as e:
            logger.error(f"BEM模型构建失败: {e}")
            raise
            
    def _validate_bem_model(self, bem_model: Dict) -> Dict:
        """验证BEM模型"""
        try:
            validation_results = {
                'geometry_valid': True,
                'conductivity_valid': True,
                'matrices_valid': True,
                'overall_valid': True
            }
            
            # 验证几何结构
            meshes = bem_model['meshes']
            for tissue, mesh in meshes.items():
                if not self._validate_mesh_quality(mesh):
                    validation_results['geometry_valid'] = False
                    logger.warning(f"{tissue} 网格质量不合格")
                    
            # 验证导电率
            conductivities = bem_model['conductivities']
            for tissue, conductivity in conductivities.items():
                if conductivity <= 0:
                    validation_results['conductivity_valid'] = False
                    logger.warning(f"{tissue} 导电率无效: {conductivity}")
                    
            # 验证BEM矩阵
            bem_matrices = bem_model['bem_matrices']
            if not self._validate_bem_matrices(bem_matrices):
                validation_results['matrices_valid'] = False
                logger.warning("BEM矩阵验证失败")
                
            # 总体验证
            validation_results['overall_valid'] = all([
                validation_results['geometry_valid'],
                validation_results['conductivity_valid'],
                validation_results['matrices_valid']
            ])
            
            return validation_results
            
        except Exception as e:
            logger.error(f"BEM模型验证失败: {e}")
            return {'overall_valid': False}
            
    def _validate_mesh_quality(self, mesh: Dict) -> bool:
        """验证网格质量"""
        try:
            vertices = mesh['vertices']
            faces = mesh['faces']
            
            # 检查基本结构
            if len(vertices) < 3 or len(faces) < 1:
                return False
                
            # 检查面的有效性
            max_vertex_idx = len(vertices) - 1
            if np.any(faces > max_vertex_idx) or np.any(faces < 0):
                return False
                
            # 检查网格封闭性
            if not self._is_mesh_closed(vertices, faces):
                return False
                
            return True
            
        except Exception as e:
            logger.warning(f"网格质量验证失败: {e}")
            return False
            
    def _is_mesh_closed(self, vertices: np.ndarray, faces: np.ndarray) -> bool:
        """检查网格是否封闭"""
        try:
            # 使用trimesh检查网格封闭性
            mesh = trimesh.Trimesh(vertices=vertices, faces=faces)
            return mesh.is_closed
        except:
            return False
            
    def _validate_bem_matrices(self, bem_matrices: Dict) -> bool:
        """验证BEM矩阵"""
        try:
            for matrix_name, matrix in bem_matrices.items():
                if matrix is None or matrix.size == 0:
                    return False
                    
                # 检查矩阵条件数
                if matrix.ndim == 2 and matrix.shape[0] == matrix.shape[1]:
                    try:
                        cond_num = np.linalg.cond(matrix)
                        if cond_num > 1e12:  # 条件数过大
                            logger.warning(f"{matrix_name} 矩阵条件数过大: {cond_num}")
                            return False
                    except:
                        pass
                        
            return True
            
        except Exception as e:
            logger.warning(f"BEM矩阵验证失败: {e}")
            return False


class MeshGenerator:
    """网格生成器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.mesh_config = config['bem_modeling']['mesh']
        
    def generate_meshes(self, tissue_masks: Dict[str, np.ndarray], 
                       voxel_size: Tuple[float, float, float]) -> Dict[str, Dict]:
        """
        生成高质量三角网格
        
        参数:
            tissue_masks: 组织分割结果
            voxel_size: 体素尺寸
            
        返回:
            Dict: 各组织的三角网格
        """
        try:
            meshes = {}
            
            # 根据模型类型选择组织
            model_type = self.config['bem_modeling']['model_type']
            
            if model_type == '3_layer':
                tissue_order = ['scalp', 'skull', 'brain']
                # 合并灰质和白质为脑组织
                if 'gray' in tissue_masks and 'white' in tissue_masks:
                    brain_mask = tissue_masks['gray'] | tissue_masks['white']
                    tissue_masks['brain'] = brain_mask
            else:  # 5_layer
                tissue_order = ['scalp', 'skull', 'csf', 'gray', 'white']
                
            # 为每个组织生成网格
            for tissue in tissue_order:
                if tissue in tissue_masks:
                    logger.info(f"生成 {tissue} 组织网格")
                    mesh = self._generate_single_mesh(
                        tissue_masks[tissue], tissue, voxel_size
                    )
                    if mesh is not None:
                        meshes[tissue] = mesh
                    else:
                        logger.warning(f"{tissue} 网格生成失败")
                        
            return meshes
            
        except Exception as e:
            logger.error(f"网格生成失败: {e}")
            raise
            
    def _generate_single_mesh(self, mask: np.ndarray, tissue_name: str, 
                            voxel_size: Tuple[float, float, float]) -> Optional[Dict]:
        """生成单个组织的网格"""
        try:
            # 使用marching cubes算法生成初始网格
            vertices, faces, normals, values = measure.marching_cubes(
                mask, level=0.5, spacing=voxel_size
            )
            
            if len(vertices) == 0 or len(faces) == 0:
                logger.warning(f"{tissue_name} 组织无法生成有效网格")
                return None
                
            # 网格简化和优化
            vertices, faces = self._optimize_mesh(vertices, faces, tissue_name)
            
            # 计算法向量
            normals = self._calculate_normals(vertices, faces)
            
            # 创建网格字典
            mesh = {
                'vertices': vertices,
                'faces': faces,
                'normals': normals,
                'tissue_name': tissue_name,
                'num_vertices': len(vertices),
                'num_faces': len(faces)
            }
            
            logger.info(f"{tissue_name} 网格生成完成: {len(vertices)} 顶点, {len(faces)} 面")
            return mesh
            
        except Exception as e:
            logger.error(f"{tissue_name} 网格生成失败: {e}")
            return None
            
    def _optimize_mesh(self, vertices: np.ndarray, faces: np.ndarray, 
                      tissue_name: str) -> Tuple[np.ndarray, np.ndarray]:
        """优化网格质量"""
        try:
            # 获取目标面数
            target_faces = self.mesh_config.get(f'{tissue_name}_density', 5120)
            
            # 如果面数过多，进行简化
            if len(faces) > target_faces * 1.5:
                # 使用trimesh进行网格简化
                mesh = trimesh.Trimesh(vertices=vertices, faces=faces)
                
                # 简化网格
                simplified = mesh.simplify_quadric_decimation(target_faces)
                
                if simplified is not None and len(simplified.vertices) > 0:
                    vertices = simplified.vertices
                    faces = simplified.faces
                    logger.info(f"{tissue_name} 网格简化: {len(faces)} 面")
                    
            # 平滑网格
            vertices = self._smooth_mesh(vertices, faces)
            
            return vertices, faces
            
        except Exception as e:
            logger.warning(f"{tissue_name} 网格优化失败: {e}")
            return vertices, faces
            
    def _smooth_mesh(self, vertices: np.ndarray, faces: np.ndarray, 
                    iterations: int = 3) -> np.ndarray:
        """平滑网格顶点"""
        try:
            smoothed_vertices = vertices.copy()
            
            for _ in range(iterations):
                # 拉普拉斯平滑
                new_vertices = smoothed_vertices.copy()
                
                for i in range(len(vertices)):
                    # 找到相邻顶点
                    adjacent_faces = faces[np.any(faces == i, axis=1)]
                    adjacent_vertices = np.unique(adjacent_faces.flatten())
                    adjacent_vertices = adjacent_vertices[adjacent_vertices != i]
                    
                    if len(adjacent_vertices) > 0:
                        # 计算相邻顶点的平均位置
                        avg_pos = np.mean(smoothed_vertices[adjacent_vertices], axis=0)
                        
                        # 平滑因子
                        lambda_smooth = 0.1
                        new_vertices[i] = (1 - lambda_smooth) * smoothed_vertices[i] + lambda_smooth * avg_pos
                        
                smoothed_vertices = new_vertices
                
            return smoothed_vertices
            
        except Exception as e:
            logger.warning(f"网格平滑失败: {e}")
            return vertices
            
    def _calculate_normals(self, vertices: np.ndarray, faces: np.ndarray) -> np.ndarray:
        """计算顶点法向量"""
        try:
            # 初始化法向量数组
            normals = np.zeros_like(vertices)
            
            # 计算每个面的法向量
            for face in faces:
                v0, v1, v2 = vertices[face]
                
                # 计算面法向量
                edge1 = v1 - v0
                edge2 = v2 - v0
                face_normal = np.cross(edge1, edge2)
                
                # 归一化
                norm = np.linalg.norm(face_normal)
                if norm > 0:
                    face_normal /= norm
                    
                # 累加到顶点法向量
                normals[face] += face_normal
                
            # 归一化顶点法向量
            for i in range(len(normals)):
                norm = np.linalg.norm(normals[i])
                if norm > 0:
                    normals[i] /= norm
                    
            return normals
            
        except Exception as e:
            logger.warning(f"法向量计算失败: {e}")
            return np.zeros_like(vertices)


class ConductivityManager:
    """导电率管理器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.conductivity_config = config['bem_modeling']['conductivity']
        
    def get_conductivities(self) -> Dict[str, float]:
        """获取组织导电率"""
        try:
            # 从配置文件获取导电率值
            conductivities = {}
            
            for tissue, conductivity in self.conductivity_config.items():
                conductivities[tissue] = float(conductivity)
                
            # 验证导电率值
            self._validate_conductivities(conductivities)
            
            logger.info(f"导电率设置: {conductivities}")
            return conductivities
            
        except Exception as e:
            logger.error(f"导电率获取失败: {e}")
            raise
            
    def _validate_conductivities(self, conductivities: Dict[str, float]):
        """验证导电率值"""
        try:
            # 检查导电率范围
            valid_ranges = {
                'scalp': (0.1, 1.0),
                'skull': (0.001, 0.1),
                'csf': (1.0, 2.0),
                'gray': (0.1, 1.0),
                'white': (0.05, 0.5),
                'brain': (0.1, 1.0)  # 3层模型中的脑组织
            }
            
            for tissue, conductivity in conductivities.items():
                if tissue in valid_ranges:
                    min_val, max_val = valid_ranges[tissue]
                    if not (min_val <= conductivity <= max_val):
                        logger.warning(f"{tissue} 导电率 {conductivity} 超出合理范围 [{min_val}, {max_val}]")
                        
                if conductivity <= 0:
                    raise ValueError(f"{tissue} 导电率必须为正值: {conductivity}")
                    
        except Exception as e:
            logger.error(f"导电率验证失败: {e}")
            raise


class BoundaryCalculator:
    """边界条件计算器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.numerical_accuracy = config['bem_modeling']['numerical_accuracy']
        
    def calculate_bem_matrices(self, meshes: Dict[str, Dict], 
                             conductivities: Dict[str, float],
                             voxel_size: Tuple[float, float, float]) -> Dict[str, np.ndarray]:
        """
        计算BEM边界条件矩阵
        
        参数:
            meshes: 组织网格
            conductivities: 导电率
            voxel_size: 体素尺寸
            
        返回:
            Dict: BEM矩阵
        """
        try:
            logger.info("开始计算BEM边界条件矩阵")
            
            bem_matrices = {}
            
            # 计算几何矩阵
            geometry_matrix = self._calculate_geometry_matrix(meshes)
            bem_matrices['geometry'] = geometry_matrix
            
            # 计算导电率矩阵
            conductivity_matrix = self._calculate_conductivity_matrix(
                meshes, conductivities
            )
            bem_matrices['conductivity'] = conductivity_matrix
            
            # 计算系统矩阵
            system_matrix = self._calculate_system_matrix(
                geometry_matrix, conductivity_matrix
            )
            bem_matrices['system'] = system_matrix
            
            # 计算导联场矩阵
            leadfield_matrix = self._calculate_leadfield_matrix(
                meshes, system_matrix, voxel_size
            )
            bem_matrices['leadfield'] = leadfield_matrix
            
            logger.info("BEM边界条件矩阵计算完成")
            return bem_matrices
            
        except Exception as e:
            logger.error(f"BEM矩阵计算失败: {e}")
            raise

    def _calculate_geometry_matrix(self, meshes: Dict[str, Dict]) -> np.ndarray:
        """计算几何矩阵"""
        try:
            # 收集所有顶点
            all_vertices = []
            vertex_offsets = {}
            current_offset = 0

            for tissue, mesh in meshes.items():
                vertices = mesh['vertices']
                all_vertices.append(vertices)
                vertex_offsets[tissue] = current_offset
                current_offset += len(vertices)

            if not all_vertices:
                raise ValueError("没有有效的网格数据")

            # 合并所有顶点
            combined_vertices = np.vstack(all_vertices)
            total_vertices = len(combined_vertices)

            # 初始化几何矩阵
            geometry_matrix = np.zeros((total_vertices, total_vertices))

            # 计算顶点间的几何关系
            for i in range(total_vertices):
                for j in range(total_vertices):
                    if i != j:
                        # 计算顶点间距离
                        distance = np.linalg.norm(combined_vertices[i] - combined_vertices[j])

                        if distance > self.numerical_accuracy:
                            # 使用格林函数
                            geometry_matrix[i, j] = 1.0 / (4.0 * np.pi * distance)
                        else:
                            # 处理奇异性
                            geometry_matrix[i, j] = 0.0

            logger.info(f"几何矩阵计算完成: {total_vertices}x{total_vertices}")
            return geometry_matrix

        except Exception as e:
            logger.error(f"几何矩阵计算失败: {e}")
            raise

    def _calculate_conductivity_matrix(self, meshes: Dict[str, Dict],
                                     conductivities: Dict[str, float]) -> np.ndarray:
        """计算导电率矩阵"""
        try:
            # 计算总顶点数
            total_vertices = sum(len(mesh['vertices']) for mesh in meshes.values())

            # 初始化导电率矩阵
            conductivity_matrix = np.zeros((total_vertices, total_vertices))

            current_offset = 0
            for tissue, mesh in meshes.items():
                num_vertices = len(mesh['vertices'])
                conductivity = conductivities.get(tissue, 0.33)  # 默认导电率

                # 在对角线上设置导电率
                for i in range(num_vertices):
                    idx = current_offset + i
                    conductivity_matrix[idx, idx] = conductivity

                current_offset += num_vertices

            logger.info(f"导电率矩阵计算完成: {total_vertices}x{total_vertices}")
            return conductivity_matrix

        except Exception as e:
            logger.error(f"导电率矩阵计算失败: {e}")
            raise

    def _calculate_system_matrix(self, geometry_matrix: np.ndarray,
                               conductivity_matrix: np.ndarray) -> np.ndarray:
        """计算系统矩阵"""
        try:
            # 系统矩阵 = 几何矩阵 * 导电率矩阵
            system_matrix = np.dot(geometry_matrix, conductivity_matrix)

            # 检查矩阵条件数
            cond_num = np.linalg.cond(system_matrix)
            if cond_num > 1e12:
                logger.warning(f"系统矩阵条件数过大: {cond_num}")

                # 添加正则化
                regularization = self.numerical_accuracy
                system_matrix += regularization * np.eye(system_matrix.shape[0])

            logger.info(f"系统矩阵计算完成，条件数: {cond_num:.2e}")
            return system_matrix

        except Exception as e:
            logger.error(f"系统矩阵计算失败: {e}")
            raise

    def _calculate_leadfield_matrix(self, meshes: Dict[str, Dict],
                                  system_matrix: np.ndarray,
                                  voxel_size: Tuple[float, float, float]) -> np.ndarray:
        """计算导联场矩阵"""
        try:
            # 定义源空间网格
            source_grid = self._create_source_grid(meshes, voxel_size)
            num_sources = len(source_grid)

            # 计算总顶点数（电极数）
            total_vertices = sum(len(mesh['vertices']) for mesh in meshes.values())

            # 初始化导联场矩阵 (电极数 x 源数)
            leadfield_matrix = np.zeros((total_vertices, num_sources))

            # 计算每个源位置的导联场
            for source_idx, source_pos in enumerate(source_grid):
                # 计算源到各个边界点的贡献
                source_contribution = self._calculate_source_contribution(
                    source_pos, meshes, system_matrix
                )

                leadfield_matrix[:, source_idx] = source_contribution

                if source_idx % 100 == 0:
                    logger.info(f"导联场计算进度: {source_idx}/{num_sources}")

            logger.info(f"导联场矩阵计算完成: {total_vertices}x{num_sources}")
            return leadfield_matrix

        except Exception as e:
            logger.error(f"导联场矩阵计算失败: {e}")
            raise

    def _create_source_grid(self, meshes: Dict[str, Dict],
                          voxel_size: Tuple[float, float, float]) -> np.ndarray:
        """创建源空间网格"""
        try:
            # 获取脑组织边界
            brain_tissues = ['gray', 'white', 'brain']  # 可能的脑组织名称
            brain_vertices = []

            for tissue in brain_tissues:
                if tissue in meshes:
                    brain_vertices.append(meshes[tissue]['vertices'])

            if not brain_vertices:
                # 如果没有脑组织，使用所有组织
                brain_vertices = [mesh['vertices'] for mesh in meshes.values()]

            # 合并脑组织顶点
            all_brain_vertices = np.vstack(brain_vertices)

            # 计算边界框
            min_coords = np.min(all_brain_vertices, axis=0)
            max_coords = np.max(all_brain_vertices, axis=0)

            # 根据配置的源空间间距创建网格
            spacing = self.config.get('source_localization', {}).get('source_space', {}).get('spacing', 5)  # 5mm默认

            # 创建规则网格
            x_coords = np.arange(min_coords[0], max_coords[0], spacing)
            y_coords = np.arange(min_coords[1], max_coords[1], spacing)
            z_coords = np.arange(min_coords[2], max_coords[2], spacing)

            # 生成网格点
            xx, yy, zz = np.meshgrid(x_coords, y_coords, z_coords)
            source_grid = np.column_stack([xx.ravel(), yy.ravel(), zz.ravel()])

            logger.info(f"源空间网格创建完成: {len(source_grid)} 个源点")
            return source_grid

        except Exception as e:
            logger.error(f"源空间网格创建失败: {e}")
            raise

    def _calculate_source_contribution(self, source_pos: np.ndarray,
                                     meshes: Dict[str, Dict],
                                     system_matrix: np.ndarray) -> np.ndarray:
        """计算单个源的贡献"""
        try:
            # 收集所有边界顶点
            all_vertices = []
            for mesh in meshes.values():
                all_vertices.append(mesh['vertices'])

            combined_vertices = np.vstack(all_vertices)
            num_vertices = len(combined_vertices)

            # 计算源到各边界点的贡献
            contribution = np.zeros(num_vertices)

            for i, vertex in enumerate(combined_vertices):
                # 计算距离
                distance = np.linalg.norm(source_pos - vertex)

                if distance > self.numerical_accuracy:
                    # 使用偶极子模型
                    contribution[i] = 1.0 / (4.0 * np.pi * distance**2)
                else:
                    contribution[i] = 0.0

            # 通过系统矩阵求解边界条件
            try:
                # 求解线性系统 Ax = b
                boundary_solution = linalg.solve(system_matrix, contribution)
                return boundary_solution
            except linalg.LinAlgError:
                # 如果直接求解失败，使用最小二乘法
                boundary_solution = linalg.lstsq(system_matrix, contribution)[0]
                return boundary_solution

        except Exception as e:
            logger.warning(f"源贡献计算失败: {e}")
            return np.zeros(len(combined_vertices))


class GeometryValidator:
    """几何验证器"""

    def __init__(self, config: Dict):
        self.config = config

    def validate_geometry(self, meshes: Dict[str, Dict]) -> bool:
        """验证几何结构"""
        try:
            logger.info("开始几何结构验证")

            validation_results = []

            for tissue, mesh in meshes.items():
                # 验证单个网格
                is_valid = self._validate_single_mesh(mesh, tissue)
                validation_results.append(is_valid)

            # 验证网格间的拓扑关系
            topology_valid = self._validate_topology(meshes)
            validation_results.append(topology_valid)

            overall_valid = all(validation_results)

            if overall_valid:
                logger.info("几何结构验证通过")
            else:
                logger.warning("几何结构验证失败")

            return overall_valid

        except Exception as e:
            logger.error(f"几何结构验证失败: {e}")
            return False

    def _validate_single_mesh(self, mesh: Dict, tissue_name: str) -> bool:
        """验证单个网格"""
        try:
            vertices = mesh['vertices']
            faces = mesh['faces']

            # 检查基本结构
            if len(vertices) < 4 or len(faces) < 4:
                logger.warning(f"{tissue_name} 网格顶点或面数过少")
                return False

            # 检查面的有效性
            max_vertex_idx = len(vertices) - 1
            if np.any(faces > max_vertex_idx) or np.any(faces < 0):
                logger.warning(f"{tissue_name} 网格面索引无效")
                return False

            # 检查网格封闭性
            try:
                mesh_obj = trimesh.Trimesh(vertices=vertices, faces=faces)
                if not mesh_obj.is_closed:
                    logger.warning(f"{tissue_name} 网格不封闭")
                    return False
            except:
                logger.warning(f"{tissue_name} 网格结构异常")
                return False

            # 检查网格质量
            quality_score = self._calculate_mesh_quality(vertices, faces)
            if quality_score < 0.3:  # 质量阈值
                logger.warning(f"{tissue_name} 网格质量过低: {quality_score:.3f}")
                return False

            logger.info(f"{tissue_name} 网格验证通过，质量评分: {quality_score:.3f}")
            return True

        except Exception as e:
            logger.error(f"{tissue_name} 网格验证失败: {e}")
            return False

    def _calculate_mesh_quality(self, vertices: np.ndarray, faces: np.ndarray) -> float:
        """计算网格质量评分"""
        try:
            quality_scores = []

            for face in faces:
                # 获取三角形顶点
                v0, v1, v2 = vertices[face]

                # 计算边长
                edge1 = np.linalg.norm(v1 - v0)
                edge2 = np.linalg.norm(v2 - v1)
                edge3 = np.linalg.norm(v0 - v2)

                # 计算面积
                cross_product = np.cross(v1 - v0, v2 - v0)
                area = 0.5 * np.linalg.norm(cross_product)

                if area > 0:
                    # 计算形状质量（接近等边三角形时质量最高）
                    perimeter = edge1 + edge2 + edge3
                    quality = 4.0 * np.sqrt(3.0) * area / (perimeter**2)
                    quality_scores.append(quality)

            return np.mean(quality_scores) if quality_scores else 0.0

        except Exception as e:
            logger.warning(f"网格质量计算失败: {e}")
            return 0.0

    def _validate_topology(self, meshes: Dict[str, Dict]) -> bool:
        """验证网格拓扑关系"""
        try:
            # 检查网格层次关系（外层包含内层）
            tissue_order = ['scalp', 'skull', 'csf', 'gray', 'white']

            for i in range(len(tissue_order) - 1):
                outer_tissue = tissue_order[i]
                inner_tissue = tissue_order[i + 1]

                if outer_tissue in meshes and inner_tissue in meshes:
                    if not self._check_containment(meshes[outer_tissue], meshes[inner_tissue]):
                        logger.warning(f"{outer_tissue} 未正确包含 {inner_tissue}")
                        return False

            return True

        except Exception as e:
            logger.error(f"拓扑关系验证失败: {e}")
            return False

    def _check_containment(self, outer_mesh: Dict, inner_mesh: Dict) -> bool:
        """检查外层网格是否包含内层网格"""
        try:
            # 简化的包含检查：检查内层顶点是否在外层网格内部
            outer_vertices = outer_mesh['vertices']
            inner_vertices = inner_mesh['vertices']

            # 计算外层网格的边界框
            outer_min = np.min(outer_vertices, axis=0)
            outer_max = np.max(outer_vertices, axis=0)

            # 检查内层顶点是否在边界框内
            inner_min = np.min(inner_vertices, axis=0)
            inner_max = np.max(inner_vertices, axis=0)

            # 简单的边界框包含检查
            contained = (np.all(inner_min >= outer_min) and
                        np.all(inner_max <= outer_max))

            return contained

        except Exception as e:
            logger.warning(f"包含关系检查失败: {e}")
            return True  # 默认通过

    def repair_geometry(self, meshes: Dict[str, Dict]) -> Dict[str, Dict]:
        """修复几何结构"""
        try:
            logger.info("开始几何结构修复")

            repaired_meshes = {}

            for tissue, mesh in meshes.items():
                repaired_mesh = self._repair_single_mesh(mesh, tissue)
                if repaired_mesh is not None:
                    repaired_meshes[tissue] = repaired_mesh
                else:
                    logger.warning(f"{tissue} 网格修复失败，保留原始网格")
                    repaired_meshes[tissue] = mesh

            logger.info("几何结构修复完成")
            return repaired_meshes

        except Exception as e:
            logger.error(f"几何结构修复失败: {e}")
            return meshes

    def _repair_single_mesh(self, mesh: Dict, tissue_name: str) -> Optional[Dict]:
        """修复单个网格"""
        try:
            vertices = mesh['vertices']
            faces = mesh['faces']

            # 使用trimesh进行网格修复
            mesh_obj = trimesh.Trimesh(vertices=vertices, faces=faces)

            # 修复网格
            mesh_obj.remove_duplicate_faces()
            mesh_obj.remove_degenerate_faces()
            mesh_obj.remove_unreferenced_vertices()

            # 如果网格不封闭，尝试填充洞
            if not mesh_obj.is_closed:
                try:
                    mesh_obj.fill_holes()
                except:
                    logger.warning(f"{tissue_name} 网格洞填充失败")

            # 重新计算法向量
            normals = self._calculate_vertex_normals(mesh_obj.vertices, mesh_obj.faces)

            repaired_mesh = {
                'vertices': mesh_obj.vertices,
                'faces': mesh_obj.faces,
                'normals': normals,
                'tissue_name': tissue_name,
                'num_vertices': len(mesh_obj.vertices),
                'num_faces': len(mesh_obj.faces)
            }

            logger.info(f"{tissue_name} 网格修复完成")
            return repaired_mesh

        except Exception as e:
            logger.error(f"{tissue_name} 网格修复失败: {e}")
            return None

    def _calculate_vertex_normals(self, vertices: np.ndarray, faces: np.ndarray) -> np.ndarray:
        """计算顶点法向量"""
        try:
            normals = np.zeros_like(vertices)

            # 计算每个面的法向量并累加到顶点
            for face in faces:
                v0, v1, v2 = vertices[face]

                # 计算面法向量
                edge1 = v1 - v0
                edge2 = v2 - v0
                face_normal = np.cross(edge1, edge2)

                # 归一化
                norm = np.linalg.norm(face_normal)
                if norm > 0:
                    face_normal /= norm

                # 累加到顶点
                normals[face] += face_normal

            # 归一化顶点法向量
            for i in range(len(normals)):
                norm = np.linalg.norm(normals[i])
                if norm > 0:
                    normals[i] /= norm

            return normals

        except Exception as e:
            logger.warning(f"顶点法向量计算失败: {e}")
            return np.zeros_like(vertices)
