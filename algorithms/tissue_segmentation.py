"""
EEG源定位系统 - 高精度组织分割算法

该模块实现高精度的头部组织分割算法，确保分割误差控制在1mm以内。
特别优化颅骨-脑脊液交界区的精度，这是信号衰减的关键界面。

主要功能：
1. FreeSurfer风格的高精度分割
2. SimpleITK增强分割算法
3. 多尺度边界细化
4. 颅骨-脑脊液界面特殊优化
5. 亚体素级精度控制
"""

import numpy as np
import SimpleITK as sitk
from scipy import ndimage, optimize
from skimage import morphology, measure, filters, segmentation
from typing import Dict, List, Optional, Tuple, Union
import logging
from pathlib import Path
import cv2

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class HighPrecisionTissueSegmenter:
    """高精度组织分割器"""
    
    def __init__(self, config: Dict):
        """
        初始化高精度分割器
        
        参数:
            config: 配置字典
        """
        self.config = config
        self.segmentation_config = config['head_modeling']['tissue_segmentation']
        self.accuracy_threshold = self.segmentation_config['accuracy_threshold']  # 1mm
        
        # 初始化子算法
        self.freesurfer_segmenter = FreeSurferStyleSegmenter(config)
        self.simpleitk_segmenter = SimpleITKSegmenter(config)
        self.boundary_refiner = BoundaryRefiner(config)
        self.skull_csf_optimizer = SkullCSFOptimizer(config)
        
        logger.info(f"高精度组织分割器初始化完成，精度阈值: {self.accuracy_threshold}mm")
        
    def segment_with_high_precision(self, mri_data: np.ndarray, 
                                  voxel_size: Tuple[float, float, float]) -> Dict[str, np.ndarray]:
        """
        执行高精度组织分割
        
        参数:
            mri_data: MRI数据
            voxel_size: 体素尺寸 (x, y, z) in mm
            
        返回:
            Dict: 高精度组织分割结果
        """
        try:
            logger.info("开始高精度组织分割")
            
            # 检查体素尺寸是否满足精度要求
            max_voxel_size = max(voxel_size)
            if max_voxel_size > self.accuracy_threshold:
                logger.warning(f"体素尺寸 {max_voxel_size}mm 超过精度阈值 {self.accuracy_threshold}mm")
                # 需要上采样到满足精度要求
                mri_data, voxel_size = self._upsample_to_target_resolution(mri_data, voxel_size)
                
            # 步骤1: 多算法初始分割
            initial_masks = self._multi_algorithm_segmentation(mri_data)
            
            # 步骤2: 边界精细化
            refined_masks = self.boundary_refiner.refine_boundaries(
                mri_data, initial_masks, voxel_size
            )
            
            # 步骤3: 颅骨-脑脊液界面特殊优化
            optimized_masks = self.skull_csf_optimizer.optimize_skull_csf_boundary(
                mri_data, refined_masks, voxel_size
            )
            
            # 步骤4: 亚体素级精度控制
            final_masks = self._subvoxel_precision_control(
                mri_data, optimized_masks, voxel_size
            )
            
            # 步骤5: 质量验证
            quality_metrics = self._validate_precision(final_masks, voxel_size)
            
            logger.info(f"高精度分割完成，平均精度: {quality_metrics['average_precision']:.3f}mm")
            
            return {
                'tissue_masks': final_masks,
                'quality_metrics': quality_metrics,
                'voxel_size': voxel_size
            }
            
        except Exception as e:
            logger.error(f"高精度组织分割失败: {e}")
            raise
            
    def _upsample_to_target_resolution(self, data: np.ndarray, 
                                     current_voxel_size: Tuple[float, float, float]) -> Tuple[np.ndarray, Tuple[float, float, float]]:
        """上采样到目标分辨率"""
        try:
            target_resolution = self.accuracy_threshold  # 1mm
            
            # 计算上采样因子
            scale_factors = [current_size / target_resolution for current_size in current_voxel_size]
            
            # 使用高质量插值进行上采样
            upsampled_data = ndimage.zoom(data, scale_factors, order=3, prefilter=True)
            
            new_voxel_size = (target_resolution, target_resolution, target_resolution)
            
            logger.info(f"数据上采样: {data.shape} -> {upsampled_data.shape}, "
                       f"体素尺寸: {current_voxel_size} -> {new_voxel_size}")
            
            return upsampled_data, new_voxel_size
            
        except Exception as e:
            logger.error(f"数据上采样失败: {e}")
            return data, current_voxel_size
            
    def _multi_algorithm_segmentation(self, data: np.ndarray) -> Dict[str, np.ndarray]:
        """多算法融合分割"""
        try:
            # 获取FreeSurfer风格分割结果
            freesurfer_masks = self.freesurfer_segmenter.segment(data)
            
            # 获取SimpleITK分割结果
            simpleitk_masks = self.simpleitk_segmenter.segment(data)
            
            # 融合多个算法的结果
            fused_masks = self._fuse_segmentation_results(freesurfer_masks, simpleitk_masks)
            
            return fused_masks
            
        except Exception as e:
            logger.error(f"多算法分割失败: {e}")
            raise
            
    def _fuse_segmentation_results(self, masks1: Dict[str, np.ndarray], 
                                 masks2: Dict[str, np.ndarray]) -> Dict[str, np.ndarray]:
        """融合分割结果"""
        try:
            fused_masks = {}
            
            for tissue in masks1.keys():
                if tissue in masks2:
                    # 使用投票机制融合
                    mask1 = masks1[tissue].astype(float)
                    mask2 = masks2[tissue].astype(float)
                    
                    # 加权平均（可以根据算法可靠性调整权重）
                    weight1, weight2 = 0.6, 0.4  # FreeSurfer权重更高
                    fused_mask = weight1 * mask1 + weight2 * mask2
                    
                    # 阈值化得到二值mask
                    fused_masks[tissue] = (fused_mask > 0.5).astype(np.uint8)
                else:
                    fused_masks[tissue] = masks1[tissue]
                    
            return fused_masks
            
        except Exception as e:
            logger.error(f"分割结果融合失败: {e}")
            return masks1
            
    def _subvoxel_precision_control(self, data: np.ndarray, masks: Dict[str, np.ndarray], 
                                  voxel_size: Tuple[float, float, float]) -> Dict[str, np.ndarray]:
        """亚体素级精度控制"""
        try:
            refined_masks = {}
            
            for tissue, mask in masks.items():
                # 使用亚体素级边界检测
                subvoxel_mask = self._subvoxel_boundary_detection(data, mask, voxel_size)
                refined_masks[tissue] = subvoxel_mask
                
            return refined_masks
            
        except Exception as e:
            logger.error(f"亚体素精度控制失败: {e}")
            return masks
            
    def _subvoxel_boundary_detection(self, data: np.ndarray, mask: np.ndarray, 
                                   voxel_size: Tuple[float, float, float]) -> np.ndarray:
        """亚体素级边界检测"""
        try:
            # 计算梯度幅值
            gradient = np.gradient(data.astype(float))
            gradient_magnitude = np.sqrt(sum(g**2 for g in gradient))
            
            # 在mask边界附近进行亚体素级细化
            boundary = mask - morphology.binary_erosion(mask)
            
            # 使用梯度信息调整边界
            refined_boundary = boundary.copy()
            
            # 在边界区域应用亚体素级调整
            boundary_coords = np.where(boundary > 0)
            
            for i, (x, y, z) in enumerate(zip(*boundary_coords)):
                if i % 1000 == 0:  # 避免过度计算
                    # 在局部区域内寻找最佳边界位置
                    local_region = data[max(0, x-1):x+2, max(0, y-1):y+2, max(0, z-1):z+2]
                    local_gradient = gradient_magnitude[max(0, x-1):x+2, max(0, y-1):y+2, max(0, z-1):z+2]
                    
                    # 基于梯度最大值调整边界
                    if local_gradient.size > 0:
                        max_grad_idx = np.unravel_index(np.argmax(local_gradient), local_gradient.shape)
                        # 这里可以实现更复杂的亚体素级调整逻辑
                        
            return refined_boundary
            
        except Exception as e:
            logger.warning(f"亚体素边界检测失败: {e}")
            return mask
            
    def _validate_precision(self, masks: Dict[str, np.ndarray], 
                          voxel_size: Tuple[float, float, float]) -> Dict:
        """验证分割精度"""
        try:
            quality_metrics = {}
            precisions = []
            
            for tissue, mask in masks.items():
                # 计算边界精度
                boundary_precision = self._calculate_boundary_precision(mask, voxel_size)
                precisions.append(boundary_precision)
                
                quality_metrics[f'{tissue}_precision'] = boundary_precision
                
            # 计算平均精度
            average_precision = np.mean(precisions)
            quality_metrics['average_precision'] = average_precision
            
            # 检查是否满足精度要求
            quality_metrics['meets_precision_requirement'] = average_precision <= self.accuracy_threshold
            
            return quality_metrics
            
        except Exception as e:
            logger.error(f"精度验证失败: {e}")
            return {'average_precision': float('inf'), 'meets_precision_requirement': False}
            
    def _calculate_boundary_precision(self, mask: np.ndarray, 
                                    voxel_size: Tuple[float, float, float]) -> float:
        """计算边界精度"""
        try:
            # 计算边界的平滑度作为精度指标
            boundary = mask - morphology.binary_erosion(mask)
            
            if np.sum(boundary) == 0:
                return 0.0
                
            # 计算边界的曲率变化
            boundary_coords = np.where(boundary > 0)
            
            if len(boundary_coords[0]) < 3:
                return 0.0
                
            # 使用边界点的局部变化估计精度
            coords = np.column_stack(boundary_coords)
            
            # 计算相邻点间的距离变化
            distances = []
            for i in range(1, len(coords)):
                dist = np.linalg.norm((coords[i] - coords[i-1]) * np.array(voxel_size))
                distances.append(dist)
                
            # 精度定义为边界变化的标准差
            precision = np.std(distances) if distances else 0.0
            
            return precision
            
        except Exception as e:
            logger.warning(f"边界精度计算失败: {e}")
            return float('inf')


class FreeSurferStyleSegmenter:
    """FreeSurfer风格分割器"""
    
    def __init__(self, config: Dict):
        self.config = config
        
    def segment(self, data: np.ndarray) -> Dict[str, np.ndarray]:
        """执行FreeSurfer风格分割"""
        try:
            # 增强的FreeSurfer风格分割
            masks = {}
            
            # 预处理：去噪和增强对比度
            denoised_data = self._denoise_image(data)
            enhanced_data = self._enhance_contrast(denoised_data)
            
            # 多阈值分割
            thresholds = self._compute_optimal_thresholds(enhanced_data)
            
            # 基于阈值创建初始分割
            tissue_labels = ['scalp', 'skull', 'csf', 'gray', 'white']
            
            for i, tissue in enumerate(tissue_labels):
                if i < len(thresholds):
                    if i == 0:
                        mask = enhanced_data > thresholds[i]
                    else:
                        mask = (enhanced_data > thresholds[i-1]) & (enhanced_data <= thresholds[i])
                else:
                    mask = enhanced_data <= thresholds[-1]
                    
                masks[tissue] = mask.astype(np.uint8)
                
            # 形态学后处理
            masks = self._morphological_postprocessing(masks)
            
            return masks
            
        except Exception as e:
            logger.error(f"FreeSurfer风格分割失败: {e}")
            raise
            
    def _denoise_image(self, data: np.ndarray) -> np.ndarray:
        """图像去噪"""
        try:
            # 使用非局部均值去噪
            denoised = filters.rank.mean(data.astype(np.uint8), morphology.ball(1))
            return denoised.astype(np.float32)
        except:
            return data.astype(np.float32)
            
    def _enhance_contrast(self, data: np.ndarray) -> np.ndarray:
        """增强对比度"""
        try:
            # 直方图均衡化
            data_uint8 = ((data - data.min()) / (data.max() - data.min()) * 255).astype(np.uint8)
            enhanced = cv2.equalizeHist(data_uint8)
            return enhanced.astype(np.float32)
        except:
            return data
            
    def _compute_optimal_thresholds(self, data: np.ndarray) -> List[float]:
        """计算最优阈值"""
        try:
            # 使用多阈值Otsu方法
            non_zero_data = data[data > 0]
            if len(non_zero_data) == 0:
                return [0.0]
                
            thresholds = filters.threshold_multiotsu(non_zero_data, classes=5)
            return list(thresholds)
            
        except Exception as e:
            logger.warning(f"阈值计算失败: {e}")
            # 返回基于统计的默认阈值
            mean_val = np.mean(data[data > 0])
            std_val = np.std(data[data > 0])
            return [mean_val - std_val, mean_val, mean_val + std_val]
            
    def _morphological_postprocessing(self, masks: Dict[str, np.ndarray]) -> Dict[str, np.ndarray]:
        """形态学后处理"""
        try:
            processed_masks = {}
            
            for tissue, mask in masks.items():
                # 去除小连通组件
                cleaned = morphology.remove_small_objects(mask.astype(bool), min_size=50)
                
                # 填充小洞
                filled = morphology.remove_small_holes(cleaned, area_threshold=25)
                
                # 平滑边界
                smoothed = morphology.binary_closing(filled, morphology.ball(1))
                
                processed_masks[tissue] = smoothed.astype(np.uint8)
                
            return processed_masks
            
        except Exception as e:
            logger.error(f"形态学后处理失败: {e}")
            return masks


class SimpleITKSegmenter:
    """SimpleITK增强分割器"""
    
    def __init__(self, config: Dict):
        self.config = config
        
    def segment(self, data: np.ndarray) -> Dict[str, np.ndarray]:
        """执行SimpleITK增强分割"""
        try:
            # 转换为SimpleITK图像
            sitk_image = sitk.GetImageFromArray(data.astype(np.float32))
            
            # 应用高斯平滑
            smoothed = sitk.SmoothingRecursiveGaussian(sitk_image, sigma=0.5)
            
            # 使用连通阈值分割
            masks = self._connected_threshold_segmentation(smoothed)
            
            # 区域生长分割
            region_masks = self._region_growing_segmentation(smoothed)
            
            # 融合结果
            final_masks = self._combine_segmentation_results(masks, region_masks)
            
            return final_masks
            
        except Exception as e:
            logger.error(f"SimpleITK分割失败: {e}")
            raise

    def _connected_threshold_segmentation(self, image: sitk.Image) -> Dict[str, np.ndarray]:
        """连通阈值分割"""
        try:
            masks = {}

            # 获取图像统计信息
            stats = sitk.LabelIntensityStatisticsImageFilter()
            stats.Execute(image > 0, image)

            mean_intensity = stats.GetMean(1)
            std_intensity = stats.GetStandardDeviation(1)

            # 定义种子点和阈值范围
            size = image.GetSize()
            center = [s // 2 for s in size]

            # 为不同组织定义参数
            tissue_params = {
                'white': {'lower': mean_intensity + 0.5 * std_intensity, 'upper': mean_intensity + 2 * std_intensity},
                'gray': {'lower': mean_intensity - 0.5 * std_intensity, 'upper': mean_intensity + 0.5 * std_intensity},
                'csf': {'lower': mean_intensity - 2 * std_intensity, 'upper': mean_intensity - 0.5 * std_intensity}
            }

            for tissue, params in tissue_params.items():
                # 连通阈值分割
                segmented = sitk.ConnectedThreshold(
                    image,
                    seedList=[center],
                    lower=params['lower'],
                    upper=params['upper']
                )

                # 转换为numpy数组
                mask_array = sitk.GetArrayFromImage(segmented)
                masks[tissue] = mask_array.astype(np.uint8)

            return masks

        except Exception as e:
            logger.error(f"连通阈值分割失败: {e}")
            return {}

    def _region_growing_segmentation(self, image: sitk.Image) -> Dict[str, np.ndarray]:
        """区域生长分割"""
        try:
            masks = {}

            # 使用置信连通区域生长
            confidence_filter = sitk.ConfidenceConnectedImageFilter()
            confidence_filter.SetMultiplier(2.0)
            confidence_filter.SetNumberOfIterations(5)
            confidence_filter.SetInitialNeighborhoodRadius(1)

            # 在不同位置设置种子点
            size = image.GetSize()
            seed_points = [
                [size[0] // 4, size[1] // 2, size[2] // 2],  # 左侧
                [3 * size[0] // 4, size[1] // 2, size[2] // 2],  # 右侧
                [size[0] // 2, size[1] // 2, size[2] // 2]   # 中心
            ]

            for i, seed in enumerate(seed_points):
                confidence_filter.SetSeed(seed)
                segmented = confidence_filter.Execute(image)

                mask_array = sitk.GetArrayFromImage(segmented)
                masks[f'region_{i}'] = mask_array.astype(np.uint8)

            return masks

        except Exception as e:
            logger.error(f"区域生长分割失败: {e}")
            return {}

    def _combine_segmentation_results(self, masks1: Dict[str, np.ndarray],
                                    masks2: Dict[str, np.ndarray]) -> Dict[str, np.ndarray]:
        """组合分割结果"""
        try:
            combined_masks = {}

            # 简化的组合逻辑
            tissue_mapping = {
                'white': ['region_0', 'region_1', 'region_2'],
                'gray': ['region_0', 'region_1'],
                'csf': ['region_2']
            }

            for tissue in ['scalp', 'skull', 'csf', 'gray', 'white']:
                if tissue in masks1:
                    combined_masks[tissue] = masks1[tissue]
                else:
                    # 创建空mask
                    if masks1:
                        shape = list(masks1.values())[0].shape
                    elif masks2:
                        shape = list(masks2.values())[0].shape
                    else:
                        continue

                    combined_masks[tissue] = np.zeros(shape, dtype=np.uint8)

            return combined_masks

        except Exception as e:
            logger.error(f"分割结果组合失败: {e}")
            return masks1 if masks1 else masks2


class BoundaryRefiner:
    """边界细化器"""

    def __init__(self, config: Dict):
        self.config = config

    def refine_boundaries(self, data: np.ndarray, masks: Dict[str, np.ndarray],
                         voxel_size: Tuple[float, float, float]) -> Dict[str, np.ndarray]:
        """细化组织边界"""
        try:
            refined_masks = {}

            for tissue, mask in masks.items():
                # 多尺度边界细化
                refined_mask = self._multiscale_boundary_refinement(data, mask, voxel_size)

                # 基于梯度的边界调整
                gradient_refined = self._gradient_based_refinement(data, refined_mask)

                # 活动轮廓模型细化
                contour_refined = self._active_contour_refinement(data, gradient_refined)

                refined_masks[tissue] = contour_refined

            return refined_masks

        except Exception as e:
            logger.error(f"边界细化失败: {e}")
            return masks

    def _multiscale_boundary_refinement(self, data: np.ndarray, mask: np.ndarray,
                                      voxel_size: Tuple[float, float, float]) -> np.ndarray:
        """多尺度边界细化"""
        try:
            # 在不同尺度上进行边界检测
            scales = [0.5, 1.0, 2.0]  # 不同的高斯核尺度
            boundary_maps = []

            for scale in scales:
                # 高斯平滑
                sigma = scale / min(voxel_size)  # 根据体素尺寸调整sigma
                smoothed = ndimage.gaussian_filter(data.astype(float), sigma=sigma)

                # 计算梯度
                gradient = np.gradient(smoothed)
                gradient_magnitude = np.sqrt(sum(g**2 for g in gradient))

                # 在mask边界附近检测强梯度
                boundary = mask - morphology.binary_erosion(mask)
                boundary_gradient = boundary * gradient_magnitude

                boundary_maps.append(boundary_gradient)

            # 融合多尺度边界信息
            combined_boundary = np.mean(boundary_maps, axis=0)

            # 基于组合边界调整mask
            threshold = np.percentile(combined_boundary[combined_boundary > 0], 75)
            refined_boundary = combined_boundary > threshold

            # 与原始mask结合
            refined_mask = mask.copy()
            refined_mask[refined_boundary] = 1

            return refined_mask.astype(np.uint8)

        except Exception as e:
            logger.warning(f"多尺度边界细化失败: {e}")
            return mask

    def _gradient_based_refinement(self, data: np.ndarray, mask: np.ndarray) -> np.ndarray:
        """基于梯度的边界细化"""
        try:
            # 计算图像梯度
            gradient = np.gradient(data.astype(float))
            gradient_magnitude = np.sqrt(sum(g**2 for g in gradient))

            # 找到mask边界
            boundary = mask - morphology.binary_erosion(mask)

            # 在边界附近寻找梯度最大值
            refined_mask = mask.copy()

            # 对每个边界点，在局部邻域内寻找梯度最大值
            boundary_coords = np.where(boundary > 0)

            for x, y, z in zip(*boundary_coords):
                # 定义局部搜索窗口
                x_min, x_max = max(0, x-1), min(data.shape[0], x+2)
                y_min, y_max = max(0, y-1), min(data.shape[1], y+2)
                z_min, z_max = max(0, z-1), min(data.shape[2], z+2)

                # 在局部窗口内找到梯度最大值位置
                local_gradient = gradient_magnitude[x_min:x_max, y_min:y_max, z_min:z_max]

                if local_gradient.size > 0:
                    max_grad_pos = np.unravel_index(np.argmax(local_gradient), local_gradient.shape)

                    # 调整边界位置
                    new_x = x_min + max_grad_pos[0]
                    new_y = y_min + max_grad_pos[1]
                    new_z = z_min + max_grad_pos[2]

                    # 更新mask
                    refined_mask[new_x, new_y, new_z] = 1

            return refined_mask.astype(np.uint8)

        except Exception as e:
            logger.warning(f"基于梯度的边界细化失败: {e}")
            return mask

    def _active_contour_refinement(self, data: np.ndarray, mask: np.ndarray) -> np.ndarray:
        """活动轮廓模型边界细化"""
        try:
            # 使用形态学活动轮廓（简化版本）
            # 这里实现一个简化的活动轮廓算法

            refined_mask = mask.copy().astype(float)

            # 迭代优化
            for iteration in range(10):
                # 计算曲率
                curvature = self._calculate_curvature(refined_mask)

                # 计算外部力（基于图像梯度）
                gradient = np.gradient(data.astype(float))
                external_force = np.sqrt(sum(g**2 for g in gradient))

                # 更新轮廓
                alpha = 0.1  # 平滑项权重
                beta = 0.1   # 外部力权重

                update = alpha * curvature - beta * external_force
                refined_mask += 0.01 * update  # 小步长更新

                # 保持在[0,1]范围内
                refined_mask = np.clip(refined_mask, 0, 1)

            # 阈值化得到二值mask
            final_mask = (refined_mask > 0.5).astype(np.uint8)

            return final_mask

        except Exception as e:
            logger.warning(f"活动轮廓细化失败: {e}")
            return mask

    def _calculate_curvature(self, mask: np.ndarray) -> np.ndarray:
        """计算mask的曲率"""
        try:
            # 计算二阶导数近似曲率
            laplacian = ndimage.laplace(mask.astype(float))
            return laplacian

        except Exception as e:
            logger.warning(f"曲率计算失败: {e}")
            return np.zeros_like(mask)


class SkullCSFOptimizer:
    """颅骨-脑脊液界面优化器"""

    def __init__(self, config: Dict):
        self.config = config

    def optimize_skull_csf_boundary(self, data: np.ndarray, masks: Dict[str, np.ndarray],
                                   voxel_size: Tuple[float, float, float]) -> Dict[str, np.ndarray]:
        """优化颅骨-脑脊液界面"""
        try:
            logger.info("开始颅骨-脑脊液界面优化")

            optimized_masks = masks.copy()

            if 'skull' in masks and 'csf' in masks:
                # 特殊优化颅骨-脑脊液界面
                skull_mask = masks['skull']
                csf_mask = masks['csf']

                # 检测界面区域
                interface_region = self._detect_skull_csf_interface(skull_mask, csf_mask)

                # 高精度界面重建
                optimized_interface = self._high_precision_interface_reconstruction(
                    data, interface_region, voxel_size
                )

                # 更新mask
                optimized_masks['skull'], optimized_masks['csf'] = self._update_masks_with_interface(
                    skull_mask, csf_mask, optimized_interface
                )

                logger.info("颅骨-脑脊液界面优化完成")

            return optimized_masks

        except Exception as e:
            logger.error(f"颅骨-脑脊液界面优化失败: {e}")
            return masks

    def _detect_skull_csf_interface(self, skull_mask: np.ndarray,
                                   csf_mask: np.ndarray) -> np.ndarray:
        """检测颅骨-脑脊液界面"""
        try:
            # 膨胀操作找到可能的界面区域
            skull_dilated = morphology.binary_dilation(skull_mask, morphology.ball(2))
            csf_dilated = morphology.binary_dilation(csf_mask, morphology.ball(2))

            # 界面区域是两个膨胀区域的交集
            interface_region = skull_dilated & csf_dilated

            return interface_region.astype(np.uint8)

        except Exception as e:
            logger.error(f"界面检测失败: {e}")
            return np.zeros_like(skull_mask)

    def _high_precision_interface_reconstruction(self, data: np.ndarray,
                                               interface_region: np.ndarray,
                                               voxel_size: Tuple[float, float, float]) -> np.ndarray:
        """高精度界面重建"""
        try:
            # 在界面区域内进行高精度分析
            interface_coords = np.where(interface_region > 0)

            if len(interface_coords[0]) == 0:
                return interface_region

            # 计算局部梯度和曲率
            gradient = np.gradient(data.astype(float))
            gradient_magnitude = np.sqrt(sum(g**2 for g in gradient))

            # 在界面区域寻找最强的边界
            refined_interface = np.zeros_like(interface_region)

            for x, y, z in zip(*interface_coords):
                # 在局部邻域内寻找最佳界面位置
                local_window = 3  # 3x3x3窗口

                x_min = max(0, x - local_window // 2)
                x_max = min(data.shape[0], x + local_window // 2 + 1)
                y_min = max(0, y - local_window // 2)
                y_max = min(data.shape[1], y + local_window // 2 + 1)
                z_min = max(0, z - local_window // 2)
                z_max = min(data.shape[2], z + local_window // 2 + 1)

                local_gradient = gradient_magnitude[x_min:x_max, y_min:y_max, z_min:z_max]

                if local_gradient.size > 0:
                    # 找到梯度最大值位置
                    max_pos = np.unravel_index(np.argmax(local_gradient), local_gradient.shape)

                    refined_x = x_min + max_pos[0]
                    refined_y = y_min + max_pos[1]
                    refined_z = z_min + max_pos[2]

                    refined_interface[refined_x, refined_y, refined_z] = 1

            return refined_interface

        except Exception as e:
            logger.error(f"高精度界面重建失败: {e}")
            return interface_region

    def _update_masks_with_interface(self, skull_mask: np.ndarray, csf_mask: np.ndarray,
                                   interface: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """根据优化的界面更新mask"""
        try:
            # 使用距离变换来分配界面像素
            skull_distance = ndimage.distance_transform_edt(skull_mask)
            csf_distance = ndimage.distance_transform_edt(csf_mask)

            # 更新的mask
            updated_skull = skull_mask.copy()
            updated_csf = csf_mask.copy()

            # 界面像素分配给距离更近的组织
            interface_coords = np.where(interface > 0)

            for x, y, z in zip(*interface_coords):
                if skull_distance[x, y, z] < csf_distance[x, y, z]:
                    updated_skull[x, y, z] = 1
                    updated_csf[x, y, z] = 0
                else:
                    updated_skull[x, y, z] = 0
                    updated_csf[x, y, z] = 1

            return updated_skull, updated_csf

        except Exception as e:
            logger.error(f"mask更新失败: {e}")
            return skull_mask, csf_mask
