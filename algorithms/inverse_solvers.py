"""
EEG源定位系统 - 逆向求解算法核心

该模块实现完整的EEG源定位算法链，包括正向建模、逆向求解和源活动重建。
支持多种经典和现代源定位算法：LORETA、sLORETA、eLORETA、MNE、dSPM等。

主要功能：
1. 多种逆向求解算法实现
2. 正则化参数优化
3. 源活动重建和时间序列分析
4. 源定位结果验证和评估
5. 多模态数据融合支持
"""

import numpy as np
from scipy import linalg, optimize
from sklearn.linear_model import Ridge
from typing import Dict, List, Optional, Tuple, Union
import logging
from pathlib import Path
import yaml

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SourceLocalizer:
    """源定位器主类"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化源定位器
        
        参数:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.source_config = self.config['source_localization']
        
        # 初始化逆向求解器
        self.inverse_solvers = {
            'loreta': LOREtaSolver(self.config),
            'sloreta': sLOREtaSolver(self.config),
            'eloreta': eLOREtaSolver(self.config),
            'mne': MNESolver(self.config),
            'dspm': dSPMSolver(self.config)
        }
        
        # 初始化其他组件
        self.regularization_manager = RegularizationManager(self.config)
        self.source_reconstructor = SourceReconstructor(self.config)
        
        logger.info("源定位器初始化完成")
        
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            raise
            
    def localize_sources(self, eeg_data: np.ndarray, leadfield_matrix: np.ndarray,
                        noise_cov: Optional[np.ndarray] = None,
                        method: str = 'sloreta') -> Dict:
        """
        执行源定位分析
        
        参数:
            eeg_data: EEG数据 (通道数 x 时间点数)
            leadfield_matrix: 导联场矩阵 (通道数 x 源数)
            noise_cov: 噪声协方差矩阵 (可选)
            method: 源定位方法
            
        返回:
            Dict: 源定位结果
        """
        try:
            logger.info(f"开始源定位分析，方法: {method}")
            
            # 验证输入数据
            self._validate_inputs(eeg_data, leadfield_matrix, noise_cov)
            
            # 预处理导联场矩阵
            processed_leadfield = self._preprocess_leadfield(leadfield_matrix)
            
            # 估计噪声协方差（如果未提供）
            if noise_cov is None:
                noise_cov = self._estimate_noise_covariance(eeg_data)
                
            # 选择逆向求解器
            if method not in self.inverse_solvers:
                raise ValueError(f"不支持的源定位方法: {method}")
                
            solver = self.inverse_solvers[method]
            
            # 优化正则化参数
            optimal_lambda = self.regularization_manager.optimize_regularization(
                eeg_data, processed_leadfield, noise_cov, method
            )
            
            # 执行逆向求解
            source_estimates = solver.solve(
                eeg_data, processed_leadfield, noise_cov, optimal_lambda
            )
            
            # 源活动重建
            reconstructed_sources = self.source_reconstructor.reconstruct_sources(
                source_estimates, processed_leadfield
            )
            
            # 计算定位质量指标
            quality_metrics = self._calculate_quality_metrics(
                eeg_data, processed_leadfield, source_estimates
            )
            
            # 构建结果字典
            result = {
                'source_estimates': source_estimates,
                'reconstructed_sources': reconstructed_sources,
                'leadfield_matrix': processed_leadfield,
                'noise_covariance': noise_cov,
                'regularization_parameter': optimal_lambda,
                'method': method,
                'quality_metrics': quality_metrics,
                'source_info': {
                    'num_sources': processed_leadfield.shape[1],
                    'num_channels': processed_leadfield.shape[0],
                    'num_timepoints': eeg_data.shape[1]
                }
            }
            
            logger.info(f"源定位分析完成，定位质量评分: {quality_metrics['overall_score']:.3f}")
            return result
            
        except Exception as e:
            logger.error(f"源定位分析失败: {e}")
            raise
            
    def _validate_inputs(self, eeg_data: np.ndarray, leadfield_matrix: np.ndarray,
                        noise_cov: Optional[np.ndarray]):
        """验证输入数据"""
        try:
            # 检查EEG数据维度
            if eeg_data.ndim != 2:
                raise ValueError(f"EEG数据必须是2维数组，当前维度: {eeg_data.ndim}")
                
            # 检查导联场矩阵维度
            if leadfield_matrix.ndim != 2:
                raise ValueError(f"导联场矩阵必须是2维数组，当前维度: {leadfield_matrix.ndim}")
                
            # 检查维度匹配
            if eeg_data.shape[0] != leadfield_matrix.shape[0]:
                raise ValueError(f"EEG数据通道数({eeg_data.shape[0]})与导联场矩阵通道数({leadfield_matrix.shape[0]})不匹配")
                
            # 检查噪声协方差矩阵
            if noise_cov is not None:
                if noise_cov.shape != (eeg_data.shape[0], eeg_data.shape[0]):
                    raise ValueError(f"噪声协方差矩阵维度不正确: {noise_cov.shape}")
                    
            logger.info("输入数据验证通过")
            
        except Exception as e:
            logger.error(f"输入数据验证失败: {e}")
            raise
            
    def _preprocess_leadfield(self, leadfield_matrix: np.ndarray) -> np.ndarray:
        """预处理导联场矩阵"""
        try:
            # 检查并处理奇异值
            U, s, Vt = linalg.svd(leadfield_matrix, full_matrices=False)
            
            # 移除过小的奇异值
            threshold = 1e-10 * s[0]  # 相对阈值
            valid_indices = s > threshold
            
            if not np.all(valid_indices):
                logger.warning(f"移除了 {np.sum(~valid_indices)} 个小奇异值")
                s_filtered = s[valid_indices]
                U_filtered = U[:, valid_indices]
                Vt_filtered = Vt[valid_indices, :]
                
                # 重构导联场矩阵
                processed_leadfield = U_filtered @ np.diag(s_filtered) @ Vt_filtered
            else:
                processed_leadfield = leadfield_matrix
                
            logger.info("导联场矩阵预处理完成")
            return processed_leadfield
            
        except Exception as e:
            logger.error(f"导联场矩阵预处理失败: {e}")
            return leadfield_matrix
            
    def _estimate_noise_covariance(self, eeg_data: np.ndarray) -> np.ndarray:
        """估计噪声协方差矩阵"""
        try:
            # 使用经验协方差估计
            # 假设数据的后10%为噪声段
            noise_samples = max(1, int(0.1 * eeg_data.shape[1]))
            noise_data = eeg_data[:, -noise_samples:]
            
            # 计算经验协方差
            noise_cov = np.cov(noise_data)
            
            # 添加正则化以确保正定性
            regularization = 1e-6 * np.trace(noise_cov) / noise_cov.shape[0]
            noise_cov += regularization * np.eye(noise_cov.shape[0])
            
            logger.info("噪声协方差矩阵估计完成")
            return noise_cov
            
        except Exception as e:
            logger.error(f"噪声协方差估计失败: {e}")
            # 返回单位矩阵作为默认值
            return np.eye(eeg_data.shape[0])
            
    def _calculate_quality_metrics(self, eeg_data: np.ndarray, 
                                 leadfield_matrix: np.ndarray,
                                 source_estimates: np.ndarray) -> Dict:
        """计算定位质量指标"""
        try:
            quality_metrics = {}
            
            # 1. 拟合优度 (Goodness of Fit)
            reconstructed_eeg = leadfield_matrix @ source_estimates
            residual = eeg_data - reconstructed_eeg
            
            # 相对残差方差
            rrv = np.sum(residual**2) / np.sum(eeg_data**2)
            gof = 1 - rrv
            quality_metrics['goodness_of_fit'] = gof
            
            # 2. 源活动稀疏性
            sparsity = self._calculate_sparsity(source_estimates)
            quality_metrics['sparsity'] = sparsity
            
            # 3. 时间一致性
            temporal_consistency = self._calculate_temporal_consistency(source_estimates)
            quality_metrics['temporal_consistency'] = temporal_consistency
            
            # 4. 空间平滑性
            spatial_smoothness = self._calculate_spatial_smoothness(source_estimates)
            quality_metrics['spatial_smoothness'] = spatial_smoothness
            
            # 5. 总体质量评分
            overall_score = np.mean([gof, sparsity, temporal_consistency, spatial_smoothness])
            quality_metrics['overall_score'] = overall_score
            
            return quality_metrics
            
        except Exception as e:
            logger.error(f"质量指标计算失败: {e}")
            return {'overall_score': 0.0}
            
    def _calculate_sparsity(self, source_estimates: np.ndarray) -> float:
        """计算源活动稀疏性"""
        try:
            # 使用L1/L2范数比值衡量稀疏性
            l1_norm = np.sum(np.abs(source_estimates))
            l2_norm = np.sqrt(np.sum(source_estimates**2))
            
            if l2_norm > 0:
                sparsity = 1 - (l1_norm / (np.sqrt(source_estimates.size) * l2_norm))
                return max(0, sparsity)
            else:
                return 0.0
                
        except Exception as e:
            logger.warning(f"稀疏性计算失败: {e}")
            return 0.0
            
    def _calculate_temporal_consistency(self, source_estimates: np.ndarray) -> float:
        """计算时间一致性"""
        try:
            if source_estimates.shape[1] < 2:
                return 1.0
                
            # 计算相邻时间点的相关性
            correlations = []
            for t in range(source_estimates.shape[1] - 1):
                corr = np.corrcoef(source_estimates[:, t], source_estimates[:, t+1])[0, 1]
                if not np.isnan(corr):
                    correlations.append(abs(corr))
                    
            return np.mean(correlations) if correlations else 0.0
            
        except Exception as e:
            logger.warning(f"时间一致性计算失败: {e}")
            return 0.0
            
    def _calculate_spatial_smoothness(self, source_estimates: np.ndarray) -> float:
        """计算空间平滑性"""
        try:
            # 简化的空间平滑性计算
            # 计算源活动的空间梯度
            spatial_gradient = np.abs(np.diff(source_estimates, axis=0))
            smoothness = 1 / (1 + np.mean(spatial_gradient))
            
            return smoothness
            
        except Exception as e:
            logger.warning(f"空间平滑性计算失败: {e}")
            return 0.0


class BaseSolver:
    """逆向求解器基类"""
    
    def __init__(self, config: Dict):
        self.config = config
        
    def solve(self, eeg_data: np.ndarray, leadfield_matrix: np.ndarray,
              noise_cov: np.ndarray, regularization: float) -> np.ndarray:
        """求解逆问题"""
        raise NotImplementedError("子类必须实现solve方法")
        
    def _apply_whitening(self, eeg_data: np.ndarray, leadfield_matrix: np.ndarray,
                        noise_cov: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """应用白化变换"""
        try:
            # 计算白化矩阵
            eigenvals, eigenvecs = linalg.eigh(noise_cov)
            
            # 处理小特征值
            eigenvals = np.maximum(eigenvals, 1e-10 * eigenvals.max())
            
            # 白化矩阵
            whitening_matrix = eigenvecs @ np.diag(1.0 / np.sqrt(eigenvals)) @ eigenvecs.T
            
            # 应用白化
            whitened_eeg = whitening_matrix @ eeg_data
            whitened_leadfield = whitening_matrix @ leadfield_matrix
            
            return whitened_eeg, whitened_leadfield
            
        except Exception as e:
            logger.warning(f"白化变换失败: {e}")
            return eeg_data, leadfield_matrix


class LOREtaSolver(BaseSolver):
    """LORETA求解器"""
    
    def solve(self, eeg_data: np.ndarray, leadfield_matrix: np.ndarray,
              noise_cov: np.ndarray, regularization: float) -> np.ndarray:
        """LORETA逆向求解"""
        try:
            logger.info("执行LORETA逆向求解")
            
            # 应用白化变换
            whitened_eeg, whitened_leadfield = self._apply_whitening(
                eeg_data, leadfield_matrix, noise_cov
            )
            
            # LORETA解：最小化源活动的拉普拉斯算子
            L = whitened_leadfield
            LtL = L.T @ L
            
            # 构建拉普拉斯算子（简化版本）
            num_sources = L.shape[1]
            laplacian = self._build_laplacian_operator(num_sources)
            
            # LORETA逆算子
            inverse_operator = linalg.inv(LtL + regularization * laplacian) @ L.T
            
            # 计算源估计
            source_estimates = inverse_operator @ whitened_eeg
            
            logger.info("LORETA求解完成")
            return source_estimates
            
        except Exception as e:
            logger.error(f"LORETA求解失败: {e}")
            raise
            
    def _build_laplacian_operator(self, num_sources: int) -> np.ndarray:
        """构建拉普拉斯算子"""
        try:
            # 简化的拉普拉斯算子（一维情况）
            laplacian = np.zeros((num_sources, num_sources))
            
            for i in range(num_sources):
                laplacian[i, i] = 2.0
                if i > 0:
                    laplacian[i, i-1] = -1.0
                if i < num_sources - 1:
                    laplacian[i, i+1] = -1.0
                    
            return laplacian
            
        except Exception as e:
            logger.warning(f"拉普拉斯算子构建失败: {e}")
            return np.eye(num_sources)


class sLOREtaSolver(BaseSolver):
    """sLORETA求解器"""
    
    def solve(self, eeg_data: np.ndarray, leadfield_matrix: np.ndarray,
              noise_cov: np.ndarray, regularization: float) -> np.ndarray:
        """sLORETA逆向求解"""
        try:
            logger.info("执行sLORETA逆向求解")
            
            # 应用白化变换
            whitened_eeg, whitened_leadfield = self._apply_whitening(
                eeg_data, leadfield_matrix, noise_cov
            )
            
            L = whitened_leadfield
            LtL = L.T @ L
            
            # 最小范数解
            inverse_operator = linalg.inv(LtL + regularization * np.eye(LtL.shape[0])) @ L.T
            source_estimates = inverse_operator @ whitened_eeg
            
            # sLORETA标准化
            resolution_matrix = inverse_operator @ L
            
            # 计算标准化因子
            standardization_factors = np.sqrt(np.diag(resolution_matrix))
            standardization_factors = np.maximum(standardization_factors, 1e-10)
            
            # 应用标准化
            source_estimates = source_estimates / standardization_factors[:, np.newaxis]
            
            logger.info("sLORETA求解完成")
            return source_estimates
            
        except Exception as e:
            logger.error(f"sLORETA求解失败: {e}")
            raise


class eLOREtaSolver(BaseSolver):
    """eLORETA求解器"""
    
    def solve(self, eeg_data: np.ndarray, leadfield_matrix: np.ndarray,
              noise_cov: np.ndarray, regularization: float) -> np.ndarray:
        """eLORETA逆向求解"""
        try:
            logger.info("执行eLORETA逆向求解")
            
            # 应用白化变换
            whitened_eeg, whitened_leadfield = self._apply_whitening(
                eeg_data, leadfield_matrix, noise_cov
            )
            
            L = whitened_leadfield
            LtL = L.T @ L
            
            # 计算加权最小范数解
            W = self._compute_eloreta_weights(L, regularization)
            
            # eLORETA逆算子
            inverse_operator = W @ linalg.inv(L.T @ W @ L + regularization * np.eye(L.shape[1])) @ L.T @ W
            
            # 计算源估计
            source_estimates = inverse_operator @ whitened_eeg
            
            logger.info("eLORETA求解完成")
            return source_estimates
            
        except Exception as e:
            logger.error(f"eLORETA求解失败: {e}")
            raise
            
    def _compute_eloreta_weights(self, leadfield_matrix: np.ndarray, 
                               regularization: float) -> np.ndarray:
        """计算eLORETA权重矩阵"""
        try:
            L = leadfield_matrix
            LtL = L.T @ L
            
            # 计算分辨率矩阵
            T = linalg.inv(LtL + regularization * np.eye(LtL.shape[0]))
            
            # eLORETA权重
            weights = np.sqrt(np.diag(T))
            W = np.diag(weights)
            
            return W
            
        except Exception as e:
            logger.warning(f"eLORETA权重计算失败: {e}")
            return np.eye(leadfield_matrix.shape[0])


class MNESolver(BaseSolver):
    """最小范数估计求解器"""
    
    def solve(self, eeg_data: np.ndarray, leadfield_matrix: np.ndarray,
              noise_cov: np.ndarray, regularization: float) -> np.ndarray:
        """MNE逆向求解"""
        try:
            logger.info("执行MNE逆向求解")
            
            # 应用白化变换
            whitened_eeg, whitened_leadfield = self._apply_whitening(
                eeg_data, leadfield_matrix, noise_cov
            )
            
            L = whitened_leadfield
            LtL = L.T @ L
            
            # 最小范数逆算子
            inverse_operator = linalg.inv(LtL + regularization * np.eye(LtL.shape[0])) @ L.T
            
            # 计算源估计
            source_estimates = inverse_operator @ whitened_eeg
            
            logger.info("MNE求解完成")
            return source_estimates
            
        except Exception as e:
            logger.error(f"MNE求解失败: {e}")
            raise


class dSPMSolver(BaseSolver):
    """动态统计参数映射求解器"""
    
    def solve(self, eeg_data: np.ndarray, leadfield_matrix: np.ndarray,
              noise_cov: np.ndarray, regularization: float) -> np.ndarray:
        """dSPM逆向求解"""
        try:
            logger.info("执行dSPM逆向求解")
            
            # 首先计算MNE解
            mne_solver = MNESolver(self.config)
            mne_estimates = mne_solver.solve(eeg_data, leadfield_matrix, noise_cov, regularization)
            
            # 应用白化变换
            whitened_eeg, whitened_leadfield = self._apply_whitening(
                eeg_data, leadfield_matrix, noise_cov
            )
            
            L = whitened_leadfield
            LtL = L.T @ L
            
            # 计算噪声标准化因子
            inverse_operator = linalg.inv(LtL + regularization * np.eye(LtL.shape[0])) @ L.T
            resolution_matrix = inverse_operator @ L
            
            # dSPM标准化
            noise_normalization = np.sqrt(np.diag(resolution_matrix))
            noise_normalization = np.maximum(noise_normalization, 1e-10)
            
            # 应用标准化
            dspm_estimates = mne_estimates / noise_normalization[:, np.newaxis]
            
            logger.info("dSPM求解完成")
            return dspm_estimates
            
        except Exception as e:
            logger.error(f"dSPM求解失败: {e}")
            raise


class RegularizationManager:
    """正则化参数管理器"""

    def __init__(self, config: Dict):
        self.config = config
        self.regularization_config = config['source_localization']['regularization']

    def optimize_regularization(self, eeg_data: np.ndarray, leadfield_matrix: np.ndarray,
                              noise_cov: np.ndarray, method: str) -> float:
        """优化正则化参数"""
        try:
            logger.info("开始正则化参数优化")

            if self.regularization_config.get('lambda_auto', True):
                # 自动优化正则化参数
                optimal_lambda = self._auto_optimize_lambda(
                    eeg_data, leadfield_matrix, noise_cov, method
                )
            else:
                # 使用固定正则化参数
                optimal_lambda = self.regularization_config.get('lambda_value', 0.1)

            logger.info(f"最优正则化参数: {optimal_lambda:.6f}")
            return optimal_lambda

        except Exception as e:
            logger.error(f"正则化参数优化失败: {e}")
            return 0.1  # 默认值

    def _auto_optimize_lambda(self, eeg_data: np.ndarray, leadfield_matrix: np.ndarray,
                            noise_cov: np.ndarray, method: str) -> float:
        """自动优化正则化参数"""
        try:
            # 使用L-curve方法优化正则化参数
            lambda_candidates = np.logspace(-8, -1, 20)  # 候选正则化参数

            residual_norms = []
            solution_norms = []

            # 创建临时求解器
            temp_solver = self._create_temp_solver(method)

            for lambda_val in lambda_candidates:
                try:
                    # 计算源估计
                    source_est = temp_solver.solve(eeg_data, leadfield_matrix, noise_cov, lambda_val)

                    # 计算残差范数
                    reconstructed = leadfield_matrix @ source_est
                    residual_norm = np.linalg.norm(eeg_data - reconstructed)
                    residual_norms.append(residual_norm)

                    # 计算解的范数
                    solution_norm = np.linalg.norm(source_est)
                    solution_norms.append(solution_norm)

                except Exception as e:
                    logger.warning(f"正则化参数 {lambda_val} 求解失败: {e}")
                    residual_norms.append(np.inf)
                    solution_norms.append(np.inf)

            # 找到L-curve的拐点
            optimal_idx = self._find_lcurve_corner(residual_norms, solution_norms)
            optimal_lambda = lambda_candidates[optimal_idx]

            return optimal_lambda

        except Exception as e:
            logger.error(f"自动正则化参数优化失败: {e}")
            return 0.1

    def _create_temp_solver(self, method: str):
        """创建临时求解器"""
        solver_classes = {
            'loreta': LOREtaSolver,
            'sloreta': sLOREtaSolver,
            'eloreta': eLOREtaSolver,
            'mne': MNESolver,
            'dspm': dSPMSolver
        }

        if method in solver_classes:
            return solver_classes[method](self.config)
        else:
            return MNESolver(self.config)  # 默认使用MNE

    def _find_lcurve_corner(self, residual_norms: List[float],
                          solution_norms: List[float]) -> int:
        """寻找L-curve的拐点"""
        try:
            # 转换为对数尺度
            log_residual = np.log10(np.array(residual_norms) + 1e-10)
            log_solution = np.log10(np.array(solution_norms) + 1e-10)

            # 计算曲率
            curvatures = []
            for i in range(1, len(log_residual) - 1):
                # 计算二阶导数近似
                d2x = log_residual[i+1] - 2*log_residual[i] + log_residual[i-1]
                d2y = log_solution[i+1] - 2*log_solution[i] + log_solution[i-1]

                dx = log_residual[i+1] - log_residual[i-1]
                dy = log_solution[i+1] - log_solution[i-1]

                # 曲率公式
                if dx**2 + dy**2 > 0:
                    curvature = abs(dx*d2y - dy*d2x) / (dx**2 + dy**2)**1.5
                else:
                    curvature = 0

                curvatures.append(curvature)

            # 找到最大曲率点
            if curvatures:
                corner_idx = np.argmax(curvatures) + 1  # +1因为从索引1开始
                return corner_idx
            else:
                return len(residual_norms) // 2  # 默认选择中间值

        except Exception as e:
            logger.warning(f"L-curve拐点寻找失败: {e}")
            return len(residual_norms) // 2


class SourceReconstructor:
    """源活动重建器"""

    def __init__(self, config: Dict):
        self.config = config

    def reconstruct_sources(self, source_estimates: np.ndarray,
                          leadfield_matrix: np.ndarray) -> Dict:
        """重建源活动"""
        try:
            logger.info("开始源活动重建")

            reconstruction_results = {}

            # 1. 时间序列重建
            time_series = self._reconstruct_time_series(source_estimates)
            reconstruction_results['time_series'] = time_series

            # 2. 功率重建
            power_estimates = self._reconstruct_power(source_estimates)
            reconstruction_results['power'] = power_estimates

            # 3. 峰值源检测
            peak_sources = self._detect_peak_sources(source_estimates)
            reconstruction_results['peak_sources'] = peak_sources

            # 4. 源活动统计
            source_statistics = self._calculate_source_statistics(source_estimates)
            reconstruction_results['statistics'] = source_statistics

            # 5. 时频分析
            time_frequency = self._time_frequency_analysis(source_estimates)
            reconstruction_results['time_frequency'] = time_frequency

            logger.info("源活动重建完成")
            return reconstruction_results

        except Exception as e:
            logger.error(f"源活动重建失败: {e}")
            raise

    def _reconstruct_time_series(self, source_estimates: np.ndarray) -> Dict:
        """重建时间序列"""
        try:
            time_series_data = {}

            # 计算RMS时间序列
            rms_time_series = np.sqrt(np.mean(source_estimates**2, axis=0))
            time_series_data['rms'] = rms_time_series

            # 计算主成分时间序列
            if source_estimates.shape[0] > 1:
                from sklearn.decomposition import PCA
                pca = PCA(n_components=min(5, source_estimates.shape[0]))
                pca_components = pca.fit_transform(source_estimates.T).T
                time_series_data['pca_components'] = pca_components
                time_series_data['explained_variance'] = pca.explained_variance_ratio_

            # 计算峰值时间序列
            peak_time_series = np.max(np.abs(source_estimates), axis=0)
            time_series_data['peak'] = peak_time_series

            return time_series_data

        except Exception as e:
            logger.warning(f"时间序列重建失败: {e}")
            return {}

    def _reconstruct_power(self, source_estimates: np.ndarray) -> Dict:
        """重建功率分布"""
        try:
            power_data = {}

            # 总功率
            total_power = np.sum(source_estimates**2, axis=1)
            power_data['total_power'] = total_power

            # 平均功率
            mean_power = np.mean(source_estimates**2, axis=1)
            power_data['mean_power'] = mean_power

            # 峰值功率
            peak_power = np.max(source_estimates**2, axis=1)
            power_data['peak_power'] = peak_power

            # 功率分布统计
            power_data['power_statistics'] = {
                'mean': np.mean(total_power),
                'std': np.std(total_power),
                'max': np.max(total_power),
                'min': np.min(total_power)
            }

            return power_data

        except Exception as e:
            logger.warning(f"功率重建失败: {e}")
            return {}

    def _detect_peak_sources(self, source_estimates: np.ndarray,
                           threshold_percentile: float = 95) -> Dict:
        """检测峰值源"""
        try:
            peak_data = {}

            # 计算每个源的最大活动
            max_activities = np.max(np.abs(source_estimates), axis=1)

            # 设置阈值
            threshold = np.percentile(max_activities, threshold_percentile)

            # 找到峰值源
            peak_indices = np.where(max_activities > threshold)[0]
            peak_values = max_activities[peak_indices]

            # 按活动强度排序
            sorted_indices = np.argsort(peak_values)[::-1]
            peak_indices = peak_indices[sorted_indices]
            peak_values = peak_values[sorted_indices]

            peak_data['indices'] = peak_indices
            peak_data['values'] = peak_values
            peak_data['threshold'] = threshold
            peak_data['num_peaks'] = len(peak_indices)

            # 峰值时间点
            peak_times = []
            for idx in peak_indices:
                peak_time = np.argmax(np.abs(source_estimates[idx, :]))
                peak_times.append(peak_time)
            peak_data['peak_times'] = peak_times

            return peak_data

        except Exception as e:
            logger.warning(f"峰值源检测失败: {e}")
            return {}

    def _calculate_source_statistics(self, source_estimates: np.ndarray) -> Dict:
        """计算源活动统计"""
        try:
            statistics = {}

            # 基本统计
            statistics['mean'] = np.mean(source_estimates, axis=1)
            statistics['std'] = np.std(source_estimates, axis=1)
            statistics['var'] = np.var(source_estimates, axis=1)
            statistics['max'] = np.max(source_estimates, axis=1)
            statistics['min'] = np.min(source_estimates, axis=1)

            # 活动源数量
            active_threshold = 0.1 * np.max(np.abs(source_estimates))
            active_sources = np.sum(np.max(np.abs(source_estimates), axis=1) > active_threshold)
            statistics['num_active_sources'] = active_sources

            # 总体统计
            statistics['global_stats'] = {
                'total_activity': np.sum(np.abs(source_estimates)),
                'mean_activity': np.mean(np.abs(source_estimates)),
                'max_activity': np.max(np.abs(source_estimates)),
                'activity_std': np.std(source_estimates)
            }

            return statistics

        except Exception as e:
            logger.warning(f"源活动统计计算失败: {e}")
            return {}

    def _time_frequency_analysis(self, source_estimates: np.ndarray) -> Dict:
        """时频分析"""
        try:
            tf_data = {}

            # 选择几个最活跃的源进行时频分析
            max_activities = np.max(np.abs(source_estimates), axis=1)
            top_sources_idx = np.argsort(max_activities)[-5:]  # 选择前5个最活跃的源

            tf_results = []
            for idx in top_sources_idx:
                source_signal = source_estimates[idx, :]

                # 简化的时频分析（使用短时傅里叶变换）
                try:
                    from scipy import signal as scipy_signal
                    f, t, Zxx = scipy_signal.stft(source_signal, nperseg=min(64, len(source_signal)//4))

                    tf_result = {
                        'source_index': idx,
                        'frequencies': f,
                        'times': t,
                        'spectrogram': np.abs(Zxx)
                    }
                    tf_results.append(tf_result)

                except Exception as e:
                    logger.warning(f"源 {idx} 时频分析失败: {e}")

            tf_data['top_sources_tf'] = tf_results
            tf_data['analyzed_sources'] = top_sources_idx.tolist()

            return tf_data

        except Exception as e:
            logger.warning(f"时频分析失败: {e}")
            return {}
