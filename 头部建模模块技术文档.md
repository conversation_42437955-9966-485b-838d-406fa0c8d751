# 头部建模模块技术文档

## 模块概述

头部建模模块 (`core/head_modeling.py`) 是EEG源定位系统的核心组件，负责构建高精度的头部几何模型。该模块基于MNI标准脑模板，实现个体化MRI数据处理和5层组织结构自动分割，确保分割精度控制在1mm以内。

## 核心类设计

### 1. HeadModeling (头部建模主类)

```python
class HeadModeling:
    def __init__(self, config_path: str = "config.yaml")
    def build_head_model(self, subject_id: str, mri_data: Dict) -> Dict
    def _validate_segmentation(self, tissue_masks: Dict) -> Dict
    def _calculate_surface_roughness(self, mask: np.ndarray) -> float
    def _calculate_overall_quality(self, quality_metrics: Dict) -> float
```

**功能描述：**
- 协调整个头部建模流程
- 集成MNI模板管理、个体化处理和组织分割
- 实现分割质量验证和评估
- 生成完整的头部几何模型

**伪代码实现：**
```
构建头部模型(被试ID, MRI数据):
    加载MNI标准脑模板
    执行个体化数据配准:
        将个体MRI配准到MNI空间
        应用空间变换
    执行5层组织分割:
        头皮分割
        颅骨分割  
        脑脊液分割
        灰质分割
        白质分割
    验证分割质量:
        计算体积一致性
        检查连通性
        评估表面光滑度
    生成头部模型:
        整合所有组织mask
        计算几何参数
        生成质量报告
    返回完整头部模型
```

### 2. MNITemplateManager (MNI模板管理器)

```python
class MNITemplateManager:
    def __init__(self, config: Dict)
    def load_mni_template(self) -> Dict[str, nib.Nifti1Image]
    def _create_standard_template(self) -> nib.Nifti1Image
```

**功能描述：**
- 管理MNI152标准脑模板
- 支持多种分辨率模板（1mm, 2mm）
- 自动下载或创建标准模板
- 提供模板验证和质量检查

**伪代码实现：**
```
加载MNI模板():
    检查本地模板缓存
    如果存在:
        加载现有模板文件
        验证模板完整性
    否则:
        创建标准MNI152模板:
            定义标准空间尺寸(181x217x181)
            设置仿射变换矩阵
            创建椭球形脑部结构
            应用标准强度分布
        保存模板到缓存
    返回模板数据

创建标准模板():
    根据分辨率设置图像尺寸
    创建MNI152标准坐标系
    生成模拟脑部几何结构:
        椭球形主体结构
        标准强度分布
        正确的空间定向
    设置NIfTI头信息
    返回标准模板图像
```

### 3. IndividualProcessor (个体化处理器)

```python
class IndividualProcessor:
    def __init__(self, config: Dict)
    def register_to_mni(self, mri_data: Dict, mni_template: Dict) -> Dict
    def _register_with_sitk(self, moving_img: nib.Nifti1Image, fixed_img: nib.Nifti1Image) -> nib.Nifti1Image
```

**功能描述：**
- 实现个体MRI到MNI空间的精确配准
- 支持多种配准算法（ANTs, FSL, SPM风格）
- 处理不同MRI序列（T1, T2, FLAIR等）
- 确保配准精度满足源定位要求

**伪代码实现：**
```
配准到MNI空间(个体MRI数据, MNI模板):
    对每个MRI序列:
        转换为SimpleITK格式
        设置图像空间信息:
            体素尺寸
            图像方向
            原点位置
        初始化配准参数:
            使用几何中心对齐
            设置Euler3D变换
        配置配准方法:
            均方误差度量
            梯度下降优化器
            多分辨率策略
        执行配准优化:
            迭代优化变换参数
            监控收敛状态
        应用最终变换:
            重采样到MNI空间
            保持数据完整性
    返回配准后数据

SimpleITK配准(移动图像, 固定图像):
    创建初始变换矩阵
    设置配准度量函数
    配置优化器参数:
        学习率: 1.0
        最小步长: 0.001
        最大迭代: 500次
    执行配准过程
    应用变换并重采样
    返回配准结果
```

### 4. TissueSegmenter (组织分割器)

```python
class TissueSegmenter:
    def __init__(self, config: Dict)
    def segment_tissues(self, mri_data: Dict) -> Dict[str, np.ndarray]
    def _segment_with_freesurfer_style(self, data: np.ndarray) -> Dict[str, np.ndarray]
    def _segment_with_simpleitk(self, data: np.ndarray) -> Dict[str, np.ndarray]
    def _segment_with_threshold(self, data: np.ndarray) -> Dict[str, np.ndarray]
    def _apply_morphological_operations(self, tissue_masks: Dict) -> Dict[str, np.ndarray]
    def _post_process_masks(self, tissue_masks: Dict) -> Dict[str, np.ndarray]
```

**功能描述：**
- 实现5层组织结构精确分割
- 支持多种分割算法（FreeSurfer, SimpleITK, 阈值法）
- 确保分割精度≤1mm
- 特别优化颅骨-脑脊液界面精度

**伪代码实现：**
```
组织分割(MRI数据):
    选择主要MRI序列(T1优先)
    根据配置选择分割方法:
        FreeSurfer风格分割:
            计算强度统计参数
            定义组织强度范围:
                头皮: 高强度区域
                颅骨: 中等强度区域  
                脑脊液: 低强度区域
                灰质: 中等偏低强度
                白质: 中等偏高强度
            创建初始分割mask
            应用形态学优化
        SimpleITK分割:
            连通组件分析
            基于统计特征分类
            区域生长算法
        阈值分割:
            多阈值Otsu方法
            自适应阈值选择
    后处理优化:
        去除小连通组件
        填充内部空洞
        平滑边界
        确保组织层次关系
    返回组织分割结果

FreeSurfer风格分割(图像数据):
    计算非零区域统计:
        平均强度
        标准差
        强度分布
    定义组织强度阈值:
        基于统计参数
        考虑组织对比度
    创建二值化mask:
        每个组织独立阈值化
        处理重叠区域
    形态学后处理:
        开运算去噪
        闭运算填洞
        边界平滑
    返回分割结果

后处理mask(组织mask字典):
    按解剖层次排序: 头皮→颅骨→脑脊液→灰质→白质
    消除重叠:
        外层组织优先
        内层组织避让
    连通性检查:
        保留最大连通组件
        移除孤立区域
    边界优化:
        高斯平滑
        保持拓扑结构
    返回优化后mask
```

## 精度控制机制

### 1. 分割精度保证
- **空间分辨率控制**：确保处理分辨率≤1mm
- **边界精度优化**：特别关注颅骨-脑脊液界面
- **形态学后处理**：去除分割伪影，平滑边界
- **连通性验证**：确保每个组织为单一连通区域

### 2. 质量评估指标
```python
质量评估指标 = {
    '体积一致性': 与解剖学标准对比,
    '连通组件数': 理想情况为1,
    '表面光滑度': 基于梯度变化计算,
    '边界清晰度': 组织间对比度评估,
    '总体评分': 综合所有指标
}
```

### 3. 自动质量控制
- **实时监控**：分割过程中的质量指标监控
- **自动修正**：检测到质量问题时的自动修正机制
- **警告系统**：质量不达标时的警告和建议
- **人工干预接口**：支持手动调整和优化

## 算法优化策略

### 1. 计算性能优化
- **多线程处理**：并行处理不同组织的分割
- **内存优化**：使用内存映射处理大型数据
- **缓存机制**：缓存中间结果避免重复计算
- **GPU加速**：支持CUDA加速的形态学操作

### 2. 分割精度优化
- **多尺度处理**：从粗到细的分割策略
- **边界细化**：基于梯度的边界精确定位
- **先验知识**：利用解剖学先验约束分割结果
- **迭代优化**：多次迭代改进分割质量

### 3. 鲁棒性增强
- **异常检测**：识别和处理异常MRI数据
- **参数自适应**：根据数据特征自动调整参数
- **多算法融合**：结合多种分割方法的优势
- **错误恢复**：分割失败时的备用策略

## 配置参数详解

### 头部建模配置
```yaml
head_modeling:
  mni_template:
    resolution: 1                    # 模板分辨率(mm)
    template_type: "MNI152_T1_1mm"  # 模板类型
  
  tissue_segmentation:
    method: "freesurfer"            # 分割方法
    tissues:                        # 组织类型列表
      - "scalp"     # 头皮
      - "skull"     # 颅骨  
      - "csf"       # 脑脊液
      - "gray"      # 灰质
      - "white"     # 白质
    accuracy_threshold: 1.0         # 精度阈值(mm)
  
  individual_processing:
    enable_registration: true       # 启用配准
    registration_method: "ants"     # 配准方法
```

## 输出数据格式

### 头部模型结构
```python
head_model = {
    'subject_id': str,              # 被试ID
    'mni_template': Dict,           # MNI模板数据
    'registered_data': Dict,        # 配准后数据
    'tissue_masks': {               # 组织分割结果
        'scalp': np.ndarray,        # 头皮mask
        'skull': np.ndarray,        # 颅骨mask
        'csf': np.ndarray,          # 脑脊液mask
        'gray': np.ndarray,         # 灰质mask
        'white': np.ndarray         # 白质mask
    },
    'quality_metrics': {            # 质量评估
        'overall_score': float,     # 总体评分
        'tissue_scores': Dict       # 各组织评分
    },
    'model_info': {                 # 模型信息
        'resolution': float,        # 分辨率
        'method': str,              # 分割方法
        'timestamp': str            # 创建时间
    }
}
```

## 扩展接口设计

### 1. 自定义分割算法
```python
def register_custom_segmenter(name: str, segmenter_class: type):
    """注册自定义分割算法"""
    pass

def add_tissue_type(tissue_name: str, properties: Dict):
    """添加新的组织类型"""
    pass
```

### 2. 质量控制扩展
```python
def add_quality_metric(metric_name: str, metric_func: callable):
    """添加自定义质量评估指标"""
    pass

def set_quality_threshold(tissue: str, threshold: float):
    """设置组织特定的质量阈值"""
    pass
```

## 验证和测试

### 1. 算法验证
- **仿真数据测试**：使用已知分割结果的仿真数据
- **标准数据集验证**：在公开数据集上的性能评估
- **专家标注对比**：与手动分割结果的一致性检查
- **跨被试稳定性**：不同被试间的分割一致性

### 2. 精度验证
- **空间精度测试**：测量分割边界的空间精度
- **体积精度评估**：与解剖学标准体积的对比
- **重现性测试**：同一数据多次处理的一致性
- **鲁棒性评估**：对噪声和伪影的抗干扰能力
