"""
EEG源定位系统 - 主程序入口

该程序整合了完整的EEG源定位分析流程，从原始数据加载到最终的可视化输出。
支持多种数据格式和分析方法，提供完整的质量控制和结果验证。

主要功能：
1. 多格式EEG数据加载和预处理
2. MRI头部建模和BEM计算
3. 多算法源定位分析
4. 全脑电活动可视化
5. 结果质量评估和报告生成

使用方法：
python main.py --subject_id <ID> --data_type <guinea_bissau|nigeria> --method <sloreta|loreta|mne>
"""

import argparse
import logging
import sys
from pathlib import Path
import yaml
import time
from typing import Dict, List, Optional

# 导入系统模块
from core.data_manager import DataManager
from core.head_modeling import HeadModeling
from core.eeg_processing import EEGProcessor
from algorithms.tissue_segmentation import HighPrecisionTissueSegmenter
from algorithms.bem_modeling import BEMModeler
from algorithms.inverse_solvers import SourceLocalizer
from core.visualization import BrainActivityVisualizer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('eeg_source_localization.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class EEGSourceLocalizationSystem:
    """EEG源定位系统主类"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化EEG源定位系统
        
        参数:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config()
        
        # 初始化各个模块
        logger.info("初始化EEG源定位系统...")
        
        self.data_manager = DataManager(config_path)
        self.head_modeler = HeadModeling(config_path)
        self.eeg_processor = EEGProcessor(config_path)
        self.tissue_segmenter = HighPrecisionTissueSegmenter(self.config)
        self.bem_modeler = BEMModeler(config_path)
        self.source_localizer = SourceLocalizer(config_path)
        self.visualizer = BrainActivityVisualizer(config_path)
        
        logger.info("EEG源定位系统初始化完成")
        
    def _load_config(self) -> Dict:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            raise
            
    def run_complete_analysis(self, subject_id: str, data_type: str = "guinea_bissau",
                            method: str = "sloreta", output_dir: str = None) -> Dict:
        """
        运行完整的源定位分析
        
        参数:
            subject_id: 被试ID
            data_type: 数据类型 ("guinea_bissau" 或 "nigeria")
            method: 源定位方法
            output_dir: 输出目录
            
        返回:
            Dict: 完整的分析结果
        """
        try:
            start_time = time.time()
            logger.info(f"开始完整源定位分析 - 被试: {subject_id}, 方法: {method}")
            
            # 设置输出目录
            if output_dir is None:
                output_dir = f"results/{subject_id}_{method}_{int(time.time())}"
            Path(output_dir).mkdir(parents=True, exist_ok=True)
            
            # 步骤1: 数据加载
            logger.info("步骤1: 加载EEG和MRI数据")
            eeg_data = self.data_manager.eeg_loader.load_eeg_data(subject_id, data_type)
            mri_data = self.data_manager.mri_processor.load_mri_data(subject_id)
            
            # 步骤2: EEG数据预处理
            logger.info("步骤2: EEG数据预处理")
            eeg_result = self.eeg_processor.process_eeg_data(eeg_data, subject_id)
            processed_eeg = eeg_result['processed_data']
            
            # 步骤3: 头部建模
            logger.info("步骤3: 构建头部模型")
            head_model = self.head_modeler.build_head_model(subject_id, mri_data)
            
            # 步骤4: 高精度组织分割
            logger.info("步骤4: 高精度组织分割")
            tissue_masks = head_model['tissue_masks']
            voxel_size = (1.0, 1.0, 1.0)  # 1mm分辨率
            
            segmentation_result = self.tissue_segmenter.segment_with_high_precision(
                list(mri_data.values())[0].get_fdata(), voxel_size
            )
            
            # 步骤5: BEM建模
            logger.info("步骤5: BEM边界元建模")
            bem_model = self.bem_modeler.build_bem_model(
                segmentation_result['tissue_masks'], voxel_size
            )
            
            # 步骤6: 源定位分析
            logger.info("步骤6: 源定位分析")
            eeg_matrix = processed_eeg.get_data()
            leadfield_matrix = bem_model['bem_matrices']['leadfield']
            
            source_result = self.source_localizer.localize_sources(
                eeg_matrix, leadfield_matrix, method=method
            )
            
            # 步骤7: 可视化生成
            logger.info("步骤7: 生成可视化结果")
            visualization_result = self.visualizer.generate_brain_activity_images(
                [source_result], head_model, output_dir
            )
            
            # 步骤8: 生成综合报告
            logger.info("步骤8: 生成分析报告")
            analysis_report = self._generate_analysis_report(
                subject_id, method, eeg_result, head_model, bem_model,
                source_result, visualization_result, output_dir
            )
            
            # 保存完整结果
            complete_result = {
                'subject_id': subject_id,
                'method': method,
                'data_type': data_type,
                'eeg_result': eeg_result,
                'head_model': head_model,
                'segmentation_result': segmentation_result,
                'bem_model': bem_model,
                'source_result': source_result,
                'visualization_result': visualization_result,
                'analysis_report': analysis_report,
                'output_directory': output_dir,
                'processing_time': time.time() - start_time
            }
            
            # 保存结果摘要
            self._save_result_summary(complete_result, output_dir)
            
            logger.info(f"完整源定位分析完成，耗时: {complete_result['processing_time']:.2f}秒")
            logger.info(f"结果已保存到: {output_dir}")
            
            return complete_result
            
        except Exception as e:
            logger.error(f"完整源定位分析失败: {e}")
            raise
            
    def _generate_analysis_report(self, subject_id: str, method: str,
                                eeg_result: Dict, head_model: Dict, bem_model: Dict,
                                source_result: Dict, visualization_result: Dict,
                                output_dir: str) -> Dict:
        """生成分析报告"""
        try:
            report = {
                'analysis_info': {
                    'subject_id': subject_id,
                    'method': method,
                    'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'output_directory': output_dir
                },
                'data_quality': {
                    'eeg_quality': eeg_result['final_quality'],
                    'head_model_quality': head_model['quality_metrics'],
                    'bem_validation': bem_model['validation']
                },
                'source_localization': {
                    'method': method,
                    'num_sources': source_result['source_info']['num_sources'],
                    'quality_metrics': source_result['quality_metrics'],
                    'regularization_parameter': source_result['regularization_parameter']
                },
                'visualization': {
                    'output_files': visualization_result['output_paths'],
                    'statistical_summary': visualization_result['visualization_report']['statistical_summary']
                },
                'recommendations': self._generate_recommendations(
                    eeg_result, head_model, source_result, visualization_result
                )
            }
            
            return report
            
        except Exception as e:
            logger.error(f"分析报告生成失败: {e}")
            return {'error': str(e)}
            
    def _generate_recommendations(self, eeg_result: Dict, head_model: Dict,
                                source_result: Dict, visualization_result: Dict) -> List[str]:
        """生成分析建议"""
        recommendations = []
        
        try:
            # EEG数据质量建议
            eeg_quality = eeg_result['final_quality']['overall_score']
            if eeg_quality < 0.5:
                recommendations.append("EEG数据质量较低，建议检查预处理参数或重新采集数据")
            elif eeg_quality < 0.7:
                recommendations.append("EEG数据质量中等，可考虑进一步优化预处理流程")
                
            # 头部模型质量建议
            head_quality = head_model['quality_metrics']['overall_score']
            if head_quality < 0.7:
                recommendations.append("头部模型质量需要改进，建议检查MRI数据或调整分割参数")
                
            # 源定位质量建议
            source_quality = source_result['quality_metrics']['overall_score']
            if source_quality < 0.6:
                recommendations.append("源定位质量较低，建议尝试其他算法或调整正则化参数")
            elif source_quality > 0.8:
                recommendations.append("源定位质量良好，结果可信度较高")
                
            # 可视化结果建议
            viz_report = visualization_result['visualization_report']
            if viz_report['summary']['active_sources'] < 5:
                recommendations.append("检测到的活跃源较少，可能需要降低阈值或检查数据")
            elif viz_report['summary']['active_sources'] > 100:
                recommendations.append("检测到的活跃源过多，建议提高阈值或增强正则化")
                
            if not recommendations:
                recommendations.append("分析结果质量良好，可以进行进一步的科学解释")
                
            return recommendations
            
        except Exception as e:
            logger.warning(f"建议生成失败: {e}")
            return ["分析完成，请人工检查结果质量"]
            
    def _save_result_summary(self, result: Dict, output_dir: str):
        """保存结果摘要"""
        try:
            summary_path = Path(output_dir) / "analysis_summary.yaml"
            
            # 创建可序列化的摘要
            summary = {
                'subject_id': result['subject_id'],
                'method': result['method'],
                'data_type': result['data_type'],
                'processing_time': result['processing_time'],
                'output_directory': result['output_directory'],
                'quality_scores': {
                    'eeg_quality': result['eeg_result']['final_quality']['overall_score'],
                    'head_model_quality': result['head_model']['quality_metrics']['overall_score'],
                    'source_quality': result['source_result']['quality_metrics']['overall_score']
                },
                'analysis_report': result['analysis_report']
            }
            
            with open(summary_path, 'w', encoding='utf-8') as f:
                yaml.dump(summary, f, default_flow_style=False, allow_unicode=True)
                
            logger.info(f"结果摘要已保存: {summary_path}")
            
        except Exception as e:
            logger.error(f"保存结果摘要失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='EEG源定位系统')
    parser.add_argument('--subject_id', type=str, required=True, help='被试ID')
    parser.add_argument('--data_type', type=str, default='guinea_bissau', 
                       choices=['guinea_bissau', 'nigeria'], help='数据类型')
    parser.add_argument('--method', type=str, default='sloreta',
                       choices=['loreta', 'sloreta', 'eloreta', 'mne', 'dspm'], 
                       help='源定位方法')
    parser.add_argument('--output_dir', type=str, help='输出目录')
    parser.add_argument('--config', type=str, default='config.yaml', help='配置文件路径')
    
    args = parser.parse_args()
    
    try:
        # 初始化系统
        system = EEGSourceLocalizationSystem(args.config)
        
        # 运行完整分析
        result = system.run_complete_analysis(
            subject_id=args.subject_id,
            data_type=args.data_type,
            method=args.method,
            output_dir=args.output_dir
        )
        
        # 输出结果摘要
        print("\n" + "="*60)
        print("EEG源定位分析完成")
        print("="*60)
        print(f"被试ID: {result['subject_id']}")
        print(f"分析方法: {result['method']}")
        print(f"处理时间: {result['processing_time']:.2f}秒")
        print(f"输出目录: {result['output_directory']}")
        print(f"EEG质量评分: {result['eeg_result']['final_quality']['overall_score']:.3f}")
        print(f"源定位质量评分: {result['source_result']['quality_metrics']['overall_score']:.3f}")
        print("\n建议:")
        for rec in result['analysis_report']['recommendations']:
            print(f"- {rec}")
        print("="*60)
        
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
