#!/usr/bin/env python3
"""
标准化坐标回归模型
彻底解决梯度爆炸问题的根本方案：
1. 将所有坐标标准化到[-1, 1]范围
2. 使用极简网络架构
3. 使用标准化的损失函数
4. 严格控制数值范围
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import time
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

from fixed_training_system import NormalizedEEGLesionDataset
from torch.utils.data import DataLoader

class NormalizedCoordinateModel(nn.Module):
    """标准化坐标回归模型"""
    
    def __init__(self, n_channels=14):
        super().__init__()
        
        # 极简特征提取：直接对EEG数据做全局统计
        self.eeg_stats = nn.Sequential(
            nn.AdaptiveAvgPool1d(1),  # 全局平均池化
            nn.<PERSON><PERSON>(),
            nn.Linear(n_channels, 16),
            nn.<PERSON><PERSON>(),  # 使用Tanh保证输出在[-1,1]
        )
        
        # 时序特征：简单平均
        self.temporal_stats = nn.Sequential(
            nn.Linear(n_channels, 16),
            nn.Tanh(),
        )
        
        # 坐标预测：输出标准化坐标
        self.coord_predictor = nn.Sequential(
            nn.Linear(32, 16),
            nn.Tanh(),
            nn.Linear(16, 3),  # 输出3个坐标
            nn.Tanh()  # 输出范围[-1, 1]
        )
        
        # 极保守的初始化
        self.apply(self._init_weights)
    
    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            # 极小的权重初始化
            nn.init.uniform_(m.weight, -0.01, 0.01)
            if m.bias is not None:
                nn.init.zeros_(m.bias)
    
    def forward(self, eeg_data, temporal_sequence):
        # EEG特征
        eeg_feat = self.eeg_stats(eeg_data)
        
        # 时序特征：对时序维度求平均
        temporal_mean = torch.mean(temporal_sequence, dim=1)
        temporal_feat = self.temporal_stats(temporal_mean)
        
        # 融合特征
        fused = torch.cat([eeg_feat, temporal_feat], dim=1)
        
        # 预测标准化坐标 [-1, 1]
        normalized_coords = self.coord_predictor(fused)
        
        # 转换回实际坐标 [0, 255]
        actual_coords = (normalized_coords + 1.0) * 127.5
        
        return {
            'center': actual_coords,
            'normalized_center': normalized_coords
        }

class NormalizedCoordinateLoss(nn.Module):
    """标准化坐标损失函数"""
    
    def __init__(self):
        super().__init__()
        # 只使用MSE损失，但在标准化空间中计算
        self.mse_loss = nn.MSELoss()
    
    def normalize_coord(self, coord):
        """将坐标从[0,255]标准化到[-1,1]"""
        return (coord / 127.5) - 1.0
    
    def forward(self, pred_normalized, target_masks):
        batch_size = pred_normalized.shape[0]
        device = pred_normalized.device
        
        # 计算真实中心坐标
        true_centers_actual = []
        for i in range(batch_size):
            mask = target_masks[i]
            if torch.sum(mask) > 0:
                coords = torch.nonzero(mask, as_tuple=False).float()
                center = torch.mean(coords, dim=0)  # (z, y, x)
                center = center[[2, 1, 0]]  # 转换为 (x, y, z)
            else:
                center = torch.tensor([127.5, 127.5, 127.5], device=device)
            true_centers_actual.append(center)
        
        true_centers_actual = torch.stack(true_centers_actual)
        
        # 标准化真实坐标到[-1, 1]
        true_centers_normalized = self.normalize_coord(true_centers_actual)
        
        # 在标准化空间中计算损失
        mse_loss = self.mse_loss(pred_normalized, true_centers_normalized)
        
        # 计算实际距离（用于监控）
        with torch.no_grad():
            pred_actual = (pred_normalized + 1.0) * 127.5
            distances = torch.norm(pred_actual - true_centers_actual, dim=1)
            mean_distance = torch.mean(distances)
        
        return {
            'total_loss': mse_loss,
            'mse_loss': mse_loss,
            'mean_distance': mean_distance,
            'distances': distances,
            'true_centers': true_centers_actual
        }

class StableTrainer:
    """稳定的训练器"""
    
    def __init__(self, model, device):
        self.model = model.to(device)
        self.device = device
        self.criterion = NormalizedCoordinateLoss()
        
        # 极保守的优化器设置
        self.optimizer = optim.SGD(  # 使用SGD替代Adam
            self.model.parameters(),
            lr=0.001,  # 很小的学习率
            momentum=0.9,
            weight_decay=0
        )
        
        self.history = {
            'train_loss': [], 'val_loss': [],
            'train_distance': [], 'val_distance': [],
            'grad_norms': []
        }
    
    def check_gradients(self):
        """检查梯度范数"""
        total_norm = 0
        for param in self.model.parameters():
            if param.grad is not None:
                param_norm = param.grad.data.norm(2)
                total_norm += param_norm.item() ** 2
        total_norm = total_norm ** (1. / 2)
        return total_norm
    
    def train_epoch(self, train_loader):
        self.model.train()
        
        total_loss = 0.0
        total_distance = 0.0
        num_batches = 0
        grad_norms = []
        
        for batch_idx, batch in enumerate(tqdm(train_loader, desc="Training")):
            try:
                eeg_data = batch['eeg_data'].to(self.device)
                temporal_sequence = batch['temporal_sequence'].to(self.device)
                lesion_masks = batch['lesion_mask'].to(self.device)
                
                self.optimizer.zero_grad()
                
                # 前向传播
                outputs = self.model(eeg_data, temporal_sequence)
                loss_results = self.criterion(outputs['normalized_center'], lesion_masks)
                loss = loss_results['total_loss']
                
                # 检查损失
                if torch.isnan(loss) or torch.isinf(loss) or loss.item() > 10:
                    print(f"跳过异常损失: {loss.item()}")
                    continue
                
                # 反向传播
                loss.backward()
                
                # 检查梯度
                grad_norm = self.check_gradients()
                grad_norms.append(grad_norm)
                
                if grad_norm > 1.0:  # 严格的梯度阈值
                    print(f"跳过大梯度: {grad_norm:.3f}")
                    continue
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=0.5)
                
                self.optimizer.step()
                
                total_loss += loss.item()
                total_distance += loss_results['mean_distance'].item()
                num_batches += 1
                
                # 实时监控
                if batch_idx % 10 == 0:
                    print(f"  Batch {batch_idx}: Loss={loss.item():.4f}, Distance={loss_results['mean_distance'].item():.1f}, GradNorm={grad_norm:.4f}")
                
            except Exception as e:
                print(f"训练错误: {e}")
                continue
        
        avg_loss = total_loss / max(num_batches, 1)
        avg_distance = total_distance / max(num_batches, 1)
        avg_grad_norm = np.mean(grad_norms) if grad_norms else 0
        
        return {
            'loss': avg_loss, 
            'distance': avg_distance,
            'grad_norm': avg_grad_norm
        }
    
    def validate_epoch(self, val_loader):
        self.model.eval()
        
        total_loss = 0.0
        total_distance = 0.0
        num_batches = 0
        
        with torch.no_grad():
            for batch in val_loader:
                try:
                    eeg_data = batch['eeg_data'].to(self.device)
                    temporal_sequence = batch['temporal_sequence'].to(self.device)
                    lesion_masks = batch['lesion_mask'].to(self.device)
                    
                    outputs = self.model(eeg_data, temporal_sequence)
                    loss_results = self.criterion(outputs['normalized_center'], lesion_masks)
                    
                    total_loss += loss_results['total_loss'].item()
                    total_distance += loss_results['mean_distance'].item()
                    num_batches += 1
                    
                except Exception as e:
                    continue
        
        avg_loss = total_loss / max(num_batches, 1)
        avg_distance = total_distance / max(num_batches, 1)
        
        return {'loss': avg_loss, 'distance': avg_distance}
    
    def train(self, train_loader, val_loader, num_epochs=15):
        print(f"开始标准化坐标训练，共 {num_epochs} 个epoch...")
        
        best_val_distance = float('inf')
        
        for epoch in range(num_epochs):
            print(f"\n=== Epoch {epoch+1}/{num_epochs} ===")
            
            # 训练
            train_metrics = self.train_epoch(train_loader)
            
            # 验证
            val_metrics = self.validate_epoch(val_loader)
            
            # 记录历史
            self.history['train_loss'].append(train_metrics['loss'])
            self.history['val_loss'].append(val_metrics['loss'])
            self.history['train_distance'].append(train_metrics['distance'])
            self.history['val_distance'].append(val_metrics['distance'])
            self.history['grad_norms'].append(train_metrics['grad_norm'])
            
            # 打印结果
            print(f"训练 - 损失: {train_metrics['loss']:.4f}, 距离: {train_metrics['distance']:.1f}, 梯度: {train_metrics['grad_norm']:.4f}")
            print(f"验证 - 损失: {val_metrics['loss']:.4f}, 距离: {val_metrics['distance']:.1f}")
            
            # 保存最佳模型
            if val_metrics['distance'] < best_val_distance:
                best_val_distance = val_metrics['distance']
                torch.save({
                    'model_state_dict': self.model.state_dict(),
                    'val_distance': best_val_distance,
                    'history': self.history
                }, 'normalized_coord_best_model.pth')
                print(f"✓ 保存最佳模型 (距离: {best_val_distance:.1f})")
        
        print(f"\n训练完成！最佳距离: {best_val_distance:.1f} 体素")
        return self.history

def test_model_stability():
    """测试模型数值稳定性"""
    print("🧪 测试模型数值稳定性...")
    
    model = NormalizedCoordinateModel()
    criterion = NormalizedCoordinateLoss()
    
    # 创建测试数据
    batch_size = 2
    eeg_data = torch.randn(batch_size, 14, 1024) * 0.1  # 很小的输入
    temporal_sequence = torch.randn(batch_size, 10, 14) * 0.1
    lesion_masks = torch.zeros(batch_size, 256, 256, 256)
    
    # 添加病灶
    lesion_masks[0, 100:150, 100:150, 80:120] = 1.0
    lesion_masks[1, 120:170, 120:170, 100:140] = 1.0
    
    # 前向传播
    outputs = model(eeg_data, temporal_sequence)
    loss_results = criterion(outputs['normalized_center'], lesion_masks)
    
    print(f"前向传播测试:")
    print(f"  标准化坐标: {outputs['normalized_center']}")
    print(f"  实际坐标: {outputs['center']}")
    print(f"  损失: {loss_results['total_loss'].item():.6f}")
    print(f"  距离: {loss_results['mean_distance'].item():.1f}")
    
    # 反向传播测试
    loss_results['total_loss'].backward()
    
    total_norm = 0
    max_grad = 0
    for name, param in model.named_parameters():
        if param.grad is not None:
            grad_norm = param.grad.norm().item()
            total_norm += grad_norm ** 2
            max_grad = max(max_grad, grad_norm)
            print(f"  {name}: {grad_norm:.6f}")
    
    total_norm = total_norm ** 0.5
    print(f"  总梯度范数: {total_norm:.6f}")
    print(f"  最大梯度: {max_grad:.6f}")
    
    if total_norm < 0.1:
        print("✅ 梯度范数很小，数值稳定")
        return True
    elif total_norm < 1.0:
        print("✅ 梯度范数可接受")
        return True
    else:
        print("❌ 梯度范数仍然过大")
        return False

def main():
    print("🔧 标准化坐标回归模型")
    print("="*50)
    
    # 首先测试稳定性
    if not test_model_stability():
        print("❌ 模型稳定性测试失败，停止训练")
        return
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"\n使用设备: {device}")
    
    # 创建数据加载器
    try:
        train_dataset = NormalizedEEGLesionDataset(
            "eeg_lesion_training_dataset/train_pairings.pkl", 
            augment=False
        )
        val_dataset = NormalizedEEGLesionDataset(
            "eeg_lesion_training_dataset/validation_pairings.pkl", 
            augment=False
        )
        
        # 使用很小的batch size
        train_loader = DataLoader(train_dataset, batch_size=1, shuffle=True, num_workers=0)
        val_loader = DataLoader(val_dataset, batch_size=1, shuffle=False, num_workers=0)
        
        print(f"✅ 数据加载器创建成功")
        print(f"训练样本: {len(train_dataset)}, 验证样本: {len(val_dataset)}")
        
    except Exception as e:
        print(f"❌ 创建数据加载器失败: {e}")
        return
    
    # 创建模型
    model = NormalizedCoordinateModel()
    param_count = sum(p.numel() for p in model.parameters())
    print(f"模型参数数量: {param_count:,}")
    
    # 创建训练器
    trainer = StableTrainer(model, device)
    
    # 开始训练
    history = trainer.train(train_loader, val_loader, num_epochs=10)
    
    # 绘制结果
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    # 损失曲线
    axes[0].plot(history['train_loss'], 'b-', label='训练损失')
    axes[0].plot(history['val_loss'], 'r-', label='验证损失')
    axes[0].set_title('损失曲线')
    axes[0].set_xlabel('Epoch')
    axes[0].set_ylabel('MSE损失')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    
    # 距离曲线
    axes[1].plot(history['train_distance'], 'b-', label='训练距离')
    axes[1].plot(history['val_distance'], 'r-', label='验证距离')
    axes[1].set_title('定位误差曲线')
    axes[1].set_xlabel('Epoch')
    axes[1].set_ylabel('距离 (体素)')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    
    # 梯度范数曲线
    axes[2].plot(history['grad_norms'], 'g-', label='梯度范数')
    axes[2].set_title('梯度范数曲线')
    axes[2].set_xlabel('Epoch')
    axes[2].set_ylabel('梯度范数')
    axes[2].legend()
    axes[2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('normalized_coord_training_curves.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("\n✅ 标准化坐标训练完成！")
    
    # 显示最终结果
    if history['val_distance']:
        best_distance = min(history['val_distance'])
        final_grad_norm = history['grad_norms'][-1] if history['grad_norms'] else 0
        
        print(f"\n🎯 最终结果:")
        print(f"  最佳验证距离: {best_distance:.1f} 体素")
        print(f"  最终梯度范数: {final_grad_norm:.4f}")
        
        if final_grad_norm < 0.1:
            print("✅ 梯度稳定")
        else:
            print("⚠️  梯度仍需优化")
        
        if best_distance < 40:
            print("✅ 定位精度可接受")
        else:
            print("⚠️  定位精度需要改进")

if __name__ == "__main__":
    main()
