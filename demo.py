"""
EEG源定位系统 - 演示脚本

该脚本展示了如何使用EEG源定位系统进行完整的分析流程。
包括模拟数据生成、系统初始化、分析执行和结果展示。

使用方法:
python demo.py
"""

import numpy as np
import matplotlib.pyplot as plt
import tempfile
import shutil
from pathlib import Path
import yaml
import time
import logging

# 导入系统模块
from main import EEGSourceLocalizationSystem

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def create_demo_config():
    """创建演示配置"""
    config = {
        'data_paths': {
            'guinea_bissau_dir': 'demo_data/guinea_bissau',
            'nigeria_dir': 'demo_data/nigeria',
            'template_dir': 'demo_data/templates',
            'output_dir': 'demo_results'
        },
        'eeg_processing': {
            'sampling_rate': 250,
            'filtering': {
                'highpass': 0.5,
                'lowpass': 100,
                'notch': 50
            },
            'preprocessing': {
                'remove_dc': True,
                'detrend': True,
                'baseline_correction': True
            },
            'artifact_removal': {
                'enable_ica': True,
                'ica_components': 10,
                'enable_ssp': True
            },
            'channel_quality': {
                'bad_channel_threshold': 3.0,
                'correlation_threshold': 0.3
            }
        },
        'head_modeling': {
            'mni_template': {
                'resolution': 1,
                'template_type': 'MNI152_T1_1mm'
            },
            'tissue_segmentation': {
                'method': 'freesurfer',
                'tissues': ['scalp', 'skull', 'csf', 'gray', 'white'],
                'accuracy_threshold': 1.0
            },
            'individual_processing': {
                'enable_registration': True,
                'registration_method': 'ants'
            }
        },
        'bem_modeling': {
            'model_type': '3_layer',  # 使用3层模型加快演示速度
            'conductivity': {
                'scalp': 0.33,
                'skull': 0.0042,
                'brain': 0.33
            },
            'mesh': {
                'scalp_density': 1024,
                'skull_density': 1024,
                'brain_density': 1024
            },
            'numerical_accuracy': 1e-6
        },
        'source_localization': {
            'source_space': {
                'spacing': 10,  # 较大间距加快计算
                'surface_type': 'white'
            },
            'inverse_methods': ['sloreta', 'mne'],
            'regularization': {
                'lambda_auto': False,  # 使用固定值加快速度
                'lambda_value': 0.1
            },
            'noise_cov': {
                'method': 'empirical',
                'regularization': 0.1
            }
        },
        'visualization': {
            'brain_3d': {
                'backend': 'plotly',
                'surface_alpha': 0.8,
                'colormap': 'hot'
            },
            'topography': {
                'interpolation': 'cubic',
                'contour_lines': 10,
                'colorbar': True
            },
            'output_formats': ['png', 'html']
        }
    }
    return config


def create_demo_eeg_data(n_channels=32, n_timepoints=1000, sfreq=250):
    """创建演示用EEG数据"""
    try:
        import mne
        
        logger.info(f"创建演示EEG数据: {n_channels}通道, {n_timepoints}时间点")
        
        # 创建模拟的EEG信号
        # 包含一些典型的脑电成分
        times = np.arange(n_timepoints) / sfreq
        
        # 基础噪声
        data = np.random.randn(n_channels, n_timepoints) * 5e-6  # 5微伏噪声
        
        # 添加alpha波 (8-12 Hz)
        alpha_freq = 10  # Hz
        alpha_amplitude = 20e-6  # 20微伏
        for ch in range(n_channels):
            # 后部通道alpha波更强
            if ch > n_channels * 0.6:  # 后40%的通道
                alpha_signal = alpha_amplitude * np.sin(2 * np.pi * alpha_freq * times)
                # 添加一些相位差
                phase_shift = np.random.uniform(0, 2*np.pi)
                alpha_signal = alpha_amplitude * np.sin(2 * np.pi * alpha_freq * times + phase_shift)
                data[ch] += alpha_signal
                
        # 添加一些事件相关电位
        # 在几个时间点添加类似P300的成分
        erp_times = [0.3, 0.6, 0.9]  # 秒
        for erp_time in erp_times:
            erp_sample = int(erp_time * sfreq)
            if erp_sample < n_timepoints - 50:
                # P300样的正向偏转
                erp_duration = 50  # 样本点
                erp_amplitude = 15e-6  # 15微伏
                
                # 高斯形状的ERP
                erp_samples = np.arange(erp_duration)
                erp_shape = erp_amplitude * np.exp(-((erp_samples - erp_duration/2)**2) / (2 * (erp_duration/6)**2))
                
                # 添加到中央和顶部电极
                central_channels = range(n_channels//4, 3*n_channels//4)
                for ch in central_channels:
                    if erp_sample + erp_duration < n_timepoints:
                        data[ch, erp_sample:erp_sample+erp_duration] += erp_shape
                        
        # 创建通道信息
        ch_names = [f'EEG{i+1:03d}' for i in range(n_channels)]
        ch_types = ['eeg'] * n_channels
        
        info = mne.create_info(ch_names=ch_names, sfreq=sfreq, ch_types=ch_types)
        raw = mne.io.RawArray(data, info)
        
        # 设置一些标准的电极位置（简化版本）
        montage = mne.channels.make_standard_montage('standard_1020')
        # 只使用前n_channels个电极
        available_ch_names = montage.ch_names[:n_channels]
        if len(available_ch_names) >= n_channels:
            raw.rename_channels({old: new for old, new in zip(ch_names, available_ch_names)})
            raw.set_montage(montage)
            
        logger.info("演示EEG数据创建完成")
        return raw
        
    except ImportError:
        logger.error("MNE库未安装，无法创建演示EEG数据")
        raise
    except Exception as e:
        logger.error(f"创建演示EEG数据失败: {e}")
        raise


def create_demo_mri_data(shape=(64, 64, 64)):
    """创建演示用MRI数据"""
    try:
        import nibabel as nib
        
        logger.info(f"创建演示MRI数据: {shape}")
        
        # 创建简化的头部MRI数据
        data = np.zeros(shape, dtype=np.float32)
        
        # 创建球形的头部结构
        center = np.array(shape) // 2
        
        # 生成坐标网格
        x, y, z = np.ogrid[:shape[0], :shape[1], :shape[2]]
        
        # 计算到中心的距离
        distance = np.sqrt((x - center[0])**2 + (y - center[1])**2 + (z - center[2])**2)
        
        # 创建不同组织的分层结构
        brain_radius = min(shape) * 0.3
        csf_radius = brain_radius * 1.1
        skull_radius = csf_radius * 1.2
        scalp_radius = skull_radius * 1.3
        
        # 分配组织标签
        data[distance <= brain_radius] = 100  # 脑组织
        data[(distance > brain_radius) & (distance <= csf_radius)] = 50   # 脑脊液
        data[(distance > csf_radius) & (distance <= skull_radius)] = 150  # 颅骨
        data[(distance > skull_radius) & (distance <= scalp_radius)] = 75  # 头皮
        
        # 添加一些噪声使其更真实
        noise = np.random.normal(0, 5, shape)
        data = data + noise
        data = np.clip(data, 0, 255)
        
        # 创建仿射变换矩阵
        affine = np.eye(4)
        affine[:3, :3] = np.diag([2.0, 2.0, 2.0])  # 2mm分辨率
        affine[:3, 3] = -np.array(shape) * 1.0  # 设置原点
        
        # 创建NIfTI图像
        nii_img = nib.Nifti1Image(data, affine)
        
        logger.info("演示MRI数据创建完成")
        return {'T1': nii_img}
        
    except ImportError:
        logger.error("nibabel库未安装，无法创建演示MRI数据")
        raise
    except Exception as e:
        logger.error(f"创建演示MRI数据失败: {e}")
        raise


def run_demo():
    """运行完整演示"""
    print("="*60)
    print("EEG源定位系统演示")
    print("="*60)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    config_path = Path(temp_dir) / 'demo_config.yaml'
    
    try:
        # 1. 创建演示配置
        print("\n1. 创建演示配置...")
        demo_config = create_demo_config()
        demo_config['data_paths']['output_dir'] = str(Path(temp_dir) / 'demo_results')
        
        with open(config_path, 'w') as f:
            yaml.dump(demo_config, f)
        print(f"   配置文件已创建: {config_path}")
        
        # 2. 初始化系统
        print("\n2. 初始化EEG源定位系统...")
        system = EEGSourceLocalizationSystem(str(config_path))
        print("   系统初始化完成")
        
        # 3. 创建演示数据
        print("\n3. 创建演示数据...")
        demo_eeg = create_demo_eeg_data(n_channels=32, n_timepoints=500)  # 较小的数据集
        demo_mri = create_demo_mri_data(shape=(32, 32, 32))  # 较小的MRI数据
        print("   演示数据创建完成")
        
        # 4. 模拟数据加载
        print("\n4. 运行源定位分析...")
        from unittest.mock import patch
        
        start_time = time.time()
        
        with patch.object(system.data_manager.eeg_loader, 'load_eeg_data', return_value=demo_eeg):
            with patch.object(system.data_manager.mri_processor, 'load_mri_data', return_value=demo_mri):
                
                result = system.run_complete_analysis(
                    subject_id='demo_subject',
                    data_type='demo',
                    method='sloreta',
                    output_dir=str(Path(temp_dir) / 'demo_results')
                )
                
        processing_time = time.time() - start_time
        
        # 5. 显示结果
        print("\n5. 分析结果:")
        print("="*40)
        print(f"被试ID: {result['subject_id']}")
        print(f"分析方法: {result['method']}")
        print(f"处理时间: {processing_time:.2f}秒")
        print(f"EEG质量评分: {result['eeg_result']['final_quality']['overall_score']:.3f}")
        print(f"源定位质量评分: {result['source_result']['quality_metrics']['overall_score']:.3f}")
        print(f"输出目录: {result['output_directory']}")
        
        # 6. 显示建议
        print("\n6. 系统建议:")
        for i, rec in enumerate(result['analysis_report']['recommendations'], 1):
            print(f"   {i}. {rec}")
            
        # 7. 显示输出文件
        output_dir = Path(result['output_directory'])
        if output_dir.exists():
            print(f"\n7. 输出文件:")
            for file_path in output_dir.rglob('*'):
                if file_path.is_file():
                    print(f"   - {file_path.name}")
                    
        print("\n" + "="*60)
        print("演示完成！")
        print("="*60)
        
        # 询问是否保留结果
        try:
            keep_results = input("\n是否保留演示结果？(y/N): ").lower().strip()
            if keep_results == 'y':
                permanent_dir = Path('demo_results_' + str(int(time.time())))
                shutil.copytree(Path(temp_dir) / 'demo_results', permanent_dir)
                print(f"结果已保存到: {permanent_dir}")
            else:
                print("演示结果将被清理")
        except KeyboardInterrupt:
            print("\n演示结果将被清理")
            
    except Exception as e:
        print(f"\n演示过程中发生错误: {e}")
        logger.error(f"演示失败: {e}")
        
    finally:
        # 清理临时目录
        try:
            shutil.rmtree(temp_dir)
            print("临时文件已清理")
        except Exception as e:
            print(f"清理临时文件失败: {e}")


if __name__ == "__main__":
    try:
        run_demo()
    except KeyboardInterrupt:
        print("\n\n演示被用户中断")
    except Exception as e:
        print(f"\n\n演示失败: {e}")
        logger.error(f"演示失败: {e}")
        import traceback
        traceback.print_exc()
