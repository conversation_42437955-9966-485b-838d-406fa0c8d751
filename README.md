# EEG多通道源定位分析系统

## 系统概述

本系统是一个专业的EEG源定位分析平台，专门设计用于处理多通道EEG数据并进行高精度的神经源定位。系统整合了先进的信号处理、头部建模、边界元法(BEM)计算和多种逆向求解算法，为神经科学研究提供完整的源定位解决方案。

### 核心特性

- **多通道EEG数据处理**：支持多种EEG数据格式，完整的预处理和伪迹去除流程
- **高精度头部建模**：基于MRI的个体化头部模型构建，精度控制在1mm以内
- **先进的BEM建模**：支持3层和5层BEM模型，确保与真实头部结构完全一致
- **多算法源定位**：集成LORETA、sLORETA、eLORETA、MNE、dSPM等经典算法
- **全脑活动可视化**：生成高分辨率3D脑电活动强度图像和交互式可视化
- **质量控制体系**：全流程质量监控和验证，确保结果可靠性

## 系统架构

```
EEG源定位系统
├── 数据管理层 (core/data_manager.py)
│   ├── EEG数据加载器
│   └── MRI数据处理器
├── 信号处理层 (core/eeg_processing.py)
│   ├── 多通道预处理
│   ├── 数字滤波器组
│   ├── 伪迹去除器
│   └── 通道质量评估
├── 头部建模层 (core/head_modeling.py)
│   ├── MNI模板配准
│   ├── 个体化处理
│   └── 质量验证
├── 算法核心层
│   ├── 高精度组织分割 (algorithms/tissue_segmentation.py)
│   ├── BEM边界元建模 (algorithms/bem_modeling.py)
│   └── 逆向求解算法 (algorithms/inverse_solvers.py)
├── 可视化层 (core/visualization.py)
│   ├── 3D脑图渲染
│   ├── 拓扑图生成
│   ├── 时频可视化
│   └── 统计分析
└── 系统集成层 (main.py)
    ├── 流程控制
    ├── 质量监控
    └── 结果输出
```

## 快速开始

### 环境要求

- Python 3.8+
- 内存: 8GB+ (推荐16GB)
- 存储: 10GB+ 可用空间
- 操作系统: Windows 10+, macOS 10.15+, Ubuntu 18.04+

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/your-repo/eeg-source-localization.git
cd eeg-source-localization
```

2. **创建虚拟环境**
```bash
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或
venv\Scripts\activate     # Windows
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **配置系统**
```bash
cp config.yaml.example config.yaml
# 编辑config.yaml，设置数据路径和参数
```

### 基本使用

#### 命令行使用

```bash
# 基本分析
python main.py --subject_id S001 --data_type guinea_bissau --method sloreta

# 指定输出目录
python main.py --subject_id S001 --method mne --output_dir results/S001_mne

# 使用自定义配置
python main.py --subject_id S001 --method dspm --config custom_config.yaml
```

#### Python API使用

```python
from main import EEGSourceLocalizationSystem

# 初始化系统
system = EEGSourceLocalizationSystem('config.yaml')

# 运行完整分析
result = system.run_complete_analysis(
    subject_id='S001',
    data_type='guinea_bissau',
    method='sloreta',
    output_dir='results/S001'
)

# 查看结果
print(f"处理时间: {result['processing_time']:.2f}秒")
print(f"EEG质量评分: {result['eeg_result']['final_quality']['overall_score']:.3f}")
print(f"源定位质量评分: {result['source_result']['quality_metrics']['overall_score']:.3f}")
```

## 支持的算法

### 源定位算法

| 算法 | 全称 | 特点 | 适用场景 |
|------|------|------|----------|
| LORETA | Low Resolution Electromagnetic Tomography | 平滑约束，无定位偏差 | 分布源分析 |
| sLORETA | Standardized LORETA | 标准化，统计检验 | 统计分析 |
| eLORETA | Exact LORETA | 零定位误差，精确推断 | 高精度定位 |
| MNE | Minimum Norm Estimation | 计算高效，表面偏向 | 快速分析 |
| dSPM | Dynamic Statistical Parametric Mapping | 噪声标准化，动态分析 | 时间序列分析 |

### 头部模型

- **3层BEM模型**：头皮-颅骨-脑组织
- **5层BEM模型**：头皮-颅骨-脑脊液-灰质-白质

## 数据格式支持

### EEG数据格式

- **标准格式**：EDF, BDF, GDF, CNT, SET
- **研究格式**：FIF (MNE), MAT (MATLAB)
- **自定义格式**：CSV, TXT, HDF5

### MRI数据格式

- **医学格式**：NIfTI (.nii, .nii.gz), DICOM
- **研究格式**：MGZ (FreeSurfer), IMG/HDR (Analyze)

## 配置说明

### 主要配置项

```yaml
# 数据路径配置
data_paths:
  eeg_data_dir: "data/eeg"          # EEG数据目录
  mri_data_dir: "data/mri"          # MRI数据目录
  output_dir: "results"             # 输出目录

# EEG处理配置
eeg_processing:
  sampling_rate: 250                # 采样率
  filtering:
    highpass: 0.5                   # 高通滤波
    lowpass: 100                    # 低通滤波
    notch: 50                       # 工频滤波

# 源定位配置
source_localization:
  source_space:
    spacing: 5                      # 源点间距(mm)
  regularization:
    lambda_auto: true               # 自动优化正则化参数
```

## 输出结果

### 文件结构

```
results/
├── subject_analysis/
│   ├── brain_3d_activity.html     # 3D交互式脑图
│   ├── topography.png             # 拓扑图
│   ├── time_frequency.html        # 时频分析
│   ├── statistical_maps.json      # 统计分析结果
│   ├── combined_activity.npz      # 原始数值数据
│   └── analysis_summary.yaml      # 分析摘要
└── logs/
    └── eeg_source_localization.log # 系统日志
```

### 质量指标

- **EEG数据质量**：信噪比、通道质量、伪迹比例
- **头部模型质量**：分割精度、网格质量、BEM条件数
- **源定位质量**：拟合优度、稀疏性、时间一致性

## 性能优化

### 计算性能

- **并行处理**：多核CPU并行计算
- **内存优化**：大数据集分块处理
- **缓存机制**：中间结果缓存
- **GPU加速**：支持CUDA加速（可选）

### 处理时间参考

| 数据规模 | 通道数 | 时间点 | 预计时间 |
|----------|--------|--------|----------|
| 小型 | 32 | 1000 | 2-5分钟 |
| 中型 | 64 | 5000 | 10-20分钟 |
| 大型 | 128 | 10000 | 30-60分钟 |

## 测试和验证

### 运行测试

```bash
# 运行所有测试
python -m pytest tests/ -v

# 运行集成测试
python -m pytest tests/test_system_integration.py -v

# 运行性能测试
python -m pytest tests/test_system_integration.py::TestPerformanceBenchmark -v
```

### 验证方法

- **仿真数据验证**：已知源位置的仿真测试
- **标准数据集验证**：公开数据集对比
- **专家评估**：神经科学专家定性评估
- **跨方法一致性**：多算法结果对比

## 故障排除

### 常见问题

1. **内存不足**
   - 减少并行进程数
   - 启用内存映射
   - 分批处理数据

2. **处理时间过长**
   - 降低网格密度
   - 使用3层BEM模型
   - 选择更快的算法(MNE)

3. **结果质量低**
   - 检查EEG数据质量
   - 调整预处理参数
   - 优化正则化参数

### 日志分析

系统提供详细的日志记录，包括：
- 处理步骤和时间
- 质量评估结果
- 错误和警告信息
- 性能统计数据

## 贡献指南

### 开发环境设置

```bash
# 安装开发依赖
pip install -r requirements-dev.txt

# 安装pre-commit钩子
pre-commit install

# 运行代码格式化
black .
isort .
flake8 .
```

### 代码规范

- 遵循PEP 8编码规范
- 使用类型注解
- 编写完整的文档字符串
- 添加单元测试

## 许可证

本项目采用MIT许可证，详见[LICENSE](LICENSE)文件。

## 引用

如果您在研究中使用了本系统，请引用：

```bibtex
@software{eeg_source_localization,
  title={EEG多通道源定位分析系统},
  author={神经影像分析团队},
  year={2024},
  url={https://github.com/your-repo/eeg-source-localization}
}
```

## 联系方式

- 项目主页：https://github.com/your-repo/eeg-source-localization
- 问题反馈：https://github.com/your-repo/eeg-source-localization/issues
- 邮箱：<EMAIL>

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 完整的源定位分析流程
- 支持5种主流算法
- 3D可视化功能
- 质量控制体系
