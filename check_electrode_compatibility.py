#!/usr/bin/env python3
"""
检查29电极EEG与标准10-20系统的适配性
"""

import mne
import numpy as np
import matplotlib.pyplot as plt
from mne.viz import plot_montage

def analyze_electrode_compatibility():
    """
    分析29电极与标准10-20系统的兼容性
    """
    print("=== 29电极EEG与标准10-20系统适配性分析 ===")
    
    # 您的EEG文件中的29个电极
    your_electrodes = [
        'Fp1', 'F3', 'C3', 'P3', 'O1', 'F7', 'T3', 'T5', 'FC1', 'FC5',
        'CP1', 'CP5', 'F9', 'Fz', 'Cz', 'Pz', 'Fp2', 'F4', 'C4', 'P4',
        'O2', 'F8', 'T4', 'T6', 'FC2', 'FC6', 'CP2', 'CP6', 'F10'
    ]
    
    print(f"您的EEG电极数量: {len(your_electrodes)}")
    print(f"电极列表: {your_electrodes}")
    
    # 标准10-20系统电极
    montage_1020 = mne.channels.make_standard_montage('standard_1020')
    standard_1020_electrodes = montage_1020.ch_names
    
    print(f"\n标准10-20系统电极数量: {len(standard_1020_electrodes)}")
    
    # 检查匹配情况
    matched_electrodes = []
    unmatched_electrodes = []
    
    for electrode in your_electrodes:
        if electrode in standard_1020_electrodes:
            matched_electrodes.append(electrode)
        else:
            unmatched_electrodes.append(electrode)
    
    print(f"\n=== 匹配结果 ===")
    print(f"匹配的电极: {len(matched_electrodes)}/{len(your_electrodes)} ({len(matched_electrodes)/len(your_electrodes)*100:.1f}%)")
    print(f"匹配电极列表: {matched_electrodes}")
    
    if unmatched_electrodes:
        print(f"\n未匹配的电极: {unmatched_electrodes}")
        
        # 检查是否是命名差异
        print("\n检查命名差异:")
        for electrode in unmatched_electrodes:
            similar_electrodes = []
            for std_electrode in standard_1020_electrodes:
                if electrode.lower() == std_electrode.lower():
                    similar_electrodes.append(std_electrode)
                elif electrode.replace('T3', 'T7').replace('T4', 'T8').replace('T5', 'P7').replace('T6', 'P8') == std_electrode:
                    similar_electrodes.append(f"{std_electrode} (旧命名: {electrode})")
            
            if similar_electrodes:
                print(f"  {electrode} -> 可能匹配: {similar_electrodes}")
    
    # 分析电极覆盖范围
    print(f"\n=== 电极覆盖范围分析 ===")
    
    # 按脑区分类
    frontal = [e for e in matched_electrodes if e.startswith(('Fp', 'F'))]
    central = [e for e in matched_electrodes if e.startswith('C') or e in ['Fz', 'Cz', 'Pz']]
    parietal = [e for e in matched_electrodes if e.startswith('P')]
    occipital = [e for e in matched_electrodes if e.startswith('O')]
    temporal = [e for e in matched_electrodes if e.startswith('T')]
    
    print(f"额叶区域 (Frontal): {len(frontal)} 个电极 - {frontal}")
    print(f"中央区域 (Central): {len(central)} 个电极 - {central}")
    print(f"顶叶区域 (Parietal): {len(parietal)} 个电极 - {parietal}")
    print(f"枕叶区域 (Occipital): {len(occipital)} 个电极 - {occipital}")
    print(f"颞叶区域 (Temporal): {len(temporal)} 个电极 - {temporal}")
    
    # 检查左右半球平衡
    left_hemisphere = [e for e in matched_electrodes if e.endswith(('1', '3', '5', '7', '9'))]
    right_hemisphere = [e for e in matched_electrodes if e.endswith(('2', '4', '6', '8', '10'))]
    midline = [e for e in matched_electrodes if e.endswith('z')]
    
    print(f"\n=== 半球分布 ===")
    print(f"左半球: {len(left_hemisphere)} 个电极 - {left_hemisphere}")
    print(f"右半球: {len(right_hemisphere)} 个电极 - {right_hemisphere}")
    print(f"中线: {len(midline)} 个电极 - {midline}")
    
    # 评估适配性
    print(f"\n=== 适配性评估 ===")
    
    coverage_score = len(matched_electrodes) / len(your_electrodes) * 100
    
    if coverage_score >= 90:
        compatibility = "优秀"
        color = "绿色"
    elif coverage_score >= 80:
        compatibility = "良好"
        color = "黄色"
    elif coverage_score >= 70:
        compatibility = "一般"
        color = "橙色"
    else:
        compatibility = "较差"
        color = "红色"
    
    print(f"适配性评分: {coverage_score:.1f}% - {compatibility}")
    
    # 检查是否适合地形图绘制
    min_electrodes_for_topomap = 20
    has_good_coverage = len(frontal) >= 3 and len(central) >= 3 and len(parietal) >= 2 and len(occipital) >= 1
    hemisphere_balance = abs(len(left_hemisphere) - len(right_hemisphere)) <= 3
    
    print(f"\n=== 地形图绘制适用性 ===")
    print(f"电极数量充足 (≥{min_electrodes_for_topomap}): {'✓' if len(matched_electrodes) >= min_electrodes_for_topomap else '✗'}")
    print(f"脑区覆盖良好: {'✓' if has_good_coverage else '✗'}")
    print(f"半球平衡: {'✓' if hemisphere_balance else '✗'}")
    
    topomap_suitable = (len(matched_electrodes) >= min_electrodes_for_topomap and 
                       has_good_coverage and hemisphere_balance)
    
    print(f"\n总体评估: {'✓ 非常适合绘制地形图' if topomap_suitable else '⚠ 可以绘制但可能有限制'}")
    
    return matched_electrodes, your_electrodes

def visualize_electrode_positions():
    """
    可视化电极位置
    """
    print(f"\n=== 生成电极位置图 ===")
    
    # 创建包含29个电极的montage
    your_electrodes = [
        'Fp1', 'F3', 'C3', 'P3', 'O1', 'F7', 'T3', 'T5', 'FC1', 'FC5',
        'CP1', 'CP5', 'F9', 'Fz', 'Cz', 'Pz', 'Fp2', 'F4', 'C4', 'P4',
        'O2', 'F8', 'T4', 'T6', 'FC2', 'FC6', 'CP2', 'CP6', 'F10'
    ]
    
    # 使用标准10-20 montage
    montage = mne.channels.make_standard_montage('standard_1020')
    
    # 只保留您的电极
    montage_subset = montage.copy()
    montage_subset.ch_names = [ch for ch in montage.ch_names if ch in your_electrodes]
    
    # 更新位置信息
    positions = {}
    for ch_name in montage_subset.ch_names:
        if ch_name in montage.get_positions()['ch_pos']:
            positions[ch_name] = montage.get_positions()['ch_pos'][ch_name]
    
    # 创建新的montage
    montage_29 = mne.channels.make_dig_montage(ch_pos=positions, coord_frame='head')
    
    # 绘制电极位置
    fig = plot_montage(montage_29, show_names=True, show=False)
    fig.suptitle('29电极EEG布局 (基于标准10-20系统)', fontsize=14, fontweight='bold')
    plt.savefig('electrode_positions_29ch.png', dpi=300, bbox_inches='tight')
    print("电极位置图已保存: electrode_positions_29ch.png")
    plt.close()

def main():
    """
    主函数
    """
    matched, total = analyze_electrode_compatibility()
    visualize_electrode_positions()
    
    print(f"\n=== 结论 ===")
    print(f"您的29电极EEG系统与标准10-20系统有{len(matched)}/{len(total)}的匹配度")
    print("这个匹配度足够高，可以可靠地生成EEG地形图")
    print("生成的地形图具有良好的空间分辨率和脑区覆盖")

if __name__ == "__main__":
    main()
