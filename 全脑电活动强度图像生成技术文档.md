# 全脑电活动强度图像生成技术文档

## 系统概述

全脑电活动强度图像生成模块 (`core/visualization.py`) 是EEG源定位系统的可视化核心，负责将多通道源分析结果叠加融合，生成高分辨率的全脑电活动强度图像。该模块支持3D可视化、时频分析、统计显著性检验和交互式数据探索。

## 核心架构设计

### 1. BrainActivityVisualizer (全脑电活动可视化器主类)

```python
class BrainActivityVisualizer:
    def generate_brain_activity_images(self, source_results, head_model, output_dir) -> Dict
    def _combine_source_results(self, source_results) -> Dict
    def _save_visualization_results(self, results, output_dir) -> Dict
    def _generate_visualization_report(self, combined_activity, statistical_maps, output_paths) -> Dict
```

**核心功能：**
- 多通道源定位结果叠加和融合
- 统计显著性分析和阈值处理
- 多模态可视化生成
- 结果保存和报告生成

**伪代码实现：**
```
生成全脑电活动图像(多通道源定位结果, 头部模型, 输出目录):
    叠加多通道源定位结果:
        收集所有通道的源估计
        使用质量评分作为权重
        加权叠加生成综合活动图
        计算统计量:
            峰值活动分布
            平均活动强度
            时间变异性
            功率分布
    
    统计显著性分析:
        计算p值(t检验)
        多重比较校正(FDR)
        效应量计算(Cohen's d)
        置信区间估计
        聚类分析(DBSCAN)
    
    生成3D脑图:
        创建源位置3D散点图
        添加脑表面网格
        应用颜色映射和透明度
        设置交互式控件
    
    生成拓扑图:
        峰值活动拓扑图
        平均活动拓扑图
        时间变异性拓扑图
        功率分布拓扑图
    
    时频可视化:
        选择最活跃源
        时间序列绘制
        功率谱分析
        时频联合显示
    
    保存可视化结果:
        HTML交互式图表
        PNG高分辨率图像
        JSON数据文件
        NPZ原始数据
    
    生成综合报告:
        活动强度统计
        显著性检验结果
        聚类分析结果
        解释建议
    
    返回完整可视化结果
```

### 2. 多通道结果叠加算法

#### 2.1 加权叠加策略
```python
加权叠加公式 = {
    '权重计算': 'w_i = quality_score_i',
    '叠加公式': 'J_combined = Σ(w_i * J_i) / Σ(w_i)',
    '归一化': '确保权重和为1',
    '质量控制': '排除低质量通道'
}
```

**伪代码实现：**
```
叠加多通道源定位结果(源定位结果列表):
    初始化叠加矩阵
    收集质量权重:
        对每个通道结果:
            提取质量评分
            计算权重因子
            验证数据完整性
    
    执行加权叠加:
        对每个源位置:
            对每个时间点:
                加权求和所有通道贡献
                应用质量权重
        归一化处理
    
    计算综合统计量:
        峰值活动 = max(|J_combined|, axis=时间)
        平均活动 = mean(|J_combined|, axis=时间)
        功率分布 = sum(J_combined², axis=时间)
        时间方差 = var(J_combined, axis=时间)
    
    返回叠加结果和统计量
```

#### 2.2 质量控制机制
```python
质量控制策略 = {
    '权重阈值': '排除质量评分<0.3的通道',
    '异常检测': '识别和处理异常值',
    '一致性检查': '验证通道间一致性',
    '稳定性评估': '评估结果稳定性'
}
```

### 3. Brain3DRenderer (3D脑图渲染器)

```python
class Brain3DRenderer:
    def render_brain_activity(self, combined_activity, head_model, statistical_maps) -> Dict
    def _generate_source_positions(self, num_sources) -> np.ndarray
    def _get_brain_surface_mesh(self, meshes) -> Optional[Dict]
```

**3D可视化特性：**

#### 3.1 源活动3D散点图
```python
散点图配置 = {
    '位置': '3D源坐标(x,y,z)',
    '大小': '活动强度映射',
    '颜色': '活动强度色彩编码',
    '透明度': '可配置透明度',
    '交互': '悬停信息显示'
}
```

#### 3.2 脑表面网格渲染
```python
网格渲染 = {
    '表面类型': '灰质表面优先',
    '透明度': '30%半透明',
    '颜色': '浅灰色',
    '网格密度': '自适应简化'
}
```

**伪代码实现：**
```
渲染3D脑电活动图(综合活动, 头部模型, 统计图):
    生成源位置:
        如果有真实源坐标:
            使用头部模型中的源位置
        否则:
            生成球形分布的模拟位置
            半径约80mm(大脑尺寸)
    
    创建3D散点图:
        x,y,z坐标 = 源位置
        点大小 = 活动强度 * 缩放因子
        点颜色 = 活动强度色彩映射
        悬停信息 = 源索引和活动值
    
    添加脑表面:
        提取脑组织网格(灰质优先)
        设置半透明显示
        添加到3D场景
    
    配置交互控件:
        旋转、缩放、平移
        颜色条和图例
        视角控制
    
    返回交互式3D图表
```

### 4. TopographyGenerator (拓扑图生成器)

```python
class TopographyGenerator:
    def generate_topography(self, combined_activity, head_model) -> Dict
    def _plot_activity_topography(self, ax, activity_data, title, colormap)
```

**拓扑图类型：**

#### 4.1 多视图拓扑显示
```python
拓扑图类型 = {
    '峰值活动': '最大活动强度分布',
    '平均活动': '时间平均活动分布', 
    '时间变异': '活动变化程度分布',
    '功率分布': '总功率空间分布'
}
```

#### 4.2 颜色映射策略
```python
颜色映射 = {
    'hot': '峰值活动(黑-红-黄-白)',
    'viridis': '平均活动(紫-蓝-绿-黄)',
    'plasma': '时间变异(紫-粉-黄)',
    'inferno': '功率分布(黑-紫-红-黄)'
}
```

**伪代码实现：**
```
生成拓扑图(综合活动, 头部模型):
    创建2x2子图布局
    
    绘制峰值活动拓扑:
        数据 = 峰值活动分布
        颜色映射 = 'hot'
        添加等高线
        设置颜色条
    
    绘制平均活动拓扑:
        数据 = 平均活动分布
        颜色映射 = 'viridis'
        添加等高线和标注
    
    绘制时间变异拓扑:
        数据 = 时间方差分布
        颜色映射 = 'plasma'
        突出变化剧烈区域
    
    绘制功率分布拓扑:
        数据 = 功率分布
        颜色映射 = 'inferno'
        标记高功率区域
    
    统一布局和标题
    返回完整拓扑图
```

### 5. TimeFrequencyVisualizer (时频可视化器)

```python
class TimeFrequencyVisualizer:
    def visualize_time_frequency(self, combined_activity, source_results) -> Dict
    def _compute_power_spectrum(self, time_series, sampling_rate) -> Tuple[np.ndarray, np.ndarray]
```

**时频分析功能：**

#### 5.1 源选择策略
```python
源选择策略 = {
    '选择数量': '前5个最活跃源',
    '排序依据': '峰值活动强度',
    '过滤条件': '活动强度>阈值',
    '空间分布': '避免过度集中'
}
```

#### 5.2 频谱分析方法
```python
频谱分析 = {
    '方法': 'Welch功率谱密度估计',
    '窗口': 'Hanning窗',
    '重叠': '50%重叠',
    '频率范围': '0-125Hz'
}
```

**伪代码实现：**
```
时频可视化(综合活动, 源定位结果):
    选择分析源:
        计算所有源的峰值活动
        按活动强度排序
        选择前5个最活跃源
    
    创建多子图布局:
        行数 = 选中源数量
        列数 = 2(时间序列+频谱)
    
    对每个选中源:
        提取时间序列数据
        绘制时间序列图:
            x轴 = 时间点
            y轴 = 活动幅度
            线条样式 = 实线
        
        计算功率谱:
            使用Welch方法
            设置合适的窗口长度
            计算功率谱密度
        
        绘制频谱图:
            x轴 = 频率(Hz)
            y轴 = 功率密度
            突出显示主要频率成分
    
    设置统一标题和标签
    返回交互式时频图
```

### 6. StatisticalAnalyzer (统计分析器)

```python
class StatisticalAnalyzer:
    def analyze_significance(self, combined_activity, source_results) -> Dict
    def _calculate_p_values(self, combined_activity, source_results) -> np.ndarray
    def _multiple_comparison_correction(self, p_values) -> np.ndarray
    def _calculate_effect_sizes(self, combined_activity, source_results) -> np.ndarray
    def _perform_cluster_analysis(self, combined_activity) -> Dict
```

**统计分析方法：**

#### 6.1 显著性检验
```python
显著性检验 = {
    '检验方法': '单样本t检验',
    '零假设': '源活动=0',
    '备择假设': '源活动≠0',
    '显著性水平': 'α=0.05'
}
```

#### 6.2 多重比较校正
```python
多重比较校正 = {
    '方法': 'Benjamini-Hochberg FDR',
    '控制率': '假发现率<0.05',
    '适用场景': '大量源位置同时检验',
    '保守性': '平衡I型和II型错误'
}
```

#### 6.3 效应量计算
```python
效应量计算 = {
    '指标': "Cohen's d",
    '公式': 'd = (μ - 0) / σ',
    '解释': {
        '小效应': 'd ≈ 0.2',
        '中效应': 'd ≈ 0.5', 
        '大效应': 'd ≈ 0.8'
    }
}
```

#### 6.4 聚类分析
```python
聚类分析 = {
    '算法': 'DBSCAN密度聚类',
    '参数': 'eps=0.1*std, min_samples=3',
    '优势': '自动确定聚类数量',
    '输出': '聚类标签和噪声点'
}
```

**伪代码实现：**
```
统计显著性分析(综合活动, 源定位结果):
    计算p值:
        对每个源位置:
            收集所有通道的活动值
            执行单样本t检验
            记录p值
    
    多重比较校正:
        使用Benjamini-Hochberg方法
        控制假发现率<0.05
        获得校正后p值
    
    计算效应量:
        对每个源位置:
            计算Cohen's d
            评估效应大小
    
    置信区间估计:
        使用Bootstrap方法
        重采样1000次
        计算95%置信区间
    
    聚类分析:
        使用DBSCAN算法
        基于活动强度聚类
        识别活动集中区域
    
    生成统计摘要:
        显著源数量
        效应量分布
        聚类结果
    
    返回完整统计图
```

## 可视化输出格式

### 1. 交互式HTML图表
```python
HTML输出 = {
    '3D脑图': 'Plotly交互式3D散点图',
    '时频图': '多子图时频分析',
    '特性': '缩放、旋转、悬停信息',
    '兼容性': '现代浏览器支持'
}
```

### 2. 高分辨率图像
```python
图像输出 = {
    '拓扑图': 'PNG格式，300DPI',
    '尺寸': '12x10英寸',
    '颜色': '全彩色输出',
    '用途': '论文发表和报告'
}
```

### 3. 数据文件
```python
数据输出 = {
    'JSON': '统计分析结果',
    'NPZ': '原始数值数据',
    '格式': 'NumPy压缩格式',
    '用途': '后续分析和处理'
}
```

## 质量控制和验证

### 1. 可视化质量检查
```python
质量检查项目 = {
    '数据完整性': '检查缺失值和异常值',
    '颜色映射': '验证颜色范围和对比度',
    '空间一致性': '确保坐标系统正确',
    '统计有效性': '验证统计检验假设'
}
```

### 2. 结果验证
```python
验证方法 = {
    '交叉验证': '不同通道子集的一致性',
    '仿真验证': '已知源位置的恢复精度',
    '专家评估': '神经科学专家的定性评估',
    '文献对比': '与已发表结果的对比'
}
```

## 配置参数详解

### 可视化配置
```yaml
visualization:
  # 3D脑图配置
  brain_3d:
    backend: "plotly"                   # 渲染后端
    surface_alpha: 0.8                  # 表面透明度
    colormap: "hot"                     # 颜色映射
    
  # 拓扑图配置  
  topography:
    interpolation: "cubic"              # 插值方法
    contour_lines: 10                   # 等高线数量
    colorbar: true                      # 显示颜色条
    
  # 输出格式
  output_formats:
    - "png"                             # PNG图像
    - "svg"                             # 矢量图形
    - "html"                            # 交互式HTML
```

## 性能优化策略

### 1. 渲染优化
- **网格简化**：自适应减少网格复杂度
- **LOD技术**：距离相关的细节层次
- **批量渲染**：减少绘制调用次数
- **缓存机制**：缓存重复使用的几何体

### 2. 内存优化
- **数据压缩**：压缩大型数据集
- **流式处理**：分块处理大数据
- **内存池**：重用内存分配
- **垃圾回收**：及时释放不需要的对象

### 3. 交互优化
- **响应式设计**：适应不同屏幕尺寸
- **异步加载**：后台加载大型数据
- **进度指示**：显示处理进度
- **错误处理**：优雅的错误恢复

## 扩展接口设计

### 1. 自定义可视化
```python
def register_custom_visualizer(name: str, visualizer_class: type):
    """注册自定义可视化器"""
    pass

def add_colormap(name: str, colormap: Dict):
    """添加自定义颜色映射"""
    pass
```

### 2. 统计方法扩展
```python
def register_statistical_test(name: str, test_func: callable):
    """注册自定义统计检验"""
    pass

def add_correction_method(name: str, correction_func: callable):
    """添加多重比较校正方法"""
    pass
```
