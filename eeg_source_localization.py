#!/usr/bin/env python3
"""
EEG源定位分析
使用MNE-Python实现完整的EEG源定位流程

参考文献：
1. <PERSON>fort et al. (2013). MEG and EEG data analysis with MNE-Python. Front Neurosci.
2. <PERSON><PERSON> et al. (2007). Review on solving the forward problem in EEG source analysis. J Neuroeng Rehabil.
3. <PERSON> et al. (2004). EEG source imaging. Clin Neurophysiol.
4. Vorwerk et al. (2014). The FieldTrip-SimBio pipeline for EEG forward solutions. Biomed Eng Online.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import gzip
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# MNE-Python imports
import mne
from mne.datasets import fetch_fsaverage
from mne import setup_source_space, setup_volume_source_space
from mne.bem import make_watershed_bem, make_bem_model, make_bem_solution
from mne.forward import make_forward_solution
from mne.inverse_sparse import mixed_norm
from mne.minimum_norm import make_inverse_operator, apply_inverse

# 3D visualization
from mne.viz import plot_alignment
import pyvista as pv

class EEGSourceLocalizer:
    """EEG源定位分析器"""
    
    def __init__(self, subjects_dir=None):
        """
        初始化源定位分析器
        
        Parameters:
        -----------
        subjects_dir : str or None
            FreeSurfer subjects目录路径
        """
        # 设置MNE参数
        mne.set_log_level('WARNING')
        
        # 生物组织电导率参数 (S/m)
        # 基于最新文献综述 (Vorwerk et al., 2014; Dannhauer et al., 2011)
        self.conductivity = {
            'scalp': 0.33,      # 头皮电导率 (Dannhauer et al., 2011)
            'skull': 0.0042,    # 颅骨电导率 (Vorwerk et al., 2014) 
            'csf': 1.79,        # 脑脊液电导率 (Baumann et al., 1997)
            'brain': 0.33       # 脑组织电导率 (Hallez et al., 2007)
        }
        
        # 14通道EEG电极位置 (10-20系统)
        self.eeg_channels = ['AF3', 'AF4', 'F3', 'F4', 'F7', 'F8', 'FC5', 'FC6', 
                            'O1', 'O2', 'P7', 'P8', 'T7', 'T8']
        
        # 设置subjects目录
        if subjects_dir is None:
            # 使用MNE的fsaverage模板
            self.subjects_dir = fetch_fsaverage(verbose=False)
        else:
            self.subjects_dir = subjects_dir
            
        self.subject = 'fsaverage'
        
        print("🧠 EEG源定位分析器初始化完成")
        print(f"📁 使用模板: {self.subject}")
        print(f"🔬 生物组织电导率参数:")
        for tissue, conductivity in self.conductivity.items():
            print(f"   {tissue}: {conductivity} S/m")
    
    def load_eeg_data(self, file_path, subject_id=None):
        """
        加载EEG数据并创建MNE Raw对象
        
        Parameters:
        -----------
        file_path : str
            EEG数据文件路径
        subject_id : str
            受试者ID
            
        Returns:
        --------
        raw : mne.io.Raw
            MNE Raw对象
        """
        print(f"📊 加载EEG数据: {file_path}")
        
        try:
            # 加载压缩的CSV文件
            with gzip.open(file_path, 'rt') as f:
                df = pd.read_csv(f)
            
            # 检查通道可用性
            available_channels = [ch for ch in self.eeg_channels if ch in df.columns]
            missing_channels = [ch for ch in self.eeg_channels if ch not in df.columns]
            
            if len(available_channels) < 10:
                raise ValueError(f"可用通道数量不足: {len(available_channels)}/14")
            
            if missing_channels:
                print(f"⚠️  缺失通道: {missing_channels}")
            
            # 提取EEG数据
            eeg_data = df[available_channels].values.T  # [channels, time_points]
            
            # 填充缺失通道（用零填充）
            if len(available_channels) < 14:
                full_data = np.zeros((14, eeg_data.shape[1]))
                for i, ch in enumerate(self.eeg_channels):
                    if ch in available_channels:
                        ch_idx = available_channels.index(ch)
                        full_data[i] = eeg_data[ch_idx]
                eeg_data = full_data
            
            # 采样频率
            sfreq = 256.0  # Hz
            
            # 创建Info对象
            info = mne.create_info(
                ch_names=self.eeg_channels,
                sfreq=sfreq,
                ch_types='eeg'
            )
            
            # 创建Raw对象
            raw = mne.io.RawArray(eeg_data, info)
            
            # 设置标准10-20电极位置
            montage = mne.channels.make_standard_montage('standard_1020')
            raw.set_montage(montage, match_case=False)
            
            print(f"✅ EEG数据加载成功")
            print(f"   通道数: {len(raw.ch_names)}")
            print(f"   采样频率: {raw.info['sfreq']} Hz")
            print(f"   数据长度: {raw.times[-1]:.1f} 秒")
            
            return raw
            
        except Exception as e:
            print(f"❌ EEG数据加载失败: {e}")
            raise
    
    def preprocess_eeg(self, raw, l_freq=1.0, h_freq=40.0):
        """
        EEG数据预处理
        
        Parameters:
        -----------
        raw : mne.io.Raw
            原始EEG数据
        l_freq : float
            高通滤波截止频率
        h_freq : float
            低通滤波截止频率
            
        Returns:
        --------
        raw_filtered : mne.io.Raw
            预处理后的EEG数据
        """
        print(f"🔄 EEG数据预处理...")
        
        # 复制数据避免修改原始数据
        raw_filtered = raw.copy()
        
        # 设置平均参考
        raw_filtered.set_eeg_reference('average', projection=True)
        
        # 带通滤波
        raw_filtered.filter(l_freq, h_freq, fir_design='firwin', verbose=False)
        
        # 应用投影
        raw_filtered.apply_proj()
        
        print(f"✅ 预处理完成")
        print(f"   滤波范围: {l_freq}-{h_freq} Hz")
        print(f"   参考电极: 平均参考")
        
        return raw_filtered
    
    def setup_head_model(self):
        """
        构建简化的头模型和源空间
        使用球形头模型和体积源空间

        Returns:
        --------
        bem_solution : dict
            球形头模型参数
        src : mne.SourceSpaces
            体积源空间
        """
        print(f"🏗️  构建简化头模型...")

        try:
            # 使用球形头模型 (更简单，不需要MRI数据)
            sphere = mne.make_sphere_model(
                r0=(0.0, 0.0, 0.04),  # 球心位置 (m)
                head_radius=0.09,     # 头部半径 (m)
                relative_radii=(0.90, 0.92, 0.97, 1.0),  # 相对半径
                sigmas=(
                    self.conductivity['brain'],    # 脑组织
                    self.conductivity['csf'],      # 脑脊液
                    self.conductivity['skull'],    # 颅骨
                    self.conductivity['scalp']     # 头皮
                )
            )

            # 创建体积源空间 (3D网格)
            src = mne.setup_volume_source_space(
                sphere=sphere,
                pos=10.0,  # 源点间距 (mm)
                mri=None,  # 不使用MRI
                bem=None,
                verbose=False
            )

            print(f"✅ 简化头模型构建完成")
            print(f"   模型类型: 球形4层模型")
            print(f"   源空间类型: 体积源空间")
            print(f"   源点数: {src[0]['nuse']}")
            print(f"   源点间距: 10.0 mm")

            return sphere, src

        except Exception as e:
            print(f"❌ 头模型构建失败: {e}")
            raise
    
    def compute_forward_solution(self, raw, sphere_model, src):
        """
        计算正向解

        Parameters:
        -----------
        raw : mne.io.Raw
            EEG数据
        sphere_model : dict
            球形头模型
        src : mne.SourceSpaces
            源空间

        Returns:
        --------
        fwd : mne.Forward
            正向解
        """
        print(f"⚡ 计算正向解...")

        try:
            # 计算正向解 (使用球形头模型)
            fwd = make_forward_solution(
                raw.info,
                trans=None,  # 球形模型不需要变换矩阵
                src=src,
                bem=sphere_model,
                eeg=True,
                meg=False,
                mindist=5.0,  # 最小距离5mm
                n_jobs=1,
                verbose=False
            )

            print(f"✅ 正向解计算完成")
            print(f"   源点数: {fwd['nsource']}")
            print(f"   通道数: {fwd['nchan']}")
            print(f"   导联场矩阵形状: {fwd['sol']['data'].shape}")

            return fwd

        except Exception as e:
            print(f"❌ 正向解计算失败: {e}")
            raise
    
    def compute_inverse_solution(self, raw, fwd, method='dSPM', lambda2=1e-2):
        """
        计算逆解（源定位）
        
        Parameters:
        -----------
        raw : mne.io.Raw
            EEG数据
        fwd : mne.Forward
            正向解
        method : str
            逆解方法 ('MNE', 'dSPM', 'sLORETA', 'eLORETA')
        lambda2 : float
            正则化参数
            
        Returns:
        --------
        stc : mne.SourceEstimate
            源时间序列
        """
        print(f"🎯 计算逆解 (方法: {method})...")
        
        try:
            # 计算噪声协方差矩阵
            # 使用整个数据段估计噪声（假设为静息态）
            noise_cov = mne.compute_raw_covariance(
                raw, 
                tmin=0, 
                tmax=None,
                method='empirical',
                verbose=False
            )
            
            # 创建逆算子
            inverse_operator = make_inverse_operator(
                raw.info,
                fwd,
                noise_cov,
                loose=0.2,      # 松弛约束
                depth=0.8,      # 深度加权
                verbose=False
            )
            
            # 选择时间段进行源定位（选择中间5秒）
            n_times = len(raw.times)
            start_idx = max(0, n_times//2 - int(2.5*raw.info['sfreq']))
            end_idx = min(n_times, n_times//2 + int(2.5*raw.info['sfreq']))

            # 获取数据段
            data_segment = raw.get_data(start=start_idx, stop=end_idx)

            # 创建Evoked对象用于逆解
            evoked = mne.EvokedArray(
                np.mean(data_segment, axis=1, keepdims=True),  # 时间平均
                raw.info,
                tmin=0,
                verbose=False
            )

            # 应用逆解
            stc = apply_inverse(
                evoked,
                inverse_operator,
                lambda2=lambda2,
                method=method,
                pick_ori=None,
                verbose=False
            )
            
            print(f"✅ 逆解计算完成")
            print(f"   方法: {method}")
            print(f"   正则化参数: {lambda2}")
            print(f"   时间段: {tmin:.1f} - {tmax:.1f} 秒")
            print(f"   源活动形状: {stc.data.shape}")
            
            return stc, inverse_operator
            
        except Exception as e:
            print(f"❌ 逆解计算失败: {e}")
            raise
    
    def visualize_source_activity(self, stc, method='dSPM'):
        """
        可视化源活动
        
        Parameters:
        -----------
        stc : mne.SourceEstimate
            源时间序列
        method : str
            逆解方法名称
        """
        print(f"📊 可视化源活动...")
        
        try:
            # 计算平均源活动强度
            mean_activity = np.mean(np.abs(stc.data), axis=1)
            
            # 找到最强活动的源点
            max_vertex = np.argmax(mean_activity)
            max_activity = mean_activity[max_vertex]
            
            print(f"   最强源活动: {max_activity:.2e} (顶点 {max_vertex})")
            
            # 创建可视化
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            
            # 1. 源活动时间序列（前10个最强源点）
            top_vertices = np.argsort(mean_activity)[-10:]
            axes[0, 0].plot(stc.times, stc.data[top_vertices].T)
            axes[0, 0].set_title('Top 10 Source Time Series')
            axes[0, 0].set_xlabel('Time (s)')
            axes[0, 0].set_ylabel(f'{method} Value')
            axes[0, 0].grid(True, alpha=0.3)
            
            # 2. 源活动强度分布
            axes[0, 1].hist(mean_activity, bins=50, alpha=0.7, color='blue')
            axes[0, 1].set_title('Source Activity Distribution')
            axes[0, 1].set_xlabel(f'Mean {method} Value')
            axes[0, 1].set_ylabel('Count')
            axes[0, 1].grid(True, alpha=0.3)
            
            # 3. 时间平均的源活动
            time_avg = np.mean(np.abs(stc.data), axis=1)
            axes[1, 0].plot(time_avg)
            axes[1, 0].set_title('Time-Averaged Source Activity')
            axes[1, 0].set_xlabel('Source Index')
            axes[1, 0].set_ylabel(f'Mean {method} Value')
            axes[1, 0].grid(True, alpha=0.3)
            
            # 4. 源活动的时频表示（选择最强源点）
            source_ts = stc.data[max_vertex]
            axes[1, 1].plot(stc.times, source_ts)
            axes[1, 1].set_title(f'Strongest Source Time Series (Vertex {max_vertex})')
            axes[1, 1].set_xlabel('Time (s)')
            axes[1, 1].set_ylabel(f'{method} Value')
            axes[1, 1].grid(True, alpha=0.3)
            
            plt.tight_layout()
            plt.savefig(f'source_activity_{method.lower()}.png', dpi=300, bbox_inches='tight')
            plt.show()
            
            # 保存源定位结果
            stc.save(f'source_estimate_{method.lower()}')
            
            print(f"✅ 可视化完成")
            print(f"   图像保存: source_activity_{method.lower()}.png")
            print(f"   数据保存: source_estimate_{method.lower()}-lh.stc, source_estimate_{method.lower()}-rh.stc")
            
            return mean_activity, max_vertex
            
        except Exception as e:
            print(f"❌ 可视化失败: {e}")
            raise
    
    def evaluate_localization_accuracy(self, stc, true_source_pos=None):
        """
        评估源定位精度
        
        Parameters:
        -----------
        stc : mne.SourceEstimate
            源时间序列
        true_source_pos : array-like or None
            真实源位置（如果已知）
            
        Returns:
        --------
        metrics : dict
            评估指标
        """
        print(f"📏 评估源定位精度...")
        
        try:
            # 计算源活动指标
            mean_activity = np.mean(np.abs(stc.data), axis=1)
            
            # 基本统计指标
            metrics = {
                'max_activity': np.max(mean_activity),
                'mean_activity': np.mean(mean_activity),
                'std_activity': np.std(mean_activity),
                'active_sources': np.sum(mean_activity > 0.1 * np.max(mean_activity)),
                'localization_spread': np.std(mean_activity),
                'peak_to_mean_ratio': np.max(mean_activity) / np.mean(mean_activity)
            }
            
            # 空间集中度指标
            # 计算活动重心
            weights = mean_activity / np.sum(mean_activity)
            centroid = np.sum(weights * np.arange(len(mean_activity)))
            
            # 计算空间分散度
            spatial_spread = np.sqrt(np.sum(weights * (np.arange(len(mean_activity)) - centroid)**2))
            
            metrics['centroid_vertex'] = int(centroid)
            metrics['spatial_spread'] = spatial_spread
            
            # 如果提供了真实源位置，计算定位误差
            if true_source_pos is not None:
                estimated_pos = np.argmax(mean_activity)
                localization_error = np.abs(estimated_pos - true_source_pos)
                metrics['localization_error'] = localization_error
            
            print(f"✅ 精度评估完成")
            print(f"   最大活动强度: {metrics['max_activity']:.2e}")
            print(f"   活跃源点数: {metrics['active_sources']}")
            print(f"   空间分散度: {metrics['spatial_spread']:.1f}")
            print(f"   峰值/均值比: {metrics['peak_to_mean_ratio']:.1f}")
            
            return metrics
            
        except Exception as e:
            print(f"❌ 精度评估失败: {e}")
            raise

def main():
    """主函数：完整的源定位分析流程"""
    
    print("🚀 EEG源定位分析开始")
    print("="*70)
    
    # 初始化源定位分析器
    localizer = EEGSourceLocalizer()
    
    # 选择一个癫痫患者样本
    data_dir = Path("1252141/EEGs_Guinea-Bissau")
    
    # 从元数据中找到癫痫患者
    metadata = pd.read_csv("1252141/metadata_guineabissau.csv")
    epilepsy_patients = metadata[metadata['Group'] == 'Epilepsy']
    
    if len(epilepsy_patients) == 0:
        print("❌ 未找到癫痫患者数据")
        return
    
    # 选择第一个癫痫患者
    patient = epilepsy_patients.iloc[0]
    subject_id = patient['subject.id']
    eeg_file = data_dir / f"signal-{subject_id}.csv.gz"
    
    if not eeg_file.exists():
        print(f"❌ EEG文件不存在: {eeg_file}")
        return
    
    print(f"📋 选择患者: {subject_id}")
    print(f"📁 EEG文件: {eeg_file}")
    
    try:
        # 1. 加载EEG数据
        raw = localizer.load_eeg_data(str(eeg_file), subject_id)
        
        # 2. 预处理
        raw_filtered = localizer.preprocess_eeg(raw)
        
        # 3. 构建头模型
        sphere_model, src = localizer.setup_head_model()

        # 4. 计算正向解
        fwd = localizer.compute_forward_solution(raw_filtered, sphere_model, src)
        
        # 5. 计算逆解
        stc, inverse_operator = localizer.compute_inverse_solution(raw_filtered, fwd, method='dSPM')
        
        # 6. 可视化结果
        mean_activity, max_vertex = localizer.visualize_source_activity(stc, method='dSPM')
        
        # 7. 评估精度
        metrics = localizer.evaluate_localization_accuracy(stc)
        
        print("\n🎉 EEG源定位分析完成！")
        print("="*70)
        print("📊 主要结果:")
        print(f"   患者ID: {subject_id}")
        print(f"   最强源活动位置: 顶点 {max_vertex}")
        print(f"   最大活动强度: {metrics['max_activity']:.2e}")
        print(f"   活跃源点数: {metrics['active_sources']}")
        print(f"   空间集中度: {metrics['spatial_spread']:.1f}")
        
        print("\n📁 输出文件:")
        print("   - source_activity_dspm.png: 源活动可视化")
        print("   - source_estimate_dspm-lh.stc: 左半球源估计")
        print("   - source_estimate_dspm-rh.stc: 右半球源估计")
        
    except Exception as e:
        print(f"❌ 源定位分析失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
