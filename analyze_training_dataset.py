#!/usr/bin/env python3
"""
Analyze and Visualize EEG-Lesion Training Dataset
Comprehensive analysis of the generated synthetic pairings
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import pickle
import json
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

class DatasetAnalyzer:
    """Analyze the generated EEG-lesion training dataset"""
    
    def __init__(self, dataset_dir="eeg_lesion_training_dataset"):
        self.dataset_dir = Path(dataset_dir)
        self.load_dataset()
        
    def load_dataset(self):
        """Load the complete dataset"""
        print("Loading training dataset...")
        
        # Load splits
        self.splits = {}
        for split_name in ['train', 'validation', 'test']:
            split_file = self.dataset_dir / f"{split_name}_pairings.pkl"
            if split_file.exists():
                with open(split_file, 'rb') as f:
                    self.splits[split_name] = pickle.load(f)
                print(f"Loaded {len(self.splits[split_name])} {split_name} pairings")
        
        # Load metadata
        metadata_file = self.dataset_dir / "dataset_metadata.json"
        if metadata_file.exists():
            with open(metadata_file, 'r') as f:
                self.metadata = json.load(f)
            print(f"Loaded dataset metadata")
        
        # Combine all pairings for analysis
        self.all_pairings = []
        for split_name, pairings in self.splits.items():
            for pairing in pairings:
                pairing['split'] = split_name
                self.all_pairings.append(pairing)
        
        print(f"Total pairings loaded: {len(self.all_pairings)}")
    
    def analyze_dataset_statistics(self):
        """Analyze basic dataset statistics"""
        print("\n" + "="*60)
        print("DATASET STATISTICS")
        print("="*60)
        
        # Basic counts
        print(f"Total EEG-lesion pairings: {len(self.all_pairings)}")
        print(f"Unique EEG subjects: {len(set(p['eeg_subject_id'] for p in self.all_pairings))}")
        print(f"Unique lesions: {len(set(p['lesion_id'] for p in self.all_pairings))}")
        
        # Split distribution
        split_counts = Counter(p['split'] for p in self.all_pairings)
        print(f"\nSplit distribution:")
        for split, count in split_counts.items():
            percentage = (count / len(self.all_pairings)) * 100
            print(f"  {split}: {count} ({percentage:.1f}%)")
        
        # Group distribution
        group_counts = Counter(p['eeg_group'] for p in self.all_pairings)
        print(f"\nEEG group distribution:")
        for group, count in group_counts.items():
            percentage = (count / len(self.all_pairings)) * 100
            print(f"  {group}: {count} ({percentage:.1f}%)")
        
        # Dataset distribution
        dataset_counts = Counter(p['eeg_dataset'] for p in self.all_pairings)
        print(f"\nEEG dataset distribution:")
        for dataset, count in dataset_counts.items():
            percentage = (count / len(self.all_pairings)) * 100
            print(f"  {dataset}: {count} ({percentage:.1f}%)")
        
        # Lesion region distribution
        lesion_regions = [p['lesion_features']['region'] for p in self.all_pairings]
        region_counts = Counter(lesion_regions)
        print(f"\nLesion region distribution:")
        for region, count in region_counts.items():
            percentage = (count / len(self.all_pairings)) * 100
            print(f"  {region}: {count} ({percentage:.1f}%)")
    
    def analyze_compatibility_scores(self):
        """Analyze pairing compatibility scores"""
        print("\n" + "="*60)
        print("COMPATIBILITY SCORE ANALYSIS")
        print("="*60)
        
        # Extract scores
        total_scores = [p['compatibility_score'] for p in self.all_pairings]
        spatial_scores = [p['spatial_score'] for p in self.all_pairings]
        clinical_scores = [p['clinical_score'] for p in self.all_pairings]
        metadata_scores = [p['metadata_score'] for p in self.all_pairings]
        
        # Compute statistics
        score_stats = {
            'Total': total_scores,
            'Spatial': spatial_scores,
            'Clinical': clinical_scores,
            'Metadata': metadata_scores
        }
        
        print("Score statistics:")
        for score_type, scores in score_stats.items():
            print(f"  {score_type}:")
            print(f"    Mean: {np.mean(scores):.3f}")
            print(f"    Std:  {np.std(scores):.3f}")
            print(f"    Min:  {np.min(scores):.3f}")
            print(f"    Max:  {np.max(scores):.3f}")
        
        return score_stats
    
    def create_visualizations(self):
        """Create comprehensive visualizations"""
        print("\nCreating visualizations...")
        
        # Set up the plotting style
        plt.style.use('default')
        sns.set_palette("husl")
        
        # Create figure with subplots
        fig = plt.figure(figsize=(20, 16))
        
        # 1. Compatibility score distributions
        ax1 = plt.subplot(3, 4, 1)
        scores = [p['compatibility_score'] for p in self.all_pairings]
        plt.hist(scores, bins=30, alpha=0.7, edgecolor='black')
        plt.title('Total Compatibility Score Distribution')
        plt.xlabel('Compatibility Score')
        plt.ylabel('Frequency')
        
        # 2. Score components comparison
        ax2 = plt.subplot(3, 4, 2)
        score_data = {
            'Spatial': [p['spatial_score'] for p in self.all_pairings],
            'Clinical': [p['clinical_score'] for p in self.all_pairings],
            'Metadata': [p['metadata_score'] for p in self.all_pairings]
        }
        plt.boxplot(score_data.values(), labels=score_data.keys())
        plt.title('Score Components Distribution')
        plt.ylabel('Score Value')
        plt.xticks(rotation=45)
        
        # 3. Group vs compatibility scores
        ax3 = plt.subplot(3, 4, 3)
        epilepsy_scores = [p['compatibility_score'] for p in self.all_pairings if p['eeg_group'] == 'Epilepsy']
        control_scores = [p['compatibility_score'] for p in self.all_pairings if p['eeg_group'] == 'Control']
        
        plt.boxplot([epilepsy_scores, control_scores], labels=['Epilepsy', 'Control'])
        plt.title('Compatibility by EEG Group')
        plt.ylabel('Compatibility Score')
        
        # 4. Lesion region distribution
        ax4 = plt.subplot(3, 4, 4)
        regions = [p['lesion_features']['region'] for p in self.all_pairings]
        region_counts = Counter(regions)
        plt.pie(region_counts.values(), labels=region_counts.keys(), autopct='%1.1f%%')
        plt.title('Lesion Region Distribution')
        
        # 5. Lesion volume distribution
        ax5 = plt.subplot(3, 4, 5)
        volumes = [p['lesion_features']['volume_mm3'] for p in self.all_pairings]
        plt.hist(volumes, bins=30, alpha=0.7, edgecolor='black')
        plt.title('Lesion Volume Distribution')
        plt.xlabel('Volume (mm³)')
        plt.ylabel('Frequency')
        plt.yscale('log')
        
        # 6. Spatial distance analysis
        ax6 = plt.subplot(3, 4, 6)
        # Compute distances between EEG sources and lesion centroids
        distances = []
        for p in self.all_pairings:
            source_coords = np.array([p['eeg_features']['source_x'], 
                                    p['eeg_features']['source_y'], 
                                    p['eeg_features']['source_z']])
            lesion_coords = np.array([p['lesion_features']['centroid_x'],
                                    p['lesion_features']['centroid_y'],
                                    p['lesion_features']['centroid_z']])
            distance = np.linalg.norm(source_coords - lesion_coords)
            distances.append(distance)
        
        plt.hist(distances, bins=30, alpha=0.7, edgecolor='black')
        plt.title('EEG Source-Lesion Distance')
        plt.xlabel('Distance (voxels)')
        plt.ylabel('Frequency')
        
        # 7. Pairing rank distribution
        ax7 = plt.subplot(3, 4, 7)
        ranks = [p['pairing_rank'] for p in self.all_pairings]
        rank_counts = Counter(ranks)
        plt.bar(rank_counts.keys(), rank_counts.values())
        plt.title('Pairing Rank Distribution')
        plt.xlabel('Pairing Rank')
        plt.ylabel('Count')
        
        # 8. Dataset split distribution
        ax8 = plt.subplot(3, 4, 8)
        split_counts = Counter(p['split'] for p in self.all_pairings)
        plt.pie(split_counts.values(), labels=split_counts.keys(), autopct='%1.1f%%')
        plt.title('Dataset Split Distribution')
        
        # 9. Correlation matrix of scores
        ax9 = plt.subplot(3, 4, 9)
        score_df = pd.DataFrame({
            'Total': [p['compatibility_score'] for p in self.all_pairings],
            'Spatial': [p['spatial_score'] for p in self.all_pairings],
            'Clinical': [p['clinical_score'] for p in self.all_pairings],
            'Metadata': [p['metadata_score'] for p in self.all_pairings]
        })
        correlation_matrix = score_df.corr()
        sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0, ax=ax9)
        plt.title('Score Correlation Matrix')
        
        # 10. Lesion laterality distribution
        ax10 = plt.subplot(3, 4, 10)
        laterality = [p['lesion_features']['laterality'] for p in self.all_pairings]
        laterality_counts = Counter(laterality)
        plt.pie(laterality_counts.values(), labels=laterality_counts.keys(), autopct='%1.1f%%')
        plt.title('Lesion Laterality Distribution')
        
        # 11. EEG source strength vs lesion volume
        ax11 = plt.subplot(3, 4, 11)
        source_strengths = [p['eeg_features']['source_strength'] for p in self.all_pairings]
        lesion_volumes = [p['lesion_features']['volume_mm3'] for p in self.all_pairings]
        plt.scatter(source_strengths, lesion_volumes, alpha=0.6)
        plt.xlabel('EEG Source Strength')
        plt.ylabel('Lesion Volume (mm³)')
        plt.title('Source Strength vs Lesion Volume')
        plt.yscale('log')
        
        # 12. Quality assessment
        ax12 = plt.subplot(3, 4, 12)
        high_quality = sum(1 for p in self.all_pairings if p['compatibility_score'] > 0.7)
        medium_quality = sum(1 for p in self.all_pairings if 0.4 <= p['compatibility_score'] <= 0.7)
        low_quality = sum(1 for p in self.all_pairings if p['compatibility_score'] < 0.4)
        
        quality_counts = [high_quality, medium_quality, low_quality]
        quality_labels = ['High (>0.7)', 'Medium (0.4-0.7)', 'Low (<0.4)']
        plt.pie(quality_counts, labels=quality_labels, autopct='%1.1f%%')
        plt.title('Pairing Quality Distribution')
        
        plt.tight_layout()
        
        # Save the comprehensive visualization
        output_file = "eeg_lesion_dataset_analysis.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"Comprehensive analysis saved as: {output_file}")
        
        plt.show()
        
        return fig
    
    def generate_summary_report(self):
        """Generate a comprehensive summary report"""
        print("\n" + "="*60)
        print("DATASET SUMMARY REPORT")
        print("="*60)
        
        # Basic statistics
        total_pairings = len(self.all_pairings)
        unique_subjects = len(set(p['eeg_subject_id'] for p in self.all_pairings))
        unique_lesions = len(set(p['lesion_id'] for p in self.all_pairings))
        
        # Quality metrics
        high_quality = sum(1 for p in self.all_pairings if p['compatibility_score'] > 0.7)
        avg_compatibility = np.mean([p['compatibility_score'] for p in self.all_pairings])
        
        # Group balance
        epilepsy_count = sum(1 for p in self.all_pairings if p['eeg_group'] == 'Epilepsy')
        control_count = sum(1 for p in self.all_pairings if p['eeg_group'] == 'Control')
        
        print(f"Dataset Overview:")
        print(f"  Total pairings: {total_pairings}")
        print(f"  Unique EEG subjects: {unique_subjects}")
        print(f"  Unique lesions: {unique_lesions}")
        print(f"  Average compatibility score: {avg_compatibility:.3f}")
        print(f"  High-quality pairings (>0.7): {high_quality} ({high_quality/total_pairings*100:.1f}%)")
        
        print(f"\nGroup Balance:")
        print(f"  Epilepsy patients: {epilepsy_count} ({epilepsy_count/total_pairings*100:.1f}%)")
        print(f"  Control subjects: {control_count} ({control_count/total_pairings*100:.1f}%)")
        
        print(f"\nDataset Quality Assessment:")
        if avg_compatibility > 0.6:
            print("  ✓ Good overall compatibility scores")
        else:
            print("  ⚠ Low overall compatibility scores")
        
        if high_quality/total_pairings > 0.3:
            print("  ✓ Sufficient high-quality pairings")
        else:
            print("  ⚠ Limited high-quality pairings")
        
        if abs(epilepsy_count - control_count) / total_pairings < 0.3:
            print("  ✓ Reasonably balanced groups")
        else:
            print("  ⚠ Imbalanced groups")
        
        print(f"\nRecommendations:")
        print("  • Use high-quality pairings (>0.7) for initial model training")
        print("  • Consider data augmentation for underrepresented groups")
        print("  • Validate model performance across different lesion regions")
        print("  • Monitor for overfitting due to synthetic pairing nature")

def main():
    """Main analysis function"""
    print("EEG-Lesion Training Dataset Analysis")
    print("="*50)
    
    # Initialize analyzer
    analyzer = DatasetAnalyzer()
    
    # Perform comprehensive analysis
    analyzer.analyze_dataset_statistics()
    analyzer.analyze_compatibility_scores()
    analyzer.create_visualizations()
    analyzer.generate_summary_report()
    
    print("\nDataset analysis completed!")

if __name__ == "__main__":
    main()
