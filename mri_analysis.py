#!/usr/bin/env python3
"""
MRI数据分析和可视化脚本
分析带掩码的MRI数据
"""

import numpy as np
import matplotlib.pyplot as plt
import nibabel as nib
import os
from matplotlib.colors import ListedColormap
import warnings

warnings.filterwarnings('ignore')

def load_and_analyze_mri(file_path):
    """
    加载并分析MRI数据
    """
    print(f"=== 分析文件: {file_path} ===")
    
    try:
        # 加载NIfTI文件
        img = nib.load(file_path)
        data = img.get_fdata()
        header = img.header
        affine = img.affine
        
        print(f"文件路径: {file_path}")
        print(f"数据形状: {data.shape}")
        print(f"数据类型: {data.dtype}")
        print(f"体素尺寸: {header.get_zooms()}")
        print(f"数据范围: {data.min():.3f} - {data.max():.3f}")
        print(f"非零体素数: {np.count_nonzero(data)}")
        print(f"总体素数: {data.size}")
        print(f"非零比例: {np.count_nonzero(data)/data.size*100:.2f}%")
        
        # 分析数据分布
        unique_values = np.unique(data)
        print(f"唯一值数量: {len(unique_values)}")
        print(f"唯一值: {unique_values[:20]}...")  # 显示前20个值
        
        return {
            'data': data,
            'header': header,
            'affine': affine,
            'file_path': file_path,
            'unique_values': unique_values
        }
        
    except Exception as e:
        print(f"加载文件时出错: {e}")
        return None

def create_mri_slices_visualization(mri_info, output_prefix):
    """
    创建MRI切片可视化
    """
    print(f"\n=== 创建切片可视化: {output_prefix} ===")
    
    data = mri_info['data']
    
    # 获取中心切片索引
    center_x = data.shape[0] // 2
    center_y = data.shape[1] // 2
    center_z = data.shape[2] // 2
    
    # 创建三个方向的切片图
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle(f'MRI切片可视化 - {os.path.basename(mri_info["file_path"])}', 
                 fontsize=16, fontweight='bold')
    
    # 矢状面 (Sagittal) - X方向
    slice_sag = data[center_x, :, :]
    im1 = axes[0, 0].imshow(slice_sag.T, cmap='gray', origin='lower')
    axes[0, 0].set_title(f'矢状面 (X={center_x})', fontweight='bold')
    axes[0, 0].set_xlabel('Y轴')
    axes[0, 0].set_ylabel('Z轴')
    plt.colorbar(im1, ax=axes[0, 0], shrink=0.8)
    
    # 冠状面 (Coronal) - Y方向
    slice_cor = data[:, center_y, :]
    im2 = axes[0, 1].imshow(slice_cor.T, cmap='gray', origin='lower')
    axes[0, 1].set_title(f'冠状面 (Y={center_y})', fontweight='bold')
    axes[0, 1].set_xlabel('X轴')
    axes[0, 1].set_ylabel('Z轴')
    plt.colorbar(im2, ax=axes[0, 1], shrink=0.8)
    
    # 轴位面 (Axial) - Z方向
    slice_ax = data[:, :, center_z]
    im3 = axes[0, 2].imshow(slice_ax.T, cmap='gray', origin='lower')
    axes[0, 2].set_title(f'轴位面 (Z={center_z})', fontweight='bold')
    axes[0, 2].set_xlabel('X轴')
    axes[0, 2].set_ylabel('Y轴')
    plt.colorbar(im3, ax=axes[0, 2], shrink=0.8)
    
    # 如果是掩码数据，创建二值化可视化
    if len(mri_info['unique_values']) <= 10:  # 可能是掩码
        print("检测到可能的掩码数据，创建二值化可视化...")
        
        # 创建掩码可视化
        mask_data = data > 0
        
        # 矢状面掩码
        mask_sag = mask_data[center_x, :, :]
        axes[1, 0].imshow(mask_sag.T, cmap='Reds', origin='lower', alpha=0.7)
        axes[1, 0].set_title(f'掩码-矢状面', fontweight='bold')
        axes[1, 0].set_xlabel('Y轴')
        axes[1, 0].set_ylabel('Z轴')
        
        # 冠状面掩码
        mask_cor = mask_data[:, center_y, :]
        axes[1, 1].imshow(mask_cor.T, cmap='Reds', origin='lower', alpha=0.7)
        axes[1, 1].set_title(f'掩码-冠状面', fontweight='bold')
        axes[1, 1].set_xlabel('X轴')
        axes[1, 1].set_ylabel('Z轴')
        
        # 轴位面掩码
        mask_ax = mask_data[:, :, center_z]
        axes[1, 2].imshow(mask_ax.T, cmap='Reds', origin='lower', alpha=0.7)
        axes[1, 2].set_title(f'掩码-轴位面', fontweight='bold')
        axes[1, 2].set_xlabel('X轴')
        axes[1, 2].set_ylabel('Y轴')
    else:
        # 如果不是掩码，显示直方图
        axes[1, 0].hist(data[data > 0].flatten(), bins=50, alpha=0.7)
        axes[1, 0].set_title('非零值分布', fontweight='bold')
        axes[1, 0].set_xlabel('强度值')
        axes[1, 0].set_ylabel('频次')
        
        # 显示统计信息
        stats_text = f"""
        最小值: {data.min():.3f}
        最大值: {data.max():.3f}
        平均值: {data.mean():.3f}
        标准差: {data.std():.3f}
        非零体素: {np.count_nonzero(data)}
        """
        axes[1, 1].text(0.1, 0.5, stats_text, transform=axes[1, 1].transAxes,
                        fontsize=12, verticalalignment='center',
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))
        axes[1, 1].set_title('统计信息', fontweight='bold')
        axes[1, 1].axis('off')
        
        # 3D体积渲染预览
        axes[1, 2].text(0.5, 0.5, '3D体积信息\n将在单独图中显示', 
                        ha='center', va='center', transform=axes[1, 2].transAxes,
                        fontsize=12, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))
        axes[1, 2].set_title('3D可视化', fontweight='bold')
        axes[1, 2].axis('off')
    
    plt.tight_layout()
    plt.savefig(f'{output_prefix}_slices.png', dpi=300, bbox_inches='tight')
    print(f"保存切片图: {output_prefix}_slices.png")
    plt.close(fig)

def create_3d_volume_visualization(mri_info, output_prefix):
    """
    创建3D体积可视化
    """
    print(f"\n=== 创建3D体积可视化: {output_prefix} ===")
    
    data = mri_info['data']
    
    # 如果是掩码数据，创建3D掩码可视化
    if len(mri_info['unique_values']) <= 10:
        # 创建掩码的3D轮廓
        from mpl_toolkits.mplot3d import Axes3D
        
        fig = plt.figure(figsize=(15, 5))
        
        # 找到掩码的边界
        mask = data > 0
        coords = np.where(mask)
        
        if len(coords[0]) > 0:
            # 子图1: 3D散点图
            ax1 = fig.add_subplot(131, projection='3d')
            
            # 为了性能，只显示部分点
            step = max(1, len(coords[0]) // 5000)  # 最多显示5000个点
            
            ax1.scatter(coords[0][::step], coords[1][::step], coords[2][::step], 
                       c='red', alpha=0.6, s=1)
            ax1.set_title('3D掩码分布', fontweight='bold')
            ax1.set_xlabel('X轴')
            ax1.set_ylabel('Y轴')
            ax1.set_zlabel('Z轴')
            
            # 子图2: 投影到XY平面
            ax2 = fig.add_subplot(132)
            xy_projection = np.sum(mask, axis=2)
            im2 = ax2.imshow(xy_projection.T, cmap='Reds', origin='lower')
            ax2.set_title('XY平面投影', fontweight='bold')
            ax2.set_xlabel('X轴')
            ax2.set_ylabel('Y轴')
            plt.colorbar(im2, ax=ax2, shrink=0.8)
            
            # 子图3: 投影到XZ平面
            ax3 = fig.add_subplot(133)
            xz_projection = np.sum(mask, axis=1)
            im3 = ax3.imshow(xz_projection.T, cmap='Reds', origin='lower')
            ax3.set_title('XZ平面投影', fontweight='bold')
            ax3.set_xlabel('X轴')
            ax3.set_ylabel('Z轴')
            plt.colorbar(im3, ax=ax3, shrink=0.8)
        
        plt.tight_layout()
        plt.savefig(f'{output_prefix}_3d.png', dpi=300, bbox_inches='tight')
        print(f"保存3D图: {output_prefix}_3d.png")
        plt.close(fig)

def compare_masks(mask1_info, mask2_info):
    """
    比较两个掩码
    """
    print(f"\n=== 比较两个掩码 ===")
    
    data1 = mask1_info['data']
    data2 = mask2_info['data']
    
    # 确保数据形状相同
    if data1.shape != data2.shape:
        print(f"警告: 数据形状不同 - {data1.shape} vs {data2.shape}")
        return
    
    # 创建二值掩码
    mask1 = data1 > 0
    mask2 = data2 > 0
    
    # 计算重叠和差异
    intersection = mask1 & mask2
    union = mask1 | mask2
    only_mask1 = mask1 & ~mask2
    only_mask2 = mask2 & ~mask1
    
    # 计算统计信息
    dice_coefficient = 2 * np.sum(intersection) / (np.sum(mask1) + np.sum(mask2))
    jaccard_index = np.sum(intersection) / np.sum(union)
    
    print(f"掩码1体素数: {np.sum(mask1)}")
    print(f"掩码2体素数: {np.sum(mask2)}")
    print(f"重叠体素数: {np.sum(intersection)}")
    print(f"Dice系数: {dice_coefficient:.4f}")
    print(f"Jaccard指数: {jaccard_index:.4f}")
    
    # 创建比较可视化
    center_z = data1.shape[2] // 2
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('掩码比较分析', fontsize=16, fontweight='bold')
    
    # 原始掩码
    axes[0, 0].imshow(mask1[:, :, center_z].T, cmap='Reds', origin='lower', alpha=0.7)
    axes[0, 0].set_title('掩码1 (MaskInOrig)', fontweight='bold')
    
    axes[0, 1].imshow(mask2[:, :, center_z].T, cmap='Blues', origin='lower', alpha=0.7)
    axes[0, 1].set_title('掩码2 (MaskInRawData)', fontweight='bold')
    
    # 重叠显示
    overlap_img = np.zeros((*mask1[:, :, center_z].shape, 3))
    overlap_img[mask1[:, :, center_z], 0] = 1  # 红色 - 掩码1
    overlap_img[mask2[:, :, center_z], 2] = 1  # 蓝色 - 掩码2
    overlap_img[intersection[:, :, center_z], 1] = 1  # 绿色 - 重叠
    
    axes[0, 2].imshow(overlap_img.transpose(1, 0, 2), origin='lower')
    axes[0, 2].set_title('重叠分析\n红:掩码1, 蓝:掩码2, 紫:重叠', fontweight='bold')
    
    # 差异分析
    axes[1, 0].imshow(only_mask1[:, :, center_z].T, cmap='Reds', origin='lower')
    axes[1, 0].set_title('仅掩码1', fontweight='bold')
    
    axes[1, 1].imshow(only_mask2[:, :, center_z].T, cmap='Blues', origin='lower')
    axes[1, 1].set_title('仅掩码2', fontweight='bold')
    
    # 统计信息
    stats_text = f"""
    掩码1体素数: {np.sum(mask1):,}
    掩码2体素数: {np.sum(mask2):,}
    重叠体素数: {np.sum(intersection):,}
    
    Dice系数: {dice_coefficient:.4f}
    Jaccard指数: {jaccard_index:.4f}
    
    重叠率: {np.sum(intersection)/np.sum(union)*100:.1f}%
    """
    
    axes[1, 2].text(0.1, 0.5, stats_text, transform=axes[1, 2].transAxes,
                    fontsize=12, verticalalignment='center',
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))
    axes[1, 2].set_title('统计信息', fontweight='bold')
    axes[1, 2].axis('off')
    
    plt.tight_layout()
    plt.savefig('mask_comparison.png', dpi=300, bbox_inches='tight')
    print("保存掩码比较图: mask_comparison.png")
    plt.close(fig)
    
    return {
        'dice_coefficient': dice_coefficient,
        'jaccard_index': jaccard_index,
        'mask1_voxels': np.sum(mask1),
        'mask2_voxels': np.sum(mask2),
        'intersection_voxels': np.sum(intersection)
    }

def main():
    """
    主分析函数
    """
    print("=== MRI掩码数据分析 ===")
    
    # 文件路径
    file1 = "1/1_MaskInOrig.nii.gz"
    file2 = "1/1_MaskInRawData.nii.gz"
    
    # 检查文件是否存在
    if not os.path.exists(file1):
        print(f"错误: 文件 {file1} 不存在!")
        return
    if not os.path.exists(file2):
        print(f"错误: 文件 {file2} 不存在!")
        return
    
    # 创建输出目录
    os.makedirs('mri_analysis', exist_ok=True)
    
    # 分析第一个文件
    mask1_info = load_and_analyze_mri(file1)
    if mask1_info:
        create_mri_slices_visualization(mask1_info, 'mri_analysis/mask_orig')
        create_3d_volume_visualization(mask1_info, 'mri_analysis/mask_orig')
    
    print("\n" + "="*60)
    
    # 分析第二个文件
    mask2_info = load_and_analyze_mri(file2)
    if mask2_info:
        create_mri_slices_visualization(mask2_info, 'mri_analysis/mask_rawdata')
        create_3d_volume_visualization(mask2_info, 'mri_analysis/mask_rawdata')
    
    print("\n" + "="*60)
    
    # 比较两个掩码
    if mask1_info and mask2_info:
        comparison_stats = compare_masks(mask1_info, mask2_info)

        print(f"\n=== 分析完成 ===")
        print("生成的文件:")
        print("- mri_analysis/mask_orig_slices.png: 原始掩码切片")
        print("- mri_analysis/mask_orig_3d.png: 原始掩码3D可视化")
        print("- mri_analysis/mask_rawdata_slices.png: 原始数据掩码切片")
        print("- mri_analysis/mask_rawdata_3d.png: 原始数据掩码3D可视化")

        if comparison_stats:
            print("- mask_comparison.png: 掩码比较分析")
            print(f"\n=== 总结 ===")
            print(f"两个掩码的相似性分析:")
            print(f"- Dice系数: {comparison_stats['dice_coefficient']:.4f} (1.0为完全相同)")
            print(f"- Jaccard指数: {comparison_stats['jaccard_index']:.4f}")
            print(f"- 掩码1体素数: {comparison_stats['mask1_voxels']:,}")
            print(f"- 掩码2体素数: {comparison_stats['mask2_voxels']:,}")
        else:
            print(f"\n=== 总结 ===")
            print("由于数据形状不同，无法进行直接比较")
            print(f"- 掩码1 (MaskInOrig): {mask1_info['data'].shape}, {np.sum(mask1_info['data'] > 0):,} 体素")
            print(f"- 掩码2 (MaskInRawData): {mask2_info['data'].shape}, {np.sum(mask2_info['data'] > 0):,} 体素")

if __name__ == "__main__":
    main()
