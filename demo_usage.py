#!/usr/bin/env python3
"""
Demonstration of EEG-Lesion Pairing System Usage
Shows how to use the generated training dataset for model development
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import pickle
import json
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def load_training_dataset(dataset_dir="eeg_lesion_training_dataset"):
    """Load the generated training dataset"""
    dataset_path = Path(dataset_dir)
    
    print("Loading EEG-Lesion Training Dataset...")
    print("="*50)
    
    # Load splits
    splits = {}
    for split_name in ['train', 'validation', 'test']:
        split_file = dataset_path / f"{split_name}_pairings.pkl"
        if split_file.exists():
            with open(split_file, 'rb') as f:
                splits[split_name] = pickle.load(f)
            print(f"✓ Loaded {len(splits[split_name])} {split_name} pairings")
        else:
            print(f"✗ {split_file} not found")
    
    # Load metadata
    metadata_file = dataset_path / "dataset_metadata.json"
    if metadata_file.exists():
        with open(metadata_file, 'r') as f:
            metadata = json.load(f)
        print(f"✓ Loaded dataset metadata")
    else:
        metadata = {}
        print(f"✗ Metadata file not found")
    
    return splits, metadata

def demonstrate_data_structure(splits):
    """Demonstrate the structure of the training data"""
    print("\n" + "="*50)
    print("DATA STRUCTURE DEMONSTRATION")
    print("="*50)
    
    # Get a sample pairing
    sample_pairing = splits['train'][0]
    
    print("Sample EEG-Lesion Pairing Structure:")
    print("-" * 30)
    
    # Basic information
    print(f"EEG Subject ID: {sample_pairing['eeg_subject_id']}")
    print(f"EEG Dataset: {sample_pairing['eeg_dataset']}")
    print(f"EEG Group: {sample_pairing['eeg_group']}")
    print(f"Lesion ID: {sample_pairing['lesion_id']}")
    print(f"Pairing Rank: {sample_pairing['pairing_rank']}")
    print(f"Compatibility Score: {sample_pairing['compatibility_score']:.3f}")
    
    # EEG features
    eeg_features = sample_pairing['eeg_features']
    print(f"\nEEG Features: {len(eeg_features)} total")
    print("Sample EEG features:")
    feature_samples = list(eeg_features.items())[:5]
    for feature_name, feature_value in feature_samples:
        print(f"  {feature_name}: {feature_value:.3f}")
    print("  ...")
    
    # Lesion features
    lesion_features = sample_pairing['lesion_features']
    print(f"\nLesion Features:")
    print(f"  Volume: {lesion_features['volume_mm3']:.1f} mm³")
    print(f"  Region: {lesion_features['region']}")
    print(f"  Laterality: {lesion_features['laterality']}")
    print(f"  Centroid: ({lesion_features['centroid_x']:.1f}, {lesion_features['centroid_y']:.1f}, {lesion_features['centroid_z']:.1f})")

def extract_features_and_targets(pairings):
    """Extract features and targets for machine learning"""
    print("\n" + "="*50)
    print("FEATURE EXTRACTION FOR ML")
    print("="*50)
    
    # Extract EEG features (X)
    eeg_feature_vectors = []
    lesion_targets = []
    compatibility_scores = []
    
    for pairing in pairings:
        # EEG features as input
        eeg_features = pairing['eeg_features']
        feature_vector = list(eeg_features.values())
        eeg_feature_vectors.append(feature_vector)
        
        # Lesion location as target
        lesion_features = pairing['lesion_features']
        lesion_target = [
            lesion_features['centroid_x'],
            lesion_features['centroid_y'], 
            lesion_features['centroid_z'],
            lesion_features['volume_mm3']
        ]
        lesion_targets.append(lesion_target)
        
        # Compatibility score for quality weighting
        compatibility_scores.append(pairing['compatibility_score'])
    
    X = np.array(eeg_feature_vectors)
    y = np.array(lesion_targets)
    weights = np.array(compatibility_scores)
    
    print(f"EEG Feature Matrix (X): {X.shape}")
    print(f"Lesion Target Matrix (y): {y.shape}")
    print(f"Compatibility Weights: {weights.shape}")
    print(f"Feature range: [{X.min():.2f}, {X.max():.2f}]")
    print(f"Target range: [{y.min():.2f}, {y.max():.2f}]")
    
    return X, y, weights

def demonstrate_ml_pipeline(splits):
    """Demonstrate a simple machine learning pipeline"""
    print("\n" + "="*50)
    print("MACHINE LEARNING PIPELINE DEMO")
    print("="*50)
    
    try:
        from sklearn.ensemble import RandomForestRegressor
        from sklearn.preprocessing import StandardScaler
        from sklearn.metrics import mean_squared_error, r2_score
        
        # Extract features and targets
        X_train, y_train, w_train = extract_features_and_targets(splits['train'])
        X_val, y_val, w_val = extract_features_and_targets(splits['validation'])
        
        # Standardize features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_val_scaled = scaler.transform(X_val)
        
        print("Training Random Forest model...")
        
        # Train model for lesion centroid prediction (first 3 targets)
        model = RandomForestRegressor(n_estimators=100, random_state=42)
        model.fit(X_train_scaled, y_train[:, :3], sample_weight=w_train)
        
        # Make predictions
        y_pred = model.predict(X_val_scaled)
        
        # Evaluate performance
        mse = mean_squared_error(y_val[:, :3], y_pred)
        r2 = r2_score(y_val[:, :3], y_pred)
        
        print(f"✓ Model trained successfully")
        print(f"Validation MSE: {mse:.2f}")
        print(f"Validation R²: {r2:.3f}")
        
        # Feature importance
        feature_names = list(splits['train'][0]['eeg_features'].keys())
        importances = model.feature_importances_
        top_features = sorted(zip(feature_names, importances), key=lambda x: x[1], reverse=True)[:10]
        
        print("\nTop 10 Most Important Features:")
        for i, (feature, importance) in enumerate(top_features, 1):
            print(f"  {i:2d}. {feature}: {importance:.4f}")
        
        return model, scaler
        
    except ImportError:
        print("Scikit-learn not available for ML demonstration")
        return None, None

def visualize_dataset_overview(splits, metadata):
    """Create overview visualizations"""
    print("\n" + "="*50)
    print("DATASET VISUALIZATION")
    print("="*50)
    
    # Combine all pairings
    all_pairings = []
    for split_name, pairings in splits.items():
        for pairing in pairings:
            pairing['split'] = split_name
            all_pairings.append(pairing)
    
    # Create visualizations
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # 1. Compatibility score distribution
    scores = [p['compatibility_score'] for p in all_pairings]
    axes[0, 0].hist(scores, bins=20, alpha=0.7, edgecolor='black')
    axes[0, 0].set_title('Compatibility Score Distribution')
    axes[0, 0].set_xlabel('Compatibility Score')
    axes[0, 0].set_ylabel('Frequency')
    
    # 2. Group distribution
    groups = [p['eeg_group'] for p in all_pairings]
    group_counts = pd.Series(groups).value_counts()
    axes[0, 1].pie(group_counts.values, labels=group_counts.index, autopct='%1.1f%%')
    axes[0, 1].set_title('EEG Group Distribution')
    
    # 3. Split distribution
    splits_list = [p['split'] for p in all_pairings]
    split_counts = pd.Series(splits_list).value_counts()
    axes[1, 0].bar(split_counts.index, split_counts.values)
    axes[1, 0].set_title('Dataset Split Distribution')
    axes[1, 0].set_ylabel('Number of Pairings')
    
    # 4. Lesion volume distribution
    volumes = [p['lesion_features']['volume_mm3'] for p in all_pairings]
    axes[1, 1].hist(volumes, bins=20, alpha=0.7, edgecolor='black')
    axes[1, 1].set_title('Lesion Volume Distribution')
    axes[1, 1].set_xlabel('Volume (mm³)')
    axes[1, 1].set_ylabel('Frequency')
    axes[1, 1].set_yscale('log')
    
    plt.tight_layout()
    
    # Save visualization
    output_file = "dataset_overview.png"
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"✓ Dataset overview saved as: {output_file}")
    
    plt.show()

def main():
    """Main demonstration function"""
    print("EEG-Lesion Training Dataset Usage Demonstration")
    print("="*60)
    
    # Load dataset
    splits, metadata = load_training_dataset()
    
    if not splits:
        print("No dataset found. Please run generate_training_dataset.py first.")
        return
    
    # Demonstrate data structure
    demonstrate_data_structure(splits)
    
    # Demonstrate ML pipeline
    model, scaler = demonstrate_ml_pipeline(splits)
    
    # Create visualizations
    visualize_dataset_overview(splits, metadata)
    
    # Summary
    print("\n" + "="*60)
    print("DEMONSTRATION SUMMARY")
    print("="*60)
    print("✓ Successfully loaded EEG-lesion training dataset")
    print("✓ Demonstrated data structure and feature extraction")
    if model is not None:
        print("✓ Trained example machine learning model")
    print("✓ Created dataset overview visualizations")
    print("\nThe dataset is ready for:")
    print("  • Epilepsy lesion localization model training")
    print("  • Cross-modal EEG-MRI analysis")
    print("  • Clinical decision support system development")
    print("  • Neuroscience research applications")
    
    print(f"\nDataset Statistics:")
    total_pairings = sum(len(pairings) for pairings in splits.values())
    print(f"  Total pairings: {total_pairings}")
    print(f"  Training: {len(splits.get('train', []))}")
    print(f"  Validation: {len(splits.get('validation', []))}")
    print(f"  Test: {len(splits.get('test', []))}")

if __name__ == "__main__":
    main()
