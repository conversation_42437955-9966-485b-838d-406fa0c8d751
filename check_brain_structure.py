#!/usr/bin/env python3
"""
检查MRI数据是否包含原始脑部结构
"""

import numpy as np
import matplotlib.pyplot as plt
import nibabel as nib
import os

def analyze_data_characteristics():
    """
    详细分析数据特征，判断是否为脑部结构数据
    """
    print("=== 分析数据特征 ===")
    
    # 加载数据
    img1 = nib.load("1/1_MaskInOrig.nii.gz")
    img2 = nib.load("1/1_MaskInRawData.nii.gz")
    
    data1 = img1.get_fdata()
    data2 = img2.get_fdata()
    
    print(f"\n=== 数据1 (MaskInOrig) 特征分析 ===")
    print(f"数据范围: {data1.min():.6f} - {data1.max():.6f}")
    print(f"数据类型: {data1.dtype}")
    print(f"唯一值数量: {len(np.unique(data1))}")
    print(f"唯一值前20个: {np.unique(data1)[:20]}")
    print(f"非零体素比例: {np.count_nonzero(data1)/data1.size*100:.2f}%")
    
    # 检查是否为二值掩码
    unique_vals1 = np.unique(data1)
    is_binary1 = len(unique_vals1) == 2 and 0 in unique_vals1 and 1 in unique_vals1
    print(f"是否为标准二值掩码: {is_binary1}")
    
    # 统计信息
    print(f"平均值: {data1.mean():.6f}")
    print(f"标准差: {data1.std():.6f}")
    print(f"中位数: {np.median(data1):.6f}")
    
    print(f"\n=== 数据2 (MaskInRawData) 特征分析 ===")
    print(f"数据范围: {data2.min():.6f} - {data2.max():.6f}")
    print(f"数据类型: {data2.dtype}")
    print(f"唯一值数量: {len(np.unique(data2))}")
    print(f"唯一值前20个: {np.unique(data2)[:20]}")
    print(f"非零体素比例: {np.count_nonzero(data2)/data2.size*100:.2f}%")
    
    # 检查是否为二值掩码
    unique_vals2 = np.unique(data2)
    is_binary2 = len(unique_vals2) == 2 and 0 in unique_vals2 and 1 in unique_vals2
    print(f"是否为标准二值掩码: {is_binary2}")
    
    # 统计信息
    print(f"平均值: {data2.mean():.6f}")
    print(f"标准差: {data2.std():.6f}")
    print(f"中位数: {np.median(data2):.6f}")
    
    return data1, data2, unique_vals1, unique_vals2

def check_brain_structure_patterns(data1, data2):
    """
    检查是否符合脑部结构数据的模式
    """
    print(f"\n=== 脑部结构模式检查 ===")
    
    # 检查数据1
    print(f"\n数据1 (MaskInOrig) 分析:")
    
    # 脑部结构通常有连续的灰度值
    if len(np.unique(data1)) > 100:
        print("✓ 包含多种灰度值，可能是结构数据")
    else:
        print("✗ 灰度值种类少，更像掩码数据")
    
    # 脑部结构通常有较高的非零比例
    nonzero_ratio1 = np.count_nonzero(data1) / data1.size * 100
    if nonzero_ratio1 > 20:
        print(f"✓ 非零比例高 ({nonzero_ratio1:.1f}%)，符合脑部结构")
    else:
        print(f"✗ 非零比例低 ({nonzero_ratio1:.1f}%)，更像掩码")
    
    # 检查强度分布
    if data1.max() > 10:
        print(f"✓ 最大值较高 ({data1.max():.1f})，可能是MRI强度")
    else:
        print(f"✗ 最大值较低 ({data1.max():.1f})，更像掩码")
    
    # 检查数据2
    print(f"\n数据2 (MaskInRawData) 分析:")
    
    if len(np.unique(data2)) > 100:
        print("✓ 包含多种灰度值，可能是结构数据")
    else:
        print("✗ 灰度值种类少，更像掩码数据")
    
    nonzero_ratio2 = np.count_nonzero(data2) / data2.size * 100
    if nonzero_ratio2 > 20:
        print(f"✓ 非零比例高 ({nonzero_ratio2:.1f}%)，符合脑部结构")
    else:
        print(f"✗ 非零比例低 ({nonzero_ratio2:.1f}%)，更像掩码")
    
    if data2.max() > 10:
        print(f"✓ 最大值较高 ({data2.max():.1f})，可能是MRI强度")
    else:
        print(f"✗ 最大值较低 ({data2.max():.1f})，更像掩码")

def create_intensity_analysis():
    """
    创建强度分布分析
    """
    print(f"\n=== 创建强度分布分析 ===")
    
    # 加载数据
    img1 = nib.load("1/1_MaskInOrig.nii.gz")
    img2 = nib.load("1/1_MaskInRawData.nii.gz")
    
    data1 = img1.get_fdata()
    data2 = img2.get_fdata()
    
    fig, axes = plt.subplots(2, 4, figsize=(20, 10))
    fig.suptitle('MRI数据强度分布分析', fontsize=16, fontweight='bold')
    
    # 数据1分析
    # 直方图
    axes[0, 0].hist(data1.flatten(), bins=50, alpha=0.7, color='red')
    axes[0, 0].set_title('MaskInOrig - 全部值分布')
    axes[0, 0].set_xlabel('强度值')
    axes[0, 0].set_ylabel('频次')
    axes[0, 0].set_yscale('log')
    
    # 非零值直方图
    nonzero_data1 = data1[data1 > 0]
    if len(nonzero_data1) > 0:
        axes[0, 1].hist(nonzero_data1, bins=50, alpha=0.7, color='red')
        axes[0, 1].set_title('MaskInOrig - 非零值分布')
        axes[0, 1].set_xlabel('强度值')
        axes[0, 1].set_ylabel('频次')
    
    # 中心切片
    center_z1 = data1.shape[2] // 2
    slice1 = data1[:, :, center_z1]
    im1 = axes[0, 2].imshow(slice1.T, cmap='gray', origin='lower')
    axes[0, 2].set_title('MaskInOrig - 中心切片')
    plt.colorbar(im1, ax=axes[0, 2], shrink=0.8)
    
    # 统计信息
    stats_text1 = f"""
    数据范围: {data1.min():.3f} - {data1.max():.3f}
    平均值: {data1.mean():.3f}
    标准差: {data1.std():.3f}
    非零比例: {np.count_nonzero(data1)/data1.size*100:.1f}%
    唯一值数: {len(np.unique(data1))}
    """
    axes[0, 3].text(0.1, 0.5, stats_text1, transform=axes[0, 3].transAxes,
                    fontsize=11, verticalalignment='center',
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="lightcoral"))
    axes[0, 3].set_title('MaskInOrig - 统计')
    axes[0, 3].axis('off')
    
    # 数据2分析
    # 直方图
    axes[1, 0].hist(data2.flatten(), bins=50, alpha=0.7, color='blue')
    axes[1, 0].set_title('MaskInRawData - 全部值分布')
    axes[1, 0].set_xlabel('强度值')
    axes[1, 0].set_ylabel('频次')
    axes[1, 0].set_yscale('log')
    
    # 非零值直方图
    nonzero_data2 = data2[data2 > 0]
    if len(nonzero_data2) > 0:
        axes[1, 1].hist(nonzero_data2, bins=50, alpha=0.7, color='blue')
        axes[1, 1].set_title('MaskInRawData - 非零值分布')
        axes[1, 1].set_xlabel('强度值')
        axes[1, 1].set_ylabel('频次')
    
    # 中心切片
    center_z2 = data2.shape[2] // 2
    slice2 = data2[:, :, center_z2]
    im2 = axes[1, 2].imshow(slice2.T, cmap='gray', origin='lower')
    axes[1, 2].set_title('MaskInRawData - 中心切片')
    plt.colorbar(im2, ax=axes[1, 2], shrink=0.8)
    
    # 统计信息
    stats_text2 = f"""
    数据范围: {data2.min():.3f} - {data2.max():.3f}
    平均值: {data2.mean():.3f}
    标准差: {data2.std():.3f}
    非零比例: {np.count_nonzero(data2)/data2.size*100:.1f}%
    唯一值数: {len(np.unique(data2))}
    """
    axes[1, 3].text(0.1, 0.5, stats_text2, transform=axes[1, 3].transAxes,
                    fontsize=11, verticalalignment='center',
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))
    axes[1, 3].set_title('MaskInRawData - 统计')
    axes[1, 3].axis('off')
    
    plt.tight_layout()
    plt.savefig('intensity_distribution_analysis.png', dpi=300, bbox_inches='tight')
    print("保存强度分布图: intensity_distribution_analysis.png")
    plt.close(fig)

def check_brain_anatomy_patterns(data1, data2):
    """
    检查是否符合脑部解剖结构模式
    """
    print(f"\n=== 脑部解剖结构模式检查 ===")
    
    # 检查是否有典型的脑部结构模式
    print(f"\n检查数据1 (MaskInOrig):")
    
    # 检查边缘模式 - 脑部通常有清晰的边界
    center_z1 = data1.shape[2] // 2
    slice1 = data1[:, :, center_z1]
    
    # 计算梯度来检测边缘
    grad_x = np.gradient(slice1, axis=0)
    grad_y = np.gradient(slice1, axis=1)
    gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
    
    edge_ratio1 = np.sum(gradient_magnitude > 0.1) / gradient_magnitude.size
    print(f"边缘比例: {edge_ratio1*100:.1f}%")
    
    if edge_ratio1 > 0.1:
        print("✓ 有明显边缘结构，可能是脑部图像")
    else:
        print("✗ 边缘结构少，更像掩码")
    
    print(f"\n检查数据2 (MaskInRawData):")
    
    center_z2 = data2.shape[2] // 2
    slice2 = data2[:, :, center_z2]
    
    grad_x2 = np.gradient(slice2, axis=0)
    grad_y2 = np.gradient(slice2, axis=1)
    gradient_magnitude2 = np.sqrt(grad_x2**2 + grad_y2**2)
    
    edge_ratio2 = np.sum(gradient_magnitude2 > 0.1) / gradient_magnitude2.size
    print(f"边缘比例: {edge_ratio2*100:.1f}%")
    
    if edge_ratio2 > 0.1:
        print("✓ 有明显边缘结构，可能是脑部图像")
    else:
        print("✗ 边缘结构少，更像掩码")

def final_assessment():
    """
    最终评估
    """
    print(f"\n=== 最终评估 ===")
    
    # 重新加载数据进行最终检查
    img1 = nib.load("1/1_MaskInOrig.nii.gz")
    img2 = nib.load("1/1_MaskInRawData.nii.gz")
    
    data1 = img1.get_fdata()
    data2 = img2.get_fdata()
    
    # 评估数据1
    print(f"\nMaskInOrig 评估:")
    unique_vals1 = np.unique(data1)
    
    if len(unique_vals1) == 2 and 0 in unique_vals1 and 1 in unique_vals1:
        print("🎯 结论: 标准二值掩码")
        data1_type = "二值掩码"
    elif len(unique_vals1) < 10:
        print("🎯 结论: 多值掩码或标签图")
        data1_type = "多值掩码"
    else:
        print("🎯 结论: 可能是脑部结构数据")
        data1_type = "结构数据"
    
    # 评估数据2
    print(f"\nMaskInRawData 评估:")
    unique_vals2 = np.unique(data2)
    
    if len(unique_vals2) == 2 and 0 in unique_vals2 and 1 in unique_vals2:
        print("🎯 结论: 标准二值掩码")
        data2_type = "二值掩码"
    elif len(unique_vals2) < 10:
        print("🎯 结论: 多值掩码或标签图")
        data2_type = "多值掩码"
    else:
        print("🎯 结论: 可能是脑部结构数据")
        data2_type = "结构数据"
    
    print(f"\n=== 总结 ===")
    print(f"MaskInOrig: {data1_type}")
    print(f"MaskInRawData: {data2_type}")
    
    if data1_type == "二值掩码" and data2_type == "二值掩码":
        print("\n💡 两个文件都是掩码，但代表不同的区域")
    elif data1_type != data2_type:
        print(f"\n💡 两个文件类型不同，一个是{data1_type}，一个是{data2_type}")
    else:
        print(f"\n💡 两个文件都是{data1_type}")

def main():
    """
    主分析函数
    """
    print("=== 检查MRI数据是否包含原始脑部结构 ===")
    
    # 分析数据特征
    data1, data2, unique_vals1, unique_vals2 = analyze_data_characteristics()
    
    # 检查脑部结构模式
    check_brain_structure_patterns(data1, data2)
    
    # 创建强度分析
    create_intensity_analysis()
    
    # 检查解剖结构模式
    check_brain_anatomy_patterns(data1, data2)
    
    # 最终评估
    final_assessment()

if __name__ == "__main__":
    main()
