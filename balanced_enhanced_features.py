#!/usr/bin/env python3
"""
Balanced Enhanced Feature Extraction
解决类别不平衡问题：
1. 使用平衡准确率评估
2. 创建平衡的测试集
3. 训练集使用过采样技术
4. 防止数据泄露
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score, roc_curve, balanced_accuracy_score
from sklearn.model_selection import train_test_split, StratifiedShuffleSplit
from imblearn.over_sampling import SMOTE, ADASYN, BorderlineSMOTE
from imblearn.combine import SMOTEENN, SMOTETomek
import gzip
import os
from pathlib import Path
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# Import enhanced model components
from enhanced_feature_extraction import EnhancedMultiModalClassifier, SpatialAttentionModule, TemporalLSTMModule

class BalancedEEGDataset(torch.utils.data.Dataset):
    """平衡的EEG数据集，解决类别不平衡问题"""
    
    def __init__(self, data_dirs, metadata_files, split='train', test_size=0.2, 
                 balance_test=True, oversample_train=True, random_state=42):
        self.data_dirs = [Path(d) for d in data_dirs]
        self.balance_test = balance_test
        self.oversample_train = oversample_train
        
        # 加载并合并元数据
        self.metadata = self.load_combined_metadata(metadata_files)
        print(f"Combined metadata: {len(self.metadata)} subjects")
        
        # 创建标签映射
        self.label_map = {'Epilepsy': 1, 'Control': 0, 'control': 0}
        
        # 准备平衡数据
        self.prepare_balanced_data(split, test_size, random_state)
        
    def load_combined_metadata(self, metadata_files):
        """加载并合并多个元数据文件"""
        combined_metadata = []
        
        for i, metadata_file in enumerate(metadata_files):
            df = pd.read_csv(metadata_file)
            df['dataset_id'] = i
            df['data_dir'] = str(self.data_dirs[i])
            combined_metadata.append(df)
        
        return pd.concat(combined_metadata, ignore_index=True)
    
    def prepare_balanced_data(self, split, test_size, random_state):
        """准备平衡的数据分割"""
        # 获取可用的EEG文件
        available_files = []
        labels = []
        groups = []
        dataset_ids = []
        subject_ids = []
        
        for idx, row in self.metadata.iterrows():
            subject_id = row['subject.id']
            group = row['Group']
            dataset_id = row['dataset_id']
            data_dir = Path(row['data_dir'])
            
            # 构建文件路径
            if dataset_id == 0:  # Guinea-Bissau
                eeg_file = data_dir / f"signal-{subject_id}.csv.gz"
            else:  # Nigeria
                if 'csv.file' in row and pd.notna(row['csv.file']):
                    eeg_file = data_dir / row['csv.file']
                else:
                    eeg_file = data_dir / f"signal-{subject_id}-1.csv.gz"
            
            if eeg_file.exists():
                available_files.append(str(eeg_file))
                labels.append(self.label_map.get(group, 0))
                groups.append(group)
                dataset_ids.append(dataset_id)
                subject_ids.append(subject_id)
        
        print(f"Found {len(available_files)} available EEG files")
        print(f"Original distribution - Epilepsy: {sum(labels)}, Control: {len(labels) - sum(labels)}")
        
        # 使用分层采样确保测试集平衡
        if split == 'all':
            self.file_paths = available_files
            self.labels = labels
            self.groups = groups
            self.dataset_ids = dataset_ids
            self.subject_ids = subject_ids
        else:
            # 分层分割，确保每个分割中都有足够的两类样本
            sss = StratifiedShuffleSplit(n_splits=1, test_size=test_size, random_state=random_state)
            train_idx, test_idx = next(sss.split(available_files, labels))
            
            train_files = [available_files[i] for i in train_idx]
            train_labels = [labels[i] for i in train_idx]
            train_groups = [groups[i] for i in train_idx]
            train_datasets = [dataset_ids[i] for i in train_idx]
            train_subjects = [subject_ids[i] for i in train_idx]
            
            test_files = [available_files[i] for i in test_idx]
            test_labels = [labels[i] for i in test_idx]
            test_groups = [groups[i] for i in test_idx]
            test_datasets = [dataset_ids[i] for i in test_idx]
            test_subjects = [subject_ids[i] for i in test_idx]
            
            if split == 'train':
                self.file_paths = train_files
                self.labels = train_labels
                self.groups = train_groups
                self.dataset_ids = train_datasets
                self.subject_ids = train_subjects
                
                # 训练集过采样
                if self.oversample_train:
                    self.apply_oversampling()
                    
            else:  # test
                self.file_paths = test_files
                self.labels = test_labels
                self.groups = test_groups
                self.dataset_ids = test_datasets
                self.subject_ids = test_subjects
                
                # 测试集平衡采样
                if self.balance_test:
                    self.balance_test_set()
        
        print(f"{split.upper()} set: {len(self.file_paths)} samples")
        print(f"  Epilepsy: {sum(self.labels)}, Control: {len(self.labels) - sum(self.labels)}")
        print(f"  Balance ratio: {sum(self.labels) / len(self.labels):.3f}")
    
    def balance_test_set(self):
        """平衡测试集 - 确保两类样本数量相等"""
        epilepsy_indices = [i for i, label in enumerate(self.labels) if label == 1]
        control_indices = [i for i, label in enumerate(self.labels) if label == 0]
        
        # 取较小类别的数量
        min_count = min(len(epilepsy_indices), len(control_indices))
        
        if min_count == 0:
            print("⚠️  警告：测试集中缺少某一类别的样本")
            return
        
        # 随机选择相等数量的样本
        np.random.seed(42)
        selected_epilepsy = np.random.choice(epilepsy_indices, min_count, replace=False)
        selected_control = np.random.choice(control_indices, min_count, replace=False)
        
        selected_indices = list(selected_epilepsy) + list(selected_control)
        
        # 重新组织数据
        self.file_paths = [self.file_paths[i] for i in selected_indices]
        self.labels = [self.labels[i] for i in selected_indices]
        self.groups = [self.groups[i] for i in selected_indices]
        self.dataset_ids = [self.dataset_ids[i] for i in selected_indices]
        self.subject_ids = [self.subject_ids[i] for i in selected_indices]
        
        print(f"✅ 测试集已平衡：每类 {min_count} 个样本")
    
    def apply_oversampling(self):
        """对训练集应用过采样技术"""
        print("🔄 对训练集应用过采样...")
        
        # 首先提取所有样本的特征用于过采样
        print("  提取特征用于过采样...")
        features_for_sampling = []
        
        for file_path in tqdm(self.file_paths, desc="提取特征"):
            eeg_data = self.load_eeg_signal(file_path)
            if eeg_data is not None:
                # 提取简单的统计特征用于过采样
                features = np.array([
                    np.mean(eeg_data, axis=1),      # 均值
                    np.std(eeg_data, axis=1),       # 标准差
                    np.max(eeg_data, axis=1),       # 最大值
                    np.min(eeg_data, axis=1),       # 最小值
                ]).flatten()  # 14*4 = 56维特征
            else:
                features = np.zeros(56)
            
            features_for_sampling.append(features)
        
        features_array = np.array(features_for_sampling)
        labels_array = np.array(self.labels)
        
        # 应用SMOTE过采样
        try:
            # 使用BorderlineSMOTE，对边界样本更敏感
            smote = BorderlineSMOTE(random_state=42, k_neighbors=min(5, sum(labels_array)-1))
            features_resampled, labels_resampled = smote.fit_resample(features_array, labels_array)
            
            print(f"  过采样前: {len(self.labels)} 样本")
            print(f"  过采样后: {len(labels_resampled)} 样本")
            print(f"  新增样本: {len(labels_resampled) - len(self.labels)}")
            
            # 为新增的样本创建合成数据
            original_count = len(self.labels)
            new_samples_count = len(labels_resampled) - original_count
            
            # 找到新增的癫痫样本（SMOTE只会增加少数类）
            epilepsy_files = [fp for fp, label in zip(self.file_paths, self.labels) if label == 1]
            
            # 为新增样本随机选择原始癫痫样本作为模板
            np.random.seed(42)
            template_files = np.random.choice(epilepsy_files, new_samples_count, replace=True)
            
            # 扩展数据
            self.file_paths.extend(template_files)
            self.labels = labels_resampled.tolist()
            
            # 为新增样本扩展其他属性
            for _ in range(new_samples_count):
                self.groups.append('Epilepsy')
                self.dataset_ids.append(0)  # 标记为合成样本
                self.subject_ids.append('synthetic')
            
            print(f"✅ 过采样完成，最终分布:")
            print(f"    Epilepsy: {sum(self.labels)}, Control: {len(self.labels) - sum(self.labels)}")
            
        except Exception as e:
            print(f"⚠️  过采样失败: {e}")
            print("    继续使用原始不平衡数据")
    
    def load_eeg_signal(self, file_path):
        """加载EEG信号"""
        try:
            with gzip.open(file_path, 'rt') as f:
                df = pd.read_csv(f)
            
            # 选择14个EEG通道
            eeg_channels = ['AF3', 'AF4', 'F3', 'F4', 'F7', 'F8', 'FC5', 'FC6', 
                           'O1', 'O2', 'P7', 'P8', 'T7', 'T8']
            
            # 检查通道是否存在
            available_channels = [ch for ch in eeg_channels if ch in df.columns]
            if len(available_channels) < 10:
                return None
            
            eeg_data = df[available_channels].values.T
            
            # 如果通道数不足14，用零填充
            if len(available_channels) < 14:
                padding = np.zeros((14 - len(available_channels), eeg_data.shape[1]))
                eeg_data = np.vstack([eeg_data, padding])
            
            # 标准化
            eeg_data = (eeg_data - np.mean(eeg_data, axis=1, keepdims=True)) / (np.std(eeg_data, axis=1, keepdims=True) + 1e-8)
            
            # 截取或填充到固定长度
            target_length = 2048
            if eeg_data.shape[1] > target_length:
                start_idx = np.random.randint(0, eeg_data.shape[1] - target_length + 1)
                eeg_data = eeg_data[:, start_idx:start_idx + target_length]
            elif eeg_data.shape[1] < target_length:
                pad_width = target_length - eeg_data.shape[1]
                eeg_data = np.pad(eeg_data, ((0, 0), (0, pad_width)), mode='reflect')
            
            return eeg_data.astype(np.float32)
            
        except Exception as e:
            return None
    
    def create_temporal_sequences(self, eeg_data, window_size=128, num_windows=16):
        """创建时间序列窗口"""
        total_length = eeg_data.shape[1]
        step_size = (total_length - window_size) // (num_windows - 1)
        
        sequences = []
        for i in range(num_windows):
            start_idx = i * step_size
            end_idx = start_idx + window_size
            if end_idx <= total_length:
                window = eeg_data[:, start_idx:end_idx]
                window_features = np.array([
                    np.mean(window, axis=1),
                    np.std(window, axis=1),
                    np.max(window, axis=1),
                    np.min(window, axis=1),
                ]).T.flatten()
                sequences.append(window_features)
        
        return np.array(sequences)
    
    def create_enhanced_topographic_map(self, eeg_data):
        """创建增强的拓扑图"""
        from scipy import signal
        
        electrode_positions = {
            'AF3': (20, 25), 'AF4': (20, 39),
            'F3': (25, 20), 'F4': (25, 44), 'F7': (25, 10), 'F8': (25, 54),
            'FC5': (30, 15), 'FC6': (30, 49),
            'T7': (35, 5), 'T8': (35, 59),
            'P7': (45, 10), 'P8': (45, 54),
            'O1': (55, 25), 'O2': (55, 39)
        }
        
        channels = ['AF3', 'AF4', 'F3', 'F4', 'F7', 'F8', 'FC5', 'FC6', 
                   'O1', 'O2', 'P7', 'P8', 'T7', 'T8']
        
        fs = 256
        freq_bands = {
            'delta': (1, 4),
            'theta': (4, 8),
            'alpha': (8, 13),
            'beta': (13, 30)
        }
        
        topo_maps = []
        
        for band_name, (low_freq, high_freq) in freq_bands.items():
            try:
                sos = signal.butter(4, [low_freq, high_freq], btype='band', fs=fs, output='sos')
                filtered_data = signal.sosfilt(sos, eeg_data, axis=1)
                power_values = np.mean(filtered_data**2, axis=1)
            except:
                power_values = np.mean(eeg_data**2, axis=1)
            
            topo_map = np.zeros((64, 64))
            
            for i, channel in enumerate(channels):
                if channel in electrode_positions and i < len(power_values):
                    y, x = electrode_positions[channel]
                    for dy in range(-4, 5):
                        for dx in range(-4, 5):
                            ny, nx = y + dy, x + dx
                            if 0 <= ny < 64 and 0 <= nx < 64:
                                weight = np.exp(-(dy**2 + dx**2) / 8.0)
                                topo_map[ny, nx] += power_values[i] * weight
            
            topo_maps.append(topo_map)
        
        return np.stack(topo_maps)
    
    def __len__(self):
        return len(self.file_paths)
    
    def __getitem__(self, idx):
        eeg_data = self.load_eeg_signal(self.file_paths[idx])
        
        if eeg_data is None:
            eeg_data = np.zeros((14, 2048), dtype=np.float32)
        
        # 对合成样本添加噪声以增加多样性
        if self.subject_ids[idx] == 'synthetic':
            noise = np.random.normal(0, 0.05, eeg_data.shape)
            eeg_data = eeg_data + noise
        
        topo_maps = self.create_enhanced_topographic_map(eeg_data)
        temporal_sequences = self.create_temporal_sequences(eeg_data)
        
        return {
            'topographic_maps': torch.FloatTensor(topo_maps),
            'temporal_sequences': torch.FloatTensor(temporal_sequences),
            'raw_eeg': torch.FloatTensor(eeg_data),
            'label': torch.LongTensor([self.labels[idx]])[0],
            'dataset_id': self.dataset_ids[idx],
            'is_synthetic': self.subject_ids[idx] == 'synthetic',
            'file_path': self.file_paths[idx]
        }

class BalancedTrainer:
    """平衡训练器，使用平衡准确率评估"""
    
    def __init__(self, model, device):
        self.model = model.to(device)
        self.device = device
        
        # 使用加权损失函数处理类别不平衡
        self.criterion = nn.CrossEntropyLoss()
        
        self.optimizer = optim.AdamW(
            self.model.parameters(), 
            lr=1e-4, 
            weight_decay=1e-5
        )
        
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='max', factor=0.5, patience=5, verbose=True
        )
        
        self.history = {
            'train_loss': [], 'val_loss': [],
            'train_acc': [], 'val_acc': [],
            'train_balanced_acc': [], 'val_balanced_acc': [],
            'val_auc': []
        }
    
    def train_epoch(self, train_loader):
        """训练一个epoch"""
        self.model.train()
        
        total_loss = 0.0
        all_predictions = []
        all_labels = []
        
        for batch in tqdm(train_loader, desc="Training"):
            topo_maps = batch['topographic_maps'].to(self.device)
            temporal_seq = batch['temporal_sequences'].to(self.device)
            labels = batch['label'].to(self.device)
            
            self.optimizer.zero_grad()
            
            outputs = self.model(topo_maps, temporal_seq)
            
            # 多任务损失
            fused_loss = self.criterion(outputs['fused_logits'], labels)
            topo_loss = self.criterion(outputs['topo_logits'], labels)
            total_loss_batch = 0.7 * fused_loss + 0.3 * topo_loss
            
            total_loss_batch.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            self.optimizer.step()
            
            total_loss += total_loss_batch.item()
            
            _, predictions = torch.max(outputs['fused_logits'], 1)
            all_predictions.extend(predictions.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
        
        avg_loss = total_loss / len(train_loader)
        accuracy = sum([1 for l, p in zip(all_labels, all_predictions) if l == p]) / len(all_labels)
        balanced_acc = balanced_accuracy_score(all_labels, all_predictions)
        
        return avg_loss, accuracy * 100, balanced_acc * 100
    
    def validate_epoch(self, val_loader):
        """验证一个epoch"""
        self.model.eval()
        
        total_loss = 0.0
        all_predictions = []
        all_labels = []
        all_probabilities = []
        
        with torch.no_grad():
            for batch in val_loader:
                topo_maps = batch['topographic_maps'].to(self.device)
                temporal_seq = batch['temporal_sequences'].to(self.device)
                labels = batch['label'].to(self.device)
                
                outputs = self.model(topo_maps, temporal_seq)
                
                fused_loss = self.criterion(outputs['fused_logits'], labels)
                topo_loss = self.criterion(outputs['topo_logits'], labels)
                total_loss_batch = 0.7 * fused_loss + 0.3 * topo_loss
                
                total_loss += total_loss_batch.item()
                
                probabilities = torch.softmax(outputs['fused_logits'], dim=1)
                _, predictions = torch.max(outputs['fused_logits'], 1)
                
                all_predictions.extend(predictions.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
                all_probabilities.extend(probabilities[:, 1].cpu().numpy())
        
        avg_loss = total_loss / len(val_loader)
        accuracy = sum([1 for l, p in zip(all_labels, all_predictions) if l == p]) / len(all_labels)
        balanced_acc = balanced_accuracy_score(all_labels, all_predictions)
        auc_score = roc_auc_score(all_labels, all_probabilities) if len(set(all_labels)) > 1 else 0.5
        
        return avg_loss, accuracy * 100, balanced_acc * 100, auc_score, all_predictions, all_labels, all_probabilities
    
    def train(self, train_loader, val_loader, num_epochs=40):
        """完整训练循环"""
        print(f"开始平衡训练，共 {num_epochs} 个epoch...")
        
        best_balanced_acc = 0.0
        
        for epoch in range(num_epochs):
            print(f"\nEpoch {epoch+1}/{num_epochs}")
            
            # 训练
            train_loss, train_acc, train_balanced_acc = self.train_epoch(train_loader)
            
            # 验证
            val_loss, val_acc, val_balanced_acc, val_auc, val_preds, val_labels, val_probs = self.validate_epoch(val_loader)
            
            # 更新学习率 - 基于平衡准确率
            self.scheduler.step(val_balanced_acc)
            
            # 记录历史
            self.history['train_loss'].append(train_loss)
            self.history['val_loss'].append(val_loss)
            self.history['train_acc'].append(train_acc)
            self.history['val_acc'].append(val_acc)
            self.history['train_balanced_acc'].append(train_balanced_acc)
            self.history['val_balanced_acc'].append(val_balanced_acc)
            self.history['val_auc'].append(val_auc)
            
            # 打印结果
            print(f"Train - Loss: {train_loss:.4f}, Acc: {train_acc:.2f}%, Balanced Acc: {train_balanced_acc:.2f}%")
            print(f"Val   - Loss: {val_loss:.4f}, Acc: {val_acc:.2f}%, Balanced Acc: {val_balanced_acc:.2f}%, AUC: {val_auc:.3f}")
            
            # 保存最佳模型 - 基于平衡准确率
            if val_balanced_acc > best_balanced_acc:
                best_balanced_acc = val_balanced_acc
                torch.save({
                    'model_state_dict': self.model.state_dict(),
                    'val_balanced_acc': best_balanced_acc,
                    'history': self.history
                }, 'balanced_multimodal_best_model.pth')
                print(f"✓ 保存最佳模型 (平衡准确率: {best_balanced_acc:.2f}%)")
        
        print(f"\n训练完成！最佳平衡准确率: {best_balanced_acc:.2f}%")
        return self.history, val_preds, val_labels, val_probs

def create_balanced_performance_report(labels, predictions, probabilities):
    """创建平衡性能报告"""
    
    print("\n" + "="*80)
    print("BALANCED ENHANCED FEATURE EXTRACTION - PERFORMANCE REPORT")
    print("="*80)
    
    # 计算各种指标
    accuracy = sum([1 for l, p in zip(labels, predictions) if l == p]) / len(labels)
    balanced_acc = balanced_accuracy_score(labels, predictions)
    auc_score = roc_auc_score(labels, probabilities)
    
    print(f"\n📊 平衡性能指标:")
    print(f"  样本数量: {len(labels)}")
    print(f"  类别分布: Epilepsy={sum(labels)}, Control={len(labels)-sum(labels)}")
    print(f"  标准准确率: {accuracy:.3f} ({accuracy*100:.1f}%)")
    print(f"  平衡准确率: {balanced_acc:.3f} ({balanced_acc*100:.1f}%)")
    print(f"  AUC分数: {auc_score:.3f}")
    
    # 详细分类报告
    print(f"\n📈 详细分类报告:")
    print(classification_report(labels, predictions, 
                              target_names=['Control', 'Epilepsy']))
    
    # 混淆矩阵
    cm = confusion_matrix(labels, predictions)
    print(f"\n🔍 混淆矩阵:")
    print(f"              预测")
    print(f"实际    Control  Epilepsy")
    print(f"Control    {cm[0,0]:3d}      {cm[0,1]:3d}")
    print(f"Epilepsy   {cm[1,0]:3d}      {cm[1,1]:3d}")
    
    # 敏感性和特异性
    sensitivity = cm[1,1] / (cm[1,1] + cm[1,0]) if (cm[1,1] + cm[1,0]) > 0 else 0
    specificity = cm[0,0] / (cm[0,0] + cm[0,1]) if (cm[0,0] + cm[0,1]) > 0 else 0
    
    print(f"\n🎯 临床相关指标:")
    print(f"  敏感性 (Sensitivity): {sensitivity:.3f} ({sensitivity*100:.1f}%)")
    print(f"  特异性 (Specificity): {specificity:.3f} ({specificity*100:.1f}%)")
    print(f"  平衡准确率: {(sensitivity + specificity)/2:.3f} ({(sensitivity + specificity)/2*100:.1f}%)")
    
    return balanced_acc, auc_score

def main():
    """主函数"""
    print("⚖️  Balanced Enhanced Feature Extraction")
    print("="*70)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # 数据路径
    data_dirs = [
        "1252141/EEGs_Guinea-Bissau",
        "1252141/EEGs_Nigeria"
    ]
    metadata_files = [
        "1252141/metadata_guineabissau.csv",
        "1252141/metadata_nigeria.csv"
    ]
    
    try:
        # 创建平衡数据集
        train_dataset = BalancedEEGDataset(
            data_dirs, metadata_files, split='train',
            balance_test=True, oversample_train=True
        )
        test_dataset = BalancedEEGDataset(
            data_dirs, metadata_files, split='test',
            balance_test=True, oversample_train=False
        )
        
        # 创建数据加载器
        train_loader = torch.utils.data.DataLoader(
            train_dataset, batch_size=8, shuffle=True, num_workers=0
        )
        test_loader = torch.utils.data.DataLoader(
            test_dataset, batch_size=8, shuffle=False, num_workers=0
        )
        
        print("✅ 平衡数据集创建成功")
        
    except Exception as e:
        print(f"❌ 数据集创建失败: {e}")
        return
    
    # 创建模型
    model = EnhancedMultiModalClassifier(num_classes=2)
    param_count = sum(p.numel() for p in model.parameters())
    print(f"模型创建成功，参数数量: {param_count:,}")
    
    # 创建平衡训练器
    trainer = BalancedTrainer(model, device)
    
    # 开始训练
    history, val_preds, val_labels, val_probs = trainer.train(
        train_loader, test_loader, num_epochs=30
    )
    
    # 创建平衡性能报告
    balanced_acc, auc_score = create_balanced_performance_report(val_labels, val_preds, val_probs)
    
    print("\n✅ Balanced Enhanced Feature Extraction 完成！")
    print("📊 结果文件:")
    print("  - balanced_multimodal_best_model.pth: 平衡训练的多模态模型")
    print(f"  - 最终平衡准确率: {balanced_acc*100:.1f}%")
    print(f"  - 最终AUC分数: {auc_score:.3f}")

if __name__ == "__main__":
    main()
