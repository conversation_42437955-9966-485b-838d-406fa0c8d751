#!/usr/bin/env python3
"""
Improved Epilepsy Detection
解决准确率问题的改进方案：
1. 多尺度时频特征提取
2. 动态连接性分析
3. 临床信息融合
4. 集成学习策略
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score, balanced_accuracy_score
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
import gzip
from pathlib import Path
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

class ImprovedEEGDataset(torch.utils.data.Dataset):
    """改进的EEG数据集，融合临床信息"""
    
    def __init__(self, data_dirs, metadata_files, split='train', test_size=0.25, random_state=42):
        self.data_dirs = [Path(d) for d in data_dirs]
        self.split = split
        
        # 加载数据并提取临床特征
        self.load_data_with_clinical_features(metadata_files, test_size, random_state)
        
    def load_data_with_clinical_features(self, metadata_files, test_size, random_state):
        """加载数据并提取临床特征"""
        print("📊 加载数据并提取临床特征...")
        
        all_samples = []
        
        # Guinea-Bissau数据集
        gb_metadata = pd.read_csv(metadata_files[0])
        for idx, row in gb_metadata.iterrows():
            subject_id = row['subject.id']
            group = row['Group']
            eeg_file = self.data_dirs[0] / f"signal-{subject_id}.csv.gz"
            
            if eeg_file.exists():
                clinical_features = self.extract_clinical_features_gb(row)
                all_samples.append({
                    'file_path': str(eeg_file),
                    'label': 1 if group == 'Epilepsy' else 0,
                    'group': group,
                    'dataset': 'Guinea-Bissau',
                    'subject_id': subject_id,
                    'clinical_features': clinical_features
                })
        
        # Nigeria数据集 - 包含丰富的临床信息
        ng_metadata = pd.read_csv(metadata_files[1])
        for idx, row in ng_metadata.iterrows():
            subject_id = row['subject.id']
            group = row['Group']
            
            if 'csv.file' in row and pd.notna(row['csv.file']):
                eeg_file = self.data_dirs[1] / row['csv.file']
            else:
                eeg_file = self.data_dirs[1] / f"signal-{subject_id}-1.csv.gz"
            
            if eeg_file.exists():
                clinical_features = self.extract_clinical_features_ng(row)
                all_samples.append({
                    'file_path': str(eeg_file),
                    'label': 1 if group.lower() == 'epilepsy' else 0,
                    'group': 'Epilepsy' if group.lower() == 'epilepsy' else 'Control',
                    'dataset': 'Nigeria',
                    'subject_id': subject_id,
                    'clinical_features': clinical_features
                })
        
        # 统计
        labels = [s['label'] for s in all_samples]
        epilepsy_count = sum(labels)
        control_count = len(labels) - epilepsy_count
        
        print(f"✅ 数据加载完成:")
        print(f"  总样本: {len(all_samples)} (癫痫: {epilepsy_count}, 对照: {control_count})")
        
        # 分割数据
        train_idx, test_idx = train_test_split(
            range(len(all_samples)), test_size=test_size, 
            random_state=random_state, stratify=labels
        )
        
        if self.split == 'train':
            self.samples = [all_samples[i] for i in train_idx]
        else:
            self.samples = [all_samples[i] for i in test_idx]
        
        # 平衡测试集
        if self.split == 'test':
            self.balance_test_set()
        
        final_labels = [s['label'] for s in self.samples]
        print(f"{self.split.upper()}集: {len(self.samples)} 样本 (癫痫: {sum(final_labels)}, 对照: {len(final_labels)-sum(final_labels)})")
    
    def extract_clinical_features_gb(self, row):
        """提取Guinea-Bissau的临床特征"""
        features = {
            'eyes_condition': 1 if 'closed' in str(row.get('Eyes.condition', '')).lower() else 0,
            'recording_period': float(row.get('recordedPeriod', 300)) / 300.0,  # 标准化
            'has_remarks': 1 if pd.notna(row.get('Remarks')) and str(row.get('Remarks')).lower() != 'na' else 0,
            'dataset_source': 0  # Guinea-Bissau = 0
        }
        return features
    
    def extract_clinical_features_ng(self, row):
        """提取Nigeria的临床特征"""
        remarks = str(row.get('remarks', '')).lower()
        
        # 提取癫痫相关的临床特征
        features = {
            'eyes_condition': 1 if row.get('first_condition', '') == 'closed' else 0,
            'recording_period': float(row.get('recordedPeriod', 268)) / 300.0,  # 标准化
            'has_medication': 1 if 'carbamazepin' in remarks or 'medicijn' in remarks else 0,
            'recent_seizure': self.extract_seizure_recency(remarks),
            'seizure_frequency': self.extract_seizure_frequency(remarks),
            'treatment_status': self.extract_treatment_status(remarks),
            'dataset_source': 1  # Nigeria = 1
        }
        return features
    
    def extract_seizure_recency(self, remarks):
        """提取发作时间近期性"""
        if 'convulsion' not in remarks:
            return 0  # 无发作信息
        
        if any(word in remarks for word in ['now', 'today', 'yesterday', 'days ago']):
            return 3  # 非常近期
        elif any(word in remarks for word in ['this month', 'last month', 'weeks ago']):
            return 2  # 近期
        elif any(word in remarks for word in ['months ago', 'year ago', 'years ago']):
            return 1  # 较远
        else:
            return 0  # 未知
    
    def extract_seizure_frequency(self, remarks):
        """提取发作频率"""
        if 'still convulsion' in remarks or 'every day' in remarks:
            return 3  # 高频
        elif 'per month' in remarks or 'monthly' in remarks:
            return 2  # 中频
        elif 'no convulsion' in remarks or 'stopped' in remarks:
            return 0  # 无发作
        else:
            return 1  # 低频/未知
    
    def extract_treatment_status(self, remarks):
        """提取治疗状态"""
        if 'no treatment' in remarks or 'stopped' in remarks:
            return 0  # 无治疗
        elif 'treatment' in remarks or 'medicijn' in remarks:
            return 1  # 有治疗
        else:
            return 0.5  # 未知
    
    def balance_test_set(self):
        """平衡测试集"""
        epilepsy_samples = [s for s in self.samples if s['label'] == 1]
        control_samples = [s for s in self.samples if s['label'] == 0]
        
        min_count = min(len(epilepsy_samples), len(control_samples))
        
        if min_count > 0:
            np.random.seed(42)
            selected_epilepsy = np.random.choice(len(epilepsy_samples), min_count, replace=False)
            selected_control = np.random.choice(len(control_samples), min_count, replace=False)
            
            balanced_samples = []
            balanced_samples.extend([epilepsy_samples[i] for i in selected_epilepsy])
            balanced_samples.extend([control_samples[i] for i in selected_control])
            
            self.samples = balanced_samples
            print(f"  ✅ 测试集已平衡：每类 {min_count} 个样本")
    
    def load_eeg_signal(self, file_path):
        """加载EEG信号"""
        try:
            with gzip.open(file_path, 'rt') as f:
                df = pd.read_csv(f)
            
            eeg_channels = ['AF3', 'AF4', 'F3', 'F4', 'F7', 'F8', 'FC5', 'FC6', 
                           'O1', 'O2', 'P7', 'P8', 'T7', 'T8']
            
            available_channels = [ch for ch in eeg_channels if ch in df.columns]
            if len(available_channels) < 10:
                return None
            
            eeg_data = df[available_channels].values.T
            
            if len(available_channels) < 14:
                padding = np.zeros((14 - len(available_channels), eeg_data.shape[1]))
                eeg_data = np.vstack([eeg_data, padding])
            
            # 标准化
            eeg_data = (eeg_data - np.mean(eeg_data, axis=1, keepdims=True)) / (np.std(eeg_data, axis=1, keepdims=True) + 1e-8)
            
            # 固定长度
            target_length = 1280  # 5秒@256Hz
            if eeg_data.shape[1] > target_length:
                start_idx = np.random.randint(0, eeg_data.shape[1] - target_length + 1)
                eeg_data = eeg_data[:, start_idx:start_idx + target_length]
            elif eeg_data.shape[1] < target_length:
                pad_width = target_length - eeg_data.shape[1]
                eeg_data = np.pad(eeg_data, ((0, 0), (0, pad_width)), mode='reflect')
            
            return eeg_data.astype(np.float32)
            
        except Exception as e:
            return None
    
    def extract_advanced_features(self, eeg_data):
        """提取高级EEG特征"""
        features = {}
        
        # 1. 多频段功率特征
        from scipy import signal
        fs = 256
        
        freq_bands = {
            'delta': (1, 4), 'theta': (4, 8), 'alpha': (8, 13), 
            'beta': (13, 30), 'gamma': (30, 50)
        }
        
        band_powers = []
        for band_name, (low_freq, high_freq) in freq_bands.items():
            try:
                sos = signal.butter(4, [low_freq, high_freq], btype='band', fs=fs, output='sos')
                filtered_data = signal.sosfilt(sos, eeg_data, axis=1)
                power = np.mean(filtered_data**2, axis=1)
                band_powers.extend(power)
            except:
                band_powers.extend(np.zeros(14))
        
        features['band_powers'] = np.array(band_powers)  # 70维
        
        # 2. 统计特征
        stats_features = []
        for ch in range(14):
            ch_data = eeg_data[ch]
            stats_features.extend([
                np.mean(ch_data), np.std(ch_data), np.var(ch_data),
                np.max(ch_data), np.min(ch_data), np.median(ch_data),
                np.percentile(ch_data, 25), np.percentile(ch_data, 75),
                np.sum(np.abs(np.diff(ch_data))),  # 总变异
                len(np.where(np.diff(np.sign(np.diff(ch_data))))[0])  # 峰值数量
            ])
        
        features['stats'] = np.array(stats_features)  # 140维
        
        # 3. 连接性特征
        correlation_matrix = np.corrcoef(eeg_data)
        # 取上三角矩阵
        triu_indices = np.triu_indices(14, k=1)
        features['connectivity'] = correlation_matrix[triu_indices]  # 91维
        
        # 4. 非线性特征
        nonlinear_features = []
        for ch in range(14):
            ch_data = eeg_data[ch]
            # 近似熵
            try:
                approx_entropy = self.approximate_entropy(ch_data, m=2, r=0.2*np.std(ch_data))
                nonlinear_features.append(approx_entropy)
            except:
                nonlinear_features.append(0)
        
        features['nonlinear'] = np.array(nonlinear_features)  # 14维
        
        return features
    
    def approximate_entropy(self, data, m, r):
        """计算近似熵"""
        N = len(data)
        
        def _maxdist(xi, xj, m):
            return max([abs(ua - va) for ua, va in zip(xi, xj)])
        
        def _phi(m):
            patterns = np.array([data[i:i+m] for i in range(N - m + 1)])
            C = np.zeros(N - m + 1)
            
            for i in range(N - m + 1):
                template = patterns[i]
                matches = sum([1 for j in range(N - m + 1) 
                              if _maxdist(template, patterns[j], m) <= r])
                C[i] = matches / float(N - m + 1)
            
            phi = np.mean(np.log(C))
            return phi
        
        return _phi(m) - _phi(m + 1)
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        sample = self.samples[idx]
        eeg_data = self.load_eeg_signal(sample['file_path'])
        
        if eeg_data is None:
            eeg_data = np.zeros((14, 1280), dtype=np.float32)
        
        # 提取高级特征
        eeg_features = self.extract_advanced_features(eeg_data)
        
        # 临床特征
        clinical_features = list(sample['clinical_features'].values())
        
        return {
            'eeg_features': eeg_features,
            'clinical_features': torch.FloatTensor(clinical_features),
            'raw_eeg': torch.FloatTensor(eeg_data),
            'label': torch.LongTensor([sample['label']])[0],
            'dataset': sample['dataset'],
            'file_path': sample['file_path']
        }

class MultiModalEpilepsyClassifier:
    """多模态癫痫分类器"""
    
    def __init__(self, device):
        self.device = device
        self.models = {}
        self.scalers = {}
        
    def prepare_features(self, dataset):
        """准备特征矩阵"""
        print("🔄 准备特征矩阵...")
        
        all_features = []
        all_labels = []
        
        for i in tqdm(range(len(dataset)), desc="提取特征"):
            sample = dataset[i]
            
            # 合并所有特征
            features = []
            features.extend(sample['eeg_features']['band_powers'])      # 70维
            features.extend(sample['eeg_features']['stats'])            # 140维
            features.extend(sample['eeg_features']['connectivity'])     # 91维
            features.extend(sample['eeg_features']['nonlinear'])        # 14维
            features.extend(sample['clinical_features'].numpy())        # 临床特征
            
            all_features.append(features)
            all_labels.append(sample['label'].item())
        
        return np.array(all_features), np.array(all_labels)
    
    def train_ensemble(self, train_dataset, val_dataset):
        """训练集成模型"""
        print("🚀 训练多模态集成分类器...")
        
        # 准备训练数据
        X_train, y_train = self.prepare_features(train_dataset)
        X_val, y_val = self.prepare_features(val_dataset)
        
        print(f"特征维度: {X_train.shape[1]}")
        
        # 特征标准化
        self.scalers['standard'] = StandardScaler()
        X_train_scaled = self.scalers['standard'].fit_transform(X_train)
        X_val_scaled = self.scalers['standard'].transform(X_val)
        
        # 训练多个模型
        models_config = {
            'rf': RandomForestClassifier(n_estimators=200, max_depth=10, random_state=42, class_weight='balanced'),
            'rf_deep': RandomForestClassifier(n_estimators=300, max_depth=15, random_state=43, class_weight='balanced'),
            'rf_wide': RandomForestClassifier(n_estimators=500, max_depth=8, random_state=44, class_weight='balanced')
        }
        
        results = {}
        
        for name, model in models_config.items():
            print(f"\n训练 {name} 模型...")
            
            # 训练
            model.fit(X_train_scaled, y_train)
            
            # 验证
            val_pred = model.predict(X_val_scaled)
            val_prob = model.predict_proba(X_val_scaled)[:, 1]
            
            # 计算指标
            accuracy = sum(val_pred == y_val) / len(y_val)
            balanced_acc = balanced_accuracy_score(y_val, val_pred)
            auc_score = roc_auc_score(y_val, val_prob)
            
            results[name] = {
                'model': model,
                'accuracy': accuracy,
                'balanced_accuracy': balanced_acc,
                'auc': auc_score,
                'predictions': val_pred,
                'probabilities': val_prob
            }
            
            print(f"  准确率: {accuracy:.3f}")
            print(f"  平衡准确率: {balanced_acc:.3f}")
            print(f"  AUC: {auc_score:.3f}")
        
        # 集成预测
        ensemble_probs = np.mean([results[name]['probabilities'] for name in results], axis=0)
        ensemble_pred = (ensemble_probs > 0.5).astype(int)
        
        ensemble_accuracy = sum(ensemble_pred == y_val) / len(y_val)
        ensemble_balanced_acc = balanced_accuracy_score(y_val, ensemble_pred)
        ensemble_auc = roc_auc_score(y_val, ensemble_probs)
        
        print(f"\n🎯 集成模型结果:")
        print(f"  准确率: {ensemble_accuracy:.3f}")
        print(f"  平衡准确率: {ensemble_balanced_acc:.3f}")
        print(f"  AUC: {ensemble_auc:.3f}")
        
        # 保存最佳模型
        self.models = {name: result['model'] for name, result in results.items()}
        
        return {
            'individual_results': results,
            'ensemble_accuracy': ensemble_accuracy,
            'ensemble_balanced_acc': ensemble_balanced_acc,
            'ensemble_auc': ensemble_auc,
            'ensemble_predictions': ensemble_pred,
            'ensemble_probabilities': ensemble_probs,
            'true_labels': y_val
        }

def create_comprehensive_report(results):
    """创建综合报告"""
    
    print("\n" + "="*80)
    print("IMPROVED EPILEPSY DETECTION - COMPREHENSIVE REPORT")
    print("="*80)
    
    y_true = results['true_labels']
    y_pred = results['ensemble_predictions']
    y_prob = results['ensemble_probabilities']
    
    print(f"\n📊 集成模型最终性能:")
    print(f"  测试样本数: {len(y_true)}")
    print(f"  类别分布: 癫痫={sum(y_true)}, 对照={len(y_true)-sum(y_true)}")
    print(f"  准确率: {results['ensemble_accuracy']:.3f} ({results['ensemble_accuracy']*100:.1f}%)")
    print(f"  平衡准确率: {results['ensemble_balanced_acc']:.3f} ({results['ensemble_balanced_acc']*100:.1f}%)")
    print(f"  AUC分数: {results['ensemble_auc']:.3f}")
    
    print(f"\n📈 详细分类报告:")
    print(classification_report(y_true, y_pred, target_names=['Control', 'Epilepsy']))
    
    cm = confusion_matrix(y_true, y_pred)
    sensitivity = cm[1,1] / (cm[1,1] + cm[1,0]) if (cm[1,1] + cm[1,0]) > 0 else 0
    specificity = cm[0,0] / (cm[0,0] + cm[0,1]) if (cm[0,0] + cm[0,1]) > 0 else 0
    
    print(f"\n🎯 临床指标:")
    print(f"  敏感性: {sensitivity:.3f} ({sensitivity*100:.1f}%)")
    print(f"  特异性: {specificity:.3f} ({specificity*100:.1f}%)")
    
    print(f"\n🔍 个体模型对比:")
    for name, result in results['individual_results'].items():
        print(f"  {name}: 平衡准确率 {result['balanced_accuracy']:.3f}, AUC {result['auc']:.3f}")
    
    print(f"\n💡 改进效果分析:")
    if results['ensemble_balanced_acc'] > 0.8:
        print("  ✅ 优秀 - 多模态特征融合显著提升了性能")
    elif results['ensemble_balanced_acc'] > 0.7:
        print("  ✅ 良好 - 特征工程和集成学习有效")
    elif results['ensemble_balanced_acc'] > 0.6:
        print("  ⚠️  一般 - 仍有改进空间")
    else:
        print("  ❌ 需要进一步优化")
    
    return results['ensemble_balanced_acc'], results['ensemble_auc']

def main():
    print("🚀 Improved Epilepsy Detection")
    print("="*70)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    data_dirs = [
        "1252141/EEGs_Guinea-Bissau",
        "1252141/EEGs_Nigeria"
    ]
    metadata_files = [
        "1252141/metadata_guineabissau.csv",
        "1252141/metadata_nigeria.csv"
    ]
    
    try:
        train_dataset = ImprovedEEGDataset(data_dirs, metadata_files, split='train')
        test_dataset = ImprovedEEGDataset(data_dirs, metadata_files, split='test')
        
        print("✅ 改进的数据集创建成功")
        
    except Exception as e:
        print(f"❌ 数据集创建失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 创建多模态分类器
    classifier = MultiModalEpilepsyClassifier(device)
    
    # 训练集成模型
    results = classifier.train_ensemble(train_dataset, test_dataset)
    
    # 创建综合报告
    balanced_acc, auc_score = create_comprehensive_report(results)
    
    print(f"\n🎉 Improved Epilepsy Detection 完成！")
    print(f"📊 最终结果:")
    print(f"  - 平衡准确率: {balanced_acc*100:.1f}%")
    print(f"  - AUC分数: {auc_score:.3f}")

if __name__ == "__main__":
    main()
