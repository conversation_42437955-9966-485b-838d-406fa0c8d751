#!/usr/bin/env python3
"""
32通道EEG地形图生成脚本
正确识别和处理32通道EEG数据
"""

import numpy as np
import matplotlib.pyplot as plt
import mne
from mne.viz import plot_topomap
import os
import warnings

warnings.filterwarnings('ignore')
mne.set_log_level('ERROR')

def identify_eeg_channels(raw):
    """
    正确识别32通道EEG系统中的所有EEG通道
    """
    print("=== 识别EEG通道 ===")
    
    # 明确标记为EEG的通道
    eeg_channels = [ch for ch in raw.ch_names if ch.startswith('EEG')]
    print(f"标记为EEG的通道: {len(eeg_channels)}个")
    
    # 检查其他可能的EEG通道
    potential_eeg = []
    data = raw.get_data()
    
    for i, ch_name in enumerate(raw.ch_names):
        if not ch_name.startswith('EEG'):
            ch_data = data[i, :]
            std_val = np.std(ch_data)
            mean_val = np.mean(ch_data)
            
            # 判断是否为EEG信号的标准
            is_eeg_like = (std_val > 1e-6 and std_val < 1e-2 and 
                          abs(mean_val) < 1e-2 and 
                          ch_name not in ['SPO2', 'HR', 'MK'])
            
            if is_eeg_like:
                potential_eeg.append(ch_name)
                print(f"发现潜在EEG通道: {ch_name} (std={std_val:.2e})")
    
    # 合并所有EEG通道
    all_eeg_channels = eeg_channels + potential_eeg
    print(f"总EEG通道数: {len(all_eeg_channels)}")
    
    return all_eeg_channels

def create_channel_mapping(eeg_channels):
    """
    创建通道名称映射，将文件中的通道名映射到标准10-20系统
    """
    print("\n=== 创建通道映射 ===")
    
    # 标准32通道EEG系统（基于国际10-20系统）
    standard_32_channels = [
        'Fp1', 'Fp2', 'F7', 'F3', 'Fz', 'F4', 'F8', 'FC5', 'FC1', 'FC2', 'FC6',
        'T7', 'C3', 'Cz', 'C4', 'T8', 'CP5', 'CP1', 'CP2', 'CP6', 'P7', 'P3', 
        'Pz', 'P4', 'P8', 'O1', 'Oz', 'O2', 'AF7', 'AF8', 'F9', 'F10'
    ]
    
    channel_mapping = {}
    
    for ch in eeg_channels:
        if ch.startswith('EEG '):
            # 移除'EEG '前缀
            clean_name = ch.replace('EEG ', '').strip()
            
            # 处理旧命名法到新命名法的转换
            if clean_name == 'T3':
                clean_name = 'T7'
            elif clean_name == 'T4':
                clean_name = 'T8'
            elif clean_name == 'T5':
                clean_name = 'P7'
            elif clean_name == 'T6':
                clean_name = 'P8'
            
            # 确保大小写正确
            for std_ch in standard_32_channels:
                if clean_name.upper() == std_ch.upper():
                    clean_name = std_ch
                    break
            
            channel_mapping[ch] = clean_name
        else:
            # 对于通道"1"和"2"，我们需要推断它们的位置
            if ch == '1':
                # 假设通道1是Oz（枕部中央）
                channel_mapping[ch] = 'Oz'
            elif ch == '2':
                # 假设通道2是AF7或AF8中的一个
                channel_mapping[ch] = 'AF7'
            else:
                channel_mapping[ch] = ch
    
    print("通道映射:")
    for orig, mapped in channel_mapping.items():
        print(f"  {orig} -> {mapped}")
    
    return channel_mapping

def prepare_32channel_eeg(file_path):
    """
    准备32通道EEG数据
    """
    print(f"加载文件: {file_path}")
    
    # 加载数据
    raw = mne.io.read_raw_edf(file_path, preload=True, verbose=False)
    
    # 识别EEG通道
    eeg_channels = identify_eeg_channels(raw)
    
    # 只保留EEG通道
    raw.pick_channels(eeg_channels)
    
    # 设置通道类型
    raw.set_channel_types({ch: 'eeg' for ch in raw.ch_names})
    
    # 创建通道映射
    channel_mapping = create_channel_mapping(eeg_channels)
    
    # 重命名通道
    raw.rename_channels(channel_mapping)
    
    # 设置标准montage
    try:
        montage = mne.channels.make_standard_montage('standard_1020')
        raw.set_montage(montage, match_case=False, on_missing='ignore')
        print(f"成功应用标准10-20 montage")
    except Exception as e:
        print(f"设置montage时出错: {e}")
    
    print(f"最终EEG通道数: {len(raw.ch_names)}")
    print(f"通道名称: {raw.ch_names}")
    
    return raw

def create_32ch_topomap(data, info, title, save_path=None):
    """
    创建32通道EEG地形图
    """
    fig, ax = plt.subplots(figsize=(12, 10))
    
    try:
        # 创建地形图
        im, _ = plot_topomap(data, info,
                           ch_type='eeg',
                           contours=10,
                           cmap='RdBu_r',
                           axes=ax,
                           show=False,
                           sphere='auto',
                           size=10)
        
        # 设置标题
        ax.set_title(title, fontsize=18, fontweight='bold', pad=25)
        
        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax, shrink=0.8, aspect=25)
        cbar.set_label('幅度 (µV)', rotation=270, labelpad=25, fontsize=14)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
            print(f"保存: {save_path}")
            plt.close(fig)
        else:
            plt.show()
            
        return True
        
    except Exception as e:
        print(f"创建地形图时出错: {e}")
        plt.close(fig)
        return False

def create_comprehensive_analysis(raw):
    """
    创建全面的32通道EEG分析
    """
    print("\n=== 创建32通道EEG地形图分析 ===")
    
    # 创建输出目录
    os.makedirs('32ch_analysis', exist_ok=True)
    
    # 1. 基础RMS地形图
    print("1. 创建基础RMS地形图...")
    data = raw.get_data()
    rms_data = np.sqrt(np.mean(data**2, axis=1))
    create_32ch_topomap(rms_data, raw.info, 
                       '32通道EEG RMS活动地形图', 
                       '32ch_analysis/32ch_rms_topomap.png')
    
    # 2. 频段分析
    print("2. 创建频段地形图...")
    freq_bands = {
        'Delta': (0.5, 4),
        'Theta': (4, 8),
        'Alpha': (8, 13),
        'Beta': (13, 30),
        'Gamma': (30, 50)
    }
    
    for band_name, (low_freq, high_freq) in freq_bands.items():
        print(f"   处理 {band_name} 频段 ({low_freq}-{high_freq} Hz)...")
        
        # 滤波
        raw_filtered = raw.copy()
        raw_filtered.filter(low_freq, high_freq, fir_design='firwin', verbose=False)
        
        # 计算功率
        data_filtered = raw_filtered.get_data()
        power = np.sqrt(np.mean(data_filtered**2, axis=1))
        
        # 创建地形图
        title = f'32通道EEG {band_name}频段功率 ({low_freq}-{high_freq} Hz)'
        save_path = f'32ch_analysis/32ch_{band_name.lower()}_topomap.png'
        create_32ch_topomap(power, raw_filtered.info, title, save_path)
    
    # 3. 创建综合总结图
    print("3. 创建综合总结图...")
    create_summary_32ch(raw)

def create_summary_32ch(raw):
    """
    创建32通道EEG综合总结图
    """
    fig, axes = plt.subplots(2, 3, figsize=(24, 16))
    fig.suptitle('32通道EEG地形图分析总结', fontsize=24, fontweight='bold')
    
    freq_bands = [
        ('Delta', (0.5, 4)),
        ('Theta', (4, 8)),
        ('Alpha', (8, 13)),
        ('Beta', (13, 30)),
        ('Gamma', (30, 50))
    ]
    
    # 频段地形图
    for i, (band_name, (low_freq, high_freq)) in enumerate(freq_bands):
        ax = axes[i//3, i%3]
        
        # 滤波和计算功率
        raw_filtered = raw.copy()
        raw_filtered.filter(low_freq, high_freq, fir_design='firwin', verbose=False)
        data = raw_filtered.get_data()
        power = np.sqrt(np.mean(data**2, axis=1))
        
        try:
            im, _ = plot_topomap(power, raw_filtered.info,
                               ch_type='eeg',
                               contours=8,
                               cmap='RdBu_r',
                               axes=ax,
                               show=False,
                               sphere='auto')
            ax.set_title(f'{band_name}\n({low_freq}-{high_freq} Hz)', 
                        fontweight='bold', fontsize=14)
        except:
            ax.text(0.5, 0.5, f'{band_name}\n映射错误', 
                   ha='center', va='center', transform=ax.transAxes)
    
    # 整体RMS
    ax = axes[1, 2]
    data = raw.get_data()
    rms_data = np.sqrt(np.mean(data**2, axis=1))
    
    try:
        im, _ = plot_topomap(rms_data, raw.info,
                           ch_type='eeg',
                           contours=8,
                           cmap='RdBu_r',
                           axes=ax,
                           show=False,
                           sphere='auto')
        ax.set_title('整体RMS活动', fontweight='bold', fontsize=14)
    except:
        ax.text(0.5, 0.5, 'RMS活动\n映射错误', 
               ha='center', va='center', transform=ax.transAxes)
    
    plt.tight_layout()
    plt.savefig('32ch_analysis/32ch_summary.png', dpi=300, bbox_inches='tight', facecolor='white')
    print("保存: 32ch_analysis/32ch_summary.png")
    plt.close(fig)

def main():
    """
    主函数
    """
    file_path = "PN00-1.edf"
    
    if not os.path.exists(file_path):
        print(f"错误: 文件 {file_path} 不存在!")
        return
    
    print("=== 32通道EEG地形图分析 ===")
    
    # 准备数据
    raw = prepare_32channel_eeg(file_path)
    
    # 创建分析
    create_comprehensive_analysis(raw)
    
    print("\n=== 分析完成 ===")
    print("生成的文件:")
    print("- 32ch_analysis/32ch_rms_topomap.png: 基础RMS地形图")
    print("- 32ch_analysis/32ch_*_topomap.png: 各频段地形图")
    print("- 32ch_analysis/32ch_summary.png: 综合总结图")
    print(f"分析的EEG通道数: {len(raw.ch_names)}")

if __name__ == "__main__":
    main()
