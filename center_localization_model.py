#!/usr/bin/env python3
"""
简化的癫痫病灶中心定位模型
专注于准确预测病灶中心位置，不考虑边界框尺寸
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import time
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# 导入基础模块
from training_pipeline import EpilepsyLocalizationModel
from fixed_training_system import NormalizedEEGLesionDataset
from torch.utils.data import DataLoader

class CenterLocalizationModel(nn.Module):
    """简化的中心定位模型"""
    
    def __init__(self, n_channels=14, feature_dim=512):
        super().__init__()
        
        # EEG特征提取 - 简化的CNN
        self.eeg_features = nn.Sequential(
            nn.Conv1d(n_channels, 64, kernel_size=7, padding=3),
            nn.BatchNorm1d(64),
            nn.<PERSON><PERSON><PERSON>(),
            nn.MaxPool1d(2),
            
            nn.Conv1d(64, 128, kernel_size=5, padding=2),
            nn.BatchNorm1d(128),
            nn.<PERSON>L<PERSON>(),
            nn.MaxPool1d(2),
            
            nn.Conv1d(128, 256, kernel_size=3, padding=1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.AdaptiveAvgPool1d(1),
            
            nn.Flatten(),
            nn.Linear(256, feature_dim),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        # 时序特征提取 - 简化的LSTM
        self.temporal_features = nn.Sequential(
            nn.LSTM(n_channels, 128, batch_first=True, bidirectional=True),
        )
        self.temporal_fc = nn.Sequential(
            nn.Linear(256, feature_dim),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        # 特征融合
        self.fusion = nn.Sequential(
            nn.Linear(feature_dim * 2, feature_dim),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(feature_dim, feature_dim // 2),
            nn.ReLU()
        )
        
        # 中心位置预测器 - 直接回归3D坐标
        self.center_predictor = nn.Sequential(
            nn.Linear(feature_dim // 2, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, 3),  # 直接输出 (x, y, z) 坐标
            nn.Sigmoid()  # 归一化到 [0, 1]
        )
        
        # 初始化权重
        self.apply(self._init_weights)
    
    def _init_weights(self, m):
        """初始化权重"""
        if isinstance(m, nn.Linear):
            torch.nn.init.xavier_uniform_(m.weight)
            if m.bias is not None:
                torch.nn.init.zeros_(m.bias)
        elif isinstance(m, nn.Conv1d):
            torch.nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
    
    def forward(self, eeg_data, temporal_sequence):
        """前向传播"""
        # EEG特征提取
        eeg_feat = self.eeg_features(eeg_data)
        
        # 时序特征提取
        temporal_out, _ = self.temporal_features(temporal_sequence)
        temporal_feat = self.temporal_fc(temporal_out[:, -1, :])  # 使用最后一个时间步
        
        # 特征融合
        fused_features = torch.cat([eeg_feat, temporal_feat], dim=1)
        fused_features = self.fusion(fused_features)
        
        # 预测中心位置 (归一化坐标 [0, 1])
        center_normalized = self.center_predictor(fused_features)
        
        # 转换到实际坐标 [0, 255]
        center_coords = center_normalized * 255.0
        
        return {
            'center': center_coords,
            'center_normalized': center_normalized,
            'features': fused_features
        }

class CenterLocalizationLoss(nn.Module):
    """中心定位专用损失函数"""
    
    def __init__(self, mse_weight=0.7, l1_weight=0.3):
        super().__init__()
        self.mse_weight = mse_weight
        self.l1_weight = l1_weight
        self.mse_loss = nn.MSELoss()
        self.l1_loss = nn.L1Loss()
    
    def forward(self, pred_center, target_masks):
        """计算中心定位损失"""
        batch_size = pred_center.shape[0]
        device = pred_center.device
        
        # 计算真实中心位置
        true_centers = []
        for i in range(batch_size):
            mask = target_masks[i]
            if torch.sum(mask) > 0:
                # 找到病灶的质心
                coords = torch.nonzero(mask, as_tuple=False).float()
                center = torch.mean(coords, dim=0)  # (z, y, x)
                # 重新排列为 (x, y, z)
                center = center[[2, 1, 0]]
            else:
                # 如果没有病灶，使用体积中心
                center = torch.tensor([127.5, 127.5, 127.5], device=device)
            
            true_centers.append(center)
        
        true_centers = torch.stack(true_centers).to(device)
        
        # 计算损失
        mse_loss = self.mse_loss(pred_center, true_centers)
        l1_loss = self.l1_loss(pred_center, true_centers)
        
        total_loss = self.mse_weight * mse_loss + self.l1_weight * l1_loss
        
        # 计算欧几里得距离误差
        distances = torch.norm(pred_center - true_centers, dim=1)
        mean_distance = torch.mean(distances)
        
        return {
            'total_loss': total_loss,
            'mse_loss': mse_loss,
            'l1_loss': l1_loss,
            'mean_distance': mean_distance,
            'distances': distances,
            'true_centers': true_centers
        }

class CenterLocalizationTrainer:
    """中心定位训练器"""
    
    def __init__(self, model, device, learning_rate=1e-4):
        self.model = model.to(device)
        self.device = device
        self.criterion = CenterLocalizationLoss()
        
        # 优化器
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=learning_rate,
            weight_decay=1e-5
        )
        
        # 学习率调度器
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='min', factor=0.5, patience=5, verbose=True
        )
        
        # 训练历史
        self.history = {
            'train_loss': [], 'val_loss': [],
            'train_distance': [], 'val_distance': []
        }
    
    def train_epoch(self, train_loader):
        """训练一个epoch"""
        self.model.train()
        
        total_loss = 0.0
        total_distance = 0.0
        num_batches = 0
        
        pbar = tqdm(train_loader, desc="Training")
        for batch_idx, batch in enumerate(pbar):
            try:
                # 数据移到设备
                eeg_data = batch['eeg_data'].to(self.device)
                temporal_sequence = batch['temporal_sequence'].to(self.device)
                lesion_masks = batch['lesion_mask'].to(self.device)
                
                # 前向传播
                self.optimizer.zero_grad()
                outputs = self.model(eeg_data, temporal_sequence)
                
                # 计算损失
                loss_results = self.criterion(outputs['center'], lesion_masks)
                loss = loss_results['total_loss']
                
                # 检查损失有效性
                if torch.isnan(loss) or torch.isinf(loss):
                    print(f"Invalid loss at batch {batch_idx}: {loss.item()}")
                    continue
                
                # 反向传播
                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                self.optimizer.step()
                
                # 记录指标
                total_loss += loss.item()
                total_distance += loss_results['mean_distance'].item()
                num_batches += 1
                
                # 更新进度条
                pbar.set_postfix({
                    'Loss': f"{loss.item():.4f}",
                    'Distance': f"{loss_results['mean_distance'].item():.2f}"
                })
                
            except Exception as e:
                print(f"Error in batch {batch_idx}: {e}")
                continue
        
        avg_loss = total_loss / max(num_batches, 1)
        avg_distance = total_distance / max(num_batches, 1)
        
        return {'loss': avg_loss, 'distance': avg_distance}
    
    def validate_epoch(self, val_loader):
        """验证一个epoch"""
        self.model.eval()
        
        total_loss = 0.0
        total_distance = 0.0
        num_batches = 0
        all_distances = []
        
        with torch.no_grad():
            for batch_idx, batch in enumerate(val_loader):
                try:
                    # 数据移到设备
                    eeg_data = batch['eeg_data'].to(self.device)
                    temporal_sequence = batch['temporal_sequence'].to(self.device)
                    lesion_masks = batch['lesion_mask'].to(self.device)
                    
                    # 前向传播
                    outputs = self.model(eeg_data, temporal_sequence)
                    
                    # 计算损失
                    loss_results = self.criterion(outputs['center'], lesion_masks)
                    loss = loss_results['total_loss']
                    
                    if not torch.isnan(loss) and not torch.isinf(loss):
                        total_loss += loss.item()
                        total_distance += loss_results['mean_distance'].item()
                        all_distances.extend(loss_results['distances'].cpu().numpy())
                        num_batches += 1
                        
                except Exception as e:
                    print(f"Validation error in batch {batch_idx}: {e}")
                    continue
        
        avg_loss = total_loss / max(num_batches, 1)
        avg_distance = total_distance / max(num_batches, 1)
        
        return {
            'loss': avg_loss, 
            'distance': avg_distance,
            'all_distances': all_distances
        }
    
    def train(self, train_loader, val_loader, num_epochs=30):
        """完整训练循环"""
        print(f"开始中心定位训练，共 {num_epochs} 个epoch...")
        
        best_val_distance = float('inf')
        patience_counter = 0
        max_patience = 10
        
        for epoch in range(num_epochs):
            start_time = time.time()
            
            # 训练
            train_metrics = self.train_epoch(train_loader)
            
            # 验证
            val_metrics = self.validate_epoch(val_loader)
            
            # 更新学习率
            self.scheduler.step(val_metrics['distance'])
            
            # 记录历史
            self.history['train_loss'].append(train_metrics['loss'])
            self.history['val_loss'].append(val_metrics['loss'])
            self.history['train_distance'].append(train_metrics['distance'])
            self.history['val_distance'].append(val_metrics['distance'])
            
            # 打印epoch总结
            epoch_time = time.time() - start_time
            print(f"\nEpoch {epoch+1}/{num_epochs} ({epoch_time:.1f}s)")
            print(f"Train - Loss: {train_metrics['loss']:.4f}, Distance: {train_metrics['distance']:.2f}")
            print(f"Val   - Loss: {val_metrics['loss']:.4f}, Distance: {val_metrics['distance']:.2f}")
            
            # 早停和模型保存
            if val_metrics['distance'] < best_val_distance:
                best_val_distance = val_metrics['distance']
                patience_counter = 0
                
                # 保存最佳模型
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'val_distance': best_val_distance,
                    'history': self.history
                }, 'center_localization_best_model.pth')
                print(f"✓ 新的最佳模型已保存 (距离: {best_val_distance:.2f})")
            else:
                patience_counter += 1
                
            if patience_counter >= max_patience:
                print(f"早停触发，在第 {epoch+1} epoch停止训练")
                break
        
        print(f"\n训练完成！最佳验证距离: {best_val_distance:.2f} 体素")
        return self.history

def create_center_data_loaders(batch_size=4):
    """创建中心定位数据加载器"""
    
    train_dataset = NormalizedEEGLesionDataset(
        "eeg_lesion_training_dataset/train_pairings.pkl", 
        augment=True
    )
    val_dataset = NormalizedEEGLesionDataset(
        "eeg_lesion_training_dataset/validation_pairings.pkl", 
        augment=False
    )
    
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    
    return train_loader, val_loader

def visualize_predictions(model, val_loader, device, num_samples=5):
    """可视化预测结果"""
    model.eval()
    
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    fig.suptitle('中心定位预测结果', fontsize=16)
    
    predictions = []
    
    with torch.no_grad():
        for batch_idx, batch in enumerate(val_loader):
            if len(predictions) >= num_samples:
                break
                
            eeg_data = batch['eeg_data'].to(device)
            temporal_sequence = batch['temporal_sequence'].to(device)
            lesion_masks = batch['lesion_mask']
            
            outputs = model(eeg_data, temporal_sequence)
            pred_centers = outputs['center'].cpu().numpy()
            
            for i in range(min(eeg_data.shape[0], num_samples - len(predictions))):
                mask = lesion_masks[i].numpy()
                pred_center = pred_centers[i]
                
                # 计算真实中心
                if np.sum(mask) > 0:
                    coords = np.where(mask > 0)
                    true_center = np.array([
                        np.mean(coords[2]),  # x
                        np.mean(coords[1]),  # y
                        np.mean(coords[0])   # z
                    ])
                else:
                    true_center = np.array([127.5, 127.5, 127.5])
                
                distance = np.linalg.norm(pred_center - true_center)
                
                predictions.append({
                    'pred_center': pred_center,
                    'true_center': true_center,
                    'distance': distance,
                    'mask': mask
                })
    
    # 绘制结果
    distances = [p['distance'] for p in predictions]
    
    # 距离分布
    axes[0, 0].hist(distances, bins=10, alpha=0.7, color='blue')
    axes[0, 0].set_title(f'定位误差分布\n平均: {np.mean(distances):.2f} 体素')
    axes[0, 0].set_xlabel('距离 (体素)')
    axes[0, 0].set_ylabel('频次')
    
    # 预测 vs 真实坐标
    for coord_idx, coord_name in enumerate(['X', 'Y', 'Z']):
        pred_coords = [p['pred_center'][coord_idx] for p in predictions]
        true_coords = [p['true_center'][coord_idx] for p in predictions]
        
        row = coord_idx // 2
        col = (coord_idx % 2) + 1
        
        axes[row, col].scatter(true_coords, pred_coords, alpha=0.7)
        axes[row, col].plot([0, 255], [0, 255], 'r--', alpha=0.8)
        axes[row, col].set_title(f'{coord_name}坐标预测')
        axes[row, col].set_xlabel(f'真实{coord_name}坐标')
        axes[row, col].set_ylabel(f'预测{coord_name}坐标')
        axes[row, col].grid(True, alpha=0.3)
    
    # 性能总结
    axes[1, 2].text(0.1, 0.8, f'样本数: {len(predictions)}', transform=axes[1, 2].transAxes)
    axes[1, 2].text(0.1, 0.6, f'平均误差: {np.mean(distances):.2f} 体素', transform=axes[1, 2].transAxes)
    axes[1, 2].text(0.1, 0.4, f'标准差: {np.std(distances):.2f} 体素', transform=axes[1, 2].transAxes)
    axes[1, 2].text(0.1, 0.2, f'最大误差: {np.max(distances):.2f} 体素', transform=axes[1, 2].transAxes)
    axes[1, 2].set_title('性能总结')
    axes[1, 2].axis('off')
    
    plt.tight_layout()
    plt.savefig('center_localization_results.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"\n📊 中心定位性能:")
    print(f"平均定位误差: {np.mean(distances):.2f} ± {np.std(distances):.2f} 体素")
    print(f"中位数误差: {np.median(distances):.2f} 体素")
    print(f"最佳定位误差: {np.min(distances):.2f} 体素")
    print(f"最差定位误差: {np.max(distances):.2f} 体素")

def main():
    """主函数"""
    print("🎯 癫痫病灶中心定位训练")
    print("="*50)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建数据加载器
    try:
        train_loader, val_loader = create_center_data_loaders(batch_size=4)
        print("✅ 数据加载器创建成功")
    except Exception as e:
        print(f"❌ 创建数据加载器失败: {e}")
        return
    
    # 创建模型
    model = CenterLocalizationModel(n_channels=14, feature_dim=512)
    print(f"模型创建成功，参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 创建训练器
    trainer = CenterLocalizationTrainer(model, device, learning_rate=1e-4)
    
    # 开始训练
    history = trainer.train(train_loader, val_loader, num_epochs=25)
    
    # 绘制训练曲线
    plt.figure(figsize=(12, 4))
    
    plt.subplot(1, 2, 1)
    plt.plot(history['train_loss'], label='训练损失')
    plt.plot(history['val_loss'], label='验证损失')
    plt.title('损失曲线')
    plt.xlabel('Epoch')
    plt.ylabel('损失')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.subplot(1, 2, 2)
    plt.plot(history['train_distance'], label='训练距离')
    plt.plot(history['val_distance'], label='验证距离')
    plt.title('定位误差曲线')
    plt.xlabel('Epoch')
    plt.ylabel('距离 (体素)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('center_training_curves.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 可视化预测结果
    visualize_predictions(model, val_loader, device)
    
    print("\n✅ 中心定位训练完成！")
    print("📊 结果文件:")
    print("  - center_localization_best_model.pth: 最佳模型")
    print("  - center_training_curves.png: 训练曲线")
    print("  - center_localization_results.png: 预测结果")

if __name__ == "__main__":
    main()
