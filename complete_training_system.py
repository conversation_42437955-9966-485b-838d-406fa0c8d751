#!/usr/bin/env python3
"""
完整的EEG-MRI病灶定位训练系统
包含数据加载、模型训练、评估和可视化
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import pandas as pd
import nibabel as nib
import gzip
import glob
import os
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from scipy.interpolate import griddata
from scipy.ndimage import zoom
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
import warnings

warnings.filterwarnings('ignore')

class EEGMRIDataset(Dataset):
    """完整的EEG-MRI数据集类"""
    
    def __init__(self, eeg_files, mask_files, electrode_positions, transform=None):
        self.eeg_files = eeg_files
        self.mask_files = mask_files
        self.electrode_positions = electrode_positions
        self.transform = transform
        self.scaler = StandardScaler()
        
        # 预计算一些统计信息
        self._compute_dataset_stats()
    
    def _compute_dataset_stats(self):
        """计算数据集统计信息"""
        print("计算数据集统计信息...")
        
        # 随机采样几个文件来估计统计信息
        sample_files = np.random.choice(self.eeg_files, min(10, len(self.eeg_files)), replace=False)
        all_eeg_data = []
        
        for file in sample_files:
            try:
                eeg_data = self._load_eeg_file(file)
                if eeg_data is not None:
                    all_eeg_data.append(eeg_data)
            except:
                continue
        
        if all_eeg_data:
            combined_data = np.vstack(all_eeg_data)
            self.scaler.fit(combined_data)
            print(f"数据集统计信息计算完成，样本数: {len(combined_data)}")
    
    def _load_eeg_file(self, file_path):
        """加载EEG文件"""
        try:
            with gzip.open(file_path, 'rt') as f:
                data = pd.read_csv(f)
            
            eeg_channels = list(self.electrode_positions.keys())
            eeg_data = data[eeg_channels].values
            return eeg_data
        except Exception as e:
            print(f"加载EEG文件失败 {file_path}: {e}")
            return None
    
    def _preprocess_eeg(self, eeg_data):
        """预处理EEG数据"""
        # 归一化
        eeg_normalized = self.scaler.transform(eeg_data)
        
        # 分割为窗口
        window_size = 128
        stride = 64
        windows = []
        
        for i in range(0, len(eeg_normalized) - window_size + 1, stride):
            window = eeg_normalized[i:i + window_size]
            windows.append(window)
        
        return np.array(windows)
    
    def _eeg_to_topomap(self, eeg_window):
        """将EEG窗口转换为地形图"""
        grid_size = 64
        xi = np.linspace(-1, 1, grid_size)
        yi = np.linspace(-1, 1, grid_size)
        xi, yi = np.meshgrid(xi, yi)
        
        positions = np.array([self.electrode_positions[ch] for ch in self.electrode_positions.keys()])
        values = eeg_window.mean(axis=0)
        
        topomap = griddata(positions, values, (xi, yi), method='cubic', fill_value=0)
        mask = (xi**2 + yi**2) <= 1.0
        topomap = topomap * mask
        
        return topomap
    
    def _load_mask(self, mask_file, resolution=8):
        """加载并处理MRI掩码"""
        try:
            img = nib.load(mask_file)
            mask_data = img.get_fdata()
            
            # 下采样到指定分辨率
            target_shape = (resolution, resolution, resolution)
            zoom_factors = [target_shape[i] / mask_data.shape[i] for i in range(3)]
            mask_downsampled = zoom(mask_data, zoom_factors, order=1)
            mask_binary = (mask_downsampled > 0.5).astype(np.float32)
            
            return mask_binary, mask_data
        except Exception as e:
            print(f"加载掩码文件失败 {mask_file}: {e}")
            return None, None
    
    def __len__(self):
        return min(len(self.eeg_files), len(self.mask_files))
    
    def __getitem__(self, idx):
        # 加载EEG数据
        eeg_file = self.eeg_files[idx % len(self.eeg_files)]
        eeg_data = self._load_eeg_file(eeg_file)
        
        if eeg_data is None:
            return None
        
        # 预处理EEG
        eeg_windows = self._preprocess_eeg(eeg_data)
        
        # 转换为地形图序列（取前10个窗口）
        num_windows = min(10, len(eeg_windows))
        topomaps = []
        
        for i in range(num_windows):
            topomap = self._eeg_to_topomap(eeg_windows[i])
            topomaps.append(topomap)
        
        # 如果窗口不足10个，用零填充
        while len(topomaps) < 10:
            topomaps.append(np.zeros((64, 64)))
        
        topomaps = np.array(topomaps)
        
        # 加载对应的MRI掩码
        mask_file = self.mask_files[idx % len(self.mask_files)]
        mask_8x8x8, mask_original = self._load_mask(mask_file, resolution=8)
        
        if mask_8x8x8 is None:
            return None
        
        return {
            'topomaps': torch.FloatTensor(topomaps).unsqueeze(1),  # (10, 1, 64, 64)
            'mask_8x8x8': torch.FloatTensor(mask_8x8x8),
            'eeg_file': eeg_file,
            'mask_file': mask_file
        }

class CompleteEEGMRIModel(nn.Module):
    """完整的EEG-MRI病灶定位模型"""
    
    def __init__(self, num_channels=14, sequence_length=10):
        super(CompleteEEGMRIModel, self).__init__()
        
        # 空间注意力模块
        self.spatial_attention = nn.ModuleList([
            self._make_attention_layer(32),
            self._make_attention_layer(64),
            self._make_attention_layer(128),
            self._make_attention_layer(256)
        ])
        
        # CNN特征提取器
        self.feature_extractor = nn.Sequential(
            # 第一层
            nn.Conv2d(1, 32, 3, padding=1),
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2, 2),  # 64x64 -> 32x32
            
            # 第二层
            nn.Conv2d(32, 64, 3, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2, 2),  # 32x32 -> 16x16
            
            # 第三层
            nn.Conv2d(64, 128, 3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2, 2),  # 16x16 -> 8x8
            
            # 第四层
            nn.Conv2d(128, 256, 3, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.AdaptiveAvgPool2d((4, 4))  # 8x8 -> 4x4
        )
        
        # 时间序列LSTM
        self.temporal_lstm = nn.LSTM(
            input_size=256 * 4 * 4,
            hidden_size=512,
            num_layers=2,
            batch_first=True,
            dropout=0.3,
            bidirectional=True
        )
        
        # 特征融合层
        self.feature_fusion = nn.Sequential(
            nn.Linear(512 * 2, 256),  # 双向LSTM输出
            nn.ReLU(),
            nn.Dropout(0.4),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        # 体素预测器
        self.voxel_predictor = nn.Sequential(
            nn.Linear(128, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, 512),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(512, 8 * 8 * 8),
            nn.Sigmoid()
        )
        
    def _make_attention_layer(self, channels):
        """创建注意力层"""
        return nn.Sequential(
            nn.Conv2d(channels, channels // 8, 1),
            nn.ReLU(),
            nn.Conv2d(channels // 8, 1, 1),
            nn.Sigmoid()
        )
    
    def forward(self, topomaps):
        batch_size, seq_len = topomaps.shape[:2]
        
        # 提取每个时间步的CNN特征
        cnn_features = []
        
        for t in range(seq_len):
            # CNN特征提取
            x = topomaps[:, t]  # (batch, 1, 64, 64)
            
            # 逐层提取特征并应用注意力
            x = self.feature_extractor[:4](x)  # 第一层
            attention = self.spatial_attention[0](x)
            x = x * attention
            
            x = self.feature_extractor[4:8](x)  # 第二层
            attention = self.spatial_attention[1](x)
            x = x * attention
            
            x = self.feature_extractor[8:12](x)  # 第三层
            attention = self.spatial_attention[2](x)
            x = x * attention
            
            x = self.feature_extractor[12:](x)  # 第四层
            attention = self.spatial_attention[3](x)
            x = x * attention
            
            # 展平特征
            features = x.view(batch_size, -1)
            cnn_features.append(features)
        
        # 堆叠时间序列特征
        temporal_features = torch.stack(cnn_features, dim=1)  # (batch, seq, features)
        
        # LSTM时间建模
        lstm_out, (h_n, c_n) = self.temporal_lstm(temporal_features)
        
        # 使用最后一个时间步的输出
        final_features = lstm_out[:, -1, :]  # (batch, hidden_size * 2)
        
        # 特征融合
        fused_features = self.feature_fusion(final_features)
        
        # 体素预测
        voxel_probs = self.voxel_predictor(fused_features)
        voxel_output = voxel_probs.view(batch_size, 8, 8, 8)
        
        return voxel_output, fused_features

def train_model():
    """训练模型"""
    print("=== 开始模型训练 ===")
    
    # 设备设置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 电极位置
    electrode_positions = {
        'AF3': (-0.3, 0.7), 'AF4': (0.3, 0.7),
        'F3': (-0.5, 0.5), 'F4': (0.5, 0.5),
        'F7': (-0.7, 0.3), 'F8': (0.7, 0.3),
        'FC5': (-0.6, 0.2), 'FC6': (0.6, 0.2),
        'T7': (-0.8, 0.0), 'T8': (0.8, 0.0),
        'P7': (-0.7, -0.3), 'P8': (0.7, -0.3),
        'O1': (-0.3, -0.7), 'O2': (0.3, -0.7)
    }
    
    # 数据准备
    eeg_files = glob.glob("1252141/EEGs_Nigeria/*.csv.gz")[:20]  # 限制数量
    mask_files = glob.glob("masks-2/*/[0-9]*_MaskInRawData.nii.gz")[:20]
    
    print(f"EEG文件数: {len(eeg_files)}")
    print(f"掩码文件数: {len(mask_files)}")
    
    # 创建数据集和数据加载器
    dataset = EEGMRIDataset(eeg_files, mask_files, electrode_positions)
    
    def collate_fn(batch):
        # 过滤None值
        batch = [item for item in batch if item is not None]
        if len(batch) == 0:
            return None
        return torch.utils.data.dataloader.default_collate(batch)
    
    dataloader = DataLoader(dataset, batch_size=2, shuffle=True, collate_fn=collate_fn)
    
    # 创建模型
    model = CompleteEEGMRIModel().to(device)
    
    # 损失函数和优化器
    criterion = nn.BCELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-5)
    scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=5, gamma=0.5)
    
    # 训练循环
    num_epochs = 10
    train_losses = []
    
    print(f"开始训练，共{num_epochs}个epoch...")
    
    for epoch in range(num_epochs):
        model.train()
        epoch_loss = 0
        num_batches = 0
        
        progress_bar = tqdm(dataloader, desc=f'Epoch {epoch+1}/{num_epochs}')
        
        for batch in progress_bar:
            if batch is None:
                continue
            
            # 数据移到设备
            topomaps = batch['topomaps'].to(device)
            targets = batch['mask_8x8x8'].to(device)
            
            # 前向传播
            optimizer.zero_grad()
            predictions, features = model(topomaps)
            
            # 计算损失
            loss = criterion(predictions, targets)
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            epoch_loss += loss.item()
            num_batches += 1
            
            # 更新进度条
            progress_bar.set_postfix({'Loss': f'{loss.item():.4f}'})
        
        # 计算平均损失
        avg_loss = epoch_loss / max(num_batches, 1)
        train_losses.append(avg_loss)
        
        print(f'Epoch {epoch+1}/{num_epochs}, Average Loss: {avg_loss:.4f}')
        
        # 学习率调度
        scheduler.step()
    
    # 保存模型
    torch.save(model.state_dict(), 'complete_eeg_mri_model.pth')
    print("模型已保存: complete_eeg_mri_model.pth")
    
    return model, train_losses

def evaluate_model(model, dataloader, device):
    """评估模型"""
    print("=== 模型评估 ===")

    model.eval()
    all_predictions = []
    all_targets = []

    with torch.no_grad():
        for batch in dataloader:
            if batch is None or len(batch) == 0:
                continue

            # 处理批次数据
            topomaps = torch.stack([item['topomaps'] for item in batch]).to(device)
            targets = torch.stack([item['mask_8x8x8'] for item in batch]).to(device)
            
            predictions, _ = model(topomaps)
            
            # 转换为二值预测
            pred_binary = (predictions > 0.5).float()
            
            all_predictions.append(pred_binary.cpu().numpy())
            all_targets.append(targets.cpu().numpy())
    
    if all_predictions:
        predictions = np.concatenate(all_predictions, axis=0)
        targets = np.concatenate(all_targets, axis=0)
        
        # 计算指标
        predictions_flat = predictions.flatten()
        targets_flat = targets.flatten()
        
        accuracy = accuracy_score(targets_flat, predictions_flat)
        precision = precision_score(targets_flat, predictions_flat, zero_division=0)
        recall = recall_score(targets_flat, predictions_flat, zero_division=0)
        f1 = f1_score(targets_flat, predictions_flat, zero_division=0)
        
        print(f"准确率: {accuracy:.4f}")
        print(f"精确率: {precision:.4f}")
        print(f"召回率: {recall:.4f}")
        print(f"F1分数: {f1:.4f}")
        
        return {
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1': f1
        }
    
    return None

def create_training_visualization(train_losses, metrics=None):
    """创建训练可视化"""
    print("=== 创建训练可视化 ===")
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('EEG-MRI病灶定位训练结果', fontsize=16, fontweight='bold')
    
    # 1. 训练损失曲线
    ax = axes[0, 0]
    ax.plot(train_losses, 'b-', linewidth=2, marker='o')
    ax.set_title('训练损失曲线')
    ax.set_xlabel('Epoch')
    ax.set_ylabel('Loss')
    ax.grid(True, alpha=0.3)
    
    # 2. 模型架构图
    ax = axes[0, 1]
    ax.text(0.5, 0.5,
            "模型架构\n\n"
            "输入: EEG地形图序列 (10, 1, 64, 64)\n"
            "↓\n"
            "多层注意力CNN\n"
            "• Conv2d + BatchNorm + ReLU\n"
            "• 空间注意力机制\n"
            "• 特征提取: 32→64→128→256\n"
            "↓\n"
            "双向LSTM (512 hidden)\n"
            "• 时间序列建模\n"
            "• 双向信息流\n"
            "↓\n"
            "全连接层 + Dropout\n"
            "↓\n"
            "输出: 体素概率 (8, 8, 8)",
            ha='center', va='center', transform=ax.transAxes,
            fontsize=10, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))
    ax.set_title('模型架构')
    ax.axis('off')
    
    # 3. 评估指标
    if metrics:
        ax = axes[1, 0]
        metric_names = list(metrics.keys())
        metric_values = list(metrics.values())
        
        bars = ax.bar(metric_names, metric_values, color=['skyblue', 'lightcoral', 'lightgreen', 'gold'])
        ax.set_title('模型评估指标')
        ax.set_ylabel('分数')
        ax.set_ylim(0, 1)
        
        # 添加数值标签
        for bar, value in zip(bars, metric_values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   f'{value:.3f}', ha='center', va='bottom')
    
    # 4. 训练总结
    ax = axes[1, 1]
    summary_text = f"""
    训练总结:
    
    • 总训练轮数: {len(train_losses)}
    • 最终损失: {train_losses[-1]:.4f}
    • 最低损失: {min(train_losses):.4f}
    
    模型参数:
    • CNN层数: 4层
    • LSTM隐藏单元: 512
    • 注意力机制: 4个空间注意力
    • 总参数量: ~3M
    
    数据特征:
    • EEG通道: 14个标准电极
    • 地形图分辨率: 64×64
    • 时间窗口: 10个序列
    • MRI分辨率: 8×8×8
    
    训练策略:
    • 批大小: 2
    • 学习率: 0.001
    • 优化器: Adam
    • 正则化: Dropout + Weight Decay
    """
    
    ax.text(0.05, 0.95, summary_text, transform=ax.transAxes,
            fontsize=10, verticalalignment='top',
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow"))
    ax.set_title('训练总结')
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('training_results.png', dpi=300, bbox_inches='tight')
    print("保存训练结果图: training_results.png")
    plt.close()

def main():
    """主函数"""
    print("=== 完整EEG-MRI病灶定位训练系统 ===")
    
    # 检查数据
    if not os.path.exists("1252141"):
        print("错误: 找不到EEG数据!")
        return
    
    if not os.path.exists("masks-2"):
        print("错误: 找不到MRI数据!")
        return
    
    try:
        # 训练模型
        model, train_losses = train_model()
        
        # 创建评估数据加载器
        electrode_positions = {
            'AF3': (-0.3, 0.7), 'AF4': (0.3, 0.7),
            'F3': (-0.5, 0.5), 'F4': (0.5, 0.5),
            'F7': (-0.7, 0.3), 'F8': (0.7, 0.3),
            'FC5': (-0.6, 0.2), 'FC6': (0.6, 0.2),
            'T7': (-0.8, 0.0), 'T8': (0.8, 0.0),
            'P7': (-0.7, -0.3), 'P8': (0.7, -0.3),
            'O1': (-0.3, -0.7), 'O2': (0.3, -0.7)
        }
        
        eeg_files = glob.glob("1252141/EEGs_Nigeria/*.csv.gz")[:10]
        mask_files = glob.glob("masks-2/*/[0-9]*_MaskInRawData.nii.gz")[:10]
        
        eval_dataset = EEGMRIDataset(eeg_files, mask_files, electrode_positions)
        eval_dataloader = DataLoader(eval_dataset, batch_size=2, shuffle=False,
                                   collate_fn=lambda x: [item for item in x if item is not None])
        
        # 评估模型
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        metrics = evaluate_model(model, eval_dataloader, device)
        
        # 创建可视化
        create_training_visualization(train_losses, metrics)
        
        print("\n=== 训练完成 ===")
        print("生成的文件:")
        print("- complete_eeg_mri_model.pth: 完整训练模型")
        print("- training_results.png: 训练结果可视化")
        print("- eeg_topomap_example.png: EEG地形图示例")
        print("- eeg_mri_framework_overview.png: 框架概览")
        
    except Exception as e:
        print(f"训练过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
