#!/usr/bin/env python3
"""
EEG系统比较分析：29电极系统与常见EEG配置的对比
"""

import matplotlib.pyplot as plt
import numpy as np

def analyze_eeg_systems():
    """
    分析不同EEG系统的特点
    """
    print("=== EEG系统配置比较分析 ===")
    
    # 您的29电极系统
    your_29ch = {
        'name': '您的29电极系统',
        'electrodes': 29,
        'coverage': ['Fp1', 'F3', 'C3', 'P3', 'O1', 'F7', 'T3', 'T5', 'FC1', 'FC5',
                    'CP1', 'CP5', 'F9', 'Fz', 'Cz', 'Pz', 'Fp2', 'F4', 'C4', 'P4',
                    'O2', 'F8', 'T4', 'T6', 'FC2', 'FC6', 'CP2', 'CP6', 'F10'],
        'applications': ['临床EEG', '睡眠研究', '癫痫监测', '认知研究'],
        'advantages': ['良好的脑区覆盖', '适中的电极密度', '标准10-20兼容'],
        'spatial_resolution': '中等'
    }
    
    # 常见EEG系统配置
    eeg_systems = {
        '标准19电极': {
            'electrodes': 19,
            'coverage': ['Fp1', 'Fp2', 'F7', 'F3', 'Fz', 'F4', 'F8', 'T3', 'C3', 'Cz', 
                        'C4', 'T4', 'T5', 'P3', 'Pz', 'P4', 'T6', 'O1', 'O2'],
            'applications': ['临床诊断', '基础研究'],
            'spatial_resolution': '基础'
        },
        '32电极系统': {
            'electrodes': 32,
            'coverage': '扩展10-20系统',
            'applications': ['研究级EEG', 'BCI应用'],
            'spatial_resolution': '良好'
        },
        '64电极系统': {
            'electrodes': 64,
            'coverage': '高密度10-10系统',
            'applications': ['高级研究', '源定位'],
            'spatial_resolution': '高'
        },
        '128电极系统': {
            'electrodes': 128,
            'coverage': '超高密度系统',
            'applications': ['精密研究', '高精度源定位'],
            'spatial_resolution': '很高'
        }
    }
    
    print(f"\n=== 您的29电极系统分析 ===")
    print(f"电极数量: {your_29ch['electrodes']}")
    print(f"空间分辨率: {your_29ch['spatial_resolution']}")
    print(f"主要应用: {', '.join(your_29ch['applications'])}")
    print(f"优势: {', '.join(your_29ch['advantages'])}")
    
    print(f"\n=== 与其他系统对比 ===")
    for system_name, system_info in eeg_systems.items():
        print(f"\n{system_name}:")
        print(f"  电极数: {system_info['electrodes']}")
        print(f"  空间分辨率: {system_info['spatial_resolution']}")
        print(f"  应用: {', '.join(system_info['applications'])}")
    
    # 分析您的系统在光谱中的位置
    electrode_counts = [19, 29, 32, 64, 128]
    system_names = ['标准19电极', '您的29电极', '32电极', '64电极', '128电极']
    
    print(f"\n=== 电极密度分析 ===")
    print("您的29电极系统位于:")
    print("- 比标准19电极系统多52%的电极")
    print("- 比32电极系统少9%的电极") 
    print("- 是64电极系统的45%")
    print("- 是128电极系统的23%")
    
    return your_29ch, eeg_systems

def evaluate_topographic_mapping_quality():
    """
    评估29电极系统的地形图绘制质量
    """
    print(f"\n=== 地形图绘制质量评估 ===")
    
    # 评估标准
    criteria = {
        '电极数量': {
            'minimum': 16,
            'good': 32,
            'excellent': 64,
            'your_value': 29,
            'weight': 0.3
        },
        '脑区覆盖': {
            'frontal': 13,  # 您有13个额叶电极
            'central': 9,   # 您有9个中央电极
            'parietal': 3,  # 您有3个顶叶电极
            'occipital': 2, # 您有2个枕叶电极
            'temporal': 4,  # 您有4个颞叶电极
            'weight': 0.4
        },
        '半球平衡': {
            'left': 13,
            'right': 13,
            'midline': 3,
            'balance_score': 1.0,  # 完美平衡
            'weight': 0.2
        },
        '标准兼容性': {
            'match_rate': 1.0,  # 100%匹配
            'weight': 0.1
        }
    }
    
    # 计算各项得分
    scores = {}
    
    # 电极数量得分
    electrode_count = criteria['电极数量']['your_value']
    if electrode_count >= criteria['电极数量']['excellent']:
        scores['电极数量'] = 100
    elif electrode_count >= criteria['电极数量']['good']:
        scores['电极数量'] = 80
    elif electrode_count >= criteria['电极数量']['minimum']:
        # 线性插值
        ratio = (electrode_count - criteria['电极数量']['minimum']) / (criteria['电极数量']['good'] - criteria['电极数量']['minimum'])
        scores['电极数量'] = 60 + ratio * 20
    else:
        scores['电极数量'] = 40
    
    # 脑区覆盖得分
    brain_regions = criteria['脑区覆盖']
    total_regions = brain_regions['frontal'] + brain_regions['central'] + brain_regions['parietal'] + brain_regions['occipital'] + brain_regions['temporal']
    coverage_score = min(100, (total_regions / 25) * 100)  # 假设25个电极为良好覆盖
    scores['脑区覆盖'] = coverage_score
    
    # 半球平衡得分
    scores['半球平衡'] = criteria['半球平衡']['balance_score'] * 100
    
    # 标准兼容性得分
    scores['标准兼容性'] = criteria['标准兼容性']['match_rate'] * 100
    
    # 计算加权总分
    total_score = 0
    for criterion, score in scores.items():
        weight = criteria[criterion.replace('脑区覆盖', '脑区覆盖').replace('半球平衡', '半球平衡').replace('标准兼容性', '标准兼容性').replace('电极数量', '电极数量')]['weight']
        total_score += score * weight
    
    print(f"各项评分:")
    for criterion, score in scores.items():
        print(f"  {criterion}: {score:.1f}/100")
    
    print(f"\n综合评分: {total_score:.1f}/100")
    
    # 评级
    if total_score >= 90:
        grade = "A+ (优秀)"
    elif total_score >= 80:
        grade = "A (良好)"
    elif total_score >= 70:
        grade = "B (中等)"
    elif total_score >= 60:
        grade = "C (及格)"
    else:
        grade = "D (不足)"
    
    print(f"地形图绘制质量等级: {grade}")
    
    return total_score, scores

def provide_recommendations():
    """
    提供使用建议
    """
    print(f"\n=== 使用建议 ===")
    
    recommendations = [
        "✓ 您的29电极系统非常适合绘制EEG地形图",
        "✓ 电极覆盖范围良好，可以可靠地显示大脑活动模式",
        "✓ 适合进行频段分析（Delta、Theta、Alpha、Beta、Gamma）",
        "✓ 可以进行基本的源定位分析",
        "✓ 适合临床应用和中等复杂度的研究",
        "",
        "注意事项:",
        "• 相比64+电极系统，空间分辨率有限",
        "• 对于精密的源定位可能需要更多电极",
        "• 适合大尺度脑网络分析，但细节分析有限"
    ]
    
    for rec in recommendations:
        print(rec)

def create_comparison_visualization():
    """
    创建比较可视化图表
    """
    print(f"\n=== 生成比较图表 ===")
    
    # 电极数量比较
    systems = ['19电极\n(标准)', '29电极\n(您的)', '32电极', '64电极', '128电极']
    electrode_counts = [19, 29, 32, 64, 128]
    colors = ['lightcoral', 'gold', 'lightblue', 'lightgreen', 'plum']
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 电极数量柱状图
    bars = ax1.bar(systems, electrode_counts, color=colors)
    ax1.set_ylabel('电极数量')
    ax1.set_title('不同EEG系统电极数量对比')
    ax1.grid(True, alpha=0.3)
    
    # 突出显示您的系统
    bars[1].set_edgecolor('red')
    bars[1].set_linewidth(3)
    
    # 添加数值标签
    for bar, count in zip(bars, electrode_counts):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{count}', ha='center', va='bottom', fontweight='bold')
    
    # 应用领域雷达图
    categories = ['临床诊断', '睡眠研究', '认知研究', '源定位', 'BCI应用']
    your_system_scores = [9, 9, 8, 6, 7]  # 您的系统在各领域的适用性评分
    
    angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
    your_system_scores += your_system_scores[:1]  # 闭合图形
    angles += angles[:1]
    
    ax2.plot(angles, your_system_scores, 'o-', linewidth=2, label='29电极系统', color='gold')
    ax2.fill(angles, your_system_scores, alpha=0.25, color='gold')
    ax2.set_xticks(angles[:-1])
    ax2.set_xticklabels(categories)
    ax2.set_ylim(0, 10)
    ax2.set_title('29电极系统应用领域适用性')
    ax2.grid(True)
    ax2.legend()
    
    plt.tight_layout()
    plt.savefig('eeg_system_comparison.png', dpi=300, bbox_inches='tight')
    print("比较图表已保存: eeg_system_comparison.png")
    plt.close()

def main():
    """
    主函数
    """
    print("正在分析您的29电极EEG系统...")
    
    # 系统比较分析
    your_system, other_systems = analyze_eeg_systems()
    
    # 地形图质量评估
    total_score, detailed_scores = evaluate_topographic_mapping_quality()
    
    # 生成比较图表
    create_comparison_visualization()
    
    # 提供建议
    provide_recommendations()
    
    print(f"\n=== 总结 ===")
    print(f"您的29电极EEG系统:")
    print(f"• 与标准10-20系统100%兼容")
    print(f"• 地形图绘制质量评分: {total_score:.1f}/100")
    print(f"• 非常适合生成高质量的EEG地形图")
    print(f"• 在临床和研究应用中表现优秀")

if __name__ == "__main__":
    main()
