#!/usr/bin/env python3
"""
Enhanced Feature Extraction with Attention and LSTM
Combines both Guinea-Bissau and Nigeria datasets with improved architecture:
1. Attention-based topographic CNN
2. LSTM for temporal sequence modeling
3. Multi-scale feature fusion
4. Increased sample size from both datasets
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score, roc_curve
from sklearn.model_selection import train_test_split
import gzip
import os
from pathlib import Path
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

class EnhancedEEGDataset(torch.utils.data.Dataset):
    """增强的EEG数据集，整合两个数据源"""
    
    def __init__(self, data_dirs, metadata_files, split='train', test_size=0.2, random_state=42):
        self.data_dirs = [Path(d) for d in data_dirs]
        
        # 加载并合并元数据
        self.metadata = self.load_combined_metadata(metadata_files)
        print(f"Combined metadata: {len(self.metadata)} subjects")
        
        # 创建标签映射
        self.label_map = {'Epilepsy': 1, 'Control': 0, 'control': 0}  # 处理大小写不一致
        
        # 准备数据
        self.prepare_data(split, test_size, random_state)
        
    def load_combined_metadata(self, metadata_files):
        """加载并合并多个元数据文件"""
        combined_metadata = []
        
        for i, metadata_file in enumerate(metadata_files):
            df = pd.read_csv(metadata_file)
            df['dataset_id'] = i  # 标记数据集来源
            df['data_dir'] = str(self.data_dirs[i])
            combined_metadata.append(df)
        
        return pd.concat(combined_metadata, ignore_index=True)
    
    def prepare_data(self, split, test_size, random_state):
        """准备训练/测试数据"""
        from sklearn.model_selection import train_test_split
        
        # 获取可用的EEG文件
        available_files = []
        labels = []
        groups = []
        dataset_ids = []
        
        for idx, row in self.metadata.iterrows():
            subject_id = row['subject.id']
            group = row['Group']
            dataset_id = row['dataset_id']
            data_dir = Path(row['data_dir'])
            
            # 构建文件路径 - 处理不同的文件命名格式
            if dataset_id == 0:  # Guinea-Bissau
                eeg_file = data_dir / f"signal-{subject_id}.csv.gz"
            else:  # Nigeria
                # Nigeria数据集有不同的命名格式
                if 'csv.file' in row and pd.notna(row['csv.file']):
                    eeg_file = data_dir / row['csv.file']
                else:
                    eeg_file = data_dir / f"signal-{subject_id}-1.csv.gz"
            
            if eeg_file.exists():
                available_files.append(str(eeg_file))
                labels.append(self.label_map.get(group, 0))
                groups.append(group)
                dataset_ids.append(dataset_id)
        
        print(f"Found {len(available_files)} available EEG files")
        print(f"Epilepsy: {sum(labels)}, Control: {len(labels) - sum(labels)}")
        print(f"Guinea-Bissau: {dataset_ids.count(0)}, Nigeria: {dataset_ids.count(1)}")
        
        # 分割数据
        if split == 'all':
            self.file_paths = available_files
            self.labels = labels
            self.groups = groups
            self.dataset_ids = dataset_ids
        else:
            train_files, test_files, train_labels, test_labels, train_groups, test_groups, train_datasets, test_datasets = train_test_split(
                available_files, labels, groups, dataset_ids, test_size=test_size, 
                random_state=random_state, stratify=labels
            )
            
            if split == 'train':
                self.file_paths = train_files
                self.labels = train_labels
                self.groups = train_groups
                self.dataset_ids = train_datasets
            else:  # test
                self.file_paths = test_files
                self.labels = test_labels
                self.groups = test_groups
                self.dataset_ids = test_datasets
        
        print(f"{split.upper()} set: {len(self.file_paths)} samples")
        print(f"  Epilepsy: {sum(self.labels)}, Control: {len(self.labels) - sum(self.labels)}")
    
    def load_eeg_signal(self, file_path):
        """加载EEG信号"""
        try:
            with gzip.open(file_path, 'rt') as f:
                df = pd.read_csv(f)
            
            # 选择14个EEG通道
            eeg_channels = ['AF3', 'AF4', 'F3', 'F4', 'F7', 'F8', 'FC5', 'FC6', 
                           'O1', 'O2', 'P7', 'P8', 'T7', 'T8']
            
            # 检查通道是否存在
            available_channels = [ch for ch in eeg_channels if ch in df.columns]
            if len(available_channels) < 10:  # 至少需要10个通道
                print(f"Warning: Only {len(available_channels)} channels available in {file_path}")
                return None
            
            eeg_data = df[available_channels].values.T  # [channels, time_points]
            
            # 如果通道数不足14，用零填充
            if len(available_channels) < 14:
                padding = np.zeros((14 - len(available_channels), eeg_data.shape[1]))
                eeg_data = np.vstack([eeg_data, padding])
            
            # 标准化
            eeg_data = (eeg_data - np.mean(eeg_data, axis=1, keepdims=True)) / (np.std(eeg_data, axis=1, keepdims=True) + 1e-8)
            
            # 截取或填充到固定长度
            target_length = 2048  # 增加长度以获得更多时间信息
            if eeg_data.shape[1] > target_length:
                # 随机选择一个起始点，增加数据多样性
                start_idx = np.random.randint(0, eeg_data.shape[1] - target_length + 1)
                eeg_data = eeg_data[:, start_idx:start_idx + target_length]
            elif eeg_data.shape[1] < target_length:
                pad_width = target_length - eeg_data.shape[1]
                eeg_data = np.pad(eeg_data, ((0, 0), (0, pad_width)), mode='reflect')
            
            return eeg_data.astype(np.float32)
            
        except Exception as e:
            print(f"Error loading {file_path}: {e}")
            return None
    
    def create_temporal_sequences(self, eeg_data, window_size=128, num_windows=16):
        """创建时间序列窗口"""
        total_length = eeg_data.shape[1]
        step_size = (total_length - window_size) // (num_windows - 1)
        
        sequences = []
        for i in range(num_windows):
            start_idx = i * step_size
            end_idx = start_idx + window_size
            if end_idx <= total_length:
                window = eeg_data[:, start_idx:end_idx]
                # 对每个窗口计算统计特征
                window_features = np.array([
                    np.mean(window, axis=1),      # 均值
                    np.std(window, axis=1),       # 标准差
                    np.max(window, axis=1),       # 最大值
                    np.min(window, axis=1),       # 最小值
                ]).T.flatten()  # [14*4] = 56维特征
                sequences.append(window_features)
        
        return np.array(sequences)  # [num_windows, 56]
    
    def create_enhanced_topographic_map(self, eeg_data):
        """创建增强的拓扑图，包含多个频段"""
        from scipy import signal
        
        # 电极位置映射到64x64网格
        electrode_positions = {
            'AF3': (20, 25), 'AF4': (20, 39),
            'F3': (25, 20), 'F4': (25, 44), 'F7': (25, 10), 'F8': (25, 54),
            'FC5': (30, 15), 'FC6': (30, 49),
            'T7': (35, 5), 'T8': (35, 59),
            'P7': (45, 10), 'P8': (45, 54),
            'O1': (55, 25), 'O2': (55, 39)
        }
        
        channels = ['AF3', 'AF4', 'F3', 'F4', 'F7', 'F8', 'FC5', 'FC6', 
                   'O1', 'O2', 'P7', 'P8', 'T7', 'T8']
        
        # 创建多频段拓扑图
        fs = 256  # 假设采样率
        freq_bands = {
            'delta': (1, 4),
            'theta': (4, 8),
            'alpha': (8, 13),
            'beta': (13, 30)
        }
        
        topo_maps = []
        
        for band_name, (low_freq, high_freq) in freq_bands.items():
            # 带通滤波
            sos = signal.butter(4, [low_freq, high_freq], btype='band', fs=fs, output='sos')
            filtered_data = signal.sosfilt(sos, eeg_data, axis=1)
            
            # 计算功率
            power_values = np.mean(filtered_data**2, axis=1)
            
            # 创建拓扑图
            topo_map = np.zeros((64, 64))
            
            for i, channel in enumerate(channels):
                if channel in electrode_positions and i < len(power_values):
                    y, x = electrode_positions[channel]
                    # 在电极位置周围创建高斯分布
                    for dy in range(-4, 5):
                        for dx in range(-4, 5):
                            ny, nx = y + dy, x + dx
                            if 0 <= ny < 64 and 0 <= nx < 64:
                                weight = np.exp(-(dy**2 + dx**2) / 8.0)
                                topo_map[ny, nx] += power_values[i] * weight
            
            topo_maps.append(topo_map)
        
        return np.stack(topo_maps)  # [4, 64, 64]
    
    def __len__(self):
        return len(self.file_paths)
    
    def __getitem__(self, idx):
        # 加载EEG数据
        eeg_data = self.load_eeg_signal(self.file_paths[idx])
        
        if eeg_data is None:
            # 返回零数据
            eeg_data = np.zeros((14, 2048), dtype=np.float32)
        
        # 创建增强拓扑图
        topo_maps = self.create_enhanced_topographic_map(eeg_data)
        
        # 创建时间序列
        temporal_sequences = self.create_temporal_sequences(eeg_data)
        
        return {
            'topographic_maps': torch.FloatTensor(topo_maps),  # [4, 64, 64]
            'temporal_sequences': torch.FloatTensor(temporal_sequences),  # [16, 56]
            'raw_eeg': torch.FloatTensor(eeg_data),  # [14, 2048]
            'label': torch.LongTensor([self.labels[idx]])[0],
            'dataset_id': self.dataset_ids[idx],
            'file_path': self.file_paths[idx]
        }

class SpatialAttentionModule(nn.Module):
    """空间注意力模块"""
    
    def __init__(self, in_channels):
        super().__init__()
        self.conv1 = nn.Conv2d(in_channels, in_channels // 8, 1)
        self.conv2 = nn.Conv2d(in_channels // 8, 1, 1)
        self.sigmoid = nn.Sigmoid()
        
    def forward(self, x):
        # x: [batch, channels, height, width]
        attention = self.conv1(x)
        attention = nn.functional.relu(attention)
        attention = self.conv2(attention)
        attention = self.sigmoid(attention)
        
        return x * attention

class EnhancedTopographicCNN(nn.Module):
    """增强的拓扑CNN，包含注意力机制"""
    
    def __init__(self, num_classes=2):
        super().__init__()
        
        # 多频段特征提取
        self.freq_conv = nn.Sequential(
            nn.Conv2d(4, 32, kernel_size=7, stride=2, padding=3),  # 输入4个频段
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=3, stride=2, padding=1),
        )
        
        # 残差块 + 注意力
        self.layer1 = self._make_layer(32, 64, 2)
        self.attention1 = SpatialAttentionModule(64)
        
        self.layer2 = self._make_layer(64, 128, 2, stride=2)
        self.attention2 = SpatialAttentionModule(128)
        
        self.layer3 = self._make_layer(128, 256, 2, stride=2)
        self.attention3 = SpatialAttentionModule(256)
        
        # 全局特征
        self.global_pool = nn.AdaptiveAvgPool2d((1, 1))
        
        # 分类头
        self.classifier = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(256, 128),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(128, num_classes)
        )
        
    def _make_layer(self, in_channels, out_channels, blocks, stride=1):
        layers = []
        layers.append(self._make_block(in_channels, out_channels, stride))
        for _ in range(1, blocks):
            layers.append(self._make_block(out_channels, out_channels))
        return nn.Sequential(*layers)
    
    def _make_block(self, in_channels, out_channels, stride=1):
        return nn.Sequential(
            nn.Conv2d(in_channels, out_channels, kernel_size=3, stride=stride, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )
    
    def forward(self, x):
        # 多频段特征提取
        x = self.freq_conv(x)
        
        # 残差块 + 注意力
        x = self.layer1(x)
        x = self.attention1(x)
        
        x = self.layer2(x)
        x = self.attention2(x)
        
        x = self.layer3(x)
        x = self.attention3(x)
        
        # 全局池化
        x = self.global_pool(x)
        x = x.view(x.size(0), -1)
        
        # 分类
        logits = self.classifier(x)
        
        return logits, x  # 返回logits和特征

class TemporalLSTMModule(nn.Module):
    """时间序列LSTM模块"""
    
    def __init__(self, input_dim=56, hidden_dim=128, num_layers=2):
        super().__init__()
        
        self.lstm = nn.LSTM(
            input_dim, hidden_dim, num_layers,
            batch_first=True, bidirectional=True, dropout=0.3
        )
        
        self.attention = nn.MultiheadAttention(
            embed_dim=hidden_dim * 2, num_heads=8, batch_first=True
        )
        
        self.fc = nn.Sequential(
            nn.Linear(hidden_dim * 2, 128),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(128, 64)
        )
        
    def forward(self, x):
        # LSTM处理
        lstm_out, _ = self.lstm(x)  # [batch, seq_len, hidden_dim*2]
        
        # 自注意力
        attended, _ = self.attention(lstm_out, lstm_out, lstm_out)
        
        # 全局池化
        pooled = torch.mean(attended, dim=1)  # [batch, hidden_dim*2]
        
        # 特征变换
        features = self.fc(pooled)
        
        return features

class EnhancedMultiModalClassifier(nn.Module):
    """增强的多模态分类器"""
    
    def __init__(self, num_classes=2):
        super().__init__()
        
        # 拓扑CNN分支
        self.topographic_cnn = EnhancedTopographicCNN(num_classes)
        
        # 时序LSTM分支
        self.temporal_lstm = TemporalLSTMModule()
        
        # 特征融合
        self.fusion = nn.Sequential(
            nn.Linear(256 + 64, 256),  # CNN特征 + LSTM特征
            nn.ReLU(),
            nn.Dropout(0.4),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(128, num_classes)
        )
        
    def forward(self, topo_maps, temporal_seq):
        # 拓扑特征
        topo_logits, topo_features = self.topographic_cnn(topo_maps)
        
        # 时序特征
        temporal_features = self.temporal_lstm(temporal_seq)
        
        # 特征融合
        combined_features = torch.cat([topo_features, temporal_features], dim=1)
        fused_logits = self.fusion(combined_features)
        
        return {
            'fused_logits': fused_logits,
            'topo_logits': topo_logits,
            'topo_features': topo_features,
            'temporal_features': temporal_features,
            'combined_features': combined_features
        }

class EnhancedTrainer:
    """增强的训练器"""
    
    def __init__(self, model, device):
        self.model = model.to(device)
        self.device = device
        
        # 损失函数
        self.criterion = nn.CrossEntropyLoss()
        
        # 优化器
        self.optimizer = optim.AdamW(
            self.model.parameters(), 
            lr=1e-4, 
            weight_decay=1e-5
        )
        
        # 学习率调度器
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='max', factor=0.5, patience=5, verbose=True
        )
        
        # 训练历史
        self.history = {
            'train_loss': [], 'val_loss': [],
            'train_acc': [], 'val_acc': [],
            'topo_acc': [], 'fused_acc': []
        }
    
    def train_epoch(self, train_loader):
        """训练一个epoch"""
        self.model.train()
        
        total_loss = 0.0
        correct_fused = 0
        correct_topo = 0
        total = 0
        
        for batch in tqdm(train_loader, desc="Training"):
            topo_maps = batch['topographic_maps'].to(self.device)
            temporal_seq = batch['temporal_sequences'].to(self.device)
            labels = batch['label'].to(self.device)
            
            self.optimizer.zero_grad()
            
            # 前向传播
            outputs = self.model(topo_maps, temporal_seq)
            
            # 计算损失 - 多任务学习
            fused_loss = self.criterion(outputs['fused_logits'], labels)
            topo_loss = self.criterion(outputs['topo_logits'], labels)
            total_loss_batch = 0.7 * fused_loss + 0.3 * topo_loss
            
            # 反向传播
            total_loss_batch.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            self.optimizer.step()
            
            # 统计
            total_loss += total_loss_batch.item()
            
            _, pred_fused = torch.max(outputs['fused_logits'].data, 1)
            _, pred_topo = torch.max(outputs['topo_logits'].data, 1)
            
            total += labels.size(0)
            correct_fused += (pred_fused == labels).sum().item()
            correct_topo += (pred_topo == labels).sum().item()
        
        avg_loss = total_loss / len(train_loader)
        fused_acc = 100.0 * correct_fused / total
        topo_acc = 100.0 * correct_topo / total
        
        return avg_loss, fused_acc, topo_acc
    
    def validate_epoch(self, val_loader):
        """验证一个epoch"""
        self.model.eval()
        
        total_loss = 0.0
        correct_fused = 0
        correct_topo = 0
        total = 0
        all_predictions = []
        all_labels = []
        all_probabilities = []
        
        with torch.no_grad():
            for batch in val_loader:
                topo_maps = batch['topographic_maps'].to(self.device)
                temporal_seq = batch['temporal_sequences'].to(self.device)
                labels = batch['label'].to(self.device)
                
                # 前向传播
                outputs = self.model(topo_maps, temporal_seq)
                
                # 计算损失
                fused_loss = self.criterion(outputs['fused_logits'], labels)
                topo_loss = self.criterion(outputs['topo_logits'], labels)
                total_loss_batch = 0.7 * fused_loss + 0.3 * topo_loss
                
                total_loss += total_loss_batch.item()
                
                # 预测
                probabilities = torch.softmax(outputs['fused_logits'], dim=1)
                _, pred_fused = torch.max(outputs['fused_logits'].data, 1)
                _, pred_topo = torch.max(outputs['topo_logits'].data, 1)
                
                total += labels.size(0)
                correct_fused += (pred_fused == labels).sum().item()
                correct_topo += (pred_topo == labels).sum().item()
                
                # 收集结果
                all_predictions.extend(pred_fused.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
                all_probabilities.extend(probabilities[:, 1].cpu().numpy())
        
        avg_loss = total_loss / len(val_loader)
        fused_acc = 100.0 * correct_fused / total
        topo_acc = 100.0 * correct_topo / total
        
        return avg_loss, fused_acc, topo_acc, all_predictions, all_labels, all_probabilities
    
    def train(self, train_loader, val_loader, num_epochs=40):
        """完整训练循环"""
        print(f"开始增强特征提取训练，共 {num_epochs} 个epoch...")
        
        best_val_acc = 0.0
        
        for epoch in range(num_epochs):
            print(f"\nEpoch {epoch+1}/{num_epochs}")
            
            # 训练
            train_loss, train_fused_acc, train_topo_acc = self.train_epoch(train_loader)
            
            # 验证
            val_loss, val_fused_acc, val_topo_acc, val_preds, val_labels, val_probs = self.validate_epoch(val_loader)
            
            # 更新学习率
            self.scheduler.step(val_fused_acc)
            
            # 记录历史
            self.history['train_loss'].append(train_loss)
            self.history['val_loss'].append(val_loss)
            self.history['train_acc'].append(train_fused_acc)
            self.history['val_acc'].append(val_fused_acc)
            self.history['topo_acc'].append(val_topo_acc)
            self.history['fused_acc'].append(val_fused_acc)
            
            # 打印结果
            print(f"Train Loss: {train_loss:.4f}, Fused Acc: {train_fused_acc:.2f}%, Topo Acc: {train_topo_acc:.2f}%")
            print(f"Val Loss: {val_loss:.4f}, Fused Acc: {val_fused_acc:.2f}%, Topo Acc: {val_topo_acc:.2f}%")
            
            # 保存最佳模型
            if val_fused_acc > best_val_acc:
                best_val_acc = val_fused_acc
                torch.save({
                    'model_state_dict': self.model.state_dict(),
                    'val_acc': best_val_acc,
                    'history': self.history
                }, 'enhanced_multimodal_best_model.pth')
                print(f"✓ 保存最佳模型 (融合准确率: {best_val_acc:.2f}%)")
        
        print(f"\n训练完成！最佳验证准确率: {best_val_acc:.2f}%")
        return self.history, val_preds, val_labels, val_probs

def main():
    """主函数"""
    print("🚀 Enhanced Feature Extraction with Attention and LSTM")
    print("="*70)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # 数据路径
    data_dirs = [
        "1252141/EEGs_Guinea-Bissau",
        "1252141/EEGs_Nigeria"
    ]
    metadata_files = [
        "1252141/metadata_guineabissau.csv",
        "1252141/metadata_nigeria.csv"
    ]
    
    try:
        # 创建增强数据集
        train_dataset = EnhancedEEGDataset(data_dirs, metadata_files, split='train')
        test_dataset = EnhancedEEGDataset(data_dirs, metadata_files, split='test')
        
        # 创建数据加载器
        train_loader = torch.utils.data.DataLoader(
            train_dataset, batch_size=8, shuffle=True, num_workers=0
        )
        test_loader = torch.utils.data.DataLoader(
            test_dataset, batch_size=8, shuffle=False, num_workers=0
        )
        
        print("✅ 增强数据集创建成功")
        
    except Exception as e:
        print(f"❌ 数据集创建失败: {e}")
        return
    
    # 创建增强模型
    model = EnhancedMultiModalClassifier(num_classes=2)
    param_count = sum(p.numel() for p in model.parameters())
    print(f"增强多模态模型创建成功，参数数量: {param_count:,}")
    
    # 创建训练器
    trainer = EnhancedTrainer(model, device)
    
    # 开始训练
    history, val_preds, val_labels, val_probs = trainer.train(
        train_loader, test_loader, num_epochs=30
    )
    
    print("\n✅ Enhanced Feature Extraction Training 完成！")
    print("📊 结果文件:")
    print("  - enhanced_multimodal_best_model.pth: 增强多模态模型")

if __name__ == "__main__":
    main()
