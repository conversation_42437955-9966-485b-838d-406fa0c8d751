# 多通道EEG数据处理引擎技术文档

## 系统概述

多通道EEG数据处理引擎 (`core/eeg_processing.py`) 是EEG源定位系统的数据预处理核心，负责将原始EEG信号转换为适合源定位分析的高质量数据。该引擎支持多种EEG数据格式，实现完整的预处理、滤波、伪迹去除和质量评估流程。

## 核心架构设计

### 1. EEGProcessor (EEG数据处理引擎主类)

```python
class EEGProcessor:
    def process_eeg_data(self, raw_data, subject_id) -> Dict
    def _handle_bad_channels(self, raw_data, channel_quality) -> mne.io.Raw
    def _final_quality_check(self, processed_data) -> Dict
    def _generate_processing_report(self, subject_id, channel_quality, final_quality) -> Dict
```

**核心处理流程：**
- 数据预处理（去直流、去趋势、基线校正）
- 通道质量评估和坏通道处理
- 多级数字滤波（高通、低通、工频）
- 多模态伪迹去除（眼电、肌电、心电、运动）
- 最终质量验证和报告生成

**伪代码实现：**
```
完整EEG数据处理(原始数据, 被试ID):
    数据预处理:
        去除直流分量
        去趋势处理
        基线校正
    
    通道质量评估:
        信号幅度检查
        信号方差分析
        通道间相关性评估
        频谱质量分析
        识别坏通道
    
    坏通道处理:
        标记坏通道
        球面样条插值修复
        重置坏通道标记
    
    数字滤波:
        高通滤波(0.5Hz) - 去除直流漂移
        低通滤波(100Hz) - 抗混叠
        工频滤波(50Hz) - 去除电源干扰
    
    伪迹去除:
        ICA眼电伪迹去除
        肌电伪迹抑制
        心电伪迹去除
        运动伪迹处理
    
    最终质量检查:
        信噪比评估
        数据完整性检查
        频谱质量验证
        生成质量评分
    
    生成处理报告:
        处理步骤记录
        通道统计信息
        质量指标汇总
        处理建议生成
    
    返回处理结果和质量报告
```

### 2. Preprocessor (数据预处理器)

```python
class Preprocessor:
    def preprocess(self, raw_data) -> mne.io.Raw
    def _remove_dc_offset(self, raw_data) -> mne.io.Raw
    def _detrend_data(self, raw_data) -> mne.io.Raw
    def _baseline_correction(self, raw_data) -> mne.io.Raw
```

**预处理策略：**
- **直流分量去除**：消除电极偏移和放大器漂移
- **去趋势处理**：移除长期趋势和低频漂移
- **基线校正**：标准化信号基线水平

**伪代码实现：**
```
数据预处理(原始EEG数据):
    去除直流分量:
        计算每通道时间平均值
        从信号中减去直流偏移
        创建校正后的数据对象
    
    去趋势处理:
        对每个通道应用线性去趋势
        使用scipy.signal.detrend函数
        保持信号的高频特征
    
    基线校正:
        选择基线时间窗口(前100个样本)
        计算基线平均值
        从整个信号中减去基线
        处理短数据的特殊情况
    
    返回预处理后的数据
```

### 3. FilterBank (数字滤波器组)

```python
class FilterBank:
    def apply_filters(self, raw_data) -> mne.io.Raw
```

**滤波器设计原则：**
- **高通滤波器**：截止频率0.5Hz，去除直流漂移和慢波干扰
- **低通滤波器**：截止频率100Hz，防止混叠和高频噪声
- **工频滤波器**：50Hz陷波，去除电源线干扰
- **FIR滤波器**：零相位失真，保持信号时域特性

**伪代码实现：**
```
应用数字滤波器(EEG数据):
    高通滤波:
        设计FIR高通滤波器(0.5Hz)
        应用零相位滤波
        去除低频漂移和直流成分
    
    低通滤波:
        设计FIR低通滤波器(100Hz)
        应用抗混叠滤波
        保留EEG有效频段
    
    工频滤波:
        设计50Hz陷波滤波器
        去除电源线干扰
        保持邻近频段信号
    
    验证滤波效果:
        检查频谱变化
        确保信号完整性
    
    返回滤波后数据
```

### 4. ArtifactRemover (伪迹去除器)

```python
class ArtifactRemover:
    def remove_artifacts(self, raw_data) -> mne.io.Raw
    def _remove_ocular_artifacts_ica(self, raw_data) -> mne.io.Raw
    def _remove_muscle_artifacts(self, raw_data) -> mne.io.Raw
    def _remove_cardiac_artifacts(self, raw_data) -> mne.io.Raw
    def _remove_movement_artifacts(self, raw_data) -> mne.io.Raw
```

**伪迹类型和处理策略：**

#### 4.1 眼电伪迹去除
```python
眼电伪迹特征 = {
    '频率范围': '0.1-10 Hz',
    '幅度特征': '高幅度，前额区域明显',
    '时间特征': '眨眼和眼动相关',
    '空间分布': '前额和眼周电极'
}
```

**伪代码实现：**
```
ICA眼电伪迹去除(EEG数据):
    配置ICA参数:
        成分数量: min(20, 通道数)
        算法: FastICA
        随机种子: 42
    
    拟合ICA模型:
        分解信号为独立成分
        计算混合矩阵和解混矩阵
    
    自动检测眼电成分:
        计算EOG相关性评分
        设置检测阈值(3.0)
        识别眼电相关成分
    
    去除眼电成分:
        排除检测到的眼电成分
        重建无眼电伪迹的信号
        保持其他脑电成分
    
    返回清洁信号
```

#### 4.2 肌电伪迹去除
```python
肌电伪迹特征 = {
    '频率范围': '30-100 Hz',
    '幅度特征': '高频高幅度',
    '时间特征': '突发性，短时间',
    '空间分布': '颞部和额部电极'
}
```

**伪代码实现：**
```
肌电伪迹去除(EEG数据):
    检测肌电伪迹:
        计算高频功率(30-100Hz)
        设置检测阈值(95百分位数)
        标记异常高功率时间点
    
    抑制肌电伪迹:
        对检测到的伪迹时间点:
            使用线性插值替换
            保持信号连续性
            避免引入新的伪迹
    
    验证处理效果:
        检查高频功率降低
        确保低频信号保持
    
    返回处理后信号
```

#### 4.3 心电伪迹去除
```python
心电伪迹特征 = {
    '频率范围': '0.8-2.0 Hz',
    '幅度特征': '周期性脉冲',
    '时间特征': '心率相关(60-100 BPM)',
    '空间分布': '左侧和后部电极'
}
```

#### 4.4 运动伪迹去除
```python
运动伪迹特征 = {
    '频率范围': '0.1-10 Hz',
    '幅度特征': '大幅度突变',
    '时间特征': '突发性，不规则',
    '空间分布': '多通道同时出现'
}
```

### 5. ChannelQualityAssessor (通道质量评估器)

```python
class ChannelQualityAssessor:
    def assess_channels(self, raw_data) -> Dict
    def _assess_amplitude_quality(self, data) -> Dict
    def _assess_variance_quality(self, data) -> Dict
    def _assess_correlation_quality(self, data) -> Dict
    def _assess_spectral_quality(self, data, sfreq) -> Dict
    def _identify_bad_channels(self, data, ch_names, quality_metrics) -> List[str]
```

**质量评估指标：**

#### 5.1 信号幅度质量
```python
幅度质量评估 = {
    '指标': 'RMS幅度',
    '正常范围': '中位数 ± 3×MAD',
    '异常检测': '幅度过高或过低',
    '处理方法': '标记为坏通道'
}
```

#### 5.2 信号方差质量
```python
方差质量评估 = {
    '指标': '信号方差',
    '正常范围': 'log方差中位数 ± 3×MAD',
    '异常检测': '方差异常高或低',
    '阈值': '可配置(默认3.0)'
}
```

#### 5.3 通道间相关性
```python
相关性质量评估 = {
    '指标': '与其他通道的平均相关性',
    '正常范围': '> 0.3',
    '异常检测': '相关性过低',
    '意义': '电极接触不良或信号异常'
}
```

#### 5.4 频谱质量
```python
频谱质量评估 = {
    '频段': {
        'delta': '1-4 Hz',
        'theta': '4-8 Hz', 
        'alpha': '8-13 Hz',
        'beta': '13-30 Hz',
        'gamma': '30-100 Hz'
    },
    '评估': '各频段功率分布合理性',
    '异常': '功率为零或无穷大'
}
```

**伪代码实现：**
```
通道质量评估(EEG数据):
    信号幅度评估:
        计算每通道RMS幅度
        使用中位数绝对偏差检测异常
        标记幅度异常通道
    
    信号方差评估:
        计算每通道方差
        对数变换处理偏态分布
        基于MAD检测方差异常
    
    通道间相关性评估:
        计算通道间相关矩阵
        计算平均相关性
        识别低相关性通道
    
    频谱质量评估:
        计算功率谱密度
        分析各频段功率
        检测频谱异常
    
    综合评估:
        收集所有异常通道
        计算通道质量评分
        生成质量报告
    
    返回质量评估结果
```

## 质量控制机制

### 1. 多层质量检查
```python
质量控制流程 = {
    '预处理质量': '基本信号质量检查',
    '通道质量': '单通道异常检测',
    '伪迹质量': '伪迹去除效果验证',
    '最终质量': '整体数据质量评估'
}
```

### 2. 自适应阈值设置
- **统计学阈值**：基于数据分布自动设置
- **鲁棒统计**：使用中位数和MAD抗异常值
- **可配置参数**：支持用户自定义阈值
- **多标准融合**：综合多个质量指标

### 3. 质量评分系统
```python
质量评分计算 = {
    'SNR评分': '信噪比归一化评分',
    '完整性评分': '数据完整性评分',
    '频谱评分': '频谱质量评分',
    '总体评分': '加权平均评分'
}
```

## 性能优化策略

### 1. 计算优化
- **向量化操作**：利用NumPy向量化加速
- **并行处理**：多通道并行计算
- **内存优化**：避免不必要的数据拷贝
- **缓存机制**：缓存重复计算结果

### 2. 算法优化
- **自适应参数**：根据数据特征调整参数
- **早停机制**：质量达标时提前终止
- **分块处理**：大数据集分块处理
- **稀疏计算**：利用数据稀疏性

### 3. 内存管理
- **流式处理**：大文件流式读取
- **内存映射**：使用内存映射文件
- **垃圾回收**：及时释放内存
- **数据类型优化**：使用合适的数据精度

## 配置参数详解

### EEG处理配置
```yaml
eeg_processing:
  # 采样率配置
  sampling_rate: 250                    # Hz
  
  # 滤波参数
  filtering:
    highpass: 0.5                       # Hz，高通截止频率
    lowpass: 100                        # Hz，低通截止频率
    notch: 50                           # Hz，工频滤波
    
  # 预处理参数
  preprocessing:
    remove_dc: true                     # 去除直流分量
    detrend: true                       # 去趋势处理
    baseline_correction: true           # 基线校正
    
  # 伪迹去除
  artifact_removal:
    enable_ica: true                    # 启用ICA
    ica_components: 20                  # ICA成分数
    enable_ssp: true                    # 启用SSP
    
  # 通道质量评估
  channel_quality:
    bad_channel_threshold: 3.0          # 坏通道检测阈值
    correlation_threshold: 0.3          # 相关性阈值
```

## 验证和测试

### 1. 算法验证
- **仿真数据测试**：使用已知特征的仿真EEG
- **标准数据集**：在公开数据集上验证
- **专家评估**：与专家手动处理结果对比
- **跨被试验证**：不同被试数据的一致性

### 2. 性能测试
- **处理速度**：不同数据量的处理时间
- **内存使用**：峰值内存占用监控
- **准确性评估**：伪迹去除准确率
- **稳定性测试**：长时间运行稳定性

### 3. 质量验证
- **信噪比改善**：处理前后SNR对比
- **伪迹去除效果**：各类伪迹的去除率
- **信号保真度**：有用信号的保持程度
- **频谱完整性**：频域特征的保持情况

## 扩展接口设计

### 1. 自定义处理器
```python
def register_custom_preprocessor(name: str, processor_class: type):
    """注册自定义预处理器"""
    pass

def register_custom_filter(name: str, filter_func: callable):
    """注册自定义滤波器"""
    pass
```

### 2. 质量评估扩展
```python
def add_quality_metric(metric_name: str, metric_func: callable):
    """添加自定义质量评估指标"""
    pass

def set_quality_threshold(metric: str, threshold: float):
    """设置质量阈值"""
    pass
```

### 3. 伪迹去除扩展
```python
def register_artifact_remover(artifact_type: str, remover_class: type):
    """注册自定义伪迹去除器"""
    pass
```
