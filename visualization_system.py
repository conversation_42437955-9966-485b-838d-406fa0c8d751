#!/usr/bin/env python3
"""
3D Visualization System for Epilepsy Lesion Localization
with Cubic Bounding Box Performance Analysis

This module provides comprehensive visualization tools including:
- Interactive 3D rendering of bounding boxes and lesions
- Performance dashboard with quantitative metrics
- Case study analysis with detailed comparisons
- Error analysis and alignment visualization
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from mpl_toolkits.mplot3d import Axes3D
from mpl_toolkits.mplot3d.art3d import Poly3DCollection
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import nibabel as nib
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

from training_pipeline import EpilepsyLocalizationModel
from epilepsy_localization_network import BoundingBoxLoss

class BoundingBoxVisualizer:
    """3D visualization system for bounding box performance analysis"""
    
    def __init__(self, model_path: str = None, device: torch.device = None):
        self.device = device or torch.device('cpu')
        
        # Load trained model if provided
        if model_path and Path(model_path).exists():
            self.model = EpilepsyLocalizationModel()
            checkpoint = torch.load(model_path, map_location=self.device)
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.model.eval()
            print(f"Loaded model from {model_path}")
        else:
            self.model = None
            print("No model loaded - using dummy predictions for visualization")
        
        # Initialize loss function for metrics
        self.bbox_loss = BoundingBoxLoss()
        
        # Color schemes
        self.colors = {
            'lesion': 'red',
            'bbox_pred': 'blue',
            'bbox_true': 'green',
            'overlap': 'purple'
        }
    
    def create_cube_vertices(self, center: np.ndarray, size: np.ndarray) -> np.ndarray:
        """Create vertices for a 3D cube"""
        cx, cy, cz = center
        sx, sy, sz = size / 2
        
        vertices = np.array([
            [cx - sx, cy - sy, cz - sz],  # 0
            [cx + sx, cy - sy, cz - sz],  # 1
            [cx + sx, cy + sy, cz - sz],  # 2
            [cx - sx, cy + sy, cz - sz],  # 3
            [cx - sx, cy - sy, cz + sz],  # 4
            [cx + sx, cy - sy, cz + sz],  # 5
            [cx + sx, cy + sy, cz + sz],  # 6
            [cx - sx, cy + sy, cz + sz],  # 7
        ])
        
        return vertices
    
    def create_cube_faces(self, vertices: np.ndarray) -> List[np.ndarray]:
        """Create faces for a 3D cube"""
        faces = [
            [vertices[0], vertices[1], vertices[2], vertices[3]],  # Bottom
            [vertices[4], vertices[5], vertices[6], vertices[7]],  # Top
            [vertices[0], vertices[1], vertices[5], vertices[4]],  # Front
            [vertices[2], vertices[3], vertices[7], vertices[6]],  # Back
            [vertices[1], vertices[2], vertices[6], vertices[5]],  # Right
            [vertices[4], vertices[7], vertices[3], vertices[0]],  # Left
        ]
        
        return faces
    
    def visualize_3d_bounding_box(self, lesion_mask: np.ndarray, 
                                 pred_center: np.ndarray, pred_size: np.ndarray,
                                 title: str = "3D Bounding Box Visualization",
                                 save_path: str = None) -> go.Figure:
        """
        Create interactive 3D visualization of lesion and predicted bounding box
        """
        # Extract lesion coordinates
        lesion_coords = np.where(lesion_mask > 0)
        
        # Create figure
        fig = go.Figure()
        
        # Add lesion points
        if len(lesion_coords[0]) > 0:
            # Subsample for performance (max 1000 points)
            n_points = len(lesion_coords[0])
            if n_points > 1000:
                indices = np.random.choice(n_points, 1000, replace=False)
                lesion_x = lesion_coords[0][indices]
                lesion_y = lesion_coords[1][indices]
                lesion_z = lesion_coords[2][indices]
            else:
                lesion_x = lesion_coords[0]
                lesion_y = lesion_coords[1]
                lesion_z = lesion_coords[2]
            
            fig.add_trace(go.Scatter3d(
                x=lesion_x, y=lesion_y, z=lesion_z,
                mode='markers',
                marker=dict(
                    size=2,
                    color='red',
                    opacity=0.6
                ),
                name='Ground Truth Lesion',
                hovertemplate='Lesion<br>X: %{x}<br>Y: %{y}<br>Z: %{z}<extra></extra>'
            ))
        
        # Add predicted bounding box
        bbox_vertices = self.create_cube_vertices(pred_center, pred_size)
        
        # Create wireframe for bounding box
        edges = [
            [0, 1], [1, 2], [2, 3], [3, 0],  # Bottom face
            [4, 5], [5, 6], [6, 7], [7, 4],  # Top face
            [0, 4], [1, 5], [2, 6], [3, 7]   # Vertical edges
        ]
        
        for edge in edges:
            start, end = edge
            fig.add_trace(go.Scatter3d(
                x=[bbox_vertices[start][0], bbox_vertices[end][0]],
                y=[bbox_vertices[start][1], bbox_vertices[end][1]],
                z=[bbox_vertices[start][2], bbox_vertices[end][2]],
                mode='lines',
                line=dict(color='blue', width=4),
                showlegend=False,
                hoverinfo='skip'
            ))
        
        # Add bounding box center
        fig.add_trace(go.Scatter3d(
            x=[pred_center[0]], y=[pred_center[1]], z=[pred_center[2]],
            mode='markers',
            marker=dict(
                size=8,
                color='blue',
                symbol='diamond'
            ),
            name='Predicted Center',
            hovertemplate='Predicted Center<br>X: %{x:.1f}<br>Y: %{y:.1f}<br>Z: %{z:.1f}<extra></extra>'
        ))
        
        # Compute and add true lesion center
        if len(lesion_coords[0]) > 0:
            true_center = np.array([
                np.mean(lesion_coords[0]),
                np.mean(lesion_coords[1]),
                np.mean(lesion_coords[2])
            ])
            
            fig.add_trace(go.Scatter3d(
                x=[true_center[0]], y=[true_center[1]], z=[true_center[2]],
                mode='markers',
                marker=dict(
                    size=8,
                    color='red',
                    symbol='diamond'
                ),
                name='True Center',
                hovertemplate='True Center<br>X: %{x:.1f}<br>Y: %{y:.1f}<br>Z: %{z:.1f}<extra></extra>'
            ))
        
        # Update layout
        fig.update_layout(
            title=title,
            scene=dict(
                xaxis_title='X (voxels)',
                yaxis_title='Y (voxels)',
                zaxis_title='Z (voxels)',
                aspectmode='cube',
                camera=dict(
                    eye=dict(x=1.5, y=1.5, z=1.5)
                )
            ),
            width=800,
            height=600,
            showlegend=True
        )
        
        if save_path:
            fig.write_html(save_path)
            print(f"3D visualization saved to {save_path}")
        
        return fig
    
    def create_performance_dashboard(self, metrics_data: Dict[str, List[float]], 
                                   save_path: str = None) -> go.Figure:
        """Create comprehensive performance dashboard"""
        
        # Create subplots
        fig = make_subplots(
            rows=2, cols=3,
            subplot_titles=[
                'Dice Score Distribution', 'IoU Score Distribution', 'Center Error Distribution',
                'Training Curves', 'Box Size Analysis', 'Performance Summary'
            ],
            specs=[
                [{"type": "histogram"}, {"type": "histogram"}, {"type": "histogram"}],
                [{"type": "scatter"}, {"type": "box"}, {"type": "bar"}]
            ]
        )
        
        # 1. Dice Score Distribution
        fig.add_trace(
            go.Histogram(
                x=metrics_data.get('dice_scores', []),
                nbinsx=20,
                name='Dice Scores',
                marker_color='blue',
                opacity=0.7
            ),
            row=1, col=1
        )
        
        # 2. IoU Score Distribution
        fig.add_trace(
            go.Histogram(
                x=metrics_data.get('iou_scores', []),
                nbinsx=20,
                name='IoU Scores',
                marker_color='green',
                opacity=0.7
            ),
            row=1, col=2
        )
        
        # 3. Center Error Distribution
        fig.add_trace(
            go.Histogram(
                x=metrics_data.get('center_errors', []),
                nbinsx=20,
                name='Center Errors',
                marker_color='red',
                opacity=0.7
            ),
            row=1, col=3
        )
        
        # 4. Training Curves
        if 'epochs' in metrics_data:
            epochs = metrics_data['epochs']
            fig.add_trace(
                go.Scatter(
                    x=epochs,
                    y=metrics_data.get('train_dice', []),
                    mode='lines',
                    name='Train Dice',
                    line=dict(color='blue')
                ),
                row=2, col=1
            )
            fig.add_trace(
                go.Scatter(
                    x=epochs,
                    y=metrics_data.get('val_dice', []),
                    mode='lines',
                    name='Val Dice',
                    line=dict(color='red')
                ),
                row=2, col=1
            )
        
        # 5. Box Size Analysis
        if 'box_sizes' in metrics_data:
            box_sizes = metrics_data['box_sizes']
            fig.add_trace(
                go.Box(
                    y=[size[0] for size in box_sizes],
                    name='Width',
                    marker_color='lightblue'
                ),
                row=2, col=2
            )
            fig.add_trace(
                go.Box(
                    y=[size[1] for size in box_sizes],
                    name='Height',
                    marker_color='lightgreen'
                ),
                row=2, col=2
            )
            fig.add_trace(
                go.Box(
                    y=[size[2] for size in box_sizes],
                    name='Depth',
                    marker_color='lightcoral'
                ),
                row=2, col=2
            )
        
        # 6. Performance Summary
        if 'dice_scores' in metrics_data and 'iou_scores' in metrics_data:
            dice_mean = np.mean(metrics_data['dice_scores'])
            iou_mean = np.mean(metrics_data['iou_scores'])
            center_error_mean = np.mean(metrics_data.get('center_errors', [0]))
            
            fig.add_trace(
                go.Bar(
                    x=['Dice Score', 'IoU Score', 'Center Error (norm)'],
                    y=[dice_mean, iou_mean, 1 - min(center_error_mean / 50, 1)],  # Normalize center error
                    marker_color=['blue', 'green', 'red'],
                    text=[f'{dice_mean:.3f}', f'{iou_mean:.3f}', f'{center_error_mean:.1f}'],
                    textposition='auto'
                ),
                row=2, col=3
            )
        
        # Update layout
        fig.update_layout(
            title_text="Epilepsy Localization Performance Dashboard",
            showlegend=True,
            height=800,
            width=1200
        )
        
        if save_path:
            fig.write_html(save_path)
            print(f"Performance dashboard saved to {save_path}")
        
        return fig
    
    def create_case_study_analysis(self, case_data: Dict, save_path: str = None) -> plt.Figure:
        """Create detailed case study analysis with multiple views"""
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle(f"Case Study: Subject {case_data.get('subject_id', 'Unknown')}", fontsize=16)
        
        lesion_mask = case_data['lesion_mask']
        pred_center = case_data['pred_center']
        pred_size = case_data['pred_size']
        
        # Create bounding box mask
        bbox_mask = np.zeros_like(lesion_mask)
        cx, cy, cz = pred_center.astype(int)
        sx, sy, sz = (pred_size / 2).astype(int)
        
        x_min, x_max = max(0, cx - sx), min(lesion_mask.shape[0], cx + sx)
        y_min, y_max = max(0, cy - sy), min(lesion_mask.shape[1], cy + sy)
        z_min, z_max = max(0, cz - sz), min(lesion_mask.shape[2], cz + sz)
        
        bbox_mask[x_min:x_max, y_min:y_max, z_min:z_max] = 1
        
        # Axial view (Z slice)
        z_slice = int(pred_center[2])
        axes[0, 0].imshow(lesion_mask[:, :, z_slice].T, cmap='Reds', alpha=0.7, origin='lower')
        axes[0, 0].contour(bbox_mask[:, :, z_slice].T, colors='blue', linewidths=2, origin='lower')
        axes[0, 0].plot(pred_center[0], pred_center[1], 'bo', markersize=8, label='Predicted Center')
        axes[0, 0].set_title(f'Axial View (Z={z_slice})')
        axes[0, 0].set_xlabel('X')
        axes[0, 0].set_ylabel('Y')
        axes[0, 0].legend()
        
        # Coronal view (Y slice)
        y_slice = int(pred_center[1])
        axes[0, 1].imshow(lesion_mask[:, y_slice, :].T, cmap='Reds', alpha=0.7, origin='lower')
        axes[0, 1].contour(bbox_mask[:, y_slice, :].T, colors='blue', linewidths=2, origin='lower')
        axes[0, 1].plot(pred_center[0], pred_center[2], 'bo', markersize=8, label='Predicted Center')
        axes[0, 1].set_title(f'Coronal View (Y={y_slice})')
        axes[0, 1].set_xlabel('X')
        axes[0, 1].set_ylabel('Z')
        axes[0, 1].legend()
        
        # Sagittal view (X slice)
        x_slice = int(pred_center[0])
        axes[0, 2].imshow(lesion_mask[x_slice, :, :].T, cmap='Reds', alpha=0.7, origin='lower')
        axes[0, 2].contour(bbox_mask[x_slice, :, :].T, colors='blue', linewidths=2, origin='lower')
        axes[0, 2].plot(pred_center[1], pred_center[2], 'bo', markersize=8, label='Predicted Center')
        axes[0, 2].set_title(f'Sagittal View (X={x_slice})')
        axes[0, 2].set_xlabel('Y')
        axes[0, 2].set_ylabel('Z')
        axes[0, 2].legend()
        
        # Metrics comparison
        dice_score = self.compute_dice_score(bbox_mask, lesion_mask)
        iou_score = self.compute_iou_score(bbox_mask, lesion_mask)
        
        metrics = ['Dice Score', 'IoU Score', 'Volume Ratio']
        values = [dice_score, iou_score, np.sum(bbox_mask) / np.sum(lesion_mask)]
        
        bars = axes[1, 0].bar(metrics, values, color=['blue', 'green', 'orange'])
        axes[1, 0].set_title('Performance Metrics')
        axes[1, 0].set_ylabel('Score')
        axes[1, 0].set_ylim(0, 1.2)
        
        # Add value labels on bars
        for bar, value in zip(bars, values):
            axes[1, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.02,
                           f'{value:.3f}', ha='center', va='bottom')
        
        # EEG topographic map (if available)
        if 'eeg_topomap' in case_data:
            im = axes[1, 1].imshow(case_data['eeg_topomap'], cmap='RdBu_r')
            axes[1, 1].set_title('EEG Topographic Map')
            axes[1, 1].axis('off')
            plt.colorbar(im, ax=axes[1, 1])
        else:
            axes[1, 1].text(0.5, 0.5, 'EEG Topomap\nNot Available', 
                           ha='center', va='center', transform=axes[1, 1].transAxes)
            axes[1, 1].set_title('EEG Topographic Map')
        
        # Error analysis
        true_center = np.array([
            np.mean(np.where(lesion_mask)[0]),
            np.mean(np.where(lesion_mask)[1]),
            np.mean(np.where(lesion_mask)[2])
        ])
        
        center_error = np.linalg.norm(pred_center - true_center)
        
        error_data = {
            'Center Error (voxels)': center_error,
            'Size Error X': abs(pred_size[0] - np.ptp(np.where(lesion_mask)[0])),
            'Size Error Y': abs(pred_size[1] - np.ptp(np.where(lesion_mask)[1])),
            'Size Error Z': abs(pred_size[2] - np.ptp(np.where(lesion_mask)[2]))
        }
        
        error_names = list(error_data.keys())
        error_values = list(error_data.values())
        
        bars = axes[1, 2].bar(error_names, error_values, color='red', alpha=0.7)
        axes[1, 2].set_title('Error Analysis')
        axes[1, 2].set_ylabel('Error (voxels)')
        axes[1, 2].tick_params(axis='x', rotation=45)
        
        # Add value labels
        for bar, value in zip(bars, error_values):
            axes[1, 2].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                           f'{value:.1f}', ha='center', va='bottom')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Case study analysis saved to {save_path}")
        
        return fig
    
    def compute_dice_score(self, pred_mask: np.ndarray, target_mask: np.ndarray) -> float:
        """Compute Dice similarity coefficient"""
        intersection = np.sum(pred_mask * target_mask)
        union = np.sum(pred_mask) + np.sum(target_mask)
        
        if union == 0:
            return 1.0 if np.sum(target_mask) == 0 else 0.0
        
        dice = (2.0 * intersection) / union
        return dice
    
    def compute_iou_score(self, pred_mask: np.ndarray, target_mask: np.ndarray) -> float:
        """Compute 3D Intersection over Union"""
        intersection = np.sum(pred_mask * target_mask)
        union = np.sum(pred_mask) + np.sum(target_mask) - intersection
        
        if union == 0:
            return 1.0 if np.sum(target_mask) == 0 else 0.0
        
        iou = intersection / union
        return iou

def main():
    """Test the visualization system"""
    print("Testing 3D Visualization System")
    print("="*40)
    
    # Create dummy data for testing
    lesion_mask = np.zeros((256, 256, 256))
    lesion_mask[100:140, 120:160, 110:150] = 1  # 40x40x40 lesion
    
    pred_center = np.array([120, 140, 130])
    pred_size = np.array([45, 45, 45])
    
    # Initialize visualizer
    visualizer = BoundingBoxVisualizer()
    
    # Test 3D visualization
    print("1. Creating 3D bounding box visualization...")
    fig_3d = visualizer.visualize_3d_bounding_box(
        lesion_mask, pred_center, pred_size,
        title="Test 3D Bounding Box Visualization",
        save_path="test_3d_bbox.html"
    )
    
    # Test performance dashboard
    print("2. Creating performance dashboard...")
    metrics_data = {
        'dice_scores': np.random.beta(8, 2, 100).tolist(),  # Skewed towards high values
        'iou_scores': np.random.beta(6, 3, 100).tolist(),
        'center_errors': np.random.gamma(2, 5, 100).tolist(),
        'epochs': list(range(50)),
        'train_dice': np.random.beta(8, 2, 50).tolist(),
        'val_dice': np.random.beta(7, 3, 50).tolist(),
        'box_sizes': [(np.random.normal(40, 10, 3)) for _ in range(100)]
    }
    
    fig_dashboard = visualizer.create_performance_dashboard(
        metrics_data,
        save_path="test_performance_dashboard.html"
    )
    
    # Test case study analysis
    print("3. Creating case study analysis...")
    case_data = {
        'subject_id': 'TEST_001',
        'lesion_mask': lesion_mask,
        'pred_center': pred_center,
        'pred_size': pred_size
    }
    
    fig_case = visualizer.create_case_study_analysis(
        case_data,
        save_path="test_case_study.png"
    )
    
    print("\n✓ All visualization components working correctly!")
    print("Generated files:")
    print("  - test_3d_bbox.html")
    print("  - test_performance_dashboard.html") 
    print("  - test_case_study.png")

if __name__ == "__main__":
    main()
