# EEG源定位系统依赖库清单

# 核心科学计算库
numpy>=1.21.0
scipy>=1.7.0
scikit-learn>=1.0.0
pandas>=1.3.0

# 神经影像处理库
nibabel>=3.2.0
nilearn>=0.8.0
mne>=1.0.0

# 图像处理库
SimpleITK>=2.1.0
opencv-python>=4.5.0
scikit-image>=0.18.0

# 可视化库
matplotlib>=3.4.0
plotly>=5.0.0
seaborn>=0.11.0

# 3D可视化 (可选，需要系统支持)
# mayavi>=4.7.0
# vtk>=9.0.0

# 并行计算库
joblib>=1.0.0
numba>=0.53.0

# 数据格式支持
h5py>=3.1.0
pyedflib>=0.1.20
xlrd>=2.0.0

# 配置管理
pyyaml>=5.4.0
configparser>=5.0.0

# 日志和调试
tqdm>=4.60.0
logging>=*******

# 测试框架
pytest>=6.2.0
pytest-cov>=2.12.0

# 文档生成
sphinx>=4.0.0
sphinx-rtd-theme>=0.5.0

# 代码质量
black>=21.0.0
flake8>=3.9.0
mypy>=0.910
