#!/usr/bin/env python3
"""
癫痫EEG时间特征分析
分析发作频率、持续时间、间隔等时间特征
"""

import numpy as np
import matplotlib.pyplot as plt
import mne
from datetime import datetime, timedelta
import warnings

warnings.filterwarnings('ignore')
mne.set_log_level('ERROR')

def parse_time_to_seconds(time_str):
    """将HH.MM.SS格式转换为秒数"""
    h, m, s = map(int, time_str.split('.'))
    return h * 3600 + m * 60 + s

def analyze_recording_timeline():
    """
    分析记录时间线
    """
    print("=== EEG记录时间分析 ===")
    
    # 根据提供的信息
    start_time_str = "19.39.33"
    end_time_str = "20.22.58"
    seizure_start_str = "19.58.36"
    seizure_end_str = "19.59.46"
    
    # 转换为秒数（从当天00:00:00开始）
    start_sec = parse_time_to_seconds(start_time_str)
    end_sec = parse_time_to_seconds(end_time_str)
    seizure_start_sec = parse_time_to_seconds(seizure_start_str)
    seizure_end_sec = parse_time_to_seconds(seizure_end_str)
    
    # 计算各种时间间隔
    total_recording_duration = end_sec - start_sec
    seizure_duration = seizure_end_sec - seizure_start_sec
    pre_seizure_duration = seizure_start_sec - start_sec
    post_seizure_duration = end_sec - seizure_end_sec
    
    print(f"记录开始时间: {start_time_str}")
    print(f"记录结束时间: {end_time_str}")
    print(f"总记录时长: {total_recording_duration}秒 ({total_recording_duration/60:.1f}分钟)")
    print()
    print(f"癫痫发作开始: {seizure_start_str}")
    print(f"癫痫发作结束: {seizure_end_str}")
    print(f"癫痫发作持续: {seizure_duration}秒 ({seizure_duration/60:.1f}分钟)")
    print()
    print(f"发作前记录时长: {pre_seizure_duration}秒 ({pre_seizure_duration/60:.1f}分钟)")
    print(f"发作后记录时长: {post_seizure_duration}秒 ({post_seizure_duration/60:.1f}分钟)")
    
    return {
        'total_duration': total_recording_duration,
        'seizure_duration': seizure_duration,
        'pre_seizure_duration': pre_seizure_duration,
        'post_seizure_duration': post_seizure_duration,
        'seizure_start_relative': pre_seizure_duration,
        'seizure_end_relative': pre_seizure_duration + seizure_duration
    }

def analyze_seizure_frequency(timeline_data):
    """
    分析癫痫发作频率
    """
    print("\n=== 癫痫发作频率分析 ===")
    
    total_hours = timeline_data['total_duration'] / 3600
    num_seizures = 1  # 根据文件名"Seizure n 1"，这是第1次发作
    
    print(f"记录时长: {total_hours:.2f}小时")
    print(f"观察到的癫痫发作次数: {num_seizures}次")
    print(f"发作频率: {num_seizures/total_hours:.2f}次/小时")
    print(f"发作频率: {num_seizures/(total_hours*24):.4f}次/天 (如果按此频率推算)")
    
    # 分析发作在记录中的位置
    seizure_position_percent = (timeline_data['seizure_start_relative'] / timeline_data['total_duration']) * 100
    print(f"发作发生在记录的{seizure_position_percent:.1f}%处")
    
    return {
        'seizures_per_hour': num_seizures/total_hours,
        'seizures_per_day_estimated': num_seizures/(total_hours/24),
        'seizure_position_percent': seizure_position_percent
    }

def analyze_seizure_characteristics(timeline_data):
    """
    分析癫痫发作特征
    """
    print("\n=== 癫痫发作特征分析 ===")
    
    seizure_duration = timeline_data['seizure_duration']
    
    print(f"发作持续时间: {seizure_duration}秒")
    print(f"发作持续时间: {seizure_duration/60:.2f}分钟")
    
    # 根据持续时间分类癫痫发作类型
    if seizure_duration < 30:
        seizure_type = "短暂发作 (<30秒)"
    elif seizure_duration < 120:
        seizure_type = "典型发作 (30秒-2分钟)"
    elif seizure_duration < 300:
        seizure_type = "延长发作 (2-5分钟)"
    else:
        seizure_type = "持续状态 (>5分钟)"
    
    print(f"发作类型分类: {seizure_type}")
    
    # 分析发作前后的观察时间
    pre_duration_min = timeline_data['pre_seizure_duration'] / 60
    post_duration_min = timeline_data['post_seizure_duration'] / 60
    
    print(f"发作前观察时间: {pre_duration_min:.1f}分钟")
    print(f"发作后观察时间: {post_duration_min:.1f}分钟")
    
    return {
        'duration_seconds': seizure_duration,
        'duration_minutes': seizure_duration/60,
        'type_classification': seizure_type,
        'pre_observation_minutes': pre_duration_min,
        'post_observation_minutes': post_duration_min
    }

def create_timeline_visualization(timeline_data):
    """
    创建时间线可视化
    """
    print("\n=== 创建时间线可视化 ===")
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))
    
    # 上图：整体时间线
    total_duration = timeline_data['total_duration']
    seizure_start = timeline_data['seizure_start_relative']
    seizure_end = timeline_data['seizure_end_relative']
    
    # 绘制整体记录时间线
    ax1.barh(0, total_duration, height=0.5, color='lightblue', alpha=0.7, label='总记录时间')
    ax1.barh(0, seizure_end - seizure_start, left=seizure_start, height=0.5, 
             color='red', alpha=0.8, label='癫痫发作期')
    
    # 添加时间标记
    time_points = [0, seizure_start, seizure_end, total_duration]
    time_labels = ['记录开始\n19:39:33', '发作开始\n19:58:36', '发作结束\n19:59:46', '记录结束\n20:22:58']
    
    for point, label in zip(time_points, time_labels):
        ax1.axvline(point, color='black', linestyle='--', alpha=0.7)
        ax1.text(point, 0.7, label, ha='center', va='bottom', fontsize=10, fontweight='bold')
    
    ax1.set_xlim(0, total_duration)
    ax1.set_ylim(-0.5, 1)
    ax1.set_xlabel('时间 (秒)', fontsize=12)
    ax1.set_title('EEG记录时间线', fontsize=14, fontweight='bold')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 下图：发作期详细分析
    seizure_duration = timeline_data['seizure_duration']
    
    # 将发作期分为几个阶段
    stages = ['发作前期\n(0-10s)', '发作高峰期\n(10-40s)', '发作后期\n(40-70s)']
    stage_durations = [10, 30, 30]
    stage_colors = ['orange', 'red', 'purple']
    
    left_pos = 0
    for i, (stage, duration, color) in enumerate(zip(stages, stage_durations, stage_colors)):
        ax2.barh(0, duration, left=left_pos, height=0.5, color=color, alpha=0.7, label=stage)
        ax2.text(left_pos + duration/2, 0, stage, ha='center', va='center', 
                fontsize=10, fontweight='bold', color='white')
        left_pos += duration
    
    ax2.set_xlim(0, seizure_duration)
    ax2.set_ylim(-0.5, 1)
    ax2.set_xlabel('发作时间 (秒)', fontsize=12)
    ax2.set_title('癫痫发作期详细分析 (70秒)', fontsize=14, fontweight='bold')
    ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('seizure_timeline_analysis.png', dpi=300, bbox_inches='tight')
    print("保存时间线分析图: seizure_timeline_analysis.png")
    plt.close(fig)

def load_and_analyze_eeg_data():
    """
    加载EEG数据并分析时间特征
    """
    print("\n=== EEG数据时间特征分析 ===")
    
    # 加载数据
    raw = mne.io.read_raw_edf('PN00-1.edf', preload=True, verbose=False)
    
    # 基本信息
    sfreq = raw.info['sfreq']
    n_samples = len(raw.times)
    duration_seconds = raw.times[-1]
    
    print(f"采样频率: {sfreq} Hz")
    print(f"总样本数: {n_samples}")
    print(f"实际记录时长: {duration_seconds:.1f}秒 ({duration_seconds/60:.1f}分钟)")
    
    # 验证时间一致性
    expected_duration = 2605  # 从时间计算得出
    time_difference = abs(duration_seconds - expected_duration)
    
    print(f"预期时长: {expected_duration}秒")
    print(f"时间差异: {time_difference:.1f}秒")
    
    if time_difference < 10:
        print("✅ 时间信息一致")
    else:
        print("⚠️ 时间信息可能有差异")
    
    return {
        'sampling_rate': sfreq,
        'total_samples': n_samples,
        'actual_duration': duration_seconds
    }

def generate_summary_report(timeline_data, frequency_data, characteristics_data, eeg_data):
    """
    生成综合分析报告
    """
    print("\n" + "="*60)
    print("癫痫EEG时间特征综合分析报告")
    print("="*60)
    
    print(f"\n📊 基本信息:")
    print(f"  患者编号: PN00")
    print(f"  癫痫发作编号: #1")
    print(f"  记录日期: 未知")
    print(f"  记录时间: 19:39:33 - 20:22:58")
    
    print(f"\n⏱️ 时间特征:")
    print(f"  总记录时长: {timeline_data['total_duration']/60:.1f}分钟")
    print(f"  癫痫发作持续时间: {characteristics_data['duration_seconds']}秒 ({characteristics_data['duration_minutes']:.1f}分钟)")
    print(f"  发作类型: {characteristics_data['type_classification']}")
    print(f"  发作位置: 记录的{frequency_data['seizure_position_percent']:.1f}%处")
    
    print(f"\n📈 发作频率:")
    print(f"  观察期间发作次数: 1次")
    print(f"  发作频率: {frequency_data['seizures_per_hour']:.2f}次/小时")
    print(f"  推算日发作频率: {frequency_data['seizures_per_day_estimated']:.2f}次/天")
    
    print(f"\n🔬 技术参数:")
    print(f"  采样频率: {eeg_data['sampling_rate']} Hz")
    print(f"  总样本数: {eeg_data['total_samples']:,}")
    print(f"  通道数: 29个EEG通道")
    
    print(f"\n📋 临床意义:")
    if characteristics_data['duration_seconds'] <= 120:
        print(f"  ✅ 发作持续时间在正常范围内")
    else:
        print(f"  ⚠️ 发作持续时间较长，需要关注")
    
    if frequency_data['seizures_per_hour'] < 1:
        print(f"  ✅ 发作频率相对较低")
    else:
        print(f"  ⚠️ 发作频率较高，需要密切监测")
    
    print(f"\n📁 生成文件:")
    print(f"  - seizure_timeline_analysis.png: 时间线分析图")
    print(f"  - seizure_analysis/: 癫痫地形图分析文件夹")

def main():
    """
    主分析函数
    """
    print("癫痫EEG时间特征分析")
    print("="*50)
    
    # 分析时间线
    timeline_data = analyze_recording_timeline()
    
    # 分析发作频率
    frequency_data = analyze_seizure_frequency(timeline_data)
    
    # 分析发作特征
    characteristics_data = analyze_seizure_characteristics(timeline_data)
    
    # 创建可视化
    create_timeline_visualization(timeline_data)
    
    # 分析EEG数据
    eeg_data = load_and_analyze_eeg_data()
    
    # 生成综合报告
    generate_summary_report(timeline_data, frequency_data, characteristics_data, eeg_data)

if __name__ == "__main__":
    main()
