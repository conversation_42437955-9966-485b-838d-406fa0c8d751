# Epileptic Focus Diagnosis and Prediction Using Multi-channel EEG Signals and sMRI Images

## 1. Research Overview

Multi-channel EEG signal wavelet decomposition → Contrastive learning with healthy samples to capture abnormal signals → sMRI-based five-tissue BEM model for source localization → U-net with probability maps + EEG + sMRI data for focus prediction.

## 2. Detailed Technical Approach

### 2.1 EEG Abnormal Signal Detection
**Input Data**: Multi-channel EEG signals (typically 64-256 channels)
- **Preprocessing**: Filtering, artifact removal, standardization
- **Wavelet Decomposition**: Morlet wavelet transform extracting δ(1-4Hz), θ(4-8Hz), α(8-13Hz), β(13-30Hz), γ(30-100Hz) bands
- **Contrastive Learning**: Patient signals compared with healthy controls, computing anomaly metrics
- **Probability Output**: Anomaly probability P(anomaly|t,ch) for each time point and channel

### 2.2 sMRI Five-tissue BEM Modeling and Source Localization
**Input Data**: T1-weighted sMRI images (1mm³ resolution)
- **Tissue Segmentation**: 3D reconstruction of scalp, skull, CSF, gray matter, white matter
- **BEM Mesh**: Triangular mesh generation for each tissue surface (~10,000 vertices)
- **Forward Model**: Lead field matrix L computation, establishing source-sensor mapping
- **Inverse Solution**: Minimum norm estimation (MNE) or beamforming for abnormal source localization
- **Probability Mapping**: Abnormal source probability distribution P(source|voxel) in brain cortex

### 2.3 U-net Focus Prediction Network
**Multi-modal Inputs**:
- Abnormal probability maps (3D voxel probabilities)
- Raw EEG time-frequency features (channels × time × frequency)
- sMRI structural information (T1 images)

**Network Architecture**:
- Encoder: 3D convolution for multi-scale feature extraction
- Skip connections: Detail information preservation
- Decoder: Upsampling for focus probability map reconstruction
- Output: Focus probability P(lesion|voxel) for each voxel

## 3. Technical Workflow

```mermaid
flowchart TD
    A[Multi-channel EEG<br/>64-256 channels] --> B[Signal Preprocessing<br/>Filtering·Artifact Removal·Normalization]
    B --> C[Wavelet Decomposition<br/>δθαβγ Band Extraction]
    C --> D[Contrastive Learning<br/>vs Healthy Controls]
    D --> E[Abnormal Signal Detection<br/>P(anomaly|t,ch)]

    F[sMRI T1 Images<br/>1mm³ Resolution] --> G[Five-tissue Segmentation<br/>Scalp·Skull·CSF·Gray·White Matter]
    G --> H[BEM Mesh Construction<br/>~10k vertices/tissue]
    H --> I[Forward Modeling<br/>Lead Field Matrix L]
    I --> J[Inverse Source Localization<br/>MNE/Beamforming]

    E --> K[Abnormal Source Localization]
    J --> K
    K --> L[Abnormal Probability Map<br/>P(source|voxel)]

    L --> M[U-net Prediction Network]
    E --> M
    F --> M
    M --> N[Focus Probability Map<br/>P(lesion|voxel)]

    style A fill:#e1f5fe
    style F fill:#e1f5fe
    style N fill:#ffebee
    style M fill:#f3e5f5
```

## 4. Expected Outcomes

### 4.1 Diagnostic Level
- Improve sensitivity and specificity of epileptic focus detection through multi-modal data fusion
- Generate probability distributions of abnormal regions for quantitative clinical diagnosis

### 4.2 Prediction Level
- Achieve precise focus location prediction using U-net network architecture
- Support personalized treatment planning for epilepsy patients

## 5. Research Significance

### 5.1 Clinical Significance
- Provide more accurate epileptic focus localization methods
- Improve success rates of epilepsy surgical procedures
- Offer objective evidence for epilepsy treatment decision-making

### 5.2 Technical Innovation
- First integration of wavelet transform, BEM modeling, and deep learning for epilepsy diagnosis
- Effective fusion of EEG and sMRI multi-modal data
- Complete technical pipeline from abnormal signal detection to focus prediction

## 6. Core Innovations and Technical Challenges

### 6.1 Innovation Point 1: EEG Contrastive Learning for Abnormal Detection

#### 6.1.1 Core Work
Achieve unsupervised abnormal detection through EEG signal contrastive learning

#### 6.1.2 Key Technical Issues
1. **Unsupervised Learning Challenge**: Lack of labeled abnormal data for supervised learning, requiring exploration of unsupervised or self-supervised methods
2. **Probability Integration Mechanism**: How to effectively integrate probability elements into contrastive learning process for regional probability display in MRI mapping
3. **Sequence Non-alignment Processing**: How to precisely identify abnormalities when EEG signals from different individuals lack alignment necessity
4. **Multi-dimensional Information Utilization**: How to reasonably utilize multi-dimensional characteristics of EEG signals (multi-channel, multi-decomposed signals) for effective information integration

### 6.2 Innovation Point 2: Abnormal Brain Region to Focus Location Prediction Mapping

#### 6.2.1 Core Work
Build prediction model from abnormal brain regions to epileptic focus locations

#### 6.2.2 Key Technical Issues
1. **Prediction Model Architecture Design**: Specific construction scheme and network architecture selection for final prediction model
2. **Abnormal Region-Focus Relationship Modeling**: How to determine correspondence between abnormal brain regions and actual focus locations
3. **Abnormal-Focus Mapping Mechanism**: Theoretical basis and technical implementation path for predicting focus locations through abnormal brain regions

```mermaid
graph TB
    subgraph "Innovation 1: EEG Contrastive Learning"
        A1[Unsupervised Abnormal Detection] --> B1[Challenge 1:<br/>Unlabeled Data Supervision]
        A1 --> B2[Challenge 2:<br/>Probability Integration]
        A1 --> B3[Challenge 3:<br/>Sequence Non-alignment]
        A1 --> B4[Challenge 4:<br/>Multi-dimensional Utilization]
    end

    subgraph "Innovation 2: Abnormal-Focus Prediction"
        A2[Prediction Model Construction] --> C1[Challenge 1:<br/>Architecture Design]
        A2 --> C2[Challenge 2:<br/>Abnormal-Focus Relationship]
        A2 --> C3[Challenge 3:<br/>Mapping Mechanism Theory]
    end

    subgraph "Technical Difficulties"
        D1[BEM Modeling Accuracy]
        D2[ML Integration in Source Localization]
        D3[Multi-modal Weight Allocation]
    end

    B1 --> D1
    B2 --> D2
    C2 --> D3

    style A1 fill:#e8f5e8
    style A2 fill:#fff3e0
    style D1 fill:#ffebee
    style D2 fill:#ffebee
    style D3 fill:#ffebee
```

## 7. Major Technical Difficulties

### 7.1 EEG Abnormal Detection Level
- **Unsupervised Learning Challenge**: Accurate abnormal signal detection without labeled data
- **Probability Modeling Complexity**: Effective integration of uncertainty and probability information into detection framework
- **Cross-individual Generalization**: Ensuring stability and accuracy of abnormal detection methods across different patients
- **Multi-modal Feature Fusion**: Effective integration strategies for multi-channel, multi-frequency signal features

### 7.2 Source Localization and Modeling Level
1. **BEM Model Accuracy Assurance**: Ensuring BEM model construction and source localization results conform to actual brain mechanisms
2. **Traditional Method Limitations**: Existing mathematical analysis-based source localization methods may have precision limitations
3. **Machine Learning Integration Possibility**: Exploring integration of machine learning methods into BEM modeling and source localization processes, rather than relying solely on traditional computational mathematical analysis

### 7.3 Focus Prediction Level
- **Abnormal-Focus Causal Relationship**: Establishing reliable mapping between abnormal brain regions and actual epileptic foci
- **Prediction Model Validation**: Validating clinical effectiveness and accuracy of prediction models
- **Multi-modal Data Weight Allocation**: Reasonable allocation of contribution weights for different modal data in U-net structure

## 8. U-net Network Architecture

```mermaid
graph TD
    subgraph "Multi-modal Inputs"
        A[EEG Time-Frequency Features<br/>Ch×T×F]
        B[Abnormal Probability Maps<br/>3D Voxels]
        C[sMRI Structure<br/>T1 Images]
    end

    subgraph "Encoder Path"
        D[3D Conv + BN + ReLU<br/>64 filters]
        E[3D Conv + BN + ReLU<br/>128 filters]
        F[3D Conv + BN + ReLU<br/>256 filters]
        G[3D Conv + BN + ReLU<br/>512 filters]
    end

    subgraph "Decoder Path"
        H[UpConv + Concat<br/>256 filters]
        I[UpConv + Concat<br/>128 filters]
        J[UpConv + Concat<br/>64 filters]
        K[1×1×1 Conv<br/>Focus Probability Output]
    end

    A --> D
    B --> D
    C --> D

    D --> E
    E --> F
    F --> G

    G --> H
    F -.Skip Connection.-> H
    H --> I
    E -.Skip Connection.-> I
    I --> J
    D -.Skip Connection.-> J
    J --> K

    style A fill:#e1f5fe
    style B fill:#e1f5fe
    style C fill:#e1f5fe
    style K fill:#ffebee
```

## 9. Next Steps

- Data collection and preprocessing algorithm implementation
- BEM model construction and validation
- U-net network architecture design and training
- Clinical data validation and performance evaluation

---

*Note: This report is based on the research design phase technical scheme. Specific implementation details will be further refined in subsequent research processes.*
