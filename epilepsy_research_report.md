# Epileptic Focus Diagnosis and Prediction Using Multi-channel EEG Signals and sMRI Images

## 1. Research Overview

Multi-channel EEG signal wavelet decomposition → Contrastive learning with healthy samples to capture abnormal signals → sMRI-based five-tissue BEM model for source localization → U-net with probability maps + EEG + sMRI data for focus prediction.

## 2. Detailed Technical Approach

### 2.1 EEG Abnormal Signal Detection
**Input Data**: Multi-channel EEG signals (typically 64-256 channels)
- **Preprocessing**: Filtering, artifact removal, standardization
- **Wavelet Decomposition**: Morlet wavelet transform extracting δ(1-4Hz), θ(4-8Hz), α(8-13Hz), β(13-30Hz), γ(30-100Hz) bands
- **Contrastive Learning**: Patient signals compared with healthy controls, computing anomaly metrics
- **Probability Output**: Anomaly probability P(anomaly|t,ch) for each time point and channel

### 2.2 sMRI Five-tissue BEM Modeling and Source Localization
**Input Data**: T1-weighted sMRI images (1mm³ resolution)
- **Tissue Segmentation**: 3D reconstruction of scalp, skull, CSF, gray matter, white matter
- **BEM Mesh**: Triangular mesh generation for each tissue surface (~10,000 vertices)
- **Forward Model**: Lead field matrix L computation, establishing source-sensor mapping
- **Inverse Solution**: Minimum norm estimation (MNE) or beamforming for abnormal source localization
- **Probability Mapping**: Abnormal source probability distribution P(source|voxel) in brain cortex

### 2.3 U-net Focus Prediction Network
**Multi-modal Inputs**:
- Abnormal probability maps (3D voxel probabilities)
- Raw EEG time-frequency features (channels × time × frequency)
- sMRI structural information (T1 images)

**Network Architecture**:
- Encoder: 3D convolution for multi-scale feature extraction
- Skip connections: Detail information preservation
- Decoder: Upsampling for focus probability map reconstruction
- Output: Focus probability P(lesion|voxel) for each voxel

## 3. Technical Workflow

**EEG Processing Pipeline:**
```
Multi-channel EEG (64-256 channels)
    ↓
Signal Preprocessing (Filtering, Artifact Removal, Normalization)
    ↓
Wavelet Decomposition (δθαβγ Band Extraction)
    ↓
Contrastive Learning (vs Healthy Controls)
    ↓
Abnormal Signal Detection → P(anomaly|t,ch)
```

**sMRI Processing Pipeline:**
```
sMRI T1 Images (1mm³ Resolution)
    ↓
Five-tissue Segmentation (Scalp, Skull, CSF, Gray Matter, White Matter)
    ↓
BEM Mesh Construction (~10k vertices/tissue)
    ↓
Forward Modeling (Lead Field Matrix L)
    ↓
Inverse Source Localization (MNE/Beamforming)
```

**Integration and Prediction:**
```
Abnormal Signal Detection + Source Localization
    ↓
Abnormal Probability Map → P(source|voxel)
    ↓
U-net Prediction Network (Probability Map + EEG + sMRI)
    ↓
Focus Probability Map → P(lesion|voxel)
```

## 4. Expected Outcomes

### 4.1 Diagnostic Level
- Improve sensitivity and specificity of epileptic focus detection through multi-modal data fusion
- Generate probability distributions of abnormal regions for quantitative clinical diagnosis

### 4.2 Prediction Level
- Achieve precise focus location prediction using U-net network architecture
- Support personalized treatment planning for epilepsy patients

## 5. Research Significance

### 5.1 Clinical Significance
- Provide more accurate epileptic focus localization methods
- Improve success rates of epilepsy surgical procedures
- Offer objective evidence for epilepsy treatment decision-making

### 5.2 Technical Innovation
- First integration of wavelet transform, BEM modeling, and deep learning for epilepsy diagnosis
- Effective fusion of EEG and sMRI multi-modal data
- Complete technical pipeline from abnormal signal detection to focus prediction

## 6. Core Innovations and Technical Challenges

### 6.1 Innovation Point 1: EEG Contrastive Learning for Abnormal Detection

#### 6.1.1 Core Work
Achieve unsupervised abnormal detection through EEG signal contrastive learning

#### 6.1.2 Key Technical Issues
1. **Unsupervised Learning Challenge**: Lack of labeled abnormal data for supervised learning, requiring exploration of unsupervised or self-supervised methods
2. **Probability Integration Mechanism**: How to effectively integrate probability elements into contrastive learning process for regional probability display in MRI mapping
3. **Sequence Non-alignment Processing**: How to precisely identify abnormalities when EEG signals from different individuals lack alignment necessity
4. **Multi-dimensional Information Utilization**: How to reasonably utilize multi-dimensional characteristics of EEG signals (multi-channel, multi-decomposed signals) for effective information integration

### 6.2 Innovation Point 2: Abnormal Brain Region to Focus Location Prediction Mapping

#### 6.2.1 Core Work
Build prediction model from abnormal brain regions to epileptic focus locations

#### 6.2.2 Key Technical Issues
1. **Prediction Model Architecture Design**: Specific construction scheme and network architecture selection for final prediction model
2. **Abnormal Region-Focus Relationship Modeling**: How to determine correspondence between abnormal brain regions and actual focus locations
3. **Abnormal-Focus Mapping Mechanism**: Theoretical basis and technical implementation path for predicting focus locations through abnormal brain regions

**Innovation Point 1: EEG Contrastive Learning**
```
Unsupervised Abnormal Detection
├── Challenge 1: Unlabeled Data Supervision
├── Challenge 2: Probability Integration
├── Challenge 3: Sequence Non-alignment
└── Challenge 4: Multi-dimensional Utilization
```

**Innovation Point 2: Abnormal-Focus Prediction**
```
Prediction Model Construction
├── Challenge 1: Architecture Design
├── Challenge 2: Abnormal-Focus Relationship
└── Challenge 3: Mapping Mechanism Theory
```

**Technical Difficulties Mapping:**
```
Challenge 1 (Unlabeled Data) → BEM Modeling Accuracy
Challenge 2 (Probability Integration) → ML Integration in Source Localization
Challenge 2 (Abnormal-Focus Relationship) → Multi-modal Weight Allocation
```

## 7. Major Technical Difficulties

### 7.1 EEG Abnormal Detection Level
- **Unsupervised Learning Challenge**: Accurate abnormal signal detection without labeled data
- **Probability Modeling Complexity**: Effective integration of uncertainty and probability information into detection framework
- **Cross-individual Generalization**: Ensuring stability and accuracy of abnormal detection methods across different patients
- **Multi-modal Feature Fusion**: Effective integration strategies for multi-channel, multi-frequency signal features

### 7.2 Source Localization and Modeling Level
1. **BEM Model Accuracy Assurance**: Ensuring BEM model construction and source localization results conform to actual brain mechanisms
2. **Traditional Method Limitations**: Existing mathematical analysis-based source localization methods may have precision limitations
3. **Machine Learning Integration Possibility**: Exploring integration of machine learning methods into BEM modeling and source localization processes, rather than relying solely on traditional computational mathematical analysis

### 7.3 Focus Prediction Level
- **Abnormal-Focus Causal Relationship**: Establishing reliable mapping between abnormal brain regions and actual epileptic foci
- **Prediction Model Validation**: Validating clinical effectiveness and accuracy of prediction models
- **Multi-modal Data Weight Allocation**: Reasonable allocation of contribution weights for different modal data in U-net structure

## 8. U-net Network Architecture

**Multi-modal Input Layer:**
```
Input 1: EEG Time-Frequency Features (Ch × T × F)
Input 2: Abnormal Probability Maps (3D Voxels)
Input 3: sMRI Structure (T1 Images)
    ↓
Combined Multi-modal Input
```

**Encoder Path (Downsampling):**
```
Layer 1: 3D Conv + BN + ReLU (64 filters)  ←─┐
    ↓                                        │
Layer 2: 3D Conv + BN + ReLU (128 filters) ←─┼─┐
    ↓                                        │ │
Layer 3: 3D Conv + BN + ReLU (256 filters) ←─┼─┼─┐
    ↓                                        │ │ │
Layer 4: 3D Conv + BN + ReLU (512 filters)  │ │ │
    ↓                                        │ │ │
```

**Decoder Path (Upsampling with Skip Connections):**
```
Layer 4 Output                               │ │ │
    ↓                                        │ │ │
UpConv + Concat (256 filters) ←──────────────┘ │ │
    ↓                                          │ │
UpConv + Concat (128 filters) ←────────────────┘ │
    ↓                                            │
UpConv + Concat (64 filters) ←───────────────────┘
    ↓
1×1×1 Conv → Focus Probability Output P(lesion|voxel)
```

## 9. Next Steps

- Data collection and preprocessing algorithm implementation
- BEM model construction and validation
- U-net network architecture design and training
- Clinical data validation and performance evaluation

---

*Note: This report is based on the research design phase technical scheme. Specific implementation details will be further refined in subsequent research processes.*
