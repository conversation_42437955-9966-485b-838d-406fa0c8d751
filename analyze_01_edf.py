#!/usr/bin/env python3
"""
分析新的01.edf文件
"""

import numpy as np
import matplotlib.pyplot as plt
import mne
import os
import warnings
from datetime import datetime

warnings.filterwarnings('ignore')
mne.set_log_level('ERROR')

def analyze_new_edf_file(file_path):
    """
    全面分析新的EDF文件
    """
    print(f"=== 分析新文件: {file_path} ===")
    
    try:
        # 尝试不同的编码方式加载文件
        try:
            raw = mne.io.read_raw_edf(file_path, preload=True, verbose=False)
        except:
            print("尝试使用latin1编码...")
            raw = mne.io.read_raw_edf(file_path, preload=True, verbose=False, encoding='latin1')
        
        print(f"✅ 文件加载成功")
        print(f"通道数: {raw.info['nchan']}")
        print(f"采样频率: {raw.info['sfreq']} Hz")
        print(f"记录时长: {raw.times[-1]:.2f}秒 ({raw.times[-1]/60:.1f}分钟)")
        print(f"数据点数: {len(raw.times)}")
        
        return raw
        
    except Exception as e:
        print(f"❌ 文件加载失败: {e}")
        return None

def compare_with_previous_file(raw_new, previous_file="PN00-1.edf"):
    """
    与之前的文件进行对比
    """
    print(f"\n=== 与{previous_file}对比 ===")
    
    try:
        try:
            raw_old = mne.io.read_raw_edf(previous_file, preload=True, verbose=False)
        except:
            raw_old = mne.io.read_raw_edf(previous_file, preload=True, verbose=False, encoding='latin1')
        
        print("文件对比:")
        print(f"  01.edf     : {raw_new.info['nchan']}通道, {raw_new.info['sfreq']}Hz, {raw_new.times[-1]/60:.1f}分钟")
        print(f"  PN00-1.edf : {raw_old.info['nchan']}通道, {raw_old.info['sfreq']}Hz, {raw_old.times[-1]/60:.1f}分钟")
        
        # 比较通道名称
        new_channels = set(raw_new.ch_names)
        old_channels = set(raw_old.ch_names)
        
        common_channels = new_channels & old_channels
        new_only = new_channels - old_channels
        old_only = old_channels - new_channels
        
        print(f"\n通道对比:")
        print(f"  共同通道: {len(common_channels)}个")
        print(f"  01.edf独有: {len(new_only)}个 - {list(new_only) if new_only else '无'}")
        print(f"  PN00-1.edf独有: {len(old_only)}个 - {list(old_only) if old_only else '无'}")
        
        return raw_old
        
    except Exception as e:
        print(f"❌ 对比文件加载失败: {e}")
        return None

def analyze_channels_detailed(raw):
    """
    详细分析通道信息
    """
    print(f"\n=== 详细通道分析 ===")
    
    print("所有通道列表:")
    for i, ch_name in enumerate(raw.ch_names):
        print(f"  {i+1:2d}. {ch_name}")
    
    # 分类通道
    eeg_channels = []
    non_eeg_channels = []
    
    for ch in raw.ch_names:
        if ch.startswith('EEG') or ch in ['1', '2']:  # 基于之前的经验
            eeg_channels.append(ch)
        else:
            non_eeg_channels.append(ch)
    
    print(f"\nEEG通道识别 ({len(eeg_channels)}个):")
    for ch in eeg_channels:
        print(f"  {ch}")
    
    print(f"\n非EEG通道 ({len(non_eeg_channels)}个):")
    for ch in non_eeg_channels:
        print(f"  {ch}")
    
    return eeg_channels, non_eeg_channels

def analyze_data_quality(raw):
    """
    分析数据质量
    """
    print(f"\n=== 数据质量分析 ===")
    
    data = raw.get_data()
    
    print("整体数据统计:")
    print(f"  数据形状: {data.shape}")
    print(f"  数据范围: {data.min():.2e} 到 {data.max():.2e}")
    print(f"  平均值: {data.mean():.2e}")
    print(f"  标准差: {data.std():.2e}")
    
    # 检查每个通道
    print(f"\n各通道数据特征:")
    print("通道名称".ljust(15) + "平均值".ljust(12) + "标准差".ljust(12) + "最小值".ljust(12) + "最大值".ljust(12) + "可能类型")
    print("-" * 80)
    
    for i, ch_name in enumerate(raw.ch_names):
        ch_data = data[i, :]
        mean_val = np.mean(ch_data)
        std_val = np.std(ch_data)
        min_val = np.min(ch_data)
        max_val = np.max(ch_data)
        
        # 判断信号类型
        if std_val == 0:
            signal_type = "常数/标记"
        elif std_val > 1e-3:
            signal_type = "心电/肌电"
        elif std_val > 1e-6:
            signal_type = "EEG"
        else:
            signal_type = "噪声/空"
        
        print(f"{ch_name:<15}{mean_val:>11.2e}{std_val:>11.2e}{min_val:>11.2e}{max_val:>11.2e} {signal_type}")

def create_basic_analysis(raw, output_dir="01_edf_analysis"):
    """
    创建基础分析图表
    """
    print(f"\n=== 创建基础分析 ===")
    
    os.makedirs(output_dir, exist_ok=True)
    
    # 识别EEG通道
    eeg_channels = [ch for ch in raw.ch_names if ch.startswith('EEG')]
    
    if len(eeg_channels) == 0:
        print("❌ 未找到EEG通道，无法创建地形图")
        return
    
    # 处理EEG数据
    raw_eeg = raw.copy()
    raw_eeg.pick_channels(eeg_channels)
    raw_eeg.set_channel_types({ch: 'eeg' for ch in raw_eeg.ch_names})
    
    # 重命名通道
    channel_mapping = {}
    for ch in raw_eeg.ch_names:
        clean_name = ch.replace('EEG ', '').strip()
        if clean_name == 'T3':
            clean_name = 'T7'
        elif clean_name == 'T4':
            clean_name = 'T8'
        elif clean_name == 'T5':
            clean_name = 'P7'
        elif clean_name == 'T6':
            clean_name = 'P8'
        elif clean_name.lower() in ['fc1', 'fc2', 'fc5', 'fc6']:
            clean_name = clean_name.upper()
        elif clean_name.lower() in ['cp1', 'cp2', 'cp5', 'cp6']:
            clean_name = clean_name.upper()
        channel_mapping[ch] = clean_name
    
    raw_eeg.rename_channels(channel_mapping)
    
    try:
        # 设置montage
        montage = mne.channels.make_standard_montage('standard_1020')
        raw_eeg.set_montage(montage, match_case=False, on_missing='ignore')
        
        # 创建基础RMS地形图
        data = raw_eeg.get_data()
        rms_data = np.sqrt(np.mean(data**2, axis=1))
        
        fig, ax = plt.subplots(figsize=(10, 8))
        
        from mne.viz import plot_topomap
        im, _ = plot_topomap(rms_data, raw_eeg.info,
                           ch_type='eeg',
                           contours=8,
                           cmap='RdBu_r',
                           axes=ax,
                           show=False,
                           sphere='auto')
        
        ax.set_title(f'01.edf - EEG RMS活动地形图\n({len(raw_eeg.ch_names)}通道)', 
                    fontsize=16, fontweight='bold')
        
        cbar = plt.colorbar(im, ax=ax, shrink=0.8)
        cbar.set_label('幅度 (µV)', rotation=270, labelpad=20)
        
        plt.tight_layout()
        save_path = f'{output_dir}/01_edf_rms_topomap.png'
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        print(f"✅ 保存地形图: {save_path}")
        plt.close(fig)
        
        return True
        
    except Exception as e:
        print(f"❌ 创建地形图失败: {e}")
        return False

def detect_seizure_activity(raw):
    """
    检测是否有癫痫活动
    """
    print(f"\n=== 癫痫活动检测 ===")
    
    # 简单的癫痫活动检测
    eeg_channels = [ch for ch in raw.ch_names if ch.startswith('EEG')]
    
    if len(eeg_channels) == 0:
        print("❌ 无EEG通道，无法检测癫痫活动")
        return
    
    raw_eeg = raw.copy()
    raw_eeg.pick_channels(eeg_channels)
    data = raw_eeg.get_data()
    
    # 计算每个时间窗口的活动强度
    window_size = int(5 * raw.info['sfreq'])  # 5秒窗口
    n_windows = data.shape[1] // window_size
    
    window_powers = []
    for i in range(n_windows):
        start_idx = i * window_size
        end_idx = (i + 1) * window_size
        window_data = data[:, start_idx:end_idx]
        power = np.mean(window_data**2)
        window_powers.append(power)
    
    window_powers = np.array(window_powers)
    
    # 检测异常高活动
    mean_power = np.mean(window_powers)
    std_power = np.std(window_powers)
    threshold = mean_power + 3 * std_power
    
    high_activity_windows = np.where(window_powers > threshold)[0]
    
    print(f"分析了{n_windows}个5秒窗口")
    print(f"平均功率: {mean_power:.2e}")
    print(f"功率标准差: {std_power:.2e}")
    print(f"异常高活动窗口: {len(high_activity_windows)}个")
    
    if len(high_activity_windows) > 0:
        print("⚠️  检测到可能的异常活动时段:")
        for window_idx in high_activity_windows:
            time_start = window_idx * 5
            time_end = (window_idx + 1) * 5
            print(f"  时间 {time_start}-{time_end}秒: 功率 {window_powers[window_idx]:.2e}")
    else:
        print("✅ 未检测到明显的异常活动")

def main():
    """
    主分析函数
    """
    file_path = "01.edf"
    
    if not os.path.exists(file_path):
        print(f"❌ 文件 {file_path} 不存在!")
        return
    
    print("=== 01.edf 文件分析 ===")
    
    # 基础分析
    raw = analyze_new_edf_file(file_path)
    if raw is None:
        return
    
    # 与之前文件对比
    compare_with_previous_file(raw)
    
    # 详细通道分析
    analyze_channels_detailed(raw)
    
    # 数据质量分析
    analyze_data_quality(raw)
    
    # 创建基础分析图表
    create_basic_analysis(raw)
    
    # 癫痫活动检测
    detect_seizure_activity(raw)
    
    print(f"\n=== 分析完成 ===")
    print("生成的文件:")
    print("- 01_edf_analysis/01_edf_rms_topomap.png: 基础地形图")

if __name__ == "__main__":
    main()
