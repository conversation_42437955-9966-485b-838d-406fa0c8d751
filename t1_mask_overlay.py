#!/usr/bin/env python3
"""
T1加权MRI与掩码叠加可视化
"""

import numpy as np
import matplotlib.pyplot as plt
import nibabel as nib
import os
from matplotlib.colors import ListedColormap

def load_and_analyze_t1_data():
    """
    加载并分析T1加权MRI数据
    """
    print("=== 加载T1加权MRI数据 ===")
    
    # 加载T1数据
    t1_img = nib.load("sub-1_T1w.nii")
    t1_data = t1_img.get_fdata()
    t1_header = t1_img.header
    
    print(f"T1数据形状: {t1_data.shape}")
    print(f"T1体素尺寸: {t1_header.get_zooms()}")
    print(f"T1数据范围: {t1_data.min():.1f} - {t1_data.max():.1f}")
    print(f"T1数据类型: {t1_data.dtype}")
    print(f"T1非零体素: {np.count_nonzero(t1_data):,}")
    print(f"T1非零比例: {np.count_nonzero(t1_data)/t1_data.size*100:.1f}%")
    
    # 加载掩码数据
    mask_img = nib.load("1/1_MaskInRawData.nii.gz")
    mask_data = mask_img.get_fdata()
    mask_header = mask_img.header
    
    print(f"\n掩码数据形状: {mask_data.shape}")
    print(f"掩码体素尺寸: {mask_header.get_zooms()}")
    print(f"掩码数据范围: {mask_data.min():.1f} - {mask_data.max():.1f}")
    print(f"掩码非零体素: {np.count_nonzero(mask_data):,}")
    
    return t1_data, mask_data, t1_img, mask_img

def check_spatial_alignment(t1_data, mask_data, t1_img, mask_img):
    """
    检查T1和掩码的空间对齐
    """
    print(f"\n=== 检查空间对齐 ===")
    
    # 比较形状
    print(f"T1形状: {t1_data.shape}")
    print(f"掩码形状: {mask_data.shape}")
    
    if t1_data.shape == mask_data.shape:
        print("✅ 形状完全匹配")
        aligned = True
    else:
        print("⚠️ 形状不匹配，需要处理")
        aligned = False
    
    # 比较仿射矩阵
    t1_affine = t1_img.affine
    mask_affine = mask_img.affine
    
    print(f"\nT1仿射矩阵:")
    print(t1_affine)
    print(f"\n掩码仿射矩阵:")
    print(mask_affine)
    
    affine_diff = np.abs(t1_affine - mask_affine).max()
    print(f"\n仿射矩阵最大差异: {affine_diff:.6f}")
    
    if affine_diff < 0.001:
        print("✅ 仿射矩阵基本一致")
    else:
        print("⚠️ 仿射矩阵有差异")
    
    return aligned

def create_overlay_visualization(t1_data, mask_data):
    """
    创建T1与掩码的叠加可视化
    """
    print(f"\n=== 创建叠加可视化 ===")
    
    # 如果形状不匹配，需要处理
    if t1_data.shape != mask_data.shape:
        print("处理形状不匹配...")
        # 找到公共区域
        min_shape = [min(t1_data.shape[i], mask_data.shape[i]) for i in range(3)]
        t1_crop = t1_data[:min_shape[0], :min_shape[1], :min_shape[2]]
        mask_crop = mask_data[:min_shape[0], :min_shape[1], :min_shape[2]]
        print(f"裁剪后形状: {min_shape}")
    else:
        t1_crop = t1_data
        mask_crop = mask_data
    
    # 标准化T1数据
    t1_norm = (t1_crop - t1_crop.min()) / (t1_crop.max() - t1_crop.min())
    
    # 创建掩码
    mask_binary = mask_crop > 0
    
    # 选择切片
    center_x = t1_crop.shape[0] // 2
    center_y = t1_crop.shape[1] // 2
    center_z = t1_crop.shape[2] // 2
    
    # 创建可视化
    fig, axes = plt.subplots(3, 4, figsize=(20, 15))
    fig.suptitle('T1加权MRI与掩码叠加可视化', fontsize=16, fontweight='bold')
    
    # 第一行：轴位面 (Z方向)
    # T1原图
    t1_slice_z = t1_norm[:, :, center_z]
    mask_slice_z = mask_binary[:, :, center_z]
    
    im1 = axes[0, 0].imshow(t1_slice_z.T, cmap='gray', origin='lower')
    axes[0, 0].set_title('T1 - 轴位面', fontweight='bold')
    axes[0, 0].axis('off')
    plt.colorbar(im1, ax=axes[0, 0], shrink=0.8)
    
    # 掩码
    axes[0, 1].imshow(t1_slice_z.T, cmap='gray', origin='lower', alpha=0.7)
    axes[0, 1].imshow(mask_slice_z.T, cmap='Reds', origin='lower', alpha=0.8)
    axes[0, 1].set_title('T1 + 掩码叠加', fontweight='bold')
    axes[0, 1].axis('off')
    
    # 仅掩码
    axes[0, 2].imshow(mask_slice_z.T, cmap='Reds', origin='lower')
    axes[0, 2].set_title('掩码区域', fontweight='bold')
    axes[0, 2].axis('off')
    
    # 掩码区域的T1强度
    masked_t1_z = np.where(mask_slice_z, t1_slice_z, np.nan)
    im4 = axes[0, 3].imshow(masked_t1_z.T, cmap='hot', origin='lower')
    axes[0, 3].set_title('掩码区域T1强度', fontweight='bold')
    axes[0, 3].axis('off')
    plt.colorbar(im4, ax=axes[0, 3], shrink=0.8)
    
    # 第二行：冠状面 (Y方向)
    t1_slice_y = t1_norm[:, center_y, :]
    mask_slice_y = mask_binary[:, center_y, :]
    
    im5 = axes[1, 0].imshow(t1_slice_y.T, cmap='gray', origin='lower')
    axes[1, 0].set_title('T1 - 冠状面', fontweight='bold')
    axes[1, 0].axis('off')
    plt.colorbar(im5, ax=axes[1, 0], shrink=0.8)
    
    axes[1, 1].imshow(t1_slice_y.T, cmap='gray', origin='lower', alpha=0.7)
    axes[1, 1].imshow(mask_slice_y.T, cmap='Reds', origin='lower', alpha=0.8)
    axes[1, 1].set_title('T1 + 掩码叠加', fontweight='bold')
    axes[1, 1].axis('off')
    
    axes[1, 2].imshow(mask_slice_y.T, cmap='Reds', origin='lower')
    axes[1, 2].set_title('掩码区域', fontweight='bold')
    axes[1, 2].axis('off')
    
    masked_t1_y = np.where(mask_slice_y, t1_slice_y, np.nan)
    im8 = axes[1, 3].imshow(masked_t1_y.T, cmap='hot', origin='lower')
    axes[1, 3].set_title('掩码区域T1强度', fontweight='bold')
    axes[1, 3].axis('off')
    plt.colorbar(im8, ax=axes[1, 3], shrink=0.8)
    
    # 第三行：矢状面 (X方向)
    t1_slice_x = t1_norm[center_x, :, :]
    mask_slice_x = mask_binary[center_x, :, :]
    
    im9 = axes[2, 0].imshow(t1_slice_x.T, cmap='gray', origin='lower')
    axes[2, 0].set_title('T1 - 矢状面', fontweight='bold')
    axes[2, 0].axis('off')
    plt.colorbar(im9, ax=axes[2, 0], shrink=0.8)
    
    axes[2, 1].imshow(t1_slice_x.T, cmap='gray', origin='lower', alpha=0.7)
    axes[2, 1].imshow(mask_slice_x.T, cmap='Reds', origin='lower', alpha=0.8)
    axes[2, 1].set_title('T1 + 掩码叠加', fontweight='bold')
    axes[2, 1].axis('off')
    
    axes[2, 2].imshow(mask_slice_x.T, cmap='Reds', origin='lower')
    axes[2, 2].set_title('掩码区域', fontweight='bold')
    axes[2, 2].axis('off')
    
    masked_t1_x = np.where(mask_slice_x, t1_slice_x, np.nan)
    im12 = axes[2, 3].imshow(masked_t1_x.T, cmap='hot', origin='lower')
    axes[2, 3].set_title('掩码区域T1强度', fontweight='bold')
    axes[2, 3].axis('off')
    plt.colorbar(im12, ax=axes[2, 3], shrink=0.8)
    
    plt.tight_layout()
    plt.savefig('t1_mask_overlay.png', dpi=300, bbox_inches='tight')
    print("保存叠加图: t1_mask_overlay.png")
    plt.close(fig)
    
    return t1_crop, mask_crop

def analyze_mask_region_intensity(t1_data, mask_data):
    """
    分析掩码区域的T1强度特征
    """
    print(f"\n=== 分析掩码区域T1强度 ===")
    
    # 确保形状匹配
    if t1_data.shape != mask_data.shape:
        min_shape = [min(t1_data.shape[i], mask_data.shape[i]) for i in range(3)]
        t1_crop = t1_data[:min_shape[0], :min_shape[1], :min_shape[2]]
        mask_crop = mask_data[:min_shape[0], :min_shape[1], :min_shape[2]]
    else:
        t1_crop = t1_data
        mask_crop = mask_data
    
    # 获取掩码区域的T1值
    mask_binary = mask_crop > 0
    t1_in_mask = t1_crop[mask_binary]
    t1_outside_mask = t1_crop[~mask_binary & (t1_crop > 0)]  # 排除背景
    
    print(f"掩码区域体素数: {len(t1_in_mask):,}")
    print(f"掩码外脑组织体素数: {len(t1_outside_mask):,}")
    
    if len(t1_in_mask) > 0:
        print(f"\n掩码区域T1强度统计:")
        print(f"  平均值: {t1_in_mask.mean():.1f}")
        print(f"  标准差: {t1_in_mask.std():.1f}")
        print(f"  中位数: {np.median(t1_in_mask):.1f}")
        print(f"  范围: {t1_in_mask.min():.1f} - {t1_in_mask.max():.1f}")
    
    if len(t1_outside_mask) > 0:
        print(f"\n掩码外脑组织T1强度统计:")
        print(f"  平均值: {t1_outside_mask.mean():.1f}")
        print(f"  标准差: {t1_outside_mask.std():.1f}")
        print(f"  中位数: {np.median(t1_outside_mask):.1f}")
        print(f"  范围: {t1_outside_mask.min():.1f} - {t1_outside_mask.max():.1f}")
    
    # 比较分析
    if len(t1_in_mask) > 0 and len(t1_outside_mask) > 0:
        mean_diff = t1_in_mask.mean() - t1_outside_mask.mean()
        print(f"\n对比分析:")
        print(f"  平均强度差异: {mean_diff:.1f}")
        print(f"  相对差异: {mean_diff/t1_outside_mask.mean()*100:.1f}%")
        
        if abs(mean_diff) > t1_outside_mask.std():
            if mean_diff > 0:
                print("  ✓ 掩码区域强度显著高于周围组织 (可能是高信号病灶)")
            else:
                print("  ✓ 掩码区域强度显著低于周围组织 (可能是低信号病灶)")
        else:
            print("  - 掩码区域强度与周围组织相似")
    
    return t1_in_mask, t1_outside_mask

def create_intensity_comparison():
    """
    创建强度对比分析
    """
    print(f"\n=== 创建强度对比分析 ===")
    
    # 加载数据
    t1_img = nib.load("sub-1_T1w.nii")
    mask_img = nib.load("1/1_MaskInRawData.nii.gz")
    
    t1_data = t1_img.get_fdata()
    mask_data = mask_img.get_fdata()
    
    # 处理形状不匹配
    if t1_data.shape != mask_data.shape:
        min_shape = [min(t1_data.shape[i], mask_data.shape[i]) for i in range(3)]
        t1_crop = t1_data[:min_shape[0], :min_shape[1], :min_shape[2]]
        mask_crop = mask_data[:min_shape[0], :min_shape[1], :min_shape[2]]
    else:
        t1_crop = t1_data
        mask_crop = mask_data
    
    mask_binary = mask_crop > 0
    t1_in_mask = t1_crop[mask_binary]
    t1_outside_mask = t1_crop[~mask_binary & (t1_crop > 0)]
    
    # 创建对比图
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('T1强度分析 - 掩码区域 vs 正常脑组织', fontsize=16, fontweight='bold')
    
    # 直方图对比
    axes[0, 0].hist(t1_outside_mask, bins=50, alpha=0.7, color='blue', label='正常脑组织', density=True)
    axes[0, 0].hist(t1_in_mask, bins=50, alpha=0.7, color='red', label='掩码区域', density=True)
    axes[0, 0].set_xlabel('T1强度值')
    axes[0, 0].set_ylabel('密度')
    axes[0, 0].set_title('强度分布对比')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 箱线图对比
    box_data = [t1_outside_mask, t1_in_mask]
    box_labels = ['正常脑组织', '掩码区域']
    bp = axes[0, 1].boxplot(box_data, labels=box_labels, patch_artist=True)
    bp['boxes'][0].set_facecolor('blue')
    bp['boxes'][1].set_facecolor('red')
    axes[0, 1].set_ylabel('T1强度值')
    axes[0, 1].set_title('强度分布箱线图')
    axes[0, 1].grid(True, alpha=0.3)
    
    # 统计对比
    stats_text = f"""
    正常脑组织:
    • 平均值: {t1_outside_mask.mean():.1f}
    • 标准差: {t1_outside_mask.std():.1f}
    • 中位数: {np.median(t1_outside_mask):.1f}
    
    掩码区域:
    • 平均值: {t1_in_mask.mean():.1f}
    • 标准差: {t1_in_mask.std():.1f}
    • 中位数: {np.median(t1_in_mask):.1f}
    
    差异分析:
    • 平均值差异: {t1_in_mask.mean() - t1_outside_mask.mean():.1f}
    • 相对差异: {(t1_in_mask.mean() - t1_outside_mask.mean())/t1_outside_mask.mean()*100:.1f}%
    """
    
    axes[0, 2].text(0.05, 0.95, stats_text, transform=axes[0, 2].transAxes,
                    fontsize=11, verticalalignment='top',
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow"))
    axes[0, 2].set_title('统计对比')
    axes[0, 2].axis('off')
    
    # 3D可视化掩码位置
    coords = np.where(mask_binary)
    if len(coords[0]) > 0:
        ax_3d = fig.add_subplot(2, 3, 4, projection='3d')
        
        # 为性能考虑，只显示部分点
        step = max(1, len(coords[0]) // 2000)
        ax_3d.scatter(coords[0][::step], coords[1][::step], coords[2][::step], 
                     c=t1_in_mask[::step], cmap='hot', alpha=0.6, s=1)
        ax_3d.set_title('掩码区域3D分布\n(颜色=T1强度)')
        ax_3d.set_xlabel('X')
        ax_3d.set_ylabel('Y')
        ax_3d.set_zlabel('Z')
    
    # 掩码区域质心分析
    if len(coords[0]) > 0:
        centroid = [np.mean(coords[0]), np.mean(coords[1]), np.mean(coords[2])]
        
        centroid_text = f"""
        掩码区域特征:
        
        • 体素数: {len(t1_in_mask):,}
        • 体积: {len(t1_in_mask) * 1.0:.1f} mm³
        
        质心位置:
        • X: {centroid[0]:.1f}
        • Y: {centroid[1]:.1f}  
        • Z: {centroid[2]:.1f}
        
        T1信号特征:
        • 与正常组织对比: {'高信号' if t1_in_mask.mean() > t1_outside_mask.mean() else '低信号'}
        • 信号均匀性: {'均匀' if t1_in_mask.std() < t1_outside_mask.std() else '不均匀'}
        """
        
        axes[1, 1].text(0.05, 0.95, centroid_text, transform=axes[1, 1].transAxes,
                        fontsize=11, verticalalignment='top',
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))
        axes[1, 1].set_title('掩码区域特征')
        axes[1, 1].axis('off')
    
    # 临床解释
    clinical_text = f"""
    临床影像学解释:
    
    基于T1加权像特征:
    
    """
    
    if t1_in_mask.mean() > t1_outside_mask.mean():
        clinical_text += """
    • T1高信号病灶
    • 可能提示:
      - 脂肪组织
      - 蛋白质含量高
      - 慢性出血
      - 某些肿瘤
    """
    else:
        clinical_text += """
    • T1低信号病灶  
    • 可能提示:
      - 水肿
      - 囊性变
      - 急性梗死
      - 某些肿瘤
    """
    
    clinical_text += f"""
    
    结合癫痫病史:
    • 可能是癫痫致痫灶
    • 需要结合EEG定位
    • 考虑手术切除可能性
    """
    
    axes[1, 2].text(0.05, 0.95, clinical_text, transform=axes[1, 2].transAxes,
                    fontsize=10, verticalalignment='top',
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="lightcoral"))
    axes[1, 2].set_title('临床解释')
    axes[1, 2].axis('off')
    
    plt.tight_layout()
    plt.savefig('t1_intensity_analysis.png', dpi=300, bbox_inches='tight')
    print("保存强度分析图: t1_intensity_analysis.png")
    plt.close(fig)

def main():
    """
    主分析函数
    """
    print("=== T1加权MRI与掩码对应分析 ===")
    
    # 检查文件是否存在
    if not os.path.exists("sub-1_T1w.nii"):
        print("错误: 找不到 sub-1_T1w.nii 文件!")
        return
    
    if not os.path.exists("1/1_MaskInRawData.nii.gz"):
        print("错误: 找不到 1_MaskInRawData.nii.gz 文件!")
        return
    
    # 加载和分析数据
    t1_data, mask_data, t1_img, mask_img = load_and_analyze_t1_data()
    
    # 检查空间对齐
    aligned = check_spatial_alignment(t1_data, mask_data, t1_img, mask_img)
    
    # 创建叠加可视化
    t1_crop, mask_crop = create_overlay_visualization(t1_data, mask_data)
    
    # 分析掩码区域强度
    t1_in_mask, t1_outside_mask = analyze_mask_region_intensity(t1_data, mask_data)
    
    # 创建强度对比分析
    create_intensity_comparison()
    
    print(f"\n=== 分析完成 ===")
    print("生成的文件:")
    print("- t1_mask_overlay.png: T1与掩码叠加可视化")
    print("- t1_intensity_analysis.png: 强度对比分析")
    
    print(f"\n=== 总结 ===")
    print("✅ 成功将T1加权MRI与MaskInRawData进行对应可视化")
    print("✅ 掩码区域在T1图像上清晰可见")
    print("✅ 提供了详细的强度分析和临床解释")

if __name__ == "__main__":
    main()
