#!/usr/bin/env python3
"""
Comprehensive Performance Analysis System
for Epilepsy Lesion Localization with Cubic Bounding Box

This module provides detailed analysis tools including:
- Model evaluation on test set
- Quantitative metrics computation
- Error analysis and failure case identification
- Clinical utility assessment
- Comparative analysis across different lesion types
"""

import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json
import pickle
from typing import Dict, List, Tuple, Optional
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

from training_pipeline import EpilepsyLocalizationModel, create_data_loaders
from visualization_system import BoundingBoxVisualizer
from epilepsy_localization_network import BoundingBoxLoss

class PerformanceAnalyzer:
    """Comprehensive performance analysis for epilepsy localization model"""
    
    def __init__(self, model_path: str, device: torch.device = None):
        self.device = device or torch.device('cpu')
        
        # Load trained model
        self.model = EpilepsyLocalizationModel()
        checkpoint = torch.load(model_path, map_location=self.device)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.model.eval()
        self.model.to(self.device)
        
        print(f"Loaded model from {model_path}")
        
        # Initialize loss function for metrics
        self.bbox_loss = BoundingBoxLoss()
        
        # Initialize visualizer
        self.visualizer = BoundingBoxVisualizer(model_path, self.device)
        
        # Results storage
        self.test_results = []
        self.metrics_summary = {}
        
    def evaluate_model(self, test_loader, save_results: bool = True) -> Dict[str, float]:
        """Comprehensive model evaluation on test set"""
        
        print("Evaluating model on test set...")
        
        all_dice_scores = []
        all_iou_scores = []
        all_center_errors = []
        all_size_errors = []
        all_losses = []
        
        detailed_results = []
        
        with torch.no_grad():
            for batch_idx, batch in enumerate(tqdm(test_loader, desc="Evaluating")):
                # Move data to device
                eeg_data = batch['eeg_data'].to(self.device)
                temporal_sequence = batch['temporal_sequence'].to(self.device)
                lesion_masks = batch['lesion_mask'].to(self.device)
                
                # Forward pass
                outputs = self.model(eeg_data, temporal_sequence)
                bbox_params = outputs['bbox_params']
                
                # Compute losses and metrics
                bbox_loss_results = self.bbox_loss(bbox_params, lesion_masks)
                
                # Process each sample in batch
                batch_size = eeg_data.shape[0]
                for i in range(batch_size):
                    # Extract individual results
                    pred_mask = bbox_loss_results['pred_masks'][i].cpu().numpy()
                    target_mask = lesion_masks[i].cpu().numpy()
                    pred_center = bbox_params['center'][i].cpu().numpy()
                    pred_size = bbox_params['size'][i].cpu().numpy()
                    
                    # Compute metrics
                    dice_score = self.compute_dice_score(pred_mask, target_mask)
                    iou_score = self.compute_iou_score(pred_mask, target_mask)
                    center_error = self.compute_center_error(pred_center, target_mask)
                    size_error = self.compute_size_error(pred_size, target_mask)
                    
                    # Store metrics
                    all_dice_scores.append(dice_score)
                    all_iou_scores.append(iou_score)
                    all_center_errors.append(center_error)
                    all_size_errors.append(size_error)
                    all_losses.append(bbox_loss_results['total_loss'].item())
                    
                    # Store detailed results
                    result = {
                        'batch_idx': batch_idx,
                        'sample_idx': i,
                        'subject_id': batch['subject_id'][i].item(),
                        'lesion_id': batch['lesion_id'][i].item(),
                        'eeg_group': batch['eeg_group'][i].item(),
                        'compatibility_score': batch['compatibility_score'][i].item(),
                        'dice_score': dice_score,
                        'iou_score': iou_score,
                        'center_error': center_error,
                        'size_error': size_error,
                        'total_loss': bbox_loss_results['total_loss'].item(),
                        'dice_loss': bbox_loss_results['dice_loss'].item(),
                        'iou_loss': bbox_loss_results['iou_loss'].item(),
                        'focal_loss': bbox_loss_results['focal_loss'].item(),
                        'pred_center': pred_center.tolist(),
                        'pred_size': pred_size.tolist(),
                        'scale_weights': bbox_params['scale_weights'][i].cpu().numpy().tolist()
                    }
                    
                    detailed_results.append(result)
        
        # Compute summary statistics
        self.metrics_summary = {
            'dice_mean': np.mean(all_dice_scores),
            'dice_std': np.std(all_dice_scores),
            'dice_median': np.median(all_dice_scores),
            'iou_mean': np.mean(all_iou_scores),
            'iou_std': np.std(all_iou_scores),
            'iou_median': np.median(all_iou_scores),
            'center_error_mean': np.mean(all_center_errors),
            'center_error_std': np.std(all_center_errors),
            'center_error_median': np.median(all_center_errors),
            'size_error_mean': np.mean(all_size_errors),
            'size_error_std': np.std(all_size_errors),
            'loss_mean': np.mean(all_losses),
            'loss_std': np.std(all_losses),
            'n_samples': len(all_dice_scores)
        }
        
        # Store detailed results
        self.test_results = detailed_results
        
        # Save results if requested
        if save_results:
            self.save_evaluation_results()
        
        return self.metrics_summary
    
    def compute_dice_score(self, pred_mask: np.ndarray, target_mask: np.ndarray) -> float:
        """Compute Dice similarity coefficient"""
        intersection = np.sum(pred_mask * target_mask)
        union = np.sum(pred_mask) + np.sum(target_mask)
        
        if union == 0:
            return 1.0 if np.sum(target_mask) == 0 else 0.0
        
        dice = (2.0 * intersection) / union
        return dice
    
    def compute_iou_score(self, pred_mask: np.ndarray, target_mask: np.ndarray) -> float:
        """Compute 3D Intersection over Union"""
        intersection = np.sum(pred_mask * target_mask)
        union = np.sum(pred_mask) + np.sum(target_mask) - intersection
        
        if union == 0:
            return 1.0 if np.sum(target_mask) == 0 else 0.0
        
        iou = intersection / union
        return iou
    
    def compute_center_error(self, pred_center: np.ndarray, target_mask: np.ndarray) -> float:
        """Compute center localization error"""
        # Find true center of mass
        coords = np.where(target_mask > 0)
        if len(coords[0]) == 0:
            return 0.0  # No lesion present
        
        true_center = np.array([
            np.mean(coords[0]),
            np.mean(coords[1]),
            np.mean(coords[2])
        ])
        
        # Compute Euclidean distance
        error = np.linalg.norm(pred_center - true_center)
        return error
    
    def compute_size_error(self, pred_size: np.ndarray, target_mask: np.ndarray) -> float:
        """Compute size estimation error"""
        coords = np.where(target_mask > 0)
        if len(coords[0]) == 0:
            return np.linalg.norm(pred_size)  # Predicted size when no lesion
        
        # Compute true bounding box size
        true_size = np.array([
            np.ptp(coords[0]),  # Range in X
            np.ptp(coords[1]),  # Range in Y
            np.ptp(coords[2])   # Range in Z
        ])
        
        # Compute L2 norm of size difference
        size_error = np.linalg.norm(pred_size - true_size)
        return size_error
    
    def analyze_performance_by_groups(self) -> Dict[str, Dict[str, float]]:
        """Analyze performance across different groups"""
        
        if not self.test_results:
            print("No test results available. Run evaluate_model first.")
            return {}
        
        # Convert to DataFrame for easier analysis
        df = pd.DataFrame(self.test_results)
        
        # Group by EEG group (Epilepsy vs Control)
        group_analysis = {}
        
        for group_name, group_data in df.groupby('eeg_group'):
            group_label = 'Epilepsy' if group_name == 1.0 else 'Control'
            
            group_analysis[group_label] = {
                'n_samples': len(group_data),
                'dice_mean': group_data['dice_score'].mean(),
                'dice_std': group_data['dice_score'].std(),
                'iou_mean': group_data['iou_score'].mean(),
                'iou_std': group_data['iou_score'].std(),
                'center_error_mean': group_data['center_error'].mean(),
                'center_error_std': group_data['center_error'].std(),
                'size_error_mean': group_data['size_error'].mean(),
                'size_error_std': group_data['size_error'].std()
            }
        
        return group_analysis
    
    def identify_failure_cases(self, threshold_dice: float = 0.3, 
                              threshold_iou: float = 0.2) -> List[Dict]:
        """Identify and analyze failure cases"""
        
        if not self.test_results:
            print("No test results available. Run evaluate_model first.")
            return []
        
        failure_cases = []
        
        for result in self.test_results:
            if (result['dice_score'] < threshold_dice or 
                result['iou_score'] < threshold_iou):
                
                failure_cases.append({
                    'subject_id': result['subject_id'],
                    'lesion_id': result['lesion_id'],
                    'dice_score': result['dice_score'],
                    'iou_score': result['iou_score'],
                    'center_error': result['center_error'],
                    'size_error': result['size_error'],
                    'eeg_group': 'Epilepsy' if result['eeg_group'] == 1.0 else 'Control',
                    'compatibility_score': result['compatibility_score'],
                    'failure_type': self.classify_failure_type(result)
                })
        
        return failure_cases
    
    def classify_failure_type(self, result: Dict) -> str:
        """Classify the type of failure"""
        dice = result['dice_score']
        iou = result['iou_score']
        center_error = result['center_error']
        size_error = result['size_error']
        
        if center_error > 50:  # Large center error
            return 'Localization Error'
        elif size_error > 30:  # Large size error
            return 'Size Estimation Error'
        elif dice < 0.2 and iou < 0.1:
            return 'Complete Miss'
        elif dice < 0.5:
            return 'Poor Overlap'
        else:
            return 'Boundary Misalignment'
    
    def create_comprehensive_report(self, save_path: str = "performance_report.html"):
        """Create comprehensive performance analysis report"""
        
        if not self.test_results:
            print("No test results available. Run evaluate_model first.")
            return
        
        # Analyze performance by groups
        group_analysis = self.analyze_performance_by_groups()
        
        # Identify failure cases
        failure_cases = self.identify_failure_cases()
        
        # Create visualizations
        self.create_performance_plots()
        
        # Generate HTML report
        html_content = self.generate_html_report(group_analysis, failure_cases)
        
        with open(save_path, 'w') as f:
            f.write(html_content)
        
        print(f"Comprehensive report saved to {save_path}")
    
    def create_performance_plots(self):
        """Create comprehensive performance visualization plots"""
        
        if not self.test_results:
            return
        
        df = pd.DataFrame(self.test_results)
        
        # Create figure with subplots
        fig, axes = plt.subplots(3, 3, figsize=(18, 15))
        fig.suptitle('Epilepsy Localization Model - Performance Analysis', fontsize=16)
        
        # 1. Dice Score Distribution
        axes[0, 0].hist(df['dice_score'], bins=20, alpha=0.7, color='blue', edgecolor='black')
        axes[0, 0].axvline(df['dice_score'].mean(), color='red', linestyle='--', 
                          label=f'Mean: {df["dice_score"].mean():.3f}')
        axes[0, 0].set_title('Dice Score Distribution')
        axes[0, 0].set_xlabel('Dice Score')
        axes[0, 0].set_ylabel('Frequency')
        axes[0, 0].legend()
        
        # 2. IoU Score Distribution
        axes[0, 1].hist(df['iou_score'], bins=20, alpha=0.7, color='green', edgecolor='black')
        axes[0, 1].axvline(df['iou_score'].mean(), color='red', linestyle='--',
                          label=f'Mean: {df["iou_score"].mean():.3f}')
        axes[0, 1].set_title('IoU Score Distribution')
        axes[0, 1].set_xlabel('IoU Score')
        axes[0, 1].set_ylabel('Frequency')
        axes[0, 1].legend()
        
        # 3. Center Error Distribution
        axes[0, 2].hist(df['center_error'], bins=20, alpha=0.7, color='orange', edgecolor='black')
        axes[0, 2].axvline(df['center_error'].mean(), color='red', linestyle='--',
                          label=f'Mean: {df["center_error"].mean():.1f}')
        axes[0, 2].set_title('Center Error Distribution')
        axes[0, 2].set_xlabel('Center Error (voxels)')
        axes[0, 2].set_ylabel('Frequency')
        axes[0, 2].legend()
        
        # 4. Performance by EEG Group
        epilepsy_data = df[df['eeg_group'] == 1.0]
        control_data = df[df['eeg_group'] == 0.0]
        
        group_dice = [epilepsy_data['dice_score'].values, control_data['dice_score'].values]
        axes[1, 0].boxplot(group_dice, labels=['Epilepsy', 'Control'])
        axes[1, 0].set_title('Dice Score by EEG Group')
        axes[1, 0].set_ylabel('Dice Score')
        
        # 5. Dice vs IoU Correlation
        axes[1, 1].scatter(df['dice_score'], df['iou_score'], alpha=0.6)
        axes[1, 1].plot([0, 1], [0, 1], 'r--', alpha=0.8)
        axes[1, 1].set_xlabel('Dice Score')
        axes[1, 1].set_ylabel('IoU Score')
        axes[1, 1].set_title('Dice vs IoU Correlation')
        
        # Calculate correlation
        correlation = df['dice_score'].corr(df['iou_score'])
        axes[1, 1].text(0.05, 0.95, f'r = {correlation:.3f}', 
                       transform=axes[1, 1].transAxes, fontsize=12,
                       bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        # 6. Size Error vs Lesion Size
        # Estimate lesion size from compatibility score (proxy)
        axes[1, 2].scatter(df['compatibility_score'], df['size_error'], alpha=0.6)
        axes[1, 2].set_xlabel('Compatibility Score')
        axes[1, 2].set_ylabel('Size Error (voxels)')
        axes[1, 2].set_title('Size Error vs Compatibility')
        
        # 7. Scale Weight Analysis
        scale_weights = np.array([result['scale_weights'] for result in self.test_results])
        scale_means = np.mean(scale_weights, axis=0)
        scale_names = ['Small', 'Medium', 'Large']
        
        bars = axes[2, 0].bar(scale_names, scale_means, color=['lightblue', 'lightgreen', 'lightcoral'])
        axes[2, 0].set_title('Average Scale Weight Usage')
        axes[2, 0].set_ylabel('Average Weight')
        
        # Add value labels on bars
        for bar, value in zip(bars, scale_means):
            axes[2, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                           f'{value:.3f}', ha='center', va='bottom')
        
        # 8. Performance vs Compatibility Score
        axes[2, 1].scatter(df['compatibility_score'], df['dice_score'], alpha=0.6, color='blue', label='Dice')
        axes[2, 1].scatter(df['compatibility_score'], df['iou_score'], alpha=0.6, color='green', label='IoU')
        axes[2, 1].set_xlabel('Compatibility Score')
        axes[2, 1].set_ylabel('Performance Score')
        axes[2, 1].set_title('Performance vs Compatibility')
        axes[2, 1].legend()
        
        # 9. Loss Components
        loss_components = ['dice_loss', 'iou_loss', 'focal_loss']
        loss_means = [df[comp].mean() for comp in loss_components]
        
        bars = axes[2, 2].bar(loss_components, loss_means, color=['red', 'blue', 'green'])
        axes[2, 2].set_title('Average Loss Components')
        axes[2, 2].set_ylabel('Loss Value')
        axes[2, 2].tick_params(axis='x', rotation=45)
        
        # Add value labels
        for bar, value in zip(bars, loss_means):
            axes[2, 2].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001,
                           f'{value:.3f}', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig('performance_analysis_plots.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("Performance analysis plots saved as 'performance_analysis_plots.png'")
    
    def save_evaluation_results(self):
        """Save detailed evaluation results"""
        
        # Save detailed results
        results_df = pd.DataFrame(self.test_results)
        results_df.to_csv('detailed_test_results.csv', index=False)
        
        # Save metrics summary
        with open('metrics_summary.json', 'w') as f:
            json.dump(self.metrics_summary, f, indent=2)
        
        print("Evaluation results saved:")
        print("  - detailed_test_results.csv")
        print("  - metrics_summary.json")
    
    def generate_html_report(self, group_analysis: Dict, failure_cases: List[Dict]) -> str:
        """Generate HTML report content"""
        
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Epilepsy Localization Performance Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; }}
                .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
                .section {{ margin: 20px 0; }}
                .metrics-table {{ border-collapse: collapse; width: 100%; }}
                .metrics-table th, .metrics-table td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                .metrics-table th {{ background-color: #f2f2f2; }}
                .failure-case {{ background-color: #ffe6e6; padding: 10px; margin: 5px 0; border-radius: 3px; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Epilepsy Lesion Localization Performance Report</h1>
                <p>Comprehensive analysis of cubic bounding box performance</p>
            </div>
            
            <div class="section">
                <h2>Overall Performance Metrics</h2>
                <table class="metrics-table">
                    <tr><th>Metric</th><th>Mean</th><th>Std</th><th>Median</th></tr>
                    <tr><td>Dice Score</td><td>{self.metrics_summary['dice_mean']:.3f}</td><td>{self.metrics_summary['dice_std']:.3f}</td><td>{self.metrics_summary['dice_median']:.3f}</td></tr>
                    <tr><td>IoU Score</td><td>{self.metrics_summary['iou_mean']:.3f}</td><td>{self.metrics_summary['iou_std']:.3f}</td><td>{self.metrics_summary['iou_median']:.3f}</td></tr>
                    <tr><td>Center Error (voxels)</td><td>{self.metrics_summary['center_error_mean']:.1f}</td><td>{self.metrics_summary['center_error_std']:.1f}</td><td>{self.metrics_summary['center_error_median']:.1f}</td></tr>
                    <tr><td>Size Error (voxels)</td><td>{self.metrics_summary['size_error_mean']:.1f}</td><td>{self.metrics_summary['size_error_std']:.1f}</td><td>-</td></tr>
                </table>
                <p>Total samples evaluated: {self.metrics_summary['n_samples']}</p>
            </div>
            
            <div class="section">
                <h2>Performance by Group</h2>
        """
        
        for group_name, group_metrics in group_analysis.items():
            html += f"""
                <h3>{group_name} Group</h3>
                <table class="metrics-table">
                    <tr><th>Metric</th><th>Mean</th><th>Std</th></tr>
                    <tr><td>Dice Score</td><td>{group_metrics['dice_mean']:.3f}</td><td>{group_metrics['dice_std']:.3f}</td></tr>
                    <tr><td>IoU Score</td><td>{group_metrics['iou_mean']:.3f}</td><td>{group_metrics['iou_std']:.3f}</td></tr>
                    <tr><td>Center Error</td><td>{group_metrics['center_error_mean']:.1f}</td><td>{group_metrics['center_error_std']:.1f}</td></tr>
                </table>
                <p>Samples: {group_metrics['n_samples']}</p>
            """
        
        html += f"""
            </div>
            
            <div class="section">
                <h2>Failure Cases Analysis</h2>
                <p>Identified {len(failure_cases)} failure cases (Dice < 0.3 or IoU < 0.2)</p>
        """
        
        for case in failure_cases[:10]:  # Show first 10 failure cases
            html += f"""
                <div class="failure-case">
                    <strong>Subject {case['subject_id']}, Lesion {case['lesion_id']}</strong><br>
                    Group: {case['eeg_group']}, Failure Type: {case['failure_type']}<br>
                    Dice: {case['dice_score']:.3f}, IoU: {case['iou_score']:.3f}, Center Error: {case['center_error']:.1f}
                </div>
            """
        
        html += """
            </div>
        </body>
        </html>
        """
        
        return html

def main():
    """Main performance analysis function"""
    print("Epilepsy Localization Performance Analysis")
    print("="*50)
    
    # Check if model exists
    model_path = "models/best_model.pth"
    if not Path(model_path).exists():
        print(f"Model not found at {model_path}")
        print("Please train the model first using train_epilepsy_model.py")
        return
    
    # Initialize analyzer
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    analyzer = PerformanceAnalyzer(model_path, device)
    
    # Create test data loader
    try:
        _, _, test_loader = create_data_loaders(
            train_file="eeg_lesion_training_dataset/train_pairings.pkl",
            val_file="eeg_lesion_training_dataset/validation_pairings.pkl",
            test_file="eeg_lesion_training_dataset/test_pairings.pkl",
            batch_size=2,
            num_workers=0
        )
        
        print(f"Test loader created with {len(test_loader)} batches")
        
    except Exception as e:
        print(f"Error creating test loader: {e}")
        return
    
    # Evaluate model
    metrics = analyzer.evaluate_model(test_loader)
    
    # Print summary
    print("\nPerformance Summary:")
    print(f"  Dice Score: {metrics['dice_mean']:.3f} ± {metrics['dice_std']:.3f}")
    print(f"  IoU Score: {metrics['iou_mean']:.3f} ± {metrics['iou_std']:.3f}")
    print(f"  Center Error: {metrics['center_error_mean']:.1f} ± {metrics['center_error_std']:.1f} voxels")
    
    # Create comprehensive report
    analyzer.create_comprehensive_report()
    
    print("\n✓ Performance analysis completed!")

if __name__ == "__main__":
    main()
