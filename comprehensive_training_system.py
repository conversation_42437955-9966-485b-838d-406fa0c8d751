#!/usr/bin/env python3
"""
Comprehensive EEG-Lesion Training System
Final system integrating all pairing strategies and model preparation
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import pickle
import json
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

from generate_training_dataset import TrainingDatasetGenerator
from advanced_pairing_system import AdvancedPairingSystem

class ComprehensiveTrainingSystem:
    """Complete system for EEG-lesion training dataset generation"""
    
    def __init__(self, output_dir="comprehensive_eeg_lesion_dataset"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Initialize subsystems
        self.basic_generator = TrainingDatasetGenerator()
        self.advanced_system = AdvancedPairingSystem()
        
        print(f"Comprehensive training system initialized")
        print(f"Output directory: {self.output_dir}")
    
    def generate_comprehensive_dataset(self, max_eeg_files=100, max_lesions=50):
        """Generate comprehensive dataset with multiple pairing strategies"""
        print("Generating comprehensive EEG-lesion training dataset...")
        
        # Step 1: Process all data
        print("\n1. Processing EEG and lesion data...")
        eeg_data, lesion_data = self.basic_generator.process_all_data(
            max_eeg_files=max_eeg_files, 
            max_lesions=max_lesions
        )
        
        # Step 2: Create basic intelligent pairings
        print("\n2. Creating basic intelligent pairings...")
        basic_pairings = self.basic_generator.create_intelligent_pairings(
            eeg_data, lesion_data, top_k=3
        )
        
        # Step 3: Create advanced cluster-based pairings
        print("\n3. Creating advanced cluster-based pairings...")
        advanced_pairings, eeg_kmeans, lesion_kmeans = self.advanced_system.create_cluster_based_pairings(
            eeg_data, lesion_data, top_k=3
        )
        
        # Step 4: Combine and enhance pairings
        print("\n4. Combining and enhancing pairings...")
        combined_pairings = self.combine_pairing_strategies(basic_pairings, advanced_pairings)
        
        # Step 5: Create training splits
        print("\n5. Creating training splits...")
        splits = self.create_enhanced_splits(combined_pairings)
        
        # Step 6: Generate visualizations
        print("\n6. Creating comprehensive visualizations...")
        self.create_comprehensive_visualizations(
            eeg_data, lesion_data, combined_pairings, eeg_kmeans, lesion_kmeans
        )
        
        # Step 7: Save complete dataset
        print("\n7. Saving comprehensive dataset...")
        metadata = {
            'total_eeg_files': len(eeg_data),
            'total_lesions': len(lesion_data),
            'total_pairings': len(combined_pairings),
            'pairing_strategies': ['intelligent_clinical_spatial', 'cluster_based_ml'],
            'clustering_used': True,
            'top_k_matches': 3,
            'split_ratios': {'train': 0.7, 'validation': 0.15, 'test': 0.15}
        }
        
        self.save_comprehensive_dataset(splits, metadata, eeg_kmeans, lesion_kmeans)
        
        return splits, metadata
    
    def combine_pairing_strategies(self, basic_pairings, advanced_pairings):
        """Combine multiple pairing strategies"""
        print("Combining pairing strategies...")
        
        # Create a mapping of basic pairings
        basic_map = {}
        for pairing in basic_pairings:
            key = (pairing['eeg_subject_id'], pairing['lesion_id'])
            basic_map[key] = pairing
        
        # Enhance advanced pairings with basic information
        enhanced_pairings = []
        for adv_pairing in advanced_pairings:
            key = (adv_pairing['eeg_subject_id'], adv_pairing['lesion_id'])
            
            # Start with advanced pairing
            enhanced = adv_pairing.copy()
            
            # Add basic pairing information if available
            if key in basic_map:
                basic = basic_map[key]
                enhanced['basic_compatibility'] = basic['compatibility_score']
                enhanced['basic_spatial'] = basic['spatial_score']
                enhanced['basic_clinical'] = basic['clinical_score']
            else:
                enhanced['basic_compatibility'] = 0.5  # Default
                enhanced['basic_spatial'] = 0.5
                enhanced['basic_clinical'] = 0.5
            
            # Compute final combined score
            enhanced['final_score'] = (
                0.4 * enhanced['compatibility_score'] +  # Advanced score
                0.3 * enhanced.get('basic_compatibility', 0.5) +  # Basic score
                0.3 * enhanced['cluster_compatibility']  # Cluster score
            )
            
            enhanced_pairings.append(enhanced)
        
        # Sort by final score
        enhanced_pairings.sort(key=lambda x: x['final_score'], reverse=True)
        
        print(f"Combined {len(enhanced_pairings)} enhanced pairings")
        return enhanced_pairings
    
    def create_enhanced_splits(self, pairings):
        """Create enhanced training splits with stratification"""
        print("Creating enhanced training splits...")
        
        # Group by subject and ensure balanced splits
        subject_groups = {}
        for pairing in pairings:
            key = f"{pairing['eeg_dataset']}_{pairing['eeg_subject_id']}"
            if key not in subject_groups:
                subject_groups[key] = {
                    'pairings': [],
                    'group': pairing['eeg_group'],
                    'cluster': pairing.get('eeg_cluster', 0)
                }
            subject_groups[key]['pairings'].append(pairing)
        
        # Stratified split by group
        epilepsy_subjects = [k for k, v in subject_groups.items() if v['group'] == 'Epilepsy']
        control_subjects = [k for k, v in subject_groups.items() if v['group'] == 'Control']
        
        np.random.shuffle(epilepsy_subjects)
        np.random.shuffle(control_subjects)
        
        # Split each group proportionally
        def split_subjects(subjects, train_ratio=0.7, val_ratio=0.15):
            n = len(subjects)
            n_train = int(n * train_ratio)
            n_val = int(n * val_ratio)
            
            return {
                'train': subjects[:n_train],
                'validation': subjects[n_train:n_train + n_val],
                'test': subjects[n_train + n_val:]
            }
        
        epilepsy_splits = split_subjects(epilepsy_subjects)
        control_splits = split_subjects(control_subjects)
        
        # Combine splits
        splits = {'train': [], 'validation': [], 'test': []}
        
        for split_name in splits.keys():
            # Add epilepsy subjects
            for subject in epilepsy_splits[split_name]:
                splits[split_name].extend(subject_groups[subject]['pairings'])
            
            # Add control subjects
            for subject in control_splits[split_name]:
                splits[split_name].extend(subject_groups[subject]['pairings'])
        
        print(f"Enhanced splits created:")
        for split_name, split_pairings in splits.items():
            epilepsy_count = sum(1 for p in split_pairings if p['eeg_group'] == 'Epilepsy')
            control_count = sum(1 for p in split_pairings if p['eeg_group'] == 'Control')
            print(f"  {split_name}: {len(split_pairings)} total ({epilepsy_count} epilepsy, {control_count} control)")
        
        return splits
    
    def create_comprehensive_visualizations(self, eeg_data, lesion_data, pairings, eeg_kmeans, lesion_kmeans):
        """Create comprehensive visualizations"""
        print("Creating comprehensive visualizations...")
        
        # Create cluster visualizations
        cluster_fig = self.advanced_system.visualize_clusters(eeg_data, lesion_data, eeg_kmeans, lesion_kmeans)
        
        # Create pairing analysis visualization
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # 1. Final score distribution
        final_scores = [p['final_score'] for p in pairings]
        axes[0, 0].hist(final_scores, bins=30, alpha=0.7, edgecolor='black')
        axes[0, 0].set_title('Final Compatibility Score Distribution')
        axes[0, 0].set_xlabel('Final Score')
        axes[0, 0].set_ylabel('Frequency')
        
        # 2. Score component comparison
        score_components = {
            'Advanced': [p['compatibility_score'] for p in pairings],
            'Basic': [p.get('basic_compatibility', 0.5) for p in pairings],
            'Cluster': [p['cluster_compatibility'] for p in pairings],
            'Final': final_scores
        }
        
        box_data = [score_components[key] for key in score_components.keys()]
        axes[0, 1].boxplot(box_data, labels=list(score_components.keys()))
        axes[0, 1].set_title('Score Component Comparison')
        axes[0, 1].set_ylabel('Score Value')
        axes[0, 1].tick_params(axis='x', rotation=45)
        
        # 3. Cluster pairing matrix
        eeg_clusters = [p.get('eeg_cluster', 0) for p in pairings]
        lesion_clusters = [p.get('lesion_cluster', 0) for p in pairings]
        
        # Create pairing frequency matrix
        max_eeg_cluster = max(eeg_clusters) + 1
        max_lesion_cluster = max(lesion_clusters) + 1
        pairing_matrix = np.zeros((max_eeg_cluster, max_lesion_cluster))
        
        for eeg_c, lesion_c in zip(eeg_clusters, lesion_clusters):
            pairing_matrix[eeg_c, lesion_c] += 1
        
        im = axes[0, 2].imshow(pairing_matrix, cmap='Blues', aspect='auto')
        axes[0, 2].set_title('EEG-Lesion Cluster Pairing Matrix')
        axes[0, 2].set_xlabel('Lesion Cluster')
        axes[0, 2].set_ylabel('EEG Cluster')
        plt.colorbar(im, ax=axes[0, 2])
        
        # 4. Group vs final scores
        epilepsy_scores = [p['final_score'] for p in pairings if p['eeg_group'] == 'Epilepsy']
        control_scores = [p['final_score'] for p in pairings if p['eeg_group'] == 'Control']
        
        axes[1, 0].boxplot([epilepsy_scores, control_scores], labels=['Epilepsy', 'Control'])
        axes[1, 0].set_title('Final Score by EEG Group')
        axes[1, 0].set_ylabel('Final Score')
        
        # 5. Pairing quality distribution
        high_quality = sum(1 for p in pairings if p['final_score'] > 0.8)
        medium_quality = sum(1 for p in pairings if 0.6 <= p['final_score'] <= 0.8)
        low_quality = sum(1 for p in pairings if p['final_score'] < 0.6)
        
        quality_counts = [high_quality, medium_quality, low_quality]
        quality_labels = ['High (>0.8)', 'Medium (0.6-0.8)', 'Low (<0.6)']
        axes[1, 1].pie(quality_counts, labels=quality_labels, autopct='%1.1f%%')
        axes[1, 1].set_title('Pairing Quality Distribution')
        
        # 6. Score correlation matrix
        score_df = pd.DataFrame({
            'Advanced': [p['compatibility_score'] for p in pairings],
            'Basic': [p.get('basic_compatibility', 0.5) for p in pairings],
            'Cluster': [p['cluster_compatibility'] for p in pairings],
            'Final': final_scores
        })
        
        correlation_matrix = score_df.corr()
        sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0, ax=axes[1, 2])
        axes[1, 2].set_title('Score Correlation Matrix')
        
        plt.tight_layout()
        
        # Save comprehensive visualization
        output_file = self.output_dir / "comprehensive_analysis.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"Comprehensive analysis saved as: {output_file}")
        
        plt.show()
        
        return fig
    
    def save_comprehensive_dataset(self, splits, metadata, eeg_kmeans, lesion_kmeans):
        """Save the complete comprehensive dataset"""
        print(f"Saving comprehensive dataset to {self.output_dir}")
        
        # Save splits
        for split_name, pairings in splits.items():
            split_file = self.output_dir / f"{split_name}_pairings.pkl"
            with open(split_file, 'wb') as f:
                pickle.dump(pairings, f)
            print(f"Saved {len(pairings)} {split_name} pairings")
        
        # Save clustering models
        models_file = self.output_dir / "clustering_models.pkl"
        with open(models_file, 'wb') as f:
            pickle.dump({
                'eeg_kmeans': eeg_kmeans,
                'lesion_kmeans': lesion_kmeans,
                'scaler': self.advanced_system.scaler
            }, f)
        print("Saved clustering models")
        
        # Save metadata
        metadata_file = self.output_dir / "comprehensive_metadata.json"
        with open(metadata_file, 'w') as f:
            json.dump(metadata, f, indent=2)
        print("Saved comprehensive metadata")
        
        # Create dataset summary
        self.create_dataset_summary(splits, metadata)
        
        print("Comprehensive dataset saved successfully!")
    
    def create_dataset_summary(self, splits, metadata):
        """Create a comprehensive dataset summary"""
        summary_file = self.output_dir / "dataset_summary.md"
        
        with open(summary_file, 'w') as f:
            f.write("# Comprehensive EEG-Lesion Training Dataset\n\n")
            f.write("## Dataset Overview\n\n")
            f.write(f"- **Total EEG files processed**: {metadata['total_eeg_files']}\n")
            f.write(f"- **Total lesions processed**: {metadata['total_lesions']}\n")
            f.write(f"- **Total pairings created**: {metadata['total_pairings']}\n")
            f.write(f"- **Pairing strategies used**: {', '.join(metadata['pairing_strategies'])}\n")
            f.write(f"- **Machine learning clustering**: {'Yes' if metadata['clustering_used'] else 'No'}\n\n")
            
            f.write("## Dataset Splits\n\n")
            for split_name, pairings in splits.items():
                epilepsy_count = sum(1 for p in pairings if p['eeg_group'] == 'Epilepsy')
                control_count = sum(1 for p in pairings if p['eeg_group'] == 'Control')
                f.write(f"- **{split_name.title()}**: {len(pairings)} pairings ({epilepsy_count} epilepsy, {control_count} control)\n")
            
            f.write("\n## Features Included\n\n")
            f.write("- Comprehensive EEG spectral features (292 features per recording)\n")
            f.write("- EEG source localization estimates\n")
            f.write("- Lesion spatial and morphological characteristics\n")
            f.write("- Multiple compatibility scores (spatial, clinical, cluster-based)\n")
            f.write("- Machine learning cluster assignments\n")
            f.write("- Clinical metadata (epilepsy vs control, recording conditions)\n\n")
            
            f.write("## Usage Instructions\n\n")
            f.write("1. Load pairings using pickle: `pickle.load(open('train_pairings.pkl', 'rb'))`\n")
            f.write("2. Extract EEG features from `pairing['eeg_features']`\n")
            f.write("3. Extract lesion masks using `pairing['lesion_id']`\n")
            f.write("4. Use compatibility scores for quality filtering\n")
            f.write("5. Apply clustering models for new data classification\n\n")
            
            f.write("## Quality Metrics\n\n")
            all_pairings = []
            for pairings in splits.values():
                all_pairings.extend(pairings)
            
            avg_score = np.mean([p['final_score'] for p in all_pairings])
            high_quality = sum(1 for p in all_pairings if p['final_score'] > 0.8)
            
            f.write(f"- **Average compatibility score**: {avg_score:.3f}\n")
            f.write(f"- **High-quality pairings (>0.8)**: {high_quality} ({high_quality/len(all_pairings)*100:.1f}%)\n")
        
        print(f"Dataset summary saved as: {summary_file}")

def main():
    """Generate the comprehensive training dataset"""
    print("Comprehensive EEG-Lesion Training Dataset Generation")
    print("="*60)
    
    # Initialize comprehensive system
    system = ComprehensiveTrainingSystem()
    
    # Generate comprehensive dataset
    splits, metadata = system.generate_comprehensive_dataset(
        max_eeg_files=100,  # Adjust based on computational resources
        max_lesions=50      # Adjust based on available lesions
    )
    
    print("\n" + "="*60)
    print("COMPREHENSIVE DATASET GENERATION COMPLETED!")
    print("="*60)
    print(f"Dataset saved in: {system.output_dir}")
    print("Ready for epilepsy lesion localization model training!")

if __name__ == "__main__":
    main()
