#!/usr/bin/env python3
"""
EEG-MRI病灶定位深度学习框架
实现多模态融合的病灶定位训练
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import pandas as pd
import nibabel as nib
import gzip
import glob
import os
from sklearn.preprocessing import StandardScaler
from scipy.interpolate import griddata
import matplotlib.pyplot as plt
import warnings

warnings.filterwarnings('ignore')

# 设置设备
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

class EEGDataProcessor:
    """EEG数据预处理器"""
    
    def __init__(self):
        # 标准10-20系统电极位置 (14个电极)
        self.electrode_positions = {
            'AF3': (-0.3, 0.7), 'AF4': (0.3, 0.7),
            'F3': (-0.5, 0.5), 'F4': (0.5, 0.5),
            'F7': (-0.7, 0.3), 'F8': (0.7, 0.3),
            'FC5': (-0.6, 0.2), 'FC6': (0.6, 0.2),
            'T7': (-0.8, 0.0), 'T8': (0.8, 0.0),
            'P7': (-0.7, -0.3), 'P8': (0.7, -0.3),
            'O1': (-0.3, -0.7), 'O2': (0.3, -0.7)
        }
        
        self.scaler = StandardScaler()
        self.eeg_channels = list(self.electrode_positions.keys())
        
    def load_eeg_file(self, file_path):
        """加载单个EEG文件"""
        try:
            with gzip.open(file_path, 'rt') as f:
                data = pd.read_csv(f)
            
            # 提取EEG通道数据
            eeg_data = data[self.eeg_channels].values
            return eeg_data
        except Exception as e:
            print(f"Error loading {file_path}: {e}")
            return None
    
    def preprocess_eeg(self, eeg_data):
        """预处理EEG数据"""
        # 1. 归一化
        eeg_normalized = self.scaler.fit_transform(eeg_data)
        
        # 2. 滑动窗口分割 (1秒窗口，假设128Hz采样率)
        window_size = 128
        stride = 64
        
        windows = []
        for i in range(0, len(eeg_normalized) - window_size + 1, stride):
            window = eeg_normalized[i:i + window_size]
            windows.append(window)
        
        return np.array(windows)
    
    def eeg_to_topomap(self, eeg_window):
        """将EEG窗口转换为2D地形图"""
        # 创建64x64的地形图
        grid_size = 64
        xi = np.linspace(-1, 1, grid_size)
        yi = np.linspace(-1, 1, grid_size)
        xi, yi = np.meshgrid(xi, yi)
        
        # 获取电极位置和值
        positions = np.array([self.electrode_positions[ch] for ch in self.eeg_channels])
        values = eeg_window.mean(axis=0)  # 对时间维度取平均
        
        # 插值生成地形图
        topomap = griddata(positions, values, (xi, yi), method='cubic', fill_value=0)
        
        # 创建头部掩码
        mask = (xi**2 + yi**2) <= 1.0
        topomap = topomap * mask
        
        return topomap

class AttentionCNN(nn.Module):
    """多层注意力CNN用于EEG特征提取"""
    
    def __init__(self, input_channels=1):
        super(AttentionCNN, self).__init__()
        
        # CNN层
        self.conv1 = nn.Conv2d(input_channels, 32, 3, padding=1)
        self.conv2 = nn.Conv2d(32, 64, 3, padding=1)
        self.conv3 = nn.Conv2d(64, 128, 3, padding=1)
        self.conv4 = nn.Conv2d(128, 256, 3, padding=1)
        
        # 注意力机制
        self.attention1 = SpatialAttention(32)
        self.attention2 = SpatialAttention(64)
        self.attention3 = SpatialAttention(128)
        self.attention4 = SpatialAttention(256)
        
        # 池化层
        self.pool = nn.MaxPool2d(2, 2)
        self.adaptive_pool = nn.AdaptiveAvgPool2d((4, 4))
        
        # Dropout
        self.dropout = nn.Dropout(0.3)
        
    def forward(self, x):
        features = []
        
        # 第一层
        x = F.relu(self.conv1(x))
        x = self.attention1(x)
        features.append(x)
        x = self.pool(x)
        
        # 第二层
        x = F.relu(self.conv2(x))
        x = self.attention2(x)
        features.append(x)
        x = self.pool(x)
        
        # 第三层
        x = F.relu(self.conv3(x))
        x = self.attention3(x)
        features.append(x)
        x = self.pool(x)
        
        # 第四层
        x = F.relu(self.conv4(x))
        x = self.attention4(x)
        features.append(x)
        x = self.adaptive_pool(x)
        
        return x, features

class SpatialAttention(nn.Module):
    """空间注意力模块"""
    
    def __init__(self, channels):
        super(SpatialAttention, self).__init__()
        self.conv = nn.Conv2d(channels, 1, 1)
        self.sigmoid = nn.Sigmoid()
        
    def forward(self, x):
        attention = self.conv(x)
        attention = self.sigmoid(attention)
        return x * attention

class EEGTemporalLSTM(nn.Module):
    """LSTM用于学习EEG时间序列关系"""
    
    def __init__(self, input_size=256*4*4, hidden_size=512, num_layers=2):
        super(EEGTemporalLSTM, self).__init__()
        
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, 
                           batch_first=True, dropout=0.3)
        self.fc = nn.Linear(hidden_size, 256)
        
    def forward(self, x):
        # x shape: (batch, sequence, features)
        lstm_out, (h_n, c_n) = self.lstm(x)
        
        # 使用最后一个时间步的输出
        output = self.fc(lstm_out[:, -1, :])
        return output

class MRIVoxelPredictor(nn.Module):
    """MRI体素概率预测器"""
    
    def __init__(self, eeg_features=256, mri_shape=(8, 8, 8)):
        super(MRIVoxelPredictor, self).__init__()
        
        self.mri_shape = mri_shape
        total_voxels = np.prod(mri_shape)
        
        # 特征融合层
        self.fusion_fc = nn.Sequential(
            nn.Linear(eeg_features, 512),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(512, 1024),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(1024, total_voxels),
            nn.Sigmoid()  # 输出概率
        )
        
    def forward(self, eeg_features):
        voxel_probs = self.fusion_fc(eeg_features)
        return voxel_probs.view(-1, *self.mri_shape)

class ProgressiveResolutionMRI(nn.Module):
    """渐进式分辨率MRI预测器"""
    
    def __init__(self, eeg_features=256, cnn_features=None):
        super(ProgressiveResolutionMRI, self).__init__()
        
        self.current_resolution = 8
        self.target_resolution = 256
        
        # 不同分辨率的预测器
        self.predictors = nn.ModuleDict({
            '8': MRIVoxelPredictor(eeg_features, (8, 8, 8)),
            '16': MRIVoxelPredictor(eeg_features + 64, (16, 16, 16)),
            '32': MRIVoxelPredictor(eeg_features + 128, (32, 32, 32)),
            '64': MRIVoxelPredictor(eeg_features + 256, (64, 64, 64)),
        })
        
        # 上采样层
        self.upsample = nn.Upsample(scale_factor=2, mode='trilinear', align_corners=False)
        
    def forward(self, eeg_features, cnn_features=None, resolution=8):
        resolution_str = str(resolution)
        
        if resolution_str in self.predictors:
            if cnn_features is not None and resolution > 8:
                # 融合CNN特征
                combined_features = torch.cat([eeg_features, cnn_features], dim=1)
                return self.predictors[resolution_str](combined_features)
            else:
                return self.predictors[resolution_str](eeg_features)
        else:
            raise ValueError(f"Resolution {resolution} not supported")

class EEGMRIDataset(Dataset):
    """EEG-MRI数据集"""
    
    def __init__(self, eeg_files, mask_files, processor):
        self.eeg_files = eeg_files
        self.mask_files = mask_files
        self.processor = processor
        
    def __len__(self):
        return len(self.eeg_files)
    
    def __getitem__(self, idx):
        # 加载EEG数据
        eeg_data = self.processor.load_eeg_file(self.eeg_files[idx])
        if eeg_data is None:
            return None
        
        # 预处理EEG
        eeg_windows = self.processor.preprocess_eeg(eeg_data)
        
        # 转换为地形图序列
        topomaps = []
        for window in eeg_windows[:10]:  # 取前10个窗口
            topomap = self.processor.eeg_to_topomap(window)
            topomaps.append(topomap)
        
        topomaps = np.array(topomaps)
        
        # 加载对应的MRI掩码
        mask_file = self.mask_files[idx % len(self.mask_files)]  # 简单映射
        mask_img = nib.load(mask_file)
        mask_data = mask_img.get_fdata()
        
        # 下采样到8x8x8
        mask_8x8x8 = self._downsample_mask(mask_data, (8, 8, 8))
        
        return {
            'topomaps': torch.FloatTensor(topomaps).unsqueeze(1),  # (seq, 1, 64, 64)
            'mask_8x8x8': torch.FloatTensor(mask_8x8x8),
            'mask_original': torch.FloatTensor(mask_data > 0)
        }
    
    def _downsample_mask(self, mask, target_shape):
        """下采样掩码到目标形状"""
        from scipy.ndimage import zoom
        
        zoom_factors = [target_shape[i] / mask.shape[i] for i in range(3)]
        downsampled = zoom(mask, zoom_factors, order=1)
        return (downsampled > 0.5).astype(np.float32)

class EEGMRILocalizationModel(nn.Module):
    """完整的EEG-MRI病灶定位模型"""
    
    def __init__(self):
        super(EEGMRILocalizationModel, self).__init__()
        
        self.cnn = AttentionCNN(input_channels=1)
        self.lstm = EEGTemporalLSTM()
        self.mri_predictor = ProgressiveResolutionMRI()
        
    def forward(self, topomaps, resolution=8, return_features=False):
        batch_size, seq_len = topomaps.shape[:2]
        
        # CNN特征提取
        cnn_features_list = []
        cnn_feature_maps = []
        
        for i in range(seq_len):
            features, feature_maps = self.cnn(topomaps[:, i])
            cnn_features_list.append(features.view(batch_size, -1))
            cnn_feature_maps.append(feature_maps)
        
        # LSTM时间序列学习
        cnn_features = torch.stack(cnn_features_list, dim=1)
        eeg_features = self.lstm(cnn_features)
        
        # MRI预测
        if resolution > 8 and cnn_feature_maps:
            # 使用CNN特征进行融合
            combined_cnn_features = cnn_feature_maps[-1][-1].view(batch_size, -1)
            mri_prediction = self.mri_predictor(eeg_features, combined_cnn_features, resolution)
        else:
            mri_prediction = self.mri_predictor(eeg_features, resolution=resolution)
        
        if return_features:
            return mri_prediction, eeg_features, cnn_feature_maps
        return mri_prediction

def create_data_loaders():
    """创建数据加载器"""
    print("=== 创建数据加载器 ===")
    
    # 获取EEG文件
    eeg_files = glob.glob("1252141/EEGs_Nigeria/*.csv.gz")[:50]  # 限制数量用于测试
    
    # 获取MRI掩码文件
    mask_files = glob.glob("masks-2/*/[0-9]*_MaskInRawData.nii.gz")[:50]
    
    print(f"EEG files: {len(eeg_files)}")
    print(f"Mask files: {len(mask_files)}")
    
    # 创建处理器
    processor = EEGDataProcessor()
    
    # 创建数据集
    dataset = EEGMRIDataset(eeg_files, mask_files, processor)
    
    # 创建数据加载器
    train_loader = DataLoader(dataset, batch_size=4, shuffle=True, 
                             collate_fn=lambda x: [item for item in x if item is not None])
    
    return train_loader, processor

def train_progressive_resolution():
    """渐进式分辨率训练"""
    print("=== 开始渐进式分辨率训练 ===")
    
    # 创建数据和模型
    train_loader, processor = create_data_loaders()
    model = EEGMRILocalizationModel().to(device)
    
    # 损失函数和优化器
    criterion = nn.BCELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)
    scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=10, gamma=0.5)
    
    # 训练阶段
    resolutions = [8, 16, 32, 64]
    epochs_per_resolution = [20, 15, 10, 10]
    
    for res_idx, (resolution, epochs) in enumerate(zip(resolutions, epochs_per_resolution)):
        print(f"\n--- 训练分辨率 {resolution}x{resolution}x{resolution} ---")
        
        for epoch in range(epochs):
            model.train()
            total_loss = 0
            num_batches = 0
            
            for batch_idx, batch in enumerate(train_loader):
                if len(batch) == 0:
                    continue
                
                # 准备数据
                topomaps = torch.stack([item['topomaps'] for item in batch]).to(device)
                targets = torch.stack([item['mask_8x8x8'] for item in batch]).to(device)
                
                # 前向传播
                optimizer.zero_grad()
                predictions = model(topomaps, resolution=resolution)
                
                # 调整目标大小
                if resolution != 8:
                    targets = F.interpolate(targets.unsqueeze(1), 
                                          size=(resolution, resolution, resolution), 
                                          mode='trilinear', align_corners=False).squeeze(1)
                
                loss = criterion(predictions, targets)
                
                # 反向传播
                loss.backward()
                optimizer.step()
                
                total_loss += loss.item()
                num_batches += 1
                
                if batch_idx % 5 == 0:
                    print(f"Epoch {epoch+1}/{epochs}, Batch {batch_idx}, Loss: {loss.item():.4f}")
            
            avg_loss = total_loss / max(num_batches, 1)
            print(f"Epoch {epoch+1}/{epochs} completed, Average Loss: {avg_loss:.4f}")
            
            scheduler.step()
    
    # 保存模型
    torch.save(model.state_dict(), 'eeg_mri_localization_model.pth')
    print("模型已保存: eeg_mri_localization_model.pth")
    
    return model

def main():
    """主函数"""
    print("=== EEG-MRI病灶定位深度学习框架 ===")
    
    # 检查数据
    if not os.path.exists("1252141"):
        print("错误: 找不到EEG数据文件夹!")
        return
    
    if not os.path.exists("masks-2"):
        print("错误: 找不到MRI掩码文件夹!")
        return
    
    # 开始训练
    model = train_progressive_resolution()
    
    print("\n=== 训练完成 ===")
    print("生成的文件:")
    print("- eeg_mri_localization_model.pth: 训练好的模型")
    print("- eeg_data_exploration.png: EEG数据探索结果")

if __name__ == "__main__":
    main()
