#!/usr/bin/env python3
"""
Generate Comprehensive EEG-Lesion Training Dataset
Creates synthetic patient-lesion pairings for epilepsy localization model training
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import pickle
import json
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

from eeg_lesion_pairing_system import IntelligentPairingSystem

class TrainingDatasetGenerator:
    """Generate comprehensive training dataset with EEG-lesion pairings"""
    
    def __init__(self, output_dir="eeg_lesion_training_dataset"):
        self.pairing_system = IntelligentPairingSystem()
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Load EEG metadata
        self.gb_metadata = pd.read_csv("1252141/metadata_guineabissau.csv")
        self.ng_metadata = pd.read_csv("1252141/metadata_nigeria.csv")
        
        print(f"Initialized training dataset generator")
        print(f"Output directory: {self.output_dir}")
    
    def get_available_lesions(self):
        """Get list of available lesion IDs"""
        lesion_dirs = [d for d in Path("masks-2").iterdir() if d.is_dir()]
        lesion_ids = []
        
        for lesion_dir in lesion_dirs:
            try:
                lesion_id = int(lesion_dir.name)
                mask_file = lesion_dir / f"{lesion_id}_MaskInOrig.nii.gz"
                if mask_file.exists():
                    lesion_ids.append(lesion_id)
            except ValueError:
                continue
        
        return sorted(lesion_ids)
    
    def get_available_eeg_files(self):
        """Get list of available EEG files with metadata"""
        eeg_files = []
        
        # Guinea-Bissau files
        gb_dir = Path("1252141/EEGs_Guinea-Bissau")
        for idx, row in self.gb_metadata.iterrows():
            subject_id = row['subject.id']
            eeg_file = gb_dir / f"signal-{subject_id}.csv.gz"
            if eeg_file.exists():
                eeg_files.append({
                    'filepath': eeg_file,
                    'subject_id': subject_id,
                    'dataset': 'guinea_bissau',
                    'group': row['Group'],
                    'condition': row['Eyes.condition'],
                    'duration': row['recordedPeriod']
                })
        
        # Nigeria files
        ng_dir = Path("1252141/EEGs_Nigeria")
        for idx, row in self.ng_metadata.iterrows():
            subject_id = row['subject.id']
            session_id = row.get('session.id', 1)
            eeg_file = ng_dir / f"signal-{subject_id}-{session_id}.csv.gz"
            if eeg_file.exists():
                eeg_files.append({
                    'filepath': eeg_file,
                    'subject_id': subject_id,
                    'dataset': 'nigeria',
                    'group': row['Group'],
                    'condition': row['first_condition'],
                    'duration': row['recordedPeriod']
                })
        
        return eeg_files
    
    def process_all_data(self, max_eeg_files=None, max_lesions=None):
        """Process all EEG files and lesions to extract features"""
        print("Processing EEG files and lesions...")
        
        # Get available data
        eeg_files = self.get_available_eeg_files()
        lesion_ids = self.get_available_lesions()
        
        if max_eeg_files:
            eeg_files = eeg_files[:max_eeg_files]
        if max_lesions:
            lesion_ids = lesion_ids[:max_lesions]
        
        print(f"Processing {len(eeg_files)} EEG files and {len(lesion_ids)} lesions")
        
        # Process EEG files
        eeg_data = []
        print("Extracting EEG features...")
        for eeg_info in tqdm(eeg_files, desc="EEG Processing"):
            try:
                # Extract EEG features
                eeg_features = self.pairing_system.eeg_extractor.extract_all_features(eeg_info['filepath'])
                if eeg_features is None:
                    continue
                
                # Extract source localization
                eeg_raw, _ = self.pairing_system.eeg_extractor.load_eeg_data(eeg_info['filepath'])
                source_features = self.pairing_system.source_localizer.estimate_source_location(eeg_raw)
                
                # Combine all EEG information
                eeg_record = {
                    **eeg_info,
                    **eeg_features,
                    **source_features
                }
                eeg_data.append(eeg_record)
                
            except Exception as e:
                print(f"Error processing EEG {eeg_info['filepath']}: {e}")
                continue
        
        # Process lesions
        lesion_data = []
        print("Extracting lesion features...")
        for lesion_id in tqdm(lesion_ids, desc="Lesion Processing"):
            try:
                lesion_features = self.pairing_system.lesion_analyzer.compute_lesion_features(lesion_id)
                if lesion_features is not None:
                    lesion_data.append(lesion_features)
            except Exception as e:
                print(f"Error processing lesion {lesion_id}: {e}")
                continue
        
        print(f"Successfully processed {len(eeg_data)} EEG files and {len(lesion_data)} lesions")
        
        return eeg_data, lesion_data
    
    def create_intelligent_pairings(self, eeg_data, lesion_data, top_k=5):
        """Create intelligent EEG-lesion pairings"""
        print(f"Creating intelligent pairings (top-{top_k} matches per EEG)...")
        
        pairings = []
        
        for eeg_record in tqdm(eeg_data, desc="Creating Pairings"):
            # Extract EEG features and metadata
            eeg_features = {k: v for k, v in eeg_record.items() 
                           if k not in ['filepath', 'subject_id', 'dataset', 'group', 'condition', 'duration']}
            
            source_features = {k: v for k, v in eeg_record.items() 
                             if k.startswith('source_')}
            
            metadata = {
                'Group': eeg_record['group'],
                'condition': eeg_record['condition'],
                'dataset': eeg_record['dataset']
            }
            
            # Compute compatibility with all lesions
            lesion_scores = []
            for lesion_record in lesion_data:
                try:
                    scores = self.pairing_system.compute_pairing_score(
                        eeg_features, source_features, lesion_record, metadata
                    )
                    lesion_scores.append({
                        'lesion_id': lesion_record['lesion_id'],
                        'lesion_features': lesion_record,
                        **scores
                    })
                except Exception as e:
                    continue
            
            # Sort by total score and take top-k
            lesion_scores.sort(key=lambda x: x['total_score'], reverse=True)
            top_lesions = lesion_scores[:top_k]
            
            # Create pairings
            for rank, lesion_match in enumerate(top_lesions):
                pairing = {
                    'eeg_subject_id': eeg_record['subject_id'],
                    'eeg_dataset': eeg_record['dataset'],
                    'eeg_group': eeg_record['group'],
                    'lesion_id': lesion_match['lesion_id'],
                    'pairing_rank': rank + 1,
                    'compatibility_score': lesion_match['total_score'],
                    'spatial_score': lesion_match['spatial_score'],
                    'clinical_score': lesion_match['clinical_score'],
                    'metadata_score': lesion_match['metadata_score'],
                    'eeg_features': eeg_features,
                    'lesion_features': lesion_match['lesion_features']
                }
                pairings.append(pairing)
        
        print(f"Created {len(pairings)} EEG-lesion pairings")
        return pairings
    
    def create_training_splits(self, pairings, train_ratio=0.7, val_ratio=0.15, test_ratio=0.15):
        """Create training, validation, and test splits"""
        assert abs(train_ratio + val_ratio + test_ratio - 1.0) < 1e-6, "Split ratios must sum to 1.0"
        
        # Group pairings by EEG subject to avoid data leakage
        subject_groups = {}
        for pairing in pairings:
            key = f"{pairing['eeg_dataset']}_{pairing['eeg_subject_id']}"
            if key not in subject_groups:
                subject_groups[key] = []
            subject_groups[key].append(pairing)
        
        # Split subjects
        subjects = list(subject_groups.keys())
        np.random.shuffle(subjects)
        
        n_subjects = len(subjects)
        n_train = int(n_subjects * train_ratio)
        n_val = int(n_subjects * val_ratio)
        
        train_subjects = subjects[:n_train]
        val_subjects = subjects[n_train:n_train + n_val]
        test_subjects = subjects[n_train + n_val:]
        
        # Create splits
        train_pairings = []
        val_pairings = []
        test_pairings = []
        
        for subject in train_subjects:
            train_pairings.extend(subject_groups[subject])
        for subject in val_subjects:
            val_pairings.extend(subject_groups[subject])
        for subject in test_subjects:
            test_pairings.extend(subject_groups[subject])
        
        print(f"Dataset splits:")
        print(f"  Training: {len(train_pairings)} pairings ({len(train_subjects)} subjects)")
        print(f"  Validation: {len(val_pairings)} pairings ({len(val_subjects)} subjects)")
        print(f"  Test: {len(test_pairings)} pairings ({len(test_subjects)} subjects)")
        
        return {
            'train': train_pairings,
            'validation': val_pairings,
            'test': test_pairings
        }
    
    def save_dataset(self, splits, metadata=None):
        """Save the complete dataset"""
        print(f"Saving dataset to {self.output_dir}")
        
        # Save splits
        for split_name, pairings in splits.items():
            split_file = self.output_dir / f"{split_name}_pairings.pkl"
            with open(split_file, 'wb') as f:
                pickle.dump(pairings, f)
            print(f"Saved {len(pairings)} {split_name} pairings to {split_file}")
        
        # Save metadata
        if metadata:
            metadata_file = self.output_dir / "dataset_metadata.json"
            with open(metadata_file, 'w') as f:
                json.dump(metadata, f, indent=2)
            print(f"Saved metadata to {metadata_file}")
        
        print("Dataset saved successfully!")

def main():
    """Generate the complete training dataset"""
    print("EEG-Lesion Training Dataset Generation")
    print("="*50)
    
    # Initialize generator
    generator = TrainingDatasetGenerator()
    
    # Process data (limit for testing)
    eeg_data, lesion_data = generator.process_all_data(max_eeg_files=50, max_lesions=100)
    
    # Create intelligent pairings
    pairings = generator.create_intelligent_pairings(eeg_data, lesion_data, top_k=3)
    
    # Create training splits
    splits = generator.create_training_splits(pairings)
    
    # Create metadata
    metadata = {
        'total_eeg_files': len(eeg_data),
        'total_lesions': len(lesion_data),
        'total_pairings': len(pairings),
        'pairing_method': 'intelligent_clinical_spatial',
        'top_k_matches': 3,
        'split_ratios': {'train': 0.7, 'validation': 0.15, 'test': 0.15}
    }
    
    # Save dataset
    generator.save_dataset(splits, metadata)
    
    print("\nTraining dataset generation completed!")

if __name__ == "__main__":
    main()
