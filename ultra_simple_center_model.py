#!/usr/bin/env python3
"""
超简化中心定位模型
彻底解决梯度爆炸问题，专注于基本的中心定位功能
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import time
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

from fixed_training_system import NormalizedEEGLesionDataset
from torch.utils.data import DataLoader

class UltraSimpleCenterModel(nn.Module):
    """超简化的中心定位模型 - 最小化梯度爆炸风险"""
    
    def __init__(self, n_channels=14):
        super().__init__()
        
        # 极简EEG特征提取 - 只用全局平均池化
        self.eeg_global_pool = nn.AdaptiveAvgPool1d(1)
        self.eeg_fc = nn.Sequential(
            nn.Linear(n_channels, 32),
            nn.LayerNorm(32),  # 使用LayerNorm替代BatchNorm
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # 极简时序特征提取 - 只用平均池化
        self.temporal_pool = nn.AdaptiveAvgPool1d(1)  # 对时序维度池化
        self.temporal_fc = nn.Sequential(
            nn.Linear(n_channels, 32),
            nn.LayerNorm(32),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # 极简融合和预测
        self.predictor = nn.Sequential(
            nn.Linear(64, 32),
            nn.LayerNorm(32),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(32, 3),  # 直接输出3个坐标
            nn.Sigmoid()  # 归一化到[0,1]
        )
        
        # 保守的权重初始化
        self.apply(self._init_weights)
    
    def _init_weights(self, m):
        """极保守的权重初始化"""
        if isinstance(m, nn.Linear):
            # 使用极小的初始化
            nn.init.normal_(m.weight, mean=0.0, std=0.01)
            if m.bias is not None:
                nn.init.zeros_(m.bias)
    
    def forward(self, eeg_data, temporal_sequence):
        """前向传播"""
        batch_size = eeg_data.shape[0]
        
        # EEG特征：全局平均池化
        eeg_pooled = self.eeg_global_pool(eeg_data).squeeze(-1)  # [batch, n_channels]
        eeg_feat = self.eeg_fc(eeg_pooled)
        
        # 时序特征：对时序维度平均池化
        temporal_pooled = torch.mean(temporal_sequence, dim=1)  # [batch, n_channels]
        temporal_feat = self.temporal_fc(temporal_pooled)
        
        # 特征融合
        fused = torch.cat([eeg_feat, temporal_feat], dim=1)
        
        # 预测中心坐标
        center_normalized = self.predictor(fused)
        center_coords = center_normalized * 255.0
        
        return {
            'center': center_coords,
            'center_normalized': center_normalized
        }

class UltraSimpleLoss(nn.Module):
    """超简化损失函数 - 避免数值爆炸"""
    
    def __init__(self):
        super().__init__()
        # 只使用L1损失，避免平方项导致的数值爆炸
        self.l1_loss = nn.L1Loss()
    
    def forward(self, pred_center, target_masks):
        """计算损失"""
        batch_size = pred_center.shape[0]
        device = pred_center.device
        
        # 计算真实中心
        true_centers = []
        for i in range(batch_size):
            mask = target_masks[i]
            if torch.sum(mask) > 0:
                coords = torch.nonzero(mask, as_tuple=False).float()
                center = torch.mean(coords, dim=0)  # (z, y, x)
                center = center[[2, 1, 0]]  # 转换为 (x, y, z)
            else:
                center = torch.tensor([127.5, 127.5, 127.5], device=device)
            true_centers.append(center)
        
        true_centers = torch.stack(true_centers).to(device)
        
        # 只使用L1损失
        l1_loss = self.l1_loss(pred_center, true_centers)
        
        # 计算距离（用于监控，不参与反向传播）
        with torch.no_grad():
            distances = torch.norm(pred_center - true_centers, dim=1)
            mean_distance = torch.mean(distances)
        
        return {
            'total_loss': l1_loss,
            'l1_loss': l1_loss,
            'mean_distance': mean_distance,
            'distances': distances,
            'true_centers': true_centers
        }

class UltraSimpleTrainer:
    """超简化训练器"""
    
    def __init__(self, model, device):
        self.model = model.to(device)
        self.device = device
        self.criterion = UltraSimpleLoss()
        
        # 极低的学习率
        self.optimizer = optim.Adam(
            self.model.parameters(),
            lr=1e-5,  # 极低学习率
            weight_decay=0,  # 不使用权重衰减
            eps=1e-8
        )
        
        # 简单的学习率调度
        self.scheduler = optim.lr_scheduler.StepLR(
            self.optimizer, step_size=5, gamma=0.8
        )
        
        self.history = {
            'train_loss': [], 'val_loss': [],
            'train_distance': [], 'val_distance': []
        }
    
    def train_epoch(self, train_loader):
        """训练一个epoch"""
        self.model.train()
        
        total_loss = 0.0
        total_distance = 0.0
        num_batches = 0
        
        for batch_idx, batch in enumerate(tqdm(train_loader, desc="Training")):
            try:
                eeg_data = batch['eeg_data'].to(self.device)
                temporal_sequence = batch['temporal_sequence'].to(self.device)
                lesion_masks = batch['lesion_mask'].to(self.device)
                
                self.optimizer.zero_grad()
                outputs = self.model(eeg_data, temporal_sequence)
                loss_results = self.criterion(outputs['center'], lesion_masks)
                loss = loss_results['total_loss']
                
                # 检查损失和梯度
                if torch.isnan(loss) or torch.isinf(loss):
                    print(f"跳过无效损失: {loss.item()}")
                    continue
                
                loss.backward()
                
                # 极严格的梯度裁剪
                grad_norm = torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=0.1)
                
                # 检查梯度范数
                if grad_norm > 1.0:
                    print(f"跳过大梯度: {grad_norm:.3f}")
                    continue
                
                self.optimizer.step()
                
                total_loss += loss.item()
                total_distance += loss_results['mean_distance'].item()
                num_batches += 1
                
                # 实时监控
                if batch_idx % 5 == 0:
                    print(f"Batch {batch_idx}: Loss={loss.item():.3f}, Distance={loss_results['mean_distance'].item():.1f}, GradNorm={grad_norm:.4f}")
                
            except Exception as e:
                print(f"训练错误 batch {batch_idx}: {e}")
                continue
        
        avg_loss = total_loss / max(num_batches, 1)
        avg_distance = total_distance / max(num_batches, 1)
        
        return {'loss': avg_loss, 'distance': avg_distance}
    
    def validate_epoch(self, val_loader):
        """验证epoch"""
        self.model.eval()
        
        total_loss = 0.0
        total_distance = 0.0
        num_batches = 0
        
        with torch.no_grad():
            for batch in val_loader:
                try:
                    eeg_data = batch['eeg_data'].to(self.device)
                    temporal_sequence = batch['temporal_sequence'].to(self.device)
                    lesion_masks = batch['lesion_mask'].to(self.device)
                    
                    outputs = self.model(eeg_data, temporal_sequence)
                    loss_results = self.criterion(outputs['center'], lesion_masks)
                    
                    total_loss += loss_results['total_loss'].item()
                    total_distance += loss_results['mean_distance'].item()
                    num_batches += 1
                    
                except Exception as e:
                    print(f"验证错误: {e}")
                    continue
        
        avg_loss = total_loss / max(num_batches, 1)
        avg_distance = total_distance / max(num_batches, 1)
        
        return {'loss': avg_loss, 'distance': avg_distance}
    
    def train(self, train_loader, val_loader, num_epochs=10):
        """训练循环"""
        print(f"开始超简化训练，共 {num_epochs} 个epoch...")
        
        best_val_distance = float('inf')
        
        for epoch in range(num_epochs):
            print(f"\n=== Epoch {epoch+1}/{num_epochs} ===")
            
            # 训练
            train_metrics = self.train_epoch(train_loader)
            
            # 验证
            val_metrics = self.validate_epoch(val_loader)
            
            # 更新学习率
            self.scheduler.step()
            current_lr = self.optimizer.param_groups[0]['lr']
            
            # 记录历史
            self.history['train_loss'].append(train_metrics['loss'])
            self.history['val_loss'].append(val_metrics['loss'])
            self.history['train_distance'].append(train_metrics['distance'])
            self.history['val_distance'].append(val_metrics['distance'])
            
            # 打印结果
            print(f"训练 - 损失: {train_metrics['loss']:.3f}, 距离: {train_metrics['distance']:.1f}")
            print(f"验证 - 损失: {val_metrics['loss']:.3f}, 距离: {val_metrics['distance']:.1f}")
            print(f"学习率: {current_lr:.2e}")
            
            # 保存最佳模型
            if val_metrics['distance'] < best_val_distance:
                best_val_distance = val_metrics['distance']
                torch.save({
                    'model_state_dict': self.model.state_dict(),
                    'val_distance': best_val_distance,
                    'history': self.history
                }, 'ultra_simple_best_model.pth')
                print(f"✓ 保存最佳模型 (距离: {best_val_distance:.1f})")
        
        print(f"\n训练完成！最佳距离: {best_val_distance:.1f} 体素")
        return self.history

def test_gradient_explosion():
    """测试梯度爆炸问题"""
    print("🧪 测试梯度爆炸...")
    
    device = torch.device('cpu')  # 使用CPU便于调试
    model = UltraSimpleCenterModel().to(device)
    criterion = UltraSimpleLoss()
    
    # 创建测试数据
    batch_size = 2
    eeg_data = torch.randn(batch_size, 14, 1024) * 0.5  # 小的输入
    temporal_sequence = torch.randn(batch_size, 10, 14) * 0.5
    lesion_masks = torch.zeros(batch_size, 256, 256, 256)
    
    # 添加一些病灶
    lesion_masks[0, 100:150, 100:150, 80:120] = 1.0
    lesion_masks[1, 120:170, 120:170, 100:140] = 1.0
    
    # 前向传播
    model.train()
    outputs = model(eeg_data, temporal_sequence)
    loss_results = criterion(outputs['center'], lesion_masks)
    loss = loss_results['total_loss']
    
    print(f"前向传播:")
    print(f"  预测中心: {outputs['center']}")
    print(f"  损失: {loss.item():.3f}")
    print(f"  距离: {loss_results['mean_distance'].item():.1f}")
    
    # 反向传播
    loss.backward()
    
    # 检查梯度
    print(f"\n梯度检查:")
    total_norm = 0
    for name, param in model.named_parameters():
        if param.grad is not None:
            grad_norm = param.grad.norm().item()
            total_norm += grad_norm ** 2
            print(f"  {name}: {grad_norm:.6f}")
    
    total_norm = total_norm ** 0.5
    print(f"  总梯度范数: {total_norm:.6f}")
    
    if total_norm < 1.0:
        print("✅ 梯度范数正常")
    else:
        print("⚠️  梯度范数偏大")

def main():
    """主函数"""
    print("🚀 超简化癫痫病灶中心定位")
    print("="*50)
    
    # 先测试梯度
    test_gradient_explosion()
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"\n使用设备: {device}")
    
    # 创建数据加载器
    try:
        train_dataset = NormalizedEEGLesionDataset(
            "eeg_lesion_training_dataset/train_pairings.pkl", 
            augment=False  # 关闭数据增强
        )
        val_dataset = NormalizedEEGLesionDataset(
            "eeg_lesion_training_dataset/validation_pairings.pkl", 
            augment=False
        )
        
        train_loader = DataLoader(train_dataset, batch_size=1, shuffle=True, num_workers=0)  # batch_size=1
        val_loader = DataLoader(val_dataset, batch_size=1, shuffle=False, num_workers=0)
        
        print("✅ 数据加载器创建成功")
        print(f"训练样本: {len(train_dataset)}, 验证样本: {len(val_dataset)}")
        
    except Exception as e:
        print(f"❌ 创建数据加载器失败: {e}")
        return
    
    # 创建超简化模型
    model = UltraSimpleCenterModel()
    param_count = sum(p.numel() for p in model.parameters())
    print(f"超简化模型创建成功，参数数量: {param_count:,}")
    
    # 创建训练器
    trainer = UltraSimpleTrainer(model, device)
    
    # 开始训练
    history = trainer.train(train_loader, val_loader, num_epochs=8)
    
    # 绘制结果
    plt.figure(figsize=(12, 4))
    
    plt.subplot(1, 2, 1)
    plt.plot(history['train_loss'], 'b-', label='训练损失')
    plt.plot(history['val_loss'], 'r-', label='验证损失')
    plt.title('损失曲线')
    plt.xlabel('Epoch')
    plt.ylabel('L1损失')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.subplot(1, 2, 2)
    plt.plot(history['train_distance'], 'b-', label='训练距离')
    plt.plot(history['val_distance'], 'r-', label='验证距离')
    plt.title('定位误差曲线')
    plt.xlabel('Epoch')
    plt.ylabel('距离 (体素)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('ultra_simple_training_curves.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("\n✅ 超简化训练完成！")
    
    # 显示最终结果
    if history['val_distance']:
        best_distance = min(history['val_distance'])
        final_loss = history['val_loss'][-1]
        print(f"\n🎯 最终结果:")
        print(f"  最佳验证距离: {best_distance:.1f} 体素")
        print(f"  最终验证损失: {final_loss:.3f}")
        
        if best_distance < 50:
            print("✅ 定位精度可接受")
        else:
            print("⚠️  定位精度需要改进")

if __name__ == "__main__":
    main()
