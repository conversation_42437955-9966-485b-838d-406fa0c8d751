#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
多通道EEG信号融合与3D全脑电活动强度图生成
生成类似fMRI格式的3D体积数据和BEM模型可视化

技术要点：
1. 多通道信号的加权叠加融合
2. 3D体积网格的电活动强度映射
3. NIfTI格式输出（兼容fMRI分析软件）
4. BEM模型的3D可视化
5. 交互式3D显示
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import gzip
import os
from pathlib import Path
import warnings
import json

# 医学影像和神经科学工具
try:
    import nibabel as nib
    import mne
    from mne.datasets import fetch_fsaverage
    from mne import setup_volume_source_space
    from mne.bem import make_bem_model, make_bem_solution
    from mne.forward import make_forward_solution
    from mne.minimum_norm import make_inverse_operator, apply_inverse
    print("✅ 神经影像工具导入成功")
except ImportError as e:
    print("❌ 导入失败: {}".format(e))

# 3D可视化工具
try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    print("✅ 3D可视化工具导入成功")
except ImportError:
    print("⚠️  Plotly未安装，将使用matplotlib进行3D可视化")
    import matplotlib.pyplot as plt
    from mpl_toolkits.mplot3d import Axes3D

from scipy import ndimage, interpolate
from scipy.spatial.distance import cdist
import warnings
warnings.filterwarnings('ignore')

class MultiChannelBrainMapper:
    """多通道EEG信号融合与3D脑图生成器"""
    
    def __init__(self, voxel_size=2.0, brain_template='MNI152'):
        """
        初始化3D脑图生成器
        
        Parameters:
        -----------
        voxel_size : float
            体素大小 (mm)
        brain_template : str
            脑模板类型
        """
        print("🧠 初始化多通道脑图生成器")
        
        self.voxel_size = voxel_size
        self.brain_template = brain_template
        
        # 14通道EEG电极配置
        self.eeg_channels = [
            'AF3', 'AF4', 'F3', 'F4', 'F7', 'F8', 'FC5', 'FC6', 
            'O1', 'O2', 'P7', 'P8', 'T7', 'T8'
        ]
        
        # 电极3D位置 (标准10-20系统，单位：mm)
        self.electrode_positions_3d = {
            'AF3': np.array([-30, 80, 40]),   'AF4': np.array([30, 80, 40]),
            'F3': np.array([-50, 60, 50]),    'F4': np.array([50, 60, 50]),
            'F7': np.array([-70, 40, 30]),    'F8': np.array([70, 40, 30]),
            'FC5': np.array([-60, 20, 60]),   'FC6': np.array([60, 20, 60]),
            'T7': np.array([-80, 0, 20]),     'T8': np.array([80, 0, 20]),
            'P7': np.array([-70, -40, 30]),   'P8': np.array([70, -40, 30]),
            'O1': np.array([-30, -80, 40]),   'O2': np.array([30, -80, 40])
        }
        
        # 生物组织电导率
        self.tissue_conductivity = {
            'scalp': 0.33, 'skull': 0.0042, 'csf': 1.79,
            'gray_matter': 0.33, 'white_matter': 0.14
        }
        
        # 3D脑体积参数
        self.brain_bounds = {
            'x': (-90, 90),   # mm
            'y': (-120, 90),  # mm  
            'z': (-70, 80)    # mm
        }
        
        print("✅ 初始化完成")
        print("  体素大小: {} mm".format(voxel_size))
        print("  脑模板: {}".format(brain_template))
        print("  电极数量: {}".format(len(self.eeg_channels)))
    
    def load_eeg_data(self, eeg_file_path):
        """加载EEG数据"""
        print("📊 加载EEG数据: {}".format(eeg_file_path))
        
        try:
            with gzip.open(eeg_file_path, 'rt') as f:
                df = pd.read_csv(f)
            
            # 提取14通道数据
            eeg_data = np.zeros((14, len(df)))
            channel_weights = np.ones(14)  # 通道权重
            
            for i, ch in enumerate(self.eeg_channels):
                if ch in df.columns:
                    eeg_data[i] = df[ch].values
                else:
                    # 插值缺失通道
                    eeg_data[i] = self._interpolate_missing_channel(df, ch)
                    channel_weights[i] = 0.5  # 降低插值通道权重
            
            # 数据预处理
            eeg_data = self._preprocess_eeg_data(eeg_data)
            
            print("✅ EEG数据加载完成")
            print("  数据形状: {}".format(eeg_data.shape))
            print("  时间长度: {:.1f} 秒".format(eeg_data.shape[1] / 256))
            
            return eeg_data, channel_weights
            
        except Exception as e:
            print("❌ EEG数据加载失败: {}".format(e))
            raise
    
    def _interpolate_missing_channel(self, df, missing_channel):
        """插值缺失通道"""
        neighbor_map = {
            'AF3': ['F3', 'AF4'], 'AF4': ['F4', 'AF3'],
            'F3': ['AF3', 'FC5'], 'F4': ['AF4', 'FC6'],
            'F7': ['T7', 'F3'], 'F8': ['T8', 'F4'],
            'FC5': ['F3', 'T7'], 'FC6': ['F4', 'T8'],
            'T7': ['F7', 'P7'], 'T8': ['F8', 'P8'],
            'P7': ['T7', 'O1'], 'P8': ['T8', 'O2'],
            'O1': ['P7', 'O2'], 'O2': ['P8', 'O1']
        }
        
        if missing_channel in neighbor_map:
            neighbors = [ch for ch in neighbor_map[missing_channel] if ch in df.columns]
            if neighbors:
                return np.mean([df[ch].values for ch in neighbors], axis=0)
        
        return np.zeros(len(df))
    
    def _preprocess_eeg_data(self, eeg_data):
        """EEG数据预处理"""
        # 1. 去除直流分量
        eeg_data = eeg_data - np.mean(eeg_data, axis=1, keepdims=True)
        
        # 2. 标准化
        eeg_data = eeg_data / (np.std(eeg_data, axis=1, keepdims=True) + 1e-8)
        
        # 3. 简单带通滤波 (1-40 Hz)
        from scipy import signal
        fs = 256
        sos = signal.butter(4, [1, 40], btype='band', fs=fs, output='sos')
        
        for i in range(eeg_data.shape[0]):
            eeg_data[i] = signal.sosfilt(sos, eeg_data[i])
        
        return eeg_data
    
    def create_3d_brain_volume(self):
        """创建3D脑体积网格"""
        print("🧊 创建3D脑体积网格")
        
        # 根据体素大小创建3D网格
        x_coords = np.arange(self.brain_bounds['x'][0], 
                           self.brain_bounds['x'][1] + self.voxel_size, 
                           self.voxel_size)
        y_coords = np.arange(self.brain_bounds['y'][0], 
                           self.brain_bounds['y'][1] + self.voxel_size, 
                           self.voxel_size)
        z_coords = np.arange(self.brain_bounds['z'][0], 
                           self.brain_bounds['z'][1] + self.voxel_size, 
                           self.voxel_size)
        
        X, Y, Z = np.meshgrid(x_coords, y_coords, z_coords, indexing='ij')
        
        # 创建脑形状掩膜 (椭球形近似)
        brain_mask = self._create_brain_mask(X, Y, Z)
        
        # 体积信息
        volume_info = {
            'shape': X.shape,
            'voxel_size': self.voxel_size,
            'coordinates': (X, Y, Z),
            'brain_mask': brain_mask,
            'n_voxels': np.sum(brain_mask),
            'affine_matrix': self._create_affine_matrix()
        }
        
        print("✅ 3D脑体积创建完成")
        print("  体积形状: {}".format(X.shape))
        print("  脑内体素数: {}".format(np.sum(brain_mask)))
        print("  总体积: {:.1f} cm³".format(np.sum(brain_mask) * (self.voxel_size/10)**3))
        
        return volume_info
    
    def _create_brain_mask(self, X, Y, Z):
        """创建脑形状掩膜"""
        # 椭球形脑模型参数 (mm)
        a, b, c = 85, 100, 70  # 半轴长度
        
        # 椭球方程
        brain_mask = ((X/a)**2 + (Y/b)**2 + (Z/c)**2) <= 1.0
        
        return brain_mask
    
    def _create_affine_matrix(self):
        """创建仿射变换矩阵 (兼容NIfTI格式)"""
        affine = np.eye(4)
        affine[0, 0] = self.voxel_size  # x方向体素大小
        affine[1, 1] = self.voxel_size  # y方向体素大小
        affine[2, 2] = self.voxel_size  # z方向体素大小
        
        # 设置原点偏移
        affine[0, 3] = self.brain_bounds['x'][0]
        affine[1, 3] = self.brain_bounds['y'][0]
        affine[2, 3] = self.brain_bounds['z'][0]
        
        return affine
    
    def compute_multi_channel_fusion(self, eeg_data, channel_weights, volume_info, 
                                   fusion_method='weighted_distance'):
        """
        计算多通道信号融合的3D电活动强度
        
        Parameters:
        -----------
        eeg_data : array (n_channels, n_times)
            EEG数据
        channel_weights : array (n_channels,)
            通道权重
        volume_info : dict
            3D体积信息
        fusion_method : str
            融合方法
            
        Returns:
        --------
        brain_activity_3d : array
            3D电活动强度图
        """
        print("🔄 计算多通道信号融合")
        
        X, Y, Z = volume_info['coordinates']
        brain_mask = volume_info['brain_mask']
        
        # 初始化3D活动强度图
        brain_activity_3d = np.zeros_like(X, dtype=np.float32)
        
        # 计算每个通道的时间平均活动强度
        channel_activities = np.mean(np.abs(eeg_data), axis=1)
        
        print("  通道活动强度范围: {:.2e} - {:.2e}".format(
            np.min(channel_activities), np.max(channel_activities)))
        
        if fusion_method == 'weighted_distance':
            brain_activity_3d = self._weighted_distance_fusion(
                X, Y, Z, brain_mask, channel_activities, channel_weights)
        elif fusion_method == 'gaussian_kernel':
            brain_activity_3d = self._gaussian_kernel_fusion(
                X, Y, Z, brain_mask, channel_activities, channel_weights)
        elif fusion_method == 'inverse_distance':
            brain_activity_3d = self._inverse_distance_fusion(
                X, Y, Z, brain_mask, channel_activities, channel_weights)
        else:
            raise ValueError("未知的融合方法: {}".format(fusion_method))
        
        # 应用脑掩膜
        brain_activity_3d[~brain_mask] = 0
        
        # 标准化
        if np.max(brain_activity_3d) > 0:
            brain_activity_3d = brain_activity_3d / np.max(brain_activity_3d)
        
        print("✅ 多通道融合完成")
        print("  最大活动强度: {:.3f}".format(np.max(brain_activity_3d)))
        print("  活跃体素数: {}".format(np.sum(brain_activity_3d > 0.1)))
        
        return brain_activity_3d
    
    def _weighted_distance_fusion(self, X, Y, Z, brain_mask, channel_activities, channel_weights):
        """加权距离融合方法"""
        print("  使用加权距离融合方法")
        
        activity_3d = np.zeros_like(X)
        
        for i, ch in enumerate(self.eeg_channels):
            if ch in self.electrode_positions_3d:
                # 电极位置
                elec_pos = self.electrode_positions_3d[ch]
                
                # 计算到电极的距离
                distances = np.sqrt((X - elec_pos[0])**2 + 
                                  (Y - elec_pos[1])**2 + 
                                  (Z - elec_pos[2])**2)
                
                # 距离衰减函数 (高斯衰减)
                sigma = 30.0  # mm
                distance_weights = np.exp(-(distances**2) / (2 * sigma**2))
                
                # 加权贡献
                contribution = (channel_activities[i] * channel_weights[i] * 
                              distance_weights)
                
                activity_3d += contribution
        
        return activity_3d
    
    def _gaussian_kernel_fusion(self, X, Y, Z, brain_mask, channel_activities, channel_weights):
        """高斯核融合方法"""
        print("  使用高斯核融合方法")
        
        activity_3d = np.zeros_like(X)
        
        for i, ch in enumerate(self.eeg_channels):
            if ch in self.electrode_positions_3d:
                elec_pos = self.electrode_positions_3d[ch]
                
                # 3D高斯核
                sigma_x, sigma_y, sigma_z = 25.0, 25.0, 20.0  # mm
                
                gaussian_kernel = np.exp(
                    -((X - elec_pos[0])**2 / (2 * sigma_x**2) +
                      (Y - elec_pos[1])**2 / (2 * sigma_y**2) +
                      (Z - elec_pos[2])**2 / (2 * sigma_z**2))
                )
                
                contribution = (channel_activities[i] * channel_weights[i] * 
                              gaussian_kernel)
                
                activity_3d += contribution
        
        return activity_3d
    
    def _inverse_distance_fusion(self, X, Y, Z, brain_mask, channel_activities, channel_weights):
        """反距离加权融合方法"""
        print("  使用反距离加权融合方法")
        
        activity_3d = np.zeros_like(X)
        
        for i, ch in enumerate(self.eeg_channels):
            if ch in self.electrode_positions_3d:
                elec_pos = self.electrode_positions_3d[ch]
                
                distances = np.sqrt((X - elec_pos[0])**2 + 
                                  (Y - elec_pos[1])**2 + 
                                  (Z - elec_pos[2])**2)
                
                # 反距离权重 (避免除零)
                inv_distance_weights = 1.0 / (distances + 5.0)  # 5mm最小距离
                
                contribution = (channel_activities[i] * channel_weights[i] * 
                              inv_distance_weights)
                
                activity_3d += contribution
        
        return activity_3d
    
    def save_as_nifti(self, brain_activity_3d, volume_info, filename='eeg_brain_activity_3d.nii.gz'):
        """保存为NIfTI格式 (兼容fMRI分析软件)"""
        print("💾 保存为NIfTI格式: {}".format(filename))
        
        try:
            # 创建NIfTI图像
            nifti_img = nib.Nifti1Image(
                brain_activity_3d.astype(np.float32),
                volume_info['affine_matrix']
            )
            
            # 设置头信息
            nifti_img.header['descrip'] = b'EEG Multi-channel Brain Activity Map'
            nifti_img.header['aux_file'] = b'Generated by MultiChannelBrainMapper'
            
            # 保存文件
            nib.save(nifti_img, filename)
            
            print("✅ NIfTI文件保存成功")
            print("  文件大小: {:.1f} MB".format(os.path.getsize(filename) / 1024 / 1024))
            print("  可用于: FSL, SPM, AFNI, 3D Slicer等软件")
            
            return filename
            
        except Exception as e:
            print("❌ NIfTI保存失败: {}".format(e))
            raise
    
    def create_bem_model_visualization(self):
        """创建BEM模型可视化"""
        print("🏗️  创建BEM模型可视化")
        
        try:
            # 创建5层球形BEM模型
            bem_model = mne.make_sphere_model(
                r0=(0.0, 0.0, 0.04),  # 球心 (m)
                head_radius=0.09,     # 头部半径 (m)
                relative_radii=(0.88, 0.90, 0.92, 0.97, 1.0),  # 5层相对半径
                sigmas=(
                    self.tissue_conductivity['white_matter'],
                    self.tissue_conductivity['gray_matter'],
                    self.tissue_conductivity['csf'],
                    self.tissue_conductivity['skull'],
                    self.tissue_conductivity['scalp']
                )
            )
            
            # 提取BEM层信息
            bem_layers = []
            layer_names = ['白质', '灰质', '脑脊液', '颅骨', '头皮']
            colors = ['lightblue', 'gray', 'cyan', 'white', 'pink']
            
            for i, (name, color) in enumerate(zip(layer_names, colors)):
                if i < len(bem_model['layers']):
                    layer = bem_model['layers'][i]
                    bem_layers.append({
                        'name': name,
                        'radius': layer['rad'] * 1000,  # 转换为mm
                        'conductivity': layer['sigma'],
                        'color': color
                    })
            
            print("✅ BEM模型创建完成")
            print("  层数: {}".format(len(bem_layers)))
            
            return bem_model, bem_layers
            
        except Exception as e:
            print("❌ BEM模型创建失败: {}".format(e))
            raise

    def visualize_3d_brain_activity(self, brain_activity_3d, volume_info, bem_layers=None,
                                   threshold=0.3, use_plotly=True):
        """
        3D脑电活动可视化

        Parameters:
        -----------
        brain_activity_3d : array
            3D电活动强度图
        volume_info : dict
            体积信息
        bem_layers : list
            BEM层信息
        threshold : float
            显示阈值
        use_plotly : bool
            是否使用Plotly交互式可视化
        """
        print("📊 生成3D脑电活动可视化")

        if use_plotly:
            try:
                self._plotly_3d_visualization(brain_activity_3d, volume_info, bem_layers, threshold)
            except:
                print("⚠️  Plotly可视化失败，使用matplotlib")
                self._matplotlib_3d_visualization(brain_activity_3d, volume_info, bem_layers, threshold)
        else:
            self._matplotlib_3d_visualization(brain_activity_3d, volume_info, bem_layers, threshold)

    def _plotly_3d_visualization(self, brain_activity_3d, volume_info, bem_layers, threshold):
        """使用Plotly创建交互式3D可视化"""
        print("  使用Plotly创建交互式3D可视化")

        X, Y, Z = volume_info['coordinates']

        # 创建子图
        fig = make_subplots(
            rows=2, cols=2,
            specs=[[{'type': 'scatter3d'}, {'type': 'scatter3d'}],
                   [{'type': 'scatter3d'}, {'type': 'scatter3d'}]],
            subplot_titles=('3D脑电活动分布', 'BEM模型结构',
                          '电极位置', '活动强度切片'),
            vertical_spacing=0.1,
            horizontal_spacing=0.1
        )

        # 1. 3D脑电活动分布
        active_mask = brain_activity_3d > threshold
        if np.sum(active_mask) > 0:
            x_active = X[active_mask]
            y_active = Y[active_mask]
            z_active = Z[active_mask]
            intensity_active = brain_activity_3d[active_mask]

            fig.add_trace(
                go.Scatter3d(
                    x=x_active, y=y_active, z=z_active,
                    mode='markers',
                    marker=dict(
                        size=3,
                        color=intensity_active,
                        colorscale='Hot',
                        opacity=0.8,
                        colorbar=dict(title="活动强度", x=0.45)
                    ),
                    name='脑电活动',
                    text=[f'强度: {i:.3f}' for i in intensity_active],
                    hovertemplate='坐标: (%{x:.1f}, %{y:.1f}, %{z:.1f})<br>%{text}<extra></extra>'
                ),
                row=1, col=1
            )

        # 2. BEM模型结构
        if bem_layers:
            for i, layer in enumerate(bem_layers):
                # 创建球面
                u = np.linspace(0, 2 * np.pi, 30)
                v = np.linspace(0, np.pi, 20)

                radius = layer['radius']
                x_sphere = radius * np.outer(np.cos(u), np.sin(v))
                y_sphere = radius * np.outer(np.sin(u), np.sin(v))
                z_sphere = radius * np.outer(np.ones(np.size(u)), np.cos(v))

                fig.add_trace(
                    go.Surface(
                        x=x_sphere, y=y_sphere, z=z_sphere,
                        opacity=0.3,
                        colorscale=[[0, layer['color']], [1, layer['color']]],
                        showscale=False,
                        name=f"{layer['name']} (σ={layer['conductivity']:.4f})",
                        hovertemplate=f"{layer['name']}<br>半径: {radius:.1f}mm<br>电导率: {layer['conductivity']:.4f} S/m<extra></extra>"
                    ),
                    row=1, col=2
                )

        # 3. 电极位置
        electrode_names = []
        electrode_x, electrode_y, electrode_z = [], [], []

        for ch in self.eeg_channels:
            if ch in self.electrode_positions_3d:
                pos = self.electrode_positions_3d[ch]
                electrode_names.append(ch)
                electrode_x.append(pos[0])
                electrode_y.append(pos[1])
                electrode_z.append(pos[2])

        fig.add_trace(
            go.Scatter3d(
                x=electrode_x, y=electrode_y, z=electrode_z,
                mode='markers+text',
                marker=dict(size=8, color='red', symbol='diamond'),
                text=electrode_names,
                textposition='top center',
                name='EEG电极',
                hovertemplate='电极: %{text}<br>位置: (%{x:.1f}, %{y:.1f}, %{z:.1f})<extra></extra>'
            ),
            row=2, col=1
        )

        # 4. 活动强度切片 (冠状面)
        mid_y_idx = brain_activity_3d.shape[1] // 2
        coronal_slice = brain_activity_3d[:, mid_y_idx, :]

        fig.add_trace(
            go.Heatmap(
                z=coronal_slice.T,
                colorscale='Hot',
                showscale=True,
                colorbar=dict(title="活动强度", x=1.0),
                name='冠状面切片'
            ),
            row=2, col=2
        )

        # 更新布局
        fig.update_layout(
            title='多通道EEG信号融合3D脑电活动图',
            height=800,
            showlegend=True
        )

        # 设置3D场景
        scene_dict = dict(
            xaxis_title='X (mm)',
            yaxis_title='Y (mm)',
            zaxis_title='Z (mm)',
            aspectmode='cube'
        )

        fig.update_scenes(scene_dict)

        # 保存和显示
        fig.write_html('3d_brain_activity_interactive.html')
        fig.show()

        print("✅ 交互式3D可视化完成")
        print("  文件保存: 3d_brain_activity_interactive.html")

    def _matplotlib_3d_visualization(self, brain_activity_3d, volume_info, bem_layers, threshold):
        """使用Matplotlib创建3D可视化"""
        print("  使用Matplotlib创建3D可视化")

        fig = plt.figure(figsize=(20, 15))

        X, Y, Z = volume_info['coordinates']

        # 1. 3D脑电活动分布
        ax1 = fig.add_subplot(2, 3, 1, projection='3d')

        active_mask = brain_activity_3d > threshold
        if np.sum(active_mask) > 0:
            x_active = X[active_mask]
            y_active = Y[active_mask]
            z_active = Z[active_mask]
            intensity_active = brain_activity_3d[active_mask]

            scatter = ax1.scatter(x_active, y_active, z_active,
                                c=intensity_active, cmap='hot',
                                s=20, alpha=0.6)
            plt.colorbar(scatter, ax=ax1, label='活动强度', shrink=0.5)

        ax1.set_xlabel('X (mm)')
        ax1.set_ylabel('Y (mm)')
        ax1.set_zlabel('Z (mm)')
        ax1.set_title('3D脑电活动分布')

        # 2. BEM模型结构
        ax2 = fig.add_subplot(2, 3, 2, projection='3d')

        if bem_layers:
            for layer in bem_layers:
                # 绘制球面轮廓
                u = np.linspace(0, 2 * np.pi, 20)
                v = np.linspace(0, np.pi, 10)

                radius = layer['radius']
                x_sphere = radius * np.outer(np.cos(u), np.sin(v))
                y_sphere = radius * np.outer(np.sin(u), np.sin(v))
                z_sphere = radius * np.outer(np.ones(np.size(u)), np.cos(v))

                ax2.plot_surface(x_sphere, y_sphere, z_sphere,
                               alpha=0.3, color=layer['color'],
                               label=layer['name'])

        ax2.set_xlabel('X (mm)')
        ax2.set_ylabel('Y (mm)')
        ax2.set_zlabel('Z (mm)')
        ax2.set_title('BEM模型结构')

        # 3. 电极位置
        ax3 = fig.add_subplot(2, 3, 3, projection='3d')

        for ch in self.eeg_channels:
            if ch in self.electrode_positions_3d:
                pos = self.electrode_positions_3d[ch]
                ax3.scatter(pos[0], pos[1], pos[2],
                          c='red', s=100, marker='o')
                ax3.text(pos[0], pos[1], pos[2], ch, fontsize=8)

        ax3.set_xlabel('X (mm)')
        ax3.set_ylabel('Y (mm)')
        ax3.set_zlabel('Z (mm)')
        ax3.set_title('EEG电极位置')

        # 4. 矢状面切片
        ax4 = fig.add_subplot(2, 3, 4)
        mid_x_idx = brain_activity_3d.shape[0] // 2
        sagittal_slice = brain_activity_3d[mid_x_idx, :, :]

        im4 = ax4.imshow(sagittal_slice.T, cmap='hot', aspect='auto', origin='lower')
        ax4.set_title('矢状面切片')
        ax4.set_xlabel('Y方向')
        ax4.set_ylabel('Z方向')
        plt.colorbar(im4, ax=ax4, label='活动强度')

        # 5. 冠状面切片
        ax5 = fig.add_subplot(2, 3, 5)
        mid_y_idx = brain_activity_3d.shape[1] // 2
        coronal_slice = brain_activity_3d[:, mid_y_idx, :]

        im5 = ax5.imshow(coronal_slice.T, cmap='hot', aspect='auto', origin='lower')
        ax5.set_title('冠状面切片')
        ax5.set_xlabel('X方向')
        ax5.set_ylabel('Z方向')
        plt.colorbar(im5, ax=ax5, label='活动强度')

        # 6. 水平面切片
        ax6 = fig.add_subplot(2, 3, 6)
        mid_z_idx = brain_activity_3d.shape[2] // 2
        axial_slice = brain_activity_3d[:, :, mid_z_idx]

        im6 = ax6.imshow(axial_slice.T, cmap='hot', aspect='auto', origin='lower')
        ax6.set_title('水平面切片')
        ax6.set_xlabel('X方向')
        ax6.set_ylabel('Y方向')
        plt.colorbar(im6, ax=ax6, label='活动强度')

        plt.tight_layout()
        plt.savefig('3d_brain_activity_matplotlib.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("✅ Matplotlib 3D可视化完成")
        print("  文件保存: 3d_brain_activity_matplotlib.png")

    def generate_statistical_summary(self, brain_activity_3d, volume_info, eeg_data):
        """生成统计摘要报告"""
        print("📈 生成统计摘要报告")

        # 基本统计
        total_voxels = np.sum(volume_info['brain_mask'])
        active_voxels = np.sum(brain_activity_3d > 0.1)
        max_activity = np.max(brain_activity_3d)
        mean_activity = np.mean(brain_activity_3d[brain_activity_3d > 0])

        # 找到峰值位置
        peak_idx = np.unravel_index(np.argmax(brain_activity_3d), brain_activity_3d.shape)
        X, Y, Z = volume_info['coordinates']
        peak_coords = (X[peak_idx], Y[peak_idx], Z[peak_idx])

        # 通道贡献分析
        channel_contributions = np.mean(np.abs(eeg_data), axis=1)
        max_channel_idx = np.argmax(channel_contributions)
        max_channel = self.eeg_channels[max_channel_idx]

        # 创建报告
        report = {
            'volume_statistics': {
                'total_brain_voxels': int(total_voxels),
                'active_voxels': int(active_voxels),
                'activation_percentage': float(active_voxels / total_voxels * 100),
                'voxel_size_mm': float(self.voxel_size),
                'brain_volume_cm3': float(total_voxels * (self.voxel_size/10)**3)
            },
            'activity_statistics': {
                'max_activity': float(max_activity),
                'mean_activity': float(mean_activity),
                'peak_coordinates_mm': [float(peak_coords[0]), float(peak_coords[1]), float(peak_coords[2])],
                'activity_range': [float(np.min(brain_activity_3d)), float(np.max(brain_activity_3d))]
            },
            'channel_analysis': {
                'strongest_channel': max_channel,
                'channel_contributions': {ch: float(contrib) for ch, contrib in
                                        zip(self.eeg_channels, channel_contributions)},
                'channel_contribution_std': float(np.std(channel_contributions))
            },
            'spatial_distribution': {
                'center_of_mass': [float(x) for x in self._compute_center_of_mass(brain_activity_3d, X, Y, Z)],
                'spatial_spread': float(self._compute_spatial_spread(brain_activity_3d, X, Y, Z))
            }
        }

        # 保存报告
        with open('brain_activity_statistical_report.json', 'w') as f:
            json.dump(report, f, indent=2)

        # 打印摘要
        print("\n" + "="*60)
        print("多通道EEG 3D脑电活动统计摘要")
        print("="*60)
        print(f"🧠 脑体积统计:")
        print(f"  总脑体素数: {report['volume_statistics']['total_brain_voxels']:,}")
        print(f"  活跃体素数: {report['volume_statistics']['active_voxels']:,}")
        print(f"  激活百分比: {report['volume_statistics']['activation_percentage']:.1f}%")
        print(f"  脑体积: {report['volume_statistics']['brain_volume_cm3']:.1f} cm³")

        print(f"\n⚡ 活动强度统计:")
        print(f"  最大活动强度: {report['activity_statistics']['max_activity']:.3f}")
        print(f"  平均活动强度: {report['activity_statistics']['mean_activity']:.3f}")
        print(f"  峰值坐标: ({report['activity_statistics']['peak_coordinates_mm'][0]:.1f}, "
              f"{report['activity_statistics']['peak_coordinates_mm'][1]:.1f}, "
              f"{report['activity_statistics']['peak_coordinates_mm'][2]:.1f}) mm")

        print(f"\n📡 通道分析:")
        print(f"  最强通道: {report['channel_analysis']['strongest_channel']}")
        print(f"  通道贡献标准差: {report['channel_analysis']['channel_contribution_std']:.3f}")

        print(f"\n📍 空间分布:")
        com = report['spatial_distribution']['center_of_mass']
        print(f"  质心坐标: ({com[0]:.1f}, {com[1]:.1f}, {com[2]:.1f}) mm")
        print(f"  空间分散度: {report['spatial_distribution']['spatial_spread']:.1f} mm")

        print("="*60)

        return report

    def _compute_center_of_mass(self, activity, X, Y, Z):
        """计算活动质心"""
        total_activity = np.sum(activity)
        if total_activity == 0:
            return [0, 0, 0]

        com_x = np.sum(activity * X) / total_activity
        com_y = np.sum(activity * Y) / total_activity
        com_z = np.sum(activity * Z) / total_activity

        return [com_x, com_y, com_z]

    def _compute_spatial_spread(self, activity, X, Y, Z):
        """计算空间分散度"""
        com = self._compute_center_of_mass(activity, X, Y, Z)

        distances = np.sqrt((X - com[0])**2 + (Y - com[1])**2 + (Z - com[2])**2)

        total_activity = np.sum(activity)
        if total_activity == 0:
            return 0

        weighted_distances = activity * distances
        spread = np.sum(weighted_distances) / total_activity

        return spread

def main():
    """主函数：执行完整的多通道EEG 3D脑图生成流程"""

    print("🚀 多通道EEG信号融合与3D全脑电活动图生成")
    print("="*80)
    print("生成类似fMRI格式的3D体积数据和BEM模型可视化")
    print("="*80)

    # 初始化脑图生成器
    brain_mapper = MultiChannelBrainMapper(voxel_size=2.0, brain_template='MNI152')

    # 选择癫痫患者数据
    data_dir = Path("1252141/EEGs_Guinea-Bissau")
    metadata_file = "1252141/metadata_guineabissau.csv"

    if not os.path.exists(metadata_file):
        print("❌ 元数据文件不存在: {}".format(metadata_file))
        return

    try:
        # 加载元数据并选择癫痫患者
        metadata = pd.read_csv(metadata_file)
        epilepsy_patients = metadata[metadata['Group'] == 'Epilepsy']

        if len(epilepsy_patients) == 0:
            print("❌ 未找到癫痫患者数据")
            return

        # 选择第一个癫痫患者
        patient = epilepsy_patients.iloc[0]
        subject_id = patient['subject.id']
        eeg_file = data_dir / "signal-{}.csv.gz".format(subject_id)

        if not eeg_file.exists():
            print("❌ EEG文件不存在: {}".format(eeg_file))
            return

        print("📋 选择患者: {} (Group: {})".format(subject_id, patient['Group']))

        # 步骤1: 加载EEG数据
        print("\n步骤1: 加载EEG数据")
        eeg_data, channel_weights = brain_mapper.load_eeg_data(str(eeg_file))

        # 步骤2: 创建3D脑体积网格
        print("\n步骤2: 创建3D脑体积网格")
        volume_info = brain_mapper.create_3d_brain_volume()

        # 步骤3: 计算多通道信号融合
        print("\n步骤3: 计算多通道信号融合")

        # 尝试不同的融合方法
        fusion_methods = ['weighted_distance', 'gaussian_kernel', 'inverse_distance']
        fusion_results = {}

        for method in fusion_methods:
            print(f"  尝试融合方法: {method}")
            try:
                brain_activity_3d = brain_mapper.compute_multi_channel_fusion(
                    eeg_data, channel_weights, volume_info, fusion_method=method
                )
                fusion_results[method] = brain_activity_3d
                print(f"  ✅ {method} 融合成功")
            except Exception as e:
                print(f"  ❌ {method} 融合失败: {e}")

        if not fusion_results:
            print("❌ 所有融合方法都失败了")
            return

        # 选择最佳融合结果（活跃体素最多的）
        best_method = max(fusion_results.keys(),
                         key=lambda m: np.sum(fusion_results[m] > 0.1))
        best_brain_activity = fusion_results[best_method]

        print(f"✅ 选择最佳融合方法: {best_method}")

        # 步骤4: 创建BEM模型
        print("\n步骤4: 创建BEM模型")
        try:
            bem_model, bem_layers = brain_mapper.create_bem_model_visualization()
        except Exception as e:
            print(f"⚠️  BEM模型创建失败: {e}")
            bem_layers = None

        # 步骤5: 保存为NIfTI格式
        print("\n步骤5: 保存为NIfTI格式")
        try:
            nifti_filename = brain_mapper.save_as_nifti(
                best_brain_activity, volume_info,
                filename=f'eeg_brain_activity_3d_{best_method}.nii.gz'
            )
        except Exception as e:
            print(f"⚠️  NIfTI保存失败: {e}")
            nifti_filename = None

        # 步骤6: 生成3D可视化
        print("\n步骤6: 生成3D可视化")
        try:
            brain_mapper.visualize_3d_brain_activity(
                best_brain_activity, volume_info, bem_layers,
                threshold=0.2, use_plotly=True
            )
        except Exception as e:
            print(f"⚠️  3D可视化失败: {e}")
            try:
                brain_mapper.visualize_3d_brain_activity(
                    best_brain_activity, volume_info, bem_layers,
                    threshold=0.2, use_plotly=False
                )
            except Exception as e2:
                print(f"❌ 所有可视化方法都失败: {e2}")

        # 步骤7: 生成统计报告
        print("\n步骤7: 生成统计报告")
        try:
            statistical_report = brain_mapper.generate_statistical_summary(
                best_brain_activity, volume_info, eeg_data
            )
        except Exception as e:
            print(f"⚠️  统计报告生成失败: {e}")

        # 步骤8: 对比不同融合方法
        print("\n步骤8: 对比不同融合方法")
        if len(fusion_results) > 1:
            fig, axes = plt.subplots(2, len(fusion_results), figsize=(5*len(fusion_results), 10))

            for i, (method, activity) in enumerate(fusion_results.items()):
                # 冠状面切片
                mid_y_idx = activity.shape[1] // 2
                coronal_slice = activity[:, mid_y_idx, :]

                if len(fusion_results) == 1:
                    ax1, ax2 = axes[0], axes[1]
                else:
                    ax1, ax2 = axes[0, i], axes[1, i]

                # 冠状面
                im1 = ax1.imshow(coronal_slice.T, cmap='hot', aspect='auto', origin='lower')
                ax1.set_title(f'{method}\n冠状面切片')
                ax1.set_xlabel('X方向')
                ax1.set_ylabel('Z方向')
                plt.colorbar(im1, ax=ax1, label='活动强度')

                # 水平面切片
                mid_z_idx = activity.shape[2] // 2
                axial_slice = activity[:, :, mid_z_idx]

                im2 = ax2.imshow(axial_slice.T, cmap='hot', aspect='auto', origin='lower')
                ax2.set_title(f'{method}\n水平面切片')
                ax2.set_xlabel('X方向')
                ax2.set_ylabel('Y方向')
                plt.colorbar(im2, ax=ax2, label='活动强度')

            plt.tight_layout()
            plt.savefig('fusion_methods_comparison.png', dpi=300, bbox_inches='tight')
            plt.show()

            print("✅ 融合方法对比完成")
            print("  文件保存: fusion_methods_comparison.png")

        # 最终结果摘要
        print("\n" + "="*80)
        print("🎉 多通道EEG 3D脑电活动图生成完成！")
        print("="*80)

        print(f"📋 分析信息:")
        print(f"  患者ID: {subject_id}")
        print(f"  最佳融合方法: {best_method}")
        print(f"  体素大小: {brain_mapper.voxel_size} mm")
        print(f"  脑体积: {np.sum(volume_info['brain_mask']) * (brain_mapper.voxel_size/10)**3:.1f} cm³")

        print(f"\n📁 输出文件:")
        if nifti_filename:
            print(f"  - {nifti_filename}: NIfTI格式3D脑电活动图")
        print(f"  - 3d_brain_activity_interactive.html: 交互式3D可视化")
        print(f"  - 3d_brain_activity_matplotlib.png: 静态3D可视化")
        print(f"  - fusion_methods_comparison.png: 融合方法对比")
        print(f"  - brain_activity_statistical_report.json: 统计报告")

        print(f"\n🔬 技术特点:")
        print(f"  ✅ 多通道信号合理叠加融合")
        print(f"  ✅ 类似fMRI格式的3D体积数据")
        print(f"  ✅ 5层BEM模型可视化")
        print(f"  ✅ 交互式3D可视化")
        print(f"  ✅ 兼容FSL/SPM/AFNI等软件")

        print(f"\n🎯 应用价值:")
        print(f"  • 癫痫灶3D定位")
        print(f"  • 多模态数据融合")
        print(f"  • 手术规划支持")
        print(f"  • 科研数据分析")

        print("="*80)

    except Exception as e:
        print(f"❌ 分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
