#!/usr/bin/env python3
"""
详细问题分析脚本
基于性能分析结果，深入分析模型性能差的根本原因
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
from pathlib import Path
import json
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 导入我们的模块
from training_pipeline import EpilepsyLocalizationModel
from fixed_training_system import FixedBoundingBoxLoss, NormalizedEEGLesionDataset
from torch.utils.data import DataLoader

class DetailedProblemAnalyzer:
    """详细问题分析器"""
    
    def __init__(self, device=None):
        self.device = device or torch.device('cpu')
        self.model = EpilepsyLocalizationModel().to(self.device)
        self.criterion = FixedBoundingBoxLoss()
        
        # 从之前的分析结果
        self.performance_summary = {
            'dice_mean': 0.272,
            'dice_std': 0.203,
            'iou_mean': 0.173,
            'iou_std': 0.130,
            'center_error_mean': 45.5,
            'center_error_std': 5.3,
            'n_samples': 20
        }
    
    def analyze_fundamental_issues(self):
        """分析根本问题"""
        print("🔍 深度问题分析")
        print("="*60)
        
        issues = []
        
        # 1. Dice分数分析
        if self.performance_summary['dice_mean'] < 0.3:
            issues.append({
                'type': '严重问题',
                'category': '边界框尺寸匹配',
                'description': f"Dice分数过低 ({self.performance_summary['dice_mean']:.3f})",
                'impact': '预测的边界框与真实病灶重叠度极低',
                'possible_causes': [
                    '边界框尺寸预测不准确',
                    '多尺度策略选择错误',
                    '尺寸细化网络失效',
                    '损失函数权重不平衡'
                ]
            })
        
        # 2. IoU分数分析
        if self.performance_summary['iou_mean'] < 0.2:
            issues.append({
                'type': '严重问题',
                'category': '边界对齐精度',
                'description': f"IoU分数过低 ({self.performance_summary['iou_mean']:.3f})",
                'impact': '边界框边界与病灶边界对齐度极差',
                'possible_causes': [
                    '中心定位不准确',
                    '边界框形状不匹配病灶形状',
                    '软边界框实现问题',
                    'sigmoid参数设置不当'
                ]
            })
        
        # 3. 中心定位误差分析
        if self.performance_summary['center_error_mean'] > 40:
            issues.append({
                'type': '严重问题',
                'category': 'EEG源定位',
                'description': f"中心定位误差过大 ({self.performance_summary['center_error_mean']:.1f} 体素)",
                'impact': '边界框中心位置偏离真实病灶中心太远',
                'possible_causes': [
                    'EEG源定位算法不准确',
                    '电极位置映射错误',
                    '特征提取不充分',
                    '多模态特征融合失效'
                ]
            })
        
        # 4. 标准差分析
        if self.performance_summary['dice_std'] > 0.15:
            issues.append({
                'type': '中等问题',
                'category': '预测一致性',
                'description': f"Dice分数标准差过大 ({self.performance_summary['dice_std']:.3f})",
                'impact': '模型预测不稳定，不同样本间性能差异巨大',
                'possible_causes': [
                    '训练数据质量不一致',
                    '模型过拟合',
                    '数据增强策略不当',
                    '批次间差异过大'
                ]
            })
        
        return issues
    
    def analyze_architecture_problems(self):
        """分析架构问题"""
        print("\n🏗️ 架构问题分析")
        print("="*60)
        
        architecture_issues = []
        
        # 1. 多模态特征融合问题
        architecture_issues.append({
            'component': '特征融合模块',
            'problem': '注意力权重可能不平衡',
            'evidence': '三个分支的特征可能没有有效融合',
            'solution': '检查并调整注意力权重初始化和学习率'
        })
        
        # 2. 边界框预测网络问题
        architecture_issues.append({
            'component': '自适应立方边界框',
            'problem': '多尺度策略可能失效',
            'evidence': '预测的边界框尺寸与真实病灶尺寸不匹配',
            'solution': '重新设计尺度选择和细化机制'
        })
        
        # 3. 损失函数问题
        architecture_issues.append({
            'component': '多目标损失函数',
            'problem': '损失组件权重可能不合理',
            'evidence': 'Dice和IoU都很低，说明损失函数没有有效指导训练',
            'solution': '重新平衡损失权重，考虑自适应权重调整'
        })
        
        return architecture_issues
    
    def analyze_data_problems(self):
        """分析数据问题"""
        print("\n📊 数据问题分析")
        print("="*60)
        
        data_issues = []
        
        # 1. EEG-病灶配对质量
        data_issues.append({
            'aspect': 'EEG-病灶配对',
            'problem': '合成配对可能不够真实',
            'evidence': '即使是高兼容性分数的配对，模型性能仍然很差',
            'impact': '模型学习到的是虚假的EEG-病灶关系',
            'solution': '改进配对算法，增加更多临床约束'
        })
        
        # 2. 数据规模问题
        data_issues.append({
            'aspect': '数据规模',
            'problem': '训练数据可能不足',
            'evidence': f'只有{self.performance_summary["n_samples"]}个测试样本',
            'impact': '模型无法学习到足够的模式',
            'solution': '增加数据规模或使用数据增强'
        })
        
        # 3. 数据质量问题
        data_issues.append({
            'aspect': '数据质量',
            'problem': 'EEG数据预处理可能不当',
            'evidence': '之前发现EEG数据范围异常',
            'impact': '特征提取网络无法有效学习',
            'solution': '改进EEG数据标准化和预处理'
        })
        
        return data_issues
    
    def propose_solutions(self, issues, architecture_issues, data_issues):
        """提出解决方案"""
        print("\n💡 解决方案建议")
        print("="*60)
        
        solutions = {
            'immediate_fixes': [
                {
                    'priority': '高',
                    'action': '重新设计损失函数',
                    'details': [
                        '使用更大的Dice损失权重 (0.6)',
                        '减少Focal损失权重 (0.1)',
                        '添加中心定位专门的L2损失',
                        '实现自适应损失权重调整'
                    ]
                },
                {
                    'priority': '高',
                    'action': '改进边界框预测',
                    'details': [
                        '简化多尺度策略，使用固定尺寸',
                        '增加边界框回归的监督信号',
                        '使用更大的sigmoid参数提高边界清晰度',
                        '添加边界框形状约束'
                    ]
                },
                {
                    'priority': '中',
                    'action': '优化特征提取',
                    'details': [
                        '增加EEG特征提取网络的深度',
                        '使用预训练的CNN backbone',
                        '改进时序特征提取',
                        '添加跨模态注意力机制'
                    ]
                }
            ],
            'long_term_improvements': [
                {
                    'priority': '高',
                    'action': '改进数据配对策略',
                    'details': [
                        '使用更严格的神经解剖学约束',
                        '添加病理学知识指导',
                        '实现基于相似性的配对',
                        '增加配对质量验证'
                    ]
                },
                {
                    'priority': '中',
                    'action': '扩大数据规模',
                    'details': [
                        '使用所有可用的EEG和病灶数据',
                        '实现更复杂的数据增强',
                        '考虑使用生成模型增加数据',
                        '收集更多真实的配对数据'
                    ]
                }
            ]
        }
        
        return solutions
    
    def create_problem_visualization(self, issues, architecture_issues, data_issues):
        """创建问题可视化"""
        print("\n📈 生成问题分析可视化...")
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('癫痫病灶定位模型 - 深度问题分析', fontsize=16, fontweight='bold')
        
        # 1. 性能指标对比
        metrics = ['Dice分数', 'IoU分数', '中心误差(归一化)']
        current_values = [
            self.performance_summary['dice_mean'],
            self.performance_summary['iou_mean'],
            1 - min(self.performance_summary['center_error_mean'] / 100, 1)
        ]
        target_values = [0.7, 0.5, 0.8]  # 目标值
        
        x = np.arange(len(metrics))
        width = 0.35
        
        bars1 = axes[0, 0].bar(x - width/2, current_values, width, label='当前性能', color='red', alpha=0.7)
        bars2 = axes[0, 0].bar(x + width/2, target_values, width, label='目标性能', color='green', alpha=0.7)
        
        axes[0, 0].set_title('性能指标对比')
        axes[0, 0].set_ylabel('分数')
        axes[0, 0].set_xticks(x)
        axes[0, 0].set_xticklabels(metrics)
        axes[0, 0].legend()
        axes[0, 0].set_ylim(0, 1)
        
        # 添加数值标签
        for bars in [bars1, bars2]:
            for bar in bars:
                height = bar.get_height()
                axes[0, 0].text(bar.get_x() + bar.get_width()/2., height + 0.02,
                               f'{height:.3f}', ha='center', va='bottom')
        
        # 2. 问题严重程度分布
        problem_types = ['严重问题', '中等问题', '轻微问题']
        problem_counts = [
            len([i for i in issues if i['type'] == '严重问题']),
            len([i for i in issues if i['type'] == '中等问题']),
            len([i for i in issues if i['type'] == '轻微问题'])
        ]
        
        colors = ['red', 'orange', 'yellow']
        axes[0, 1].pie(problem_counts, labels=problem_types, colors=colors, autopct='%1.0f%%')
        axes[0, 1].set_title('问题严重程度分布')
        
        # 3. 问题类别分析
        categories = list(set([i['category'] for i in issues]))
        category_counts = [len([i for i in issues if i['category'] == cat]) for cat in categories]
        
        axes[0, 2].bar(categories, category_counts, color='skyblue', alpha=0.7)
        axes[0, 2].set_title('问题类别分布')
        axes[0, 2].set_ylabel('问题数量')
        axes[0, 2].tick_params(axis='x', rotation=45)
        
        # 4. 架构组件问题
        components = [issue['component'] for issue in architecture_issues]
        component_scores = [3 if '失效' in issue['problem'] else 2 if '不平衡' in issue['problem'] else 1 
                           for issue in architecture_issues]
        
        axes[1, 0].barh(components, component_scores, color='lightcoral', alpha=0.7)
        axes[1, 0].set_title('架构组件问题严重程度')
        axes[1, 0].set_xlabel('严重程度 (1-3)')
        
        # 5. 数据问题影响
        data_aspects = [issue['aspect'] for issue in data_issues]
        impact_scores = [3 if '虚假' in issue['impact'] else 2 if '不足' in issue['impact'] else 1 
                        for issue in data_issues]
        
        axes[1, 1].bar(data_aspects, impact_scores, color='lightgreen', alpha=0.7)
        axes[1, 1].set_title('数据问题影响程度')
        axes[1, 1].set_ylabel('影响程度 (1-3)')
        axes[1, 1].tick_params(axis='x', rotation=45)
        
        # 6. 解决方案优先级
        solution_priorities = ['高优先级', '中优先级', '低优先级']
        high_priority = 2  # immediate_fixes中的高优先级
        medium_priority = 1  # immediate_fixes中的中优先级
        low_priority = 2  # long_term_improvements
        
        priority_counts = [high_priority, medium_priority, low_priority]
        colors = ['darkred', 'orange', 'lightblue']
        
        axes[1, 2].bar(solution_priorities, priority_counts, color=colors, alpha=0.7)
        axes[1, 2].set_title('解决方案优先级分布')
        axes[1, 2].set_ylabel('方案数量')
        
        plt.tight_layout()
        plt.savefig('detailed_problem_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("✅ 问题分析可视化已保存为: detailed_problem_analysis.png")
    
    def generate_detailed_report(self, issues, architecture_issues, data_issues, solutions):
        """生成详细报告"""
        
        report = f"""
# 癫痫病灶定位模型 - 深度问题分析报告

## 📊 性能现状
- **Dice分数**: {self.performance_summary['dice_mean']:.3f} ± {self.performance_summary['dice_std']:.3f}
- **IoU分数**: {self.performance_summary['iou_mean']:.3f} ± {self.performance_summary['iou_std']:.3f}
- **中心定位误差**: {self.performance_summary['center_error_mean']:.1f} ± {self.performance_summary['center_error_std']:.1f} 体素
- **评估样本数**: {self.performance_summary['n_samples']}

## 🚨 识别的关键问题

### 严重问题
"""
        
        for issue in issues:
            if issue['type'] == '严重问题':
                report += f"""
#### {issue['category']}
- **问题描述**: {issue['description']}
- **影响**: {issue['impact']}
- **可能原因**:
"""
                for cause in issue['possible_causes']:
                    report += f"  - {cause}\n"
        
        report += """
### 架构问题分析
"""
        for issue in architecture_issues:
            report += f"""
#### {issue['component']}
- **问题**: {issue['problem']}
- **证据**: {issue['evidence']}
- **建议解决方案**: {issue['solution']}
"""
        
        report += """
### 数据问题分析
"""
        for issue in data_issues:
            report += f"""
#### {issue['aspect']}
- **问题**: {issue['problem']}
- **证据**: {issue['evidence']}
- **影响**: {issue['impact']}
- **解决方案**: {issue['solution']}
"""
        
        report += """
## 💡 解决方案建议

### 立即修复 (高优先级)
"""
        for solution in solutions['immediate_fixes']:
            if solution['priority'] == '高':
                report += f"""
#### {solution['action']}
"""
                for detail in solution['details']:
                    report += f"- {detail}\n"
        
        report += """
### 长期改进
"""
        for solution in solutions['long_term_improvements']:
            report += f"""
#### {solution['action']} (优先级: {solution['priority']})
"""
            for detail in solution['details']:
                report += f"- {detail}\n"
        
        report += f"""
## 📈 预期改进效果

通过实施上述解决方案，预期能够达到:
- **Dice分数**: 从 {self.performance_summary['dice_mean']:.3f} 提升到 0.7+
- **IoU分数**: 从 {self.performance_summary['iou_mean']:.3f} 提升到 0.5+
- **中心定位误差**: 从 {self.performance_summary['center_error_mean']:.1f} 降低到 20 体素以内

## 🎯 下一步行动计划

1. **立即行动** (1-2天):
   - 重新设计损失函数权重
   - 简化边界框预测策略
   - 优化训练超参数

2. **短期改进** (1-2周):
   - 改进特征提取网络
   - 增强数据预处理
   - 实现更好的数据增强

3. **长期优化** (1个月+):
   - 重新设计EEG-病灶配对算法
   - 扩大训练数据规模
   - 考虑使用更先进的架构

---

**报告生成时间**: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}
**分析样本数**: {self.performance_summary['n_samples']}
**模型状态**: 需要重大改进
"""
        
        # 保存报告
        with open('detailed_problem_analysis_report.md', 'w', encoding='utf-8') as f:
            f.write(report)
        
        print("✅ 详细问题分析报告已保存为: detailed_problem_analysis_report.md")
        
        return report

def main():
    """主函数"""
    print("🔍 癫痫病灶定位模型 - 深度问题分析")
    print("="*70)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    analyzer = DetailedProblemAnalyzer(device)
    
    # 分析根本问题
    issues = analyzer.analyze_fundamental_issues()
    
    # 分析架构问题
    architecture_issues = analyzer.analyze_architecture_problems()
    
    # 分析数据问题
    data_issues = analyzer.analyze_data_problems()
    
    # 提出解决方案
    solutions = analyzer.propose_solutions(issues, architecture_issues, data_issues)
    
    # 创建可视化
    analyzer.create_problem_visualization(issues, architecture_issues, data_issues)
    
    # 生成详细报告
    report = analyzer.generate_detailed_report(issues, architecture_issues, data_issues, solutions)
    
    print("\n" + "="*70)
    print("🎯 关键发现总结")
    print("="*70)
    print("❌ 主要问题:")
    print("   1. Dice分数过低 (0.272) - 边界框与病灶重叠度极差")
    print("   2. IoU分数过低 (0.173) - 边界对齐精度极差") 
    print("   3. 中心定位误差过大 (45.5体素) - EEG源定位不准确")
    print("   4. 预测不稳定 - 标准差过大")
    
    print("\n💡 核心解决方案:")
    print("   1. 重新设计损失函数 - 增加Dice权重，添加中心定位损失")
    print("   2. 简化边界框预测 - 使用固定尺寸，改进回归监督")
    print("   3. 改进EEG-病灶配对 - 增加神经解剖学约束")
    print("   4. 优化特征提取 - 增加网络深度，改进多模态融合")
    
    print(f"\n✅ 分析完成！详细报告和可视化已生成。")

if __name__ == "__main__":
    main()
