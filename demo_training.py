#!/usr/bin/env python3
"""
EEG-MRI病灶定位演示训练脚本
简化版本用于演示完整流程
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import pandas as pd
import nibabel as nib
import gzip
import glob
import os
from sklearn.preprocessing import StandardScaler
from scipy.interpolate import griddata
from scipy.ndimage import zoom
import matplotlib.pyplot as plt
import warnings

warnings.filterwarnings('ignore')

def step1_preprocess_eeg():
    """步骤1: 预处理并归一化EEG数据"""
    print("=== 步骤1: 预处理并归一化EEG数据 ===")
    
    # 标准10-20系统电极位置
    electrode_positions = {
        'AF3': (-0.3, 0.7), 'AF4': (0.3, 0.7),
        'F3': (-0.5, 0.5), 'F4': (0.5, 0.5),
        'F7': (-0.7, 0.3), 'F8': (0.7, 0.3),
        'FC5': (-0.6, 0.2), 'FC6': (0.6, 0.2),
        'T7': (-0.8, 0.0), 'T8': (0.8, 0.0),
        'P7': (-0.7, -0.3), 'P8': (0.7, -0.3),
        'O1': (-0.3, -0.7), 'O2': (0.3, -0.7)
    }
    
    eeg_channels = list(electrode_positions.keys())
    
    # 加载示例EEG文件
    eeg_files = glob.glob("1252141/EEGs_Nigeria/*.csv.gz")
    if not eeg_files:
        print("未找到EEG文件!")
        return None, None
    
    sample_file = eeg_files[0]
    print(f"加载文件: {sample_file}")
    
    with gzip.open(sample_file, 'rt') as f:
        data = pd.read_csv(f)
    
    # 提取EEG通道数据
    eeg_data = data[eeg_channels].values
    print(f"原始EEG数据形状: {eeg_data.shape}")
    
    # 归一化
    scaler = StandardScaler()
    eeg_normalized = scaler.fit_transform(eeg_data)
    print(f"归一化后数据范围: {eeg_normalized.min():.3f} to {eeg_normalized.max():.3f}")
    
    # 分割为窗口
    window_size = 128  # 1秒窗口
    stride = 64
    windows = []
    
    for i in range(0, len(eeg_normalized) - window_size + 1, stride):
        window = eeg_normalized[i:i + window_size]
        windows.append(window)
    
    eeg_windows = np.array(windows)
    print(f"EEG窗口数据形状: {eeg_windows.shape}")
    
    return eeg_windows, electrode_positions

def step2_blur_mri():
    """步骤2: 将MRI模糊化到8x8x8"""
    print("\n=== 步骤2: 将MRI模糊化到8x8x8 ===")
    
    # 加载示例MRI掩码
    mask_files = glob.glob("masks-2/*/[0-9]*_MaskInRawData.nii.gz")
    if not mask_files:
        print("未找到MRI掩码文件!")
        return None
    
    sample_mask = mask_files[0]
    print(f"加载掩码: {sample_mask}")
    
    img = nib.load(sample_mask)
    mask_data = img.get_fdata()
    print(f"原始掩码形状: {mask_data.shape}")
    
    # 下采样到8x8x8
    target_shape = (8, 8, 8)
    zoom_factors = [target_shape[i] / mask_data.shape[i] for i in range(3)]
    mask_8x8x8 = zoom(mask_data, zoom_factors, order=1)
    mask_8x8x8 = (mask_8x8x8 > 0.5).astype(np.float32)
    
    print(f"模糊化后掩码形状: {mask_8x8x8.shape}")
    print(f"模糊化后非零体素: {np.sum(mask_8x8x8)}")
    
    return mask_8x8x8, mask_data

def step3_eeg_to_topomap(eeg_windows, electrode_positions):
    """步骤3: 将EEG数据转化为2D地形图"""
    print("\n=== 步骤3: 将EEG数据转化为2D地形图 ===")
    
    def create_topomap(eeg_window):
        # 创建64x64的地形图
        grid_size = 64
        xi = np.linspace(-1, 1, grid_size)
        yi = np.linspace(-1, 1, grid_size)
        xi, yi = np.meshgrid(xi, yi)
        
        # 获取电极位置和值
        positions = np.array([electrode_positions[ch] for ch in electrode_positions.keys()])
        values = eeg_window.mean(axis=0)  # 对时间维度取平均
        
        # 插值生成地形图
        topomap = griddata(positions, values, (xi, yi), method='cubic', fill_value=0)
        
        # 创建头部掩码
        mask = (xi**2 + yi**2) <= 1.0
        topomap = topomap * mask
        
        return topomap
    
    # 转换前10个窗口为地形图
    topomaps = []
    for i in range(min(10, len(eeg_windows))):
        topomap = create_topomap(eeg_windows[i])
        topomaps.append(topomap)
    
    topomaps = np.array(topomaps)
    print(f"地形图数据形状: {topomaps.shape}")
    
    # 可视化第一个地形图
    plt.figure(figsize=(8, 6))
    plt.imshow(topomaps[0], cmap='RdBu_r', origin='lower')
    plt.title('EEG Topographic Map Example')
    plt.colorbar(label='Amplitude')
    plt.axis('off')
    plt.savefig('eeg_topomap_example.png', dpi=300, bbox_inches='tight')
    print("保存地形图示例: eeg_topomap_example.png")
    plt.close()
    
    return topomaps

def step4_create_models():
    """步骤4: 创建多层注意力CNN和LSTM模型"""
    print("\n=== 步骤4: 创建深度学习模型 ===")
    
    class SpatialAttention(nn.Module):
        def __init__(self, channels):
            super().__init__()
            self.conv = nn.Conv2d(channels, 1, 1)
            self.sigmoid = nn.Sigmoid()
        
        def forward(self, x):
            attention = self.sigmoid(self.conv(x))
            return x * attention
    
    class AttentionCNN(nn.Module):
        def __init__(self):
            super().__init__()
            self.conv1 = nn.Conv2d(1, 32, 3, padding=1)
            self.conv2 = nn.Conv2d(32, 64, 3, padding=1)
            self.conv3 = nn.Conv2d(64, 128, 3, padding=1)
            
            self.attention1 = SpatialAttention(32)
            self.attention2 = SpatialAttention(64)
            self.attention3 = SpatialAttention(128)
            
            self.pool = nn.MaxPool2d(2, 2)
            self.adaptive_pool = nn.AdaptiveAvgPool2d((4, 4))
        
        def forward(self, x):
            features = []
            
            x = F.relu(self.conv1(x))
            x = self.attention1(x)
            features.append(x)
            x = self.pool(x)
            
            x = F.relu(self.conv2(x))
            x = self.attention2(x)
            features.append(x)
            x = self.pool(x)
            
            x = F.relu(self.conv3(x))
            x = self.attention3(x)
            features.append(x)
            x = self.adaptive_pool(x)
            
            return x, features
    
    class TemporalLSTM(nn.Module):
        def __init__(self):
            super().__init__()
            self.lstm = nn.LSTM(128*4*4, 256, 2, batch_first=True, dropout=0.3)
            self.fc = nn.Linear(256, 128)
        
        def forward(self, x):
            lstm_out, _ = self.lstm(x)
            return self.fc(lstm_out[:, -1, :])
    
    class VoxelPredictor(nn.Module):
        def __init__(self, resolution=8):
            super().__init__()
            self.resolution = resolution
            total_voxels = resolution ** 3
            
            self.predictor = nn.Sequential(
                nn.Linear(128, 256),
                nn.ReLU(),
                nn.Dropout(0.3),
                nn.Linear(256, 512),
                nn.ReLU(),
                nn.Dropout(0.3),
                nn.Linear(512, total_voxels),
                nn.Sigmoid()
            )
        
        def forward(self, x):
            out = self.predictor(x)
            return out.view(-1, self.resolution, self.resolution, self.resolution)
    
    # 创建模型
    cnn_model = AttentionCNN()
    lstm_model = TemporalLSTM()
    voxel_predictor = VoxelPredictor(8)
    
    print("模型创建完成:")
    print(f"CNN参数数量: {sum(p.numel() for p in cnn_model.parameters()):,}")
    print(f"LSTM参数数量: {sum(p.numel() for p in lstm_model.parameters()):,}")
    print(f"体素预测器参数数量: {sum(p.numel() for p in voxel_predictor.parameters()):,}")
    
    return cnn_model, lstm_model, voxel_predictor

def step5_demo_training(topomaps, mask_8x8x8, models):
    """步骤5: 演示训练过程"""
    print("\n=== 步骤5: 演示训练过程 ===")
    
    cnn_model, lstm_model, voxel_predictor = models
    
    # 转换为PyTorch张量
    topomaps_tensor = torch.FloatTensor(topomaps).unsqueeze(1)  # (seq, 1, 64, 64)
    mask_tensor = torch.FloatTensor(mask_8x8x8).unsqueeze(0)  # (1, 8, 8, 8)
    
    print(f"地形图张量形状: {topomaps_tensor.shape}")
    print(f"掩码张量形状: {mask_tensor.shape}")
    
    # 设置训练模式
    cnn_model.train()
    lstm_model.train()
    voxel_predictor.train()
    
    # 优化器和损失函数
    optimizer = torch.optim.Adam(
        list(cnn_model.parameters()) + 
        list(lstm_model.parameters()) + 
        list(voxel_predictor.parameters()), 
        lr=0.001
    )
    criterion = nn.BCELoss()
    
    # 演示训练循环
    print("\n开始演示训练...")
    for epoch in range(5):  # 只训练5个epoch作为演示
        optimizer.zero_grad()
        
        # 前向传播
        cnn_features_list = []
        for i in range(topomaps_tensor.shape[0]):
            features, _ = cnn_model(topomaps_tensor[i:i+1])
            cnn_features_list.append(features.view(1, -1))
        
        # LSTM处理时间序列
        cnn_features = torch.cat(cnn_features_list, dim=0).unsqueeze(0)  # (1, seq, features)
        lstm_features = lstm_model(cnn_features)
        
        # 体素预测
        predicted_voxels = voxel_predictor(lstm_features)
        
        # 计算损失
        loss = criterion(predicted_voxels, mask_tensor)
        
        # 反向传播
        loss.backward()
        optimizer.step()
        
        print(f"Epoch {epoch+1}/5, Loss: {loss.item():.4f}")
    
    print("演示训练完成!")
    return predicted_voxels

def step6_progressive_resolution():
    """步骤6: 渐进式分辨率提升演示"""
    print("\n=== 步骤6: 渐进式分辨率提升演示 ===")
    
    resolutions = [8, 16, 32]
    
    for resolution in resolutions:
        print(f"\n训练分辨率: {resolution}x{resolution}x{resolution}")
        
        # 创建该分辨率的预测器
        class VoxelPredictor(nn.Module):
            def __init__(self, resolution):
                super().__init__()
                self.resolution = resolution
                total_voxels = resolution ** 3
                
                self.predictor = nn.Sequential(
                    nn.Linear(128, 256),
                    nn.ReLU(),
                    nn.Linear(256, 512),
                    nn.ReLU(),
                    nn.Linear(512, total_voxels),
                    nn.Sigmoid()
                )
            
            def forward(self, x):
                out = self.predictor(x)
                return out.view(-1, self.resolution, self.resolution, self.resolution)
        
        predictor = VoxelPredictor(resolution)
        print(f"分辨率 {resolution} 预测器参数: {sum(p.numel() for p in predictor.parameters()):,}")
    
    print("\n渐进式训练策略:")
    print("1. 从8x8x8开始训练基础特征")
    print("2. 逐步提升到16x16x16，融合CNN前几层特征")
    print("3. 继续提升到32x32x32，进一步融合特征")
    print("4. 最终达到目标分辨率")

def create_training_visualization():
    """创建训练过程可视化"""
    print("\n=== 创建训练过程可视化 ===")
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('EEG-MRI病灶定位深度学习框架', fontsize=16, fontweight='bold')
    
    # 1. EEG预处理流程
    ax = axes[0, 0]
    ax.text(0.5, 0.5, 
            "步骤1: EEG预处理\n\n"
            "• 加载原始EEG数据\n"
            "• 提取14个标准电极\n"
            "• 标准化归一化\n"
            "• 滑动窗口分割\n"
            "• 时间序列准备",
            ha='center', va='center', transform=ax.transAxes,
            fontsize=11, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))
    ax.set_title('EEG数据预处理')
    ax.axis('off')
    
    # 2. MRI模糊化
    ax = axes[0, 1]
    ax.text(0.5, 0.5,
            "步骤2: MRI模糊化\n\n"
            "• 加载高分辨率掩码\n"
            "• 下采样到8x8x8\n"
            "• 二值化处理\n"
            "• 体素概率计算\n"
            "• 目标数据准备",
            ha='center', va='center', transform=ax.transAxes,
            fontsize=11, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightcoral"))
    ax.set_title('MRI数据模糊化')
    ax.axis('off')
    
    # 3. 地形图转换
    ax = axes[0, 2]
    ax.text(0.5, 0.5,
            "步骤3: 地形图转换\n\n"
            "• EEG窗口数据\n"
            "• 电极位置映射\n"
            "• 空间插值\n"
            "• 64x64地形图\n"
            "• 时间序列地形图",
            ha='center', va='center', transform=ax.transAxes,
            fontsize=11, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen"))
    ax.set_title('EEG地形图生成')
    ax.axis('off')
    
    # 4. 深度学习模型
    ax = axes[1, 0]
    ax.text(0.5, 0.5,
            "步骤4: 深度学习模型\n\n"
            "• 多层注意力CNN\n"
            "• 空间特征提取\n"
            "• LSTM时间建模\n"
            "• 特征融合\n"
            "• 端到端训练",
            ha='center', va='center', transform=ax.transAxes,
            fontsize=11, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow"))
    ax.set_title('深度学习架构')
    ax.axis('off')
    
    # 5. 体素预测
    ax = axes[1, 1]
    ax.text(0.5, 0.5,
            "步骤5: 体素预测\n\n"
            "• EEG特征输入\n"
            "• 全连接网络\n"
            "• 体素概率输出\n"
            "• BCE损失函数\n"
            "• 梯度反向传播",
            ha='center', va='center', transform=ax.transAxes,
            fontsize=11, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightpink"))
    ax.set_title('体素概率预测')
    ax.axis('off')
    
    # 6. 渐进式训练
    ax = axes[1, 2]
    ax.text(0.5, 0.5,
            "步骤6: 渐进式训练\n\n"
            "• 8x8x8 → 16x16x16\n"
            "• 特征融合策略\n"
            "• 分辨率逐步提升\n"
            "• 多尺度学习\n"
            "• 最终高分辨率",
            ha='center', va='center', transform=ax.transAxes,
            fontsize=11, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))
    ax.set_title('渐进式分辨率')
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('eeg_mri_framework_overview.png', dpi=300, bbox_inches='tight')
    print("保存框架概览图: eeg_mri_framework_overview.png")
    plt.close()

def main():
    """主演示函数"""
    print("=== EEG-MRI病灶定位深度学习框架演示 ===")
    
    # 检查数据
    if not os.path.exists("1252141"):
        print("错误: 找不到EEG数据!")
        return
    
    if not os.path.exists("masks-2"):
        print("错误: 找不到MRI数据!")
        return
    
    try:
        # 步骤1: 预处理EEG数据
        eeg_windows, electrode_positions = step1_preprocess_eeg()
        if eeg_windows is None:
            return
        
        # 步骤2: 模糊化MRI
        mask_8x8x8, mask_original = step2_blur_mri()
        if mask_8x8x8 is None:
            return
        
        # 步骤3: EEG转地形图
        topomaps = step3_eeg_to_topomap(eeg_windows, electrode_positions)
        
        # 步骤4: 创建模型
        models = step4_create_models()
        
        # 步骤5: 演示训练
        predicted_voxels = step5_demo_training(topomaps, mask_8x8x8, models)
        
        # 步骤6: 渐进式分辨率
        step6_progressive_resolution()
        
        # 创建可视化
        create_training_visualization()
        
        print("\n=== 演示完成 ===")
        print("生成的文件:")
        print("- eeg_topomap_example.png: EEG地形图示例")
        print("- eeg_mri_framework_overview.png: 框架概览图")
        print("- eeg_data_exploration.png: EEG数据探索")
        
        print(f"\n=== 关键结果 ===")
        print(f"• EEG窗口数量: {len(eeg_windows)}")
        print(f"• 地形图形状: {topomaps.shape}")
        print(f"• 模糊化掩码形状: {mask_8x8x8.shape}")
        print(f"• 预测体素形状: {predicted_voxels.shape}")
        
    except Exception as e:
        print(f"演示过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
