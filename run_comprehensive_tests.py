"""
EEG源定位系统 - 综合测试执行脚本

该脚本执行完整的科研级别系统测试，包括：
1. 数据准备和验证
2. 系统功能测试
3. 性能基准测试
4. 结果质量评估
5. 错误处理和鲁棒性测试
6. 科研级别验证

使用方法:
python run_comprehensive_tests.py --data_root 1252141 --output_dir test_results
"""

import argparse
import logging
import sys
import time
import json
import yaml
from pathlib import Path
from typing import Dict, List
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('comprehensive_test.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class ComprehensiveTestRunner:
    """综合测试运行器"""
    
    def __init__(self, data_root: str, output_dir: str):
        self.data_root = Path(data_root)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 导入测试模块
        sys.path.append('tests')
        from test_real_data_validation import GuineaBissauDataValidator, RealDataEEGLoader
        
        self.validator = GuineaBissauDataValidator(str(self.data_root))
        self.eeg_loader = RealDataEEGLoader(str(self.data_root))
        
        # 测试结果存储
        self.test_results = {
            'start_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'data_validation': {},
            'system_tests': {},
            'performance_tests': {},
            'quality_assessment': {},
            'error_handling_tests': {},
            'scientific_validation': {}
        }
        
    def run_all_tests(self):
        """运行所有测试"""
        logger.info("开始综合测试执行")
        logger.info("="*60)
        
        try:
            # 1. 数据准备和验证
            logger.info("阶段1: 数据准备和验证")
            self._run_data_validation()
            
            # 2. 系统功能测试
            logger.info("阶段2: 系统功能测试")
            self._run_system_function_tests()
            
            # 3. 性能基准测试
            logger.info("阶段3: 性能基准测试")
            self._run_performance_tests()
            
            # 4. 结果质量评估
            logger.info("阶段4: 结果质量评估")
            self._run_quality_assessment()
            
            # 5. 错误处理和鲁棒性测试
            logger.info("阶段5: 错误处理和鲁棒性测试")
            self._run_error_handling_tests()
            
            # 6. 科研级别验证
            logger.info("阶段6: 科研级别验证")
            self._run_scientific_validation()
            
            # 生成综合报告
            self._generate_comprehensive_report()
            
            logger.info("="*60)
            logger.info("综合测试执行完成")
            
        except Exception as e:
            logger.error(f"综合测试执行失败: {e}")
            raise
            
    def _run_data_validation(self):
        """运行数据验证测试"""
        try:
            logger.info("1.1 验证数据完整性...")
            validation_results = self.validator.validate_data_integrity()
            
            # 保存验证结果
            self.test_results['data_validation'] = {
                'total_subjects': validation_results['total_subjects'],
                'missing_files': len(validation_results['missing_files']),
                'corrupted_files': len(validation_results['corrupted_files']),
                'quality_issues': len(validation_results['data_quality_issues']),
                'good_subjects': len([s for s in validation_results['subject_statistics'].values() 
                                    if s['n_channels'] >= 14 and s['duration'] >= 200])
            }
            
            logger.info(f"  总被试数: {validation_results['total_subjects']}")
            logger.info(f"  缺失文件: {len(validation_results['missing_files'])}")
            logger.info(f"  损坏文件: {len(validation_results['corrupted_files'])}")
            logger.info(f"  高质量被试: {self.test_results['data_validation']['good_subjects']}")
            
            # 选择测试被试
            logger.info("1.2 选择测试被试...")
            test_subjects = self.validator.select_test_subjects(n_subjects=5)
            self.test_subjects = test_subjects
            
            logger.info(f"  选择了 {len(test_subjects)} 个测试被试")
            for subject in test_subjects:
                logger.info(f"    被试 {subject['subject_id']}: {subject['group']}, "
                          f"{subject['data_stats']['n_channels']}通道")
                          
            # 保存测试被试信息
            with open(self.output_dir / 'test_subjects.json', 'w') as f:
                json.dump(test_subjects, f, indent=2, default=str)
                
        except Exception as e:
            logger.error(f"数据验证测试失败: {e}")
            self.test_results['data_validation']['error'] = str(e)
            
    def _run_system_function_tests(self):
        """运行系统功能测试"""
        try:
            logger.info("2.1 测试EEG数据加载...")
            loading_results = []
            
            for subject in self.test_subjects[:3]:  # 测试前3个被试
                subject_id = subject['subject_id']
                
                try:
                    start_time = time.time()
                    raw = self.eeg_loader.load_eeg_data(subject_id)
                    loading_time = time.time() - start_time
                    
                    loading_results.append({
                        'subject_id': subject_id,
                        'success': True,
                        'loading_time': loading_time,
                        'n_channels': raw.info['nchan'],
                        'duration': raw.times[-1],
                        'sfreq': raw.info['sfreq']
                    })
                    
                    logger.info(f"  被试 {subject_id}: 成功, {loading_time:.2f}秒")
                    
                except Exception as e:
                    loading_results.append({
                        'subject_id': subject_id,
                        'success': False,
                        'error': str(e)
                    })
                    logger.error(f"  被试 {subject_id}: 失败 - {e}")
                    
            self.test_results['system_tests']['eeg_loading'] = loading_results
            
            logger.info("2.2 测试源定位算法...")
            self._test_source_localization_algorithms()
            
        except Exception as e:
            logger.error(f"系统功能测试失败: {e}")
            self.test_results['system_tests']['error'] = str(e)
            
    def _test_source_localization_algorithms(self):
        """测试源定位算法"""
        try:
            # 导入系统模块
            sys.path.append('.')
            from main import EEGSourceLocalizationSystem
            
            # 创建测试配置
            test_config = self._create_test_config()
            config_path = self.output_dir / 'test_config.yaml'
            with open(config_path, 'w') as f:
                yaml.dump(test_config, f)
                
            # 初始化系统
            system = EEGSourceLocalizationSystem(str(config_path))
            
            # 测试不同算法
            algorithms = ['sloreta', 'mne']  # 测试主要算法
            algorithm_results = []
            
            # 选择一个测试被试
            test_subject = self.test_subjects[0]
            subject_id = test_subject['subject_id']
            
            # 加载真实EEG数据
            real_eeg_data = self.eeg_loader.load_eeg_data(subject_id)
            mock_mri_data = self._create_mock_mri_data()
            
            for algorithm in algorithms:
                try:
                    logger.info(f"    测试算法: {algorithm}")
                    
                    from unittest.mock import patch
                    
                    start_time = time.time()
                    
                    with patch.object(system.data_manager.eeg_loader, 'load_eeg_data', return_value=real_eeg_data):
                        with patch.object(system.data_manager.mri_processor, 'load_mri_data', return_value=mock_mri_data):
                            
                            result = system.run_complete_analysis(
                                subject_id=f'test_{subject_id}_{algorithm}',
                                data_type='guinea_bissau',
                                method=algorithm,
                                output_dir=str(self.output_dir / f'algorithm_test_{algorithm}')
                            )
                            
                    processing_time = time.time() - start_time
                    
                    algorithm_results.append({
                        'algorithm': algorithm,
                        'success': True,
                        'processing_time': processing_time,
                        'eeg_quality': result['eeg_result']['final_quality']['overall_score'],
                        'source_quality': result['source_result']['quality_metrics']['overall_score'],
                        'num_sources': result['source_result']['source_info']['num_sources']
                    })
                    
                    logger.info(f"      成功: {processing_time:.2f}秒, "
                              f"EEG质量={result['eeg_result']['final_quality']['overall_score']:.3f}")
                              
                except Exception as e:
                    algorithm_results.append({
                        'algorithm': algorithm,
                        'success': False,
                        'error': str(e)
                    })
                    logger.error(f"      失败: {e}")
                    
            self.test_results['system_tests']['algorithms'] = algorithm_results
            
        except Exception as e:
            logger.error(f"源定位算法测试失败: {e}")
            
    def _run_performance_tests(self):
        """运行性能测试"""
        try:
            logger.info("3.1 测试处理速度...")
            
            import psutil
            import os
            
            performance_results = []
            
            for subject in self.test_subjects[:3]:
                subject_id = subject['subject_id']
                
                try:
                    # 监控系统资源
                    process = psutil.Process(os.getpid())
                    initial_memory = process.memory_info().rss / 1024 / 1024  # MB
                    
                    start_time = time.time()
                    
                    # 加载和基本处理
                    raw = self.eeg_loader.load_eeg_data(subject_id)
                    
                    # 简单预处理
                    raw_filtered = raw.copy().filter(l_freq=0.5, h_freq=50, verbose=False)
                    
                    processing_time = time.time() - start_time
                    final_memory = process.memory_info().rss / 1024 / 1024  # MB
                    memory_usage = final_memory - initial_memory
                    
                    performance_results.append({
                        'subject_id': subject_id,
                        'n_channels': raw.info['nchan'],
                        'duration': raw.times[-1],
                        'processing_time': processing_time,
                        'memory_usage': memory_usage,
                        'throughput': raw.times[-1] / processing_time
                    })
                    
                    logger.info(f"  被试 {subject_id}: {processing_time:.2f}秒, "
                              f"{memory_usage:.1f}MB, {raw.times[-1]/processing_time:.1f}x实时")
                              
                except Exception as e:
                    logger.error(f"  被试 {subject_id} 性能测试失败: {e}")
                    
            self.test_results['performance_tests'] = {
                'individual_results': performance_results,
                'summary': {
                    'avg_throughput': np.mean([r['throughput'] for r in performance_results]) if performance_results else 0,
                    'max_memory': max([r['memory_usage'] for r in performance_results]) if performance_results else 0,
                    'avg_processing_time': np.mean([r['processing_time'] for r in performance_results]) if performance_results else 0
                }
            }
            
            if performance_results:
                summary = self.test_results['performance_tests']['summary']
                logger.info(f"  性能摘要: 平均吞吐量={summary['avg_throughput']:.1f}x实时, "
                          f"最大内存={summary['max_memory']:.1f}MB")
                          
        except Exception as e:
            logger.error(f"性能测试失败: {e}")
            self.test_results['performance_tests']['error'] = str(e)
            
    def _run_quality_assessment(self):
        """运行质量评估"""
        try:
            logger.info("4.1 评估数据质量...")
            
            quality_results = []
            
            for subject in self.test_subjects:
                subject_id = subject['subject_id']
                
                try:
                    raw = self.eeg_loader.load_eeg_data(subject_id)
                    data = raw.get_data()
                    
                    # 计算质量指标
                    quality_metrics = {
                        'subject_id': subject_id,
                        'group': subject['group'],
                        'snr': self._calculate_snr(data),
                        'signal_variance': float(np.mean(np.var(data, axis=1))),
                        'channel_correlation': float(self._calculate_channel_correlation(data)),
                        'artifact_ratio': float(self._estimate_artifact_ratio(data))
                    }
                    
                    quality_results.append(quality_metrics)
                    
                    logger.info(f"  被试 {subject_id} ({subject['group']}): "
                              f"SNR={quality_metrics['snr']:.1f}dB, "
                              f"相关性={quality_metrics['channel_correlation']:.3f}")
                              
                except Exception as e:
                    logger.error(f"  被试 {subject_id} 质量评估失败: {e}")
                    
            self.test_results['quality_assessment'] = {
                'individual_results': quality_results,
                'group_comparison': self._analyze_group_differences(quality_results)
            }
            
        except Exception as e:
            logger.error(f"质量评估失败: {e}")
            self.test_results['quality_assessment']['error'] = str(e)
            
    def _run_error_handling_tests(self):
        """运行错误处理测试"""
        try:
            logger.info("5.1 测试错误处理机制...")
            
            error_tests = []
            
            # 测试1: 无效被试ID
            try:
                self.eeg_loader.load_eeg_data(99999)  # 不存在的被试
                error_tests.append({'test': 'invalid_subject_id', 'result': 'failed_to_catch_error'})
            except Exception:
                error_tests.append({'test': 'invalid_subject_id', 'result': 'error_caught_correctly'})
                
            # 测试2: 损坏的数据文件（模拟）
            # 这里可以添加更多错误处理测试
            
            self.test_results['error_handling_tests'] = error_tests
            logger.info(f"  完成 {len(error_tests)} 个错误处理测试")
            
        except Exception as e:
            logger.error(f"错误处理测试失败: {e}")
            self.test_results['error_handling_tests']['error'] = str(e)
            
    def _run_scientific_validation(self):
        """运行科研级别验证"""
        try:
            logger.info("6.1 科研级别验证...")
            
            # 分析癫痫组和对照组的差异
            validation_results = {}
            
            if 'quality_assessment' in self.test_results and 'individual_results' in self.test_results['quality_assessment']:
                quality_results = self.test_results['quality_assessment']['individual_results']
                
                epilepsy_subjects = [r for r in quality_results if r['group'] == 'Epilepsy']
                control_subjects = [r for r in quality_results if r['group'] == 'Control']
                
                if epilepsy_subjects and control_subjects:
                    # 计算组间差异
                    epilepsy_snr = np.mean([s['snr'] for s in epilepsy_subjects])
                    control_snr = np.mean([s['snr'] for s in control_subjects])
                    
                    validation_results['group_differences'] = {
                        'epilepsy_snr': float(epilepsy_snr),
                        'control_snr': float(control_snr),
                        'snr_difference': float(abs(epilepsy_snr - control_snr)),
                        'epilepsy_n': len(epilepsy_subjects),
                        'control_n': len(control_subjects)
                    }
                    
                    logger.info(f"  癫痫组SNR: {epilepsy_snr:.1f}dB (n={len(epilepsy_subjects)})")
                    logger.info(f"  对照组SNR: {control_snr:.1f}dB (n={len(control_subjects)})")
                    
            self.test_results['scientific_validation'] = validation_results
            
        except Exception as e:
            logger.error(f"科研级别验证失败: {e}")
            self.test_results['scientific_validation']['error'] = str(e)
            
    def _generate_comprehensive_report(self):
        """生成综合报告"""
        try:
            logger.info("生成综合测试报告...")
            
            # 保存完整测试结果
            self.test_results['end_time'] = time.strftime('%Y-%m-%d %H:%M:%S')
            
            with open(self.output_dir / 'comprehensive_test_results.json', 'w') as f:
                json.dump(self.test_results, f, indent=2, default=str)
                
            # 生成Markdown报告
            self._generate_markdown_report()
            
            # 生成可视化图表
            self._generate_visualization_plots()
            
            logger.info(f"综合测试报告已保存到: {self.output_dir}")
            
        except Exception as e:
            logger.error(f"生成综合报告失败: {e}")
            
    def _generate_markdown_report(self):
        """生成Markdown格式的报告"""
        report_content = f"""# EEG源定位系统 - 综合测试报告

## 测试概述

- **测试开始时间**: {self.test_results['start_time']}
- **测试结束时间**: {self.test_results['end_time']}
- **数据来源**: 几内亚比绍EEG数据集
- **测试被试数**: {len(self.test_subjects) if hasattr(self, 'test_subjects') else 'N/A'}

## 1. 数据验证结果

"""
        
        if 'data_validation' in self.test_results:
            dv = self.test_results['data_validation']
            report_content += f"""
- **总被试数**: {dv.get('total_subjects', 'N/A')}
- **缺失文件**: {dv.get('missing_files', 'N/A')}
- **损坏文件**: {dv.get('corrupted_files', 'N/A')}
- **高质量被试**: {dv.get('good_subjects', 'N/A')}
"""

        report_content += "\n## 2. 系统功能测试结果\n"
        
        if 'system_tests' in self.test_results:
            st = self.test_results['system_tests']
            if 'eeg_loading' in st:
                successful_loads = len([r for r in st['eeg_loading'] if r.get('success', False)])
                report_content += f"\n- **EEG数据加载成功率**: {successful_loads}/{len(st['eeg_loading'])}\n"
                
            if 'algorithms' in st:
                successful_algs = len([r for r in st['algorithms'] if r.get('success', False)])
                report_content += f"- **算法测试成功率**: {successful_algs}/{len(st['algorithms'])}\n"

        report_content += "\n## 3. 性能测试结果\n"
        
        if 'performance_tests' in self.test_results and 'summary' in self.test_results['performance_tests']:
            pt = self.test_results['performance_tests']['summary']
            report_content += f"""
- **平均处理吞吐量**: {pt.get('avg_throughput', 'N/A'):.1f}x 实时
- **最大内存使用**: {pt.get('max_memory', 'N/A'):.1f} MB
- **平均处理时间**: {pt.get('avg_processing_time', 'N/A'):.2f} 秒
"""

        report_content += "\n## 4. 数据质量评估\n"
        
        if 'quality_assessment' in self.test_results and 'group_comparison' in self.test_results['quality_assessment']:
            gc = self.test_results['quality_assessment']['group_comparison']
            if gc:
                report_content += f"""
- **癫痫组平均SNR**: {gc.get('epilepsy_snr', 'N/A'):.1f} dB
- **对照组平均SNR**: {gc.get('control_snr', 'N/A'):.1f} dB
- **组间SNR差异**: {gc.get('snr_difference', 'N/A'):.1f} dB
"""

        report_content += "\n## 5. 测试结论\n\n"
        
        # 添加结论
        conclusions = []
        
        if 'data_validation' in self.test_results:
            dv = self.test_results['data_validation']
            if dv.get('good_subjects', 0) > 50:
                conclusions.append("✅ 数据质量良好，有足够的高质量被试用于分析")
            else:
                conclusions.append("⚠️ 高质量被试数量有限，可能影响分析结果")
                
        if 'performance_tests' in self.test_results:
            pt = self.test_results['performance_tests'].get('summary', {})
            if pt.get('avg_throughput', 0) > 5:
                conclusions.append("✅ 系统性能良好，处理速度满足实时要求")
            else:
                conclusions.append("⚠️ 系统性能需要优化")
                
        for conclusion in conclusions:
            report_content += f"- {conclusion}\n"
            
        # 保存报告
        with open(self.output_dir / 'comprehensive_test_report.md', 'w', encoding='utf-8') as f:
            f.write(report_content)
            
    def _generate_visualization_plots(self):
        """生成可视化图表"""
        try:
            # 设置中文字体
            plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei']
            plt.rcParams['axes.unicode_minus'] = False
            
            # 创建图表
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            fig.suptitle('EEG源定位系统测试结果可视化', fontsize=16)
            
            # 1. 数据质量分布
            if ('quality_assessment' in self.test_results and 
                'individual_results' in self.test_results['quality_assessment']):
                
                quality_data = self.test_results['quality_assessment']['individual_results']
                if quality_data:
                    snr_values = [q['snr'] for q in quality_data]
                    groups = [q['group'] for q in quality_data]
                    
                    # 按组分类
                    epilepsy_snr = [snr for snr, group in zip(snr_values, groups) if group == 'Epilepsy']
                    control_snr = [snr for snr, group in zip(snr_values, groups) if group == 'Control']
                    
                    axes[0, 0].hist([epilepsy_snr, control_snr], bins=10, alpha=0.7, 
                                   label=['Epilepsy', 'Control'], color=['red', 'blue'])
                    axes[0, 0].set_title('SNR Distribution by Group')
                    axes[0, 0].set_xlabel('SNR (dB)')
                    axes[0, 0].set_ylabel('Count')
                    axes[0, 0].legend()
            
            # 2. 性能测试结果
            if ('performance_tests' in self.test_results and 
                'individual_results' in self.test_results['performance_tests']):
                
                perf_data = self.test_results['performance_tests']['individual_results']
                if perf_data:
                    throughput = [p['throughput'] for p in perf_data]
                    memory = [p['memory_usage'] for p in perf_data]
                    
                    axes[0, 1].scatter(throughput, memory, alpha=0.7)
                    axes[0, 1].set_title('Performance: Throughput vs Memory Usage')
                    axes[0, 1].set_xlabel('Throughput (x realtime)')
                    axes[0, 1].set_ylabel('Memory Usage (MB)')
            
            # 3. 算法比较
            if ('system_tests' in self.test_results and 
                'algorithms' in self.test_results['system_tests']):
                
                alg_data = self.test_results['system_tests']['algorithms']
                successful_algs = [a for a in alg_data if a.get('success', False)]
                
                if successful_algs:
                    algorithms = [a['algorithm'] for a in successful_algs]
                    processing_times = [a['processing_time'] for a in successful_algs]
                    
                    axes[1, 0].bar(algorithms, processing_times, alpha=0.7)
                    axes[1, 0].set_title('Algorithm Processing Times')
                    axes[1, 0].set_xlabel('Algorithm')
                    axes[1, 0].set_ylabel('Processing Time (s)')
            
            # 4. 测试成功率总结
            success_rates = {}
            
            if 'system_tests' in self.test_results:
                st = self.test_results['system_tests']
                if 'eeg_loading' in st:
                    success_rates['EEG Loading'] = len([r for r in st['eeg_loading'] if r.get('success', False)]) / len(st['eeg_loading'])
                if 'algorithms' in st:
                    success_rates['Algorithms'] = len([r for r in st['algorithms'] if r.get('success', False)]) / len(st['algorithms'])
            
            if success_rates:
                categories = list(success_rates.keys())
                rates = list(success_rates.values())
                
                axes[1, 1].bar(categories, rates, alpha=0.7, color='green')
                axes[1, 1].set_title('Test Success Rates')
                axes[1, 1].set_ylabel('Success Rate')
                axes[1, 1].set_ylim(0, 1)
            
            plt.tight_layout()
            plt.savefig(self.output_dir / 'test_results_visualization.png', dpi=300, bbox_inches='tight')
            plt.close()
            
        except Exception as e:
            logger.error(f"生成可视化图表失败: {e}")
            
    def _create_test_config(self) -> Dict:
        """创建测试配置"""
        return {
            'data_paths': {
                'guinea_bissau_dir': str(self.data_root / 'EEGs_Guinea-Bissau'),
                'output_dir': str(self.output_dir / 'analysis_results')
            },
            'eeg_processing': {
                'sampling_rate': 128,
                'filtering': {'highpass': 0.5, 'lowpass': 50, 'notch': 50},
                'preprocessing': {'remove_dc': True, 'detrend': True, 'baseline_correction': True},
                'artifact_removal': {'enable_ica': True, 'ica_components': 10},
                'channel_quality': {'bad_channel_threshold': 3.0, 'correlation_threshold': 0.3}
            },
            'head_modeling': {
                'tissue_segmentation': {'accuracy_threshold': 1.0}
            },
            'bem_modeling': {
                'model_type': '3_layer',
                'conductivity': {'scalp': 0.33, 'skull': 0.0042, 'brain': 0.33},
                'mesh': {'scalp_density': 1024, 'skull_density': 1024, 'brain_density': 1024},
                'numerical_accuracy': 1e-6
            },
            'source_localization': {
                'source_space': {'spacing': 10, 'surface_type': 'white'},
                'regularization': {'lambda_auto': False, 'lambda_value': 0.1}
            },
            'visualization': {
                'output_formats': ['png', 'html']
            }
        }
        
    def _create_mock_mri_data(self):
        """创建模拟MRI数据"""
        import nibabel as nib
        
        data = np.random.randint(0, 255, (64, 64, 64), dtype=np.uint8)
        affine = np.eye(4)
        nii_img = nib.Nifti1Image(data, affine)
        
        return {'T1': nii_img}
        
    def _calculate_snr(self, data: np.ndarray) -> float:
        """计算信噪比"""
        signal_power = np.mean(np.var(data, axis=1))
        noise_estimate = np.mean(np.abs(np.diff(data, axis=1)))
        noise_power = noise_estimate ** 2
        
        if noise_power > 0:
            snr_db = 10 * np.log10(signal_power / noise_power)
            return max(snr_db, 0)
        return 0
        
    def _calculate_channel_correlation(self, data: np.ndarray) -> float:
        """计算通道间平均相关性"""
        corr_matrix = np.corrcoef(data)
        np.fill_diagonal(corr_matrix, 0)
        return np.mean(np.abs(corr_matrix))
        
    def _estimate_artifact_ratio(self, data: np.ndarray) -> float:
        """估计伪迹比例"""
        threshold = 5 * np.std(data)
        artifact_samples = np.sum(np.abs(data) > threshold)
        return artifact_samples / data.size
        
    def _analyze_group_differences(self, quality_results: List[Dict]) -> Dict:
        """分析组间差异"""
        try:
            epilepsy_subjects = [r for r in quality_results if r['group'] == 'Epilepsy']
            control_subjects = [r for r in quality_results if r['group'] == 'Control']
            
            if not epilepsy_subjects or not control_subjects:
                return {}
                
            return {
                'epilepsy_snr': float(np.mean([s['snr'] for s in epilepsy_subjects])),
                'control_snr': float(np.mean([s['snr'] for s in control_subjects])),
                'snr_difference': float(abs(np.mean([s['snr'] for s in epilepsy_subjects]) - 
                                          np.mean([s['snr'] for s in control_subjects]))),
                'epilepsy_n': len(epilepsy_subjects),
                'control_n': len(control_subjects)
            }
            
        except Exception as e:
            logger.error(f"组间差异分析失败: {e}")
            return {}


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='EEG源定位系统综合测试')
    parser.add_argument('--data_root', type=str, default='1252141', help='数据根目录')
    parser.add_argument('--output_dir', type=str, default='test_results', help='输出目录')
    
    args = parser.parse_args()
    
    try:
        # 创建测试运行器
        test_runner = ComprehensiveTestRunner(args.data_root, args.output_dir)
        
        # 运行所有测试
        test_runner.run_all_tests()
        
        print("\n" + "="*60)
        print("综合测试执行完成！")
        print(f"详细结果请查看: {args.output_dir}")
        print("="*60)
        
    except Exception as e:
        logger.error(f"综合测试执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
