# BEM建模系统技术文档

## 系统概述

边界元法(BEM)建模系统 (`algorithms/bem_modeling.py`) 是EEG源定位系统的核心计算模块，负责构建高精度的头部电磁场正向模型。该系统确保与真实头部结构完全一致，为准确的源定位提供可靠的物理基础。

## 核心算法架构

### 1. BEMModeler (BEM建模器主类)

```python
class BEMModeler:
    def build_bem_model(self, tissue_masks, voxel_size) -> Dict
    def _validate_bem_model(self, bem_model) -> Dict
    def _validate_mesh_quality(self, mesh) -> bool
    def _validate_bem_matrices(self, bem_matrices) -> bool
```

**核心功能：**
- 协调整个BEM建模流程
- 集成网格生成、导电率设置和边界计算
- 实现模型质量验证和优化
- 支持3层和5层BEM模型

**伪代码实现：**
```
构建BEM模型(组织分割结果, 体素尺寸):
    生成高质量三角网格:
        根据模型类型选择组织层
        为每个组织生成优化网格
        验证网格质量和封闭性
    
    验证几何结构:
        检查网格完整性
        验证拓扑关系
        修复几何缺陷
    
    设置导电率参数:
        加载标准导电率值
        验证参数合理性
        应用个体化调整
    
    计算BEM边界条件:
        构建几何矩阵
        计算导电率矩阵
        求解系统矩阵
        生成导联场矩阵
    
    模型验证:
        几何结构验证
        数值稳定性检查
        物理合理性验证
    
    返回完整BEM模型
```

### 2. MeshGenerator (网格生成器)

```python
class MeshGenerator:
    def generate_meshes(self, tissue_masks, voxel_size) -> Dict[str, Dict]
    def _generate_single_mesh(self, mask, tissue_name, voxel_size) -> Dict
    def _optimize_mesh(self, vertices, faces, tissue_name) -> Tuple
    def _smooth_mesh(self, vertices, faces, iterations) -> np.ndarray
    def _calculate_normals(self, vertices, faces) -> np.ndarray
```

**网格生成策略：**
- Marching Cubes算法生成初始网格
- 自适应网格简化和优化
- 拉普拉斯平滑边界处理
- 高质量法向量计算

**伪代码实现：**
```
生成三角网格(组织分割mask, 组织名称, 体素尺寸):
    使用Marching Cubes算法:
        设置等值面阈值(0.5)
        考虑体素尺寸进行空间校正
        生成初始顶点和面
    
    网格优化:
        获取目标面数密度
        如果面数过多:
            使用Trimesh进行网格简化
            应用二次误差度量简化
        执行拉普拉斯平滑:
            迭代次数: 3
            平滑因子: 0.1
            保持几何特征
    
    计算顶点法向量:
        计算每个面的法向量
        累加到相邻顶点
        归一化处理
    
    创建网格数据结构:
        顶点坐标数组
        面索引数组
        法向量数组
        网格统计信息
    
    返回优化后的网格
```

### 3. ConductivityManager (导电率管理器)

```python
class ConductivityManager:
    def get_conductivities(self) -> Dict[str, float]
    def _validate_conductivities(self, conductivities)
```

**导电率标准值：**
```python
标准导电率 = {
    'scalp': 0.33,    # S/m (头皮)
    'skull': 0.0042,  # S/m (颅骨)
    'csf': 1.79,      # S/m (脑脊液)
    'gray': 0.33,     # S/m (灰质)
    'white': 0.14     # S/m (白质)
}
```

**伪代码实现：**
```
获取组织导电率():
    从配置文件加载导电率值
    验证导电率合理性:
        检查数值范围:
            头皮: [0.1, 1.0] S/m
            颅骨: [0.001, 0.1] S/m
            脑脊液: [1.0, 2.0] S/m
            灰质: [0.1, 1.0] S/m
            白质: [0.05, 0.5] S/m
        确保所有值为正数
    应用个体化调整(可选)
    返回验证后的导电率字典
```

### 4. BoundaryCalculator (边界条件计算器)

```python
class BoundaryCalculator:
    def calculate_bem_matrices(self, meshes, conductivities, voxel_size) -> Dict
    def _calculate_geometry_matrix(self, meshes) -> np.ndarray
    def _calculate_conductivity_matrix(self, meshes, conductivities) -> np.ndarray
    def _calculate_system_matrix(self, geometry_matrix, conductivity_matrix) -> np.ndarray
    def _calculate_leadfield_matrix(self, meshes, system_matrix, voxel_size) -> np.ndarray
```

**边界条件理论基础：**
- 基于格林函数的边界积分方程
- 多层介质中的电位分布计算
- 边界条件的连续性约束
- 数值积分的高精度实现

**伪代码实现：**
```
计算BEM边界条件矩阵(网格数据, 导电率, 体素尺寸):
    计算几何矩阵:
        收集所有边界顶点
        对每对顶点计算格林函数:
            G(r,r') = 1/(4π|r-r'|)
        处理奇异性(r=r')
        构建几何关系矩阵
    
    计算导电率矩阵:
        为每个顶点分配组织导电率
        构建对角导电率矩阵
        考虑界面边界条件
    
    计算系统矩阵:
        系统矩阵 = 几何矩阵 × 导电率矩阵
        检查条件数稳定性
        应用正则化(如需要)
    
    计算导联场矩阵:
        定义源空间网格
        对每个源位置:
            计算到边界的贡献
            求解边界积分方程
            生成导联场向量
        组装完整导联场矩阵
    
    返回BEM矩阵集合
```

### 5. GeometryValidator (几何验证器)

```python
class GeometryValidator:
    def validate_geometry(self, meshes) -> bool
    def _validate_single_mesh(self, mesh, tissue_name) -> bool
    def _calculate_mesh_quality(self, vertices, faces) -> float
    def _validate_topology(self, meshes) -> bool
    def repair_geometry(self, meshes) -> Dict[str, Dict]
```

**几何验证标准：**
- 网格封闭性检查
- 拓扑一致性验证
- 网格质量评估
- 层次包含关系验证

**伪代码实现：**
```
验证几何结构(网格集合):
    对每个网格执行单独验证:
        检查基本结构完整性:
            顶点数 >= 4
            面数 >= 4
            面索引有效性
        验证网格封闭性:
            使用Trimesh检查封闭性
            计算欧拉特征数
        计算网格质量评分:
            基于三角形形状质量
            评估边长比例
            计算面积分布
    
    验证拓扑关系:
        检查层次包含关系:
            外层组织包含内层组织
            边界框包含检查
            体积一致性验证
    
    如果验证失败:
        执行几何修复:
            去除重复面和顶点
            填充网格洞
            平滑边界
            重新计算法向量
    
    返回验证结果
```

## BEM模型类型

### 1. 三层BEM模型
```python
三层模型组织 = {
    'scalp': '头皮层',
    'skull': '颅骨层', 
    'brain': '脑组织层(灰质+白质)'
}
```

**特点：**
- 计算效率高
- 适用于标准EEG分析
- 对颅骨导电率敏感

### 2. 五层BEM模型
```python
五层模型组织 = {
    'scalp': '头皮层',
    'skull': '颅骨层',
    'csf': '脑脊液层',
    'gray': '灰质层',
    'white': '白质层'
}
```

**特点：**
- 更高的建模精度
- 更好的源定位准确性
- 计算复杂度较高

## 数值精度控制

### 1. 奇异性处理
```python
奇异性处理策略 = {
    '距离阈值': 1e-6,  # 数值精度阈值
    '正则化方法': '对角加载',
    '积分方法': '自适应高斯积分'
}
```

### 2. 条件数控制
- **监控系统矩阵条件数**
- **应用Tikhonov正则化**
- **使用迭代求解器**
- **预条件技术**

### 3. 收敛性保证
- **多尺度网格细化**
- **自适应积分精度**
- **误差估计和控制**
- **数值稳定性分析**

## 性能优化策略

### 1. 计算优化
- **并行矩阵计算**：利用多核CPU并行计算几何矩阵
- **稀疏矩阵技术**：利用矩阵稀疏性减少存储和计算
- **快速多极方法**：加速远场相互作用计算
- **预计算缓存**：缓存重复使用的计算结果

### 2. 内存优化
- **分块矩阵计算**：避免大矩阵的内存占用
- **内存映射文件**：处理超大规模问题
- **数据类型优化**：使用适当精度的数据类型
- **垃圾回收管理**：及时释放不需要的内存

### 3. 数值优化
- **自适应网格细化**：在关键区域增加网格密度
- **多层快速算法**：分层处理不同尺度的相互作用
- **预条件共轭梯度**：加速线性系统求解
- **误差控制算法**：动态调整计算精度

## 配置参数详解

### BEM建模配置
```yaml
bem_modeling:
  model_type: "5_layer"              # 模型类型: 3_layer, 5_layer
  
  conductivity:                      # 导电率设置 (S/m)
    scalp: 0.33
    skull: 0.0042
    csf: 1.79
    gray: 0.33
    white: 0.14
  
  mesh:                              # 网格参数
    scalp_density: 5120              # 头皮网格面数
    skull_density: 5120              # 颅骨网格面数
    brain_density: 5120              # 脑组织网格面数
  
  numerical_accuracy: 1e-6           # 数值精度阈值
```

## 质量评估指标

### 1. 几何质量
```python
几何质量指标 = {
    '网格封闭性': '是否形成封闭表面',
    '三角形质量': '形状规整度评分',
    '拓扑一致性': '层次包含关系',
    '边界光滑度': '表面连续性'
}
```

### 2. 数值质量
```python
数值质量指标 = {
    '条件数': '系统矩阵条件数',
    '收敛性': '迭代求解收敛性',
    '精度': '数值积分精度',
    '稳定性': '扰动敏感性'
}
```

### 3. 物理质量
```python
物理质量指标 = {
    '导电率合理性': '与文献值对比',
    '边界条件': '连续性约束满足',
    '能量守恒': '总电流守恒',
    '对称性': '几何对称性保持'
}
```

## 验证和测试

### 1. 解析解验证
- **球形头模型**：与解析解精确对比
- **多层球模型**：验证多层边界条件
- **偶极子源**：测试点源响应
- **分布源**：验证扩展源处理

### 2. 数值收敛性测试
- **网格细化研究**：验证网格无关性
- **积分精度测试**：确保数值积分收敛
- **条件数分析**：评估数值稳定性
- **误差传播分析**：量化累积误差

### 3. 实际数据验证
- **仿真EEG数据**：已知源位置的验证
- **体模实验数据**：物理体模对比
- **临床数据验证**：与其他方法对比
- **跨被试一致性**：不同个体的稳定性
