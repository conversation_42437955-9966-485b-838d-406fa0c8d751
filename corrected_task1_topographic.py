#!/usr/bin/env python3
"""
Corrected Task 1: Topographic Feature Classification Training
基于正确数据分布的拓扑特征分类：
- 癫痫患者: 179个 (Guinea-Bissau: 51 + Nigeria: 128)
- 对照组: 143个 (Guinea-Bissau: 46 + Nigeria: 97)
- 使用平衡准确率评估
- 先分割再平衡
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score, roc_curve, balanced_accuracy_score
from sklearn.model_selection import train_test_split, StratifiedShuffleSplit
from imblearn.over_sampling import BorderlineSMOTE
import gzip
import os
from pathlib import Path
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

class CorrectedEEGDataset(torch.utils.data.Dataset):
    """修正的EEG数据集"""
    
    def __init__(self, data_dirs, metadata_files, split='train', test_size=0.25, random_state=42):
        self.data_dirs = [Path(d) for d in data_dirs]
        self.split = split
        
        # 加载正确的数据分布
        self.load_corrected_data(metadata_files, test_size, random_state)
        
    def load_corrected_data(self, metadata_files, test_size, random_state):
        """加载修正的数据分布"""
        print("📊 加载修正的数据分布...")
        
        all_samples = []
        
        # Guinea-Bissau数据集
        gb_metadata = pd.read_csv(metadata_files[0])
        gb_epilepsy = 0
        gb_control = 0
        
        for idx, row in gb_metadata.iterrows():
            subject_id = row['subject.id']
            group = row['Group']
            eeg_file = self.data_dirs[0] / f"signal-{subject_id}.csv.gz"
            
            if eeg_file.exists():
                label = 1 if group == 'Epilepsy' else 0
                all_samples.append({
                    'file_path': str(eeg_file),
                    'label': label,
                    'group': group,
                    'dataset': 'Guinea-Bissau',
                    'subject_id': subject_id
                })
                
                if label == 1:
                    gb_epilepsy += 1
                else:
                    gb_control += 1
        
        # Nigeria数据集 - 修正标签映射
        ng_metadata = pd.read_csv(metadata_files[1])
        ng_epilepsy = 0
        ng_control = 0
        
        for idx, row in ng_metadata.iterrows():
            subject_id = row['subject.id']
            group = row['Group']
            
            if 'csv.file' in row and pd.notna(row['csv.file']):
                eeg_file = self.data_dirs[1] / row['csv.file']
            else:
                eeg_file = self.data_dirs[1] / f"signal-{subject_id}-1.csv.gz"
            
            if eeg_file.exists():
                # 修正标签映射 - Nigeria数据集中'epilepsy'是小写
                label = 1 if group.lower() == 'epilepsy' else 0
                all_samples.append({
                    'file_path': str(eeg_file),
                    'label': label,
                    'group': 'Epilepsy' if label == 1 else 'Control',
                    'dataset': 'Nigeria',
                    'subject_id': subject_id
                })
                
                if label == 1:
                    ng_epilepsy += 1
                else:
                    ng_control += 1
        
        # 统计信息
        total_epilepsy = gb_epilepsy + ng_epilepsy
        total_control = gb_control + ng_control
        
        print(f"✅ 修正的数据分布:")
        print(f"  Guinea-Bissau: {len([s for s in all_samples if s['dataset'] == 'Guinea-Bissau'])} 样本 (癫痫: {gb_epilepsy}, 对照: {gb_control})")
        print(f"  Nigeria: {len([s for s in all_samples if s['dataset'] == 'Nigeria'])} 样本 (癫痫: {ng_epilepsy}, 对照: {ng_control})")
        print(f"  总计: {len(all_samples)} 样本 (癫痫: {total_epilepsy}, 对照: {total_control})")
        
        # 分层分割
        labels = [s['label'] for s in all_samples]
        datasets = [s['dataset'] for s in all_samples]
        
        # 创建分层标签
        stratify_labels = [f"{label}_{dataset}" for label, dataset in zip(labels, datasets)]
        
        try:
            train_idx, test_idx = train_test_split(
                range(len(all_samples)),
                test_size=test_size,
                random_state=random_state,
                stratify=stratify_labels
            )
        except ValueError:
            # 如果分层失败，使用简单随机分割
            train_idx, test_idx = train_test_split(
                range(len(all_samples)),
                test_size=test_size,
                random_state=random_state,
                stratify=labels
            )
        
        if self.split == 'train':
            self.samples = [all_samples[i] for i in train_idx]
            # 训练集过采样
            self.apply_oversampling()
        else:
            self.samples = [all_samples[i] for i in test_idx]
            # 测试集平衡
            self.balance_test_set()
        
        # 最终统计
        final_labels = [s['label'] for s in self.samples]
        final_epilepsy = sum(final_labels)
        final_control = len(final_labels) - final_epilepsy
        
        print(f"\n{self.split.upper()}集最终分布:")
        print(f"  样本数: {len(self.samples)}")
        print(f"  癫痫: {final_epilepsy}, 对照: {final_control}")
        print(f"  平衡比例: {final_epilepsy/len(final_labels):.3f} : {final_control/len(final_labels):.3f}")
    
    def balance_test_set(self):
        """平衡测试集"""
        epilepsy_samples = [s for s in self.samples if s['label'] == 1]
        control_samples = [s for s in self.samples if s['label'] == 0]
        
        min_count = min(len(epilepsy_samples), len(control_samples))
        
        if min_count > 0:
            np.random.seed(42)
            selected_epilepsy = np.random.choice(len(epilepsy_samples), min_count, replace=False)
            selected_control = np.random.choice(len(control_samples), min_count, replace=False)
            
            balanced_samples = []
            balanced_samples.extend([epilepsy_samples[i] for i in selected_epilepsy])
            balanced_samples.extend([control_samples[i] for i in selected_control])
            
            self.samples = balanced_samples
            print(f"  ✅ 测试集已平衡：每类 {min_count} 个样本")
    
    def apply_oversampling(self):
        """对训练集应用过采样"""
        print("🔄 对训练集应用过采样...")
        
        train_labels = [s['label'] for s in self.samples]
        epilepsy_count = sum(train_labels)
        control_count = len(train_labels) - epilepsy_count
        
        print(f"  过采样前: 癫痫 {epilepsy_count}, 对照 {control_count}")
        
        if epilepsy_count == 0 or control_count == 0:
            print("⚠️  训练集中缺少某一类别，跳过过采样")
            return
        
        # 提取特征用于SMOTE
        features_for_smote = []
        
        for sample in tqdm(self.samples, desc="提取特征"):
            eeg_data = self.load_eeg_signal(sample['file_path'])
            if eeg_data is not None:
                # 提取统计特征
                features = np.array([
                    np.mean(eeg_data, axis=1),      # 均值
                    np.std(eeg_data, axis=1),       # 标准差
                    np.max(eeg_data, axis=1),       # 最大值
                    np.min(eeg_data, axis=1),       # 最小值
                    np.median(eeg_data, axis=1),    # 中位数
                ]).flatten()  # 70维特征
            else:
                features = np.zeros(70)
            
            features_for_smote.append(features)
        
        features_array = np.array(features_for_smote)
        labels_array = np.array(train_labels)
        
        # 应用SMOTE
        try:
            k_neighbors = min(5, min(epilepsy_count, control_count) - 1)
            smote = BorderlineSMOTE(random_state=42, k_neighbors=k_neighbors)
            features_resampled, labels_resampled = smote.fit_resample(features_array, labels_array)
            
            # 创建新样本
            original_count = len(self.samples)
            new_samples_count = len(labels_resampled) - original_count
            
            if new_samples_count > 0:
                # 找到少数类样本作为模板
                minority_class = 1 if epilepsy_count < control_count else 0
                minority_samples = [s for s in self.samples if s['label'] == minority_class]
                
                np.random.seed(42)
                for i in range(new_samples_count):
                    template = np.random.choice(minority_samples)
                    synthetic_sample = {
                        'file_path': template['file_path'],
                        'label': minority_class,
                        'group': 'Epilepsy' if minority_class == 1 else 'Control',
                        'dataset': template['dataset'],
                        'subject_id': f"synthetic_{i}",
                        'is_synthetic': True
                    }
                    self.samples.append(synthetic_sample)
            
            final_labels = [s['label'] for s in self.samples]
            final_epilepsy = sum(final_labels)
            final_control = len(final_labels) - final_epilepsy
            
            print(f"  过采样后: 癫痫 {final_epilepsy}, 对照 {final_control}")
            
        except Exception as e:
            print(f"⚠️  过采样失败: {e}")
    
    def load_eeg_signal(self, file_path):
        """加载EEG信号"""
        try:
            with gzip.open(file_path, 'rt') as f:
                df = pd.read_csv(f)
            
            eeg_channels = ['AF3', 'AF4', 'F3', 'F4', 'F7', 'F8', 'FC5', 'FC6', 
                           'O1', 'O2', 'P7', 'P8', 'T7', 'T8']
            
            available_channels = [ch for ch in eeg_channels if ch in df.columns]
            if len(available_channels) < 10:
                return None
            
            eeg_data = df[available_channels].values.T
            
            if len(available_channels) < 14:
                padding = np.zeros((14 - len(available_channels), eeg_data.shape[1]))
                eeg_data = np.vstack([eeg_data, padding])
            
            # 标准化
            eeg_data = (eeg_data - np.mean(eeg_data, axis=1, keepdims=True)) / (np.std(eeg_data, axis=1, keepdims=True) + 1e-8)
            
            # 固定长度
            target_length = 1536
            if eeg_data.shape[1] > target_length:
                start_idx = np.random.randint(0, eeg_data.shape[1] - target_length + 1)
                eeg_data = eeg_data[:, start_idx:start_idx + target_length]
            elif eeg_data.shape[1] < target_length:
                pad_width = target_length - eeg_data.shape[1]
                eeg_data = np.pad(eeg_data, ((0, 0), (0, pad_width)), mode='reflect')
            
            return eeg_data.astype(np.float32)
            
        except Exception as e:
            return None
    
    def create_enhanced_topographic_map(self, eeg_data):
        """创建增强拓扑图"""
        from scipy import signal
        
        electrode_positions = {
            'AF3': (20, 25), 'AF4': (20, 39),
            'F3': (25, 20), 'F4': (25, 44), 'F7': (25, 10), 'F8': (25, 54),
            'FC5': (30, 15), 'FC6': (30, 49),
            'T7': (35, 5), 'T8': (35, 59),
            'P7': (45, 10), 'P8': (45, 54),
            'O1': (55, 25), 'O2': (55, 39)
        }
        
        channels = ['AF3', 'AF4', 'F3', 'F4', 'F7', 'F8', 'FC5', 'FC6', 
                   'O1', 'O2', 'P7', 'P8', 'T7', 'T8']
        
        fs = 256
        freq_bands = {
            'delta': (1, 4),
            'theta': (4, 8),
            'alpha': (8, 13),
            'beta': (13, 30)
        }
        
        topo_maps = []
        
        for band_name, (low_freq, high_freq) in freq_bands.items():
            try:
                sos = signal.butter(4, [low_freq, high_freq], btype='band', fs=fs, output='sos')
                filtered_data = signal.sosfilt(sos, eeg_data, axis=1)
                power_values = np.mean(filtered_data**2, axis=1)
            except:
                power_values = np.mean(eeg_data**2, axis=1)
            
            topo_map = np.zeros((64, 64))
            
            for i, channel in enumerate(channels):
                if channel in electrode_positions and i < len(power_values):
                    y, x = electrode_positions[channel]
                    for dy in range(-4, 5):
                        for dx in range(-4, 5):
                            ny, nx = y + dy, x + dx
                            if 0 <= ny < 64 and 0 <= nx < 64:
                                weight = np.exp(-(dy**2 + dx**2) / 8.0)
                                topo_map[ny, nx] += power_values[i] * weight
            
            topo_maps.append(topo_map)
        
        return np.stack(topo_maps)
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        sample = self.samples[idx]
        eeg_data = self.load_eeg_signal(sample['file_path'])
        
        if eeg_data is None:
            eeg_data = np.zeros((14, 1536), dtype=np.float32)
        
        # 对合成样本添加噪声
        if sample.get('is_synthetic', False):
            noise = np.random.normal(0, 0.03, eeg_data.shape)
            eeg_data = eeg_data + noise
        
        topo_maps = self.create_enhanced_topographic_map(eeg_data)
        
        return {
            'topographic_maps': torch.FloatTensor(topo_maps),
            'label': torch.LongTensor([sample['label']])[0],
            'dataset': sample['dataset'],
            'is_synthetic': sample.get('is_synthetic', False),
            'file_path': sample['file_path']
        }

class EnhancedTopographicCNN(nn.Module):
    """增强拓扑CNN"""
    
    def __init__(self, num_classes=2):
        super().__init__()
        
        # 多频段特征提取
        self.freq_conv = nn.Sequential(
            nn.Conv2d(4, 32, kernel_size=7, stride=2, padding=3),
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=3, stride=2, padding=1),
        )
        
        # 残差块
        self.layer1 = self._make_layer(32, 64, 2)
        self.layer2 = self._make_layer(64, 128, 2, stride=2)
        self.layer3 = self._make_layer(128, 256, 2, stride=2)
        
        # 全局池化
        self.global_pool = nn.AdaptiveAvgPool2d((1, 1))
        
        # 分类头
        self.classifier = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(256, 128),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(128, num_classes)
        )
        
    def _make_layer(self, in_channels, out_channels, blocks, stride=1):
        layers = []
        layers.append(self._make_block(in_channels, out_channels, stride))
        for _ in range(1, blocks):
            layers.append(self._make_block(out_channels, out_channels))
        return nn.Sequential(*layers)
    
    def _make_block(self, in_channels, out_channels, stride=1):
        return nn.Sequential(
            nn.Conv2d(in_channels, out_channels, kernel_size=3, stride=stride, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )
    
    def forward(self, x):
        x = self.freq_conv(x)
        x = self.layer1(x)
        x = self.layer2(x)
        x = self.layer3(x)
        x = self.global_pool(x)
        x = x.view(x.size(0), -1)
        logits = self.classifier(x)
        return logits

class CorrectedTrainer:
    """修正的训练器"""
    
    def __init__(self, model, device):
        self.model = model.to(device)
        self.device = device
        
        self.criterion = nn.CrossEntropyLoss()
        self.optimizer = optim.AdamW(self.model.parameters(), lr=1e-4, weight_decay=1e-5)
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='max', factor=0.5, patience=7, verbose=True
        )
        
        self.history = {
            'train_loss': [], 'val_loss': [],
            'train_acc': [], 'val_acc': [],
            'train_balanced_acc': [], 'val_balanced_acc': [],
            'val_auc': []
        }
    
    def train_epoch(self, train_loader):
        self.model.train()
        
        total_loss = 0.0
        all_predictions = []
        all_labels = []
        
        for batch in tqdm(train_loader, desc="Training"):
            topo_maps = batch['topographic_maps'].to(self.device)
            labels = batch['label'].to(self.device)
            
            self.optimizer.zero_grad()
            
            logits = self.model(topo_maps)
            loss = self.criterion(logits, labels)
            
            loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            self.optimizer.step()
            
            total_loss += loss.item()
            
            _, predictions = torch.max(logits, 1)
            all_predictions.extend(predictions.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
        
        avg_loss = total_loss / len(train_loader)
        accuracy = sum([1 for l, p in zip(all_labels, all_predictions) if l == p]) / len(all_labels)
        balanced_acc = balanced_accuracy_score(all_labels, all_predictions)
        
        return avg_loss, accuracy * 100, balanced_acc * 100
    
    def validate_epoch(self, val_loader):
        self.model.eval()
        
        total_loss = 0.0
        all_predictions = []
        all_labels = []
        all_probabilities = []
        
        with torch.no_grad():
            for batch in val_loader:
                topo_maps = batch['topographic_maps'].to(self.device)
                labels = batch['label'].to(self.device)
                
                logits = self.model(topo_maps)
                loss = self.criterion(logits, labels)
                
                total_loss += loss.item()
                
                probabilities = torch.softmax(logits, dim=1)
                _, predictions = torch.max(logits, 1)
                
                all_predictions.extend(predictions.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
                all_probabilities.extend(probabilities[:, 1].cpu().numpy())
        
        avg_loss = total_loss / len(val_loader)
        accuracy = sum([1 for l, p in zip(all_labels, all_predictions) if l == p]) / len(all_labels)
        balanced_acc = balanced_accuracy_score(all_labels, all_predictions)
        auc_score = roc_auc_score(all_labels, all_probabilities) if len(set(all_labels)) > 1 else 0.5
        
        return avg_loss, accuracy * 100, balanced_acc * 100, auc_score, all_predictions, all_labels, all_probabilities
    
    def train(self, train_loader, val_loader, num_epochs=30):
        print(f"🚀 开始修正的拓扑特征分类训练，共 {num_epochs} 个epoch...")
        
        best_balanced_acc = 0.0
        
        for epoch in range(num_epochs):
            print(f"\nEpoch {epoch+1}/{num_epochs}")
            
            train_loss, train_acc, train_balanced_acc = self.train_epoch(train_loader)
            val_loss, val_acc, val_balanced_acc, val_auc, val_preds, val_labels, val_probs = self.validate_epoch(val_loader)
            
            self.scheduler.step(val_balanced_acc)
            
            self.history['train_loss'].append(train_loss)
            self.history['val_loss'].append(val_loss)
            self.history['train_acc'].append(train_acc)
            self.history['val_acc'].append(val_acc)
            self.history['train_balanced_acc'].append(train_balanced_acc)
            self.history['val_balanced_acc'].append(val_balanced_acc)
            self.history['val_auc'].append(val_auc)
            
            print(f"训练 - 损失: {train_loss:.4f}, 准确率: {train_acc:.1f}%, 平衡准确率: {train_balanced_acc:.1f}%")
            print(f"验证 - 损失: {val_loss:.4f}, 准确率: {val_acc:.1f}%, 平衡准确率: {val_balanced_acc:.1f}%, AUC: {val_auc:.3f}")
            
            if val_balanced_acc > best_balanced_acc:
                best_balanced_acc = val_balanced_acc
                torch.save({
                    'model_state_dict': self.model.state_dict(),
                    'val_balanced_acc': best_balanced_acc,
                    'history': self.history
                }, 'corrected_task1_best_model.pth')
                print(f"✓ 保存最佳模型 (平衡准确率: {best_balanced_acc:.1f}%)")
        
        print(f"\n🎉 训练完成！最佳平衡准确率: {best_balanced_acc:.1f}%")
        return self.history, val_preds, val_labels, val_probs

def create_task1_report(labels, predictions, probabilities, history):
    """创建Task 1报告"""
    
    print("\n" + "="*80)
    print("CORRECTED TASK 1: TOPOGRAPHIC FEATURE CLASSIFICATION REPORT")
    print("="*80)
    
    accuracy = sum([1 for l, p in zip(labels, predictions) if l == p]) / len(labels)
    balanced_acc = balanced_accuracy_score(labels, predictions)
    auc_score = roc_auc_score(labels, probabilities)
    
    print(f"\n📊 最终性能指标:")
    print(f"  测试样本数: {len(labels)}")
    print(f"  类别分布: 癫痫={sum(labels)}, 对照={len(labels)-sum(labels)}")
    print(f"  标准准确率: {accuracy:.3f} ({accuracy*100:.1f}%)")
    print(f"  平衡准确率: {balanced_acc:.3f} ({balanced_acc*100:.1f}%)")
    print(f"  AUC分数: {auc_score:.3f}")
    
    print(f"\n📈 详细分类报告:")
    print(classification_report(labels, predictions, target_names=['Control', 'Epilepsy']))
    
    cm = confusion_matrix(labels, predictions)
    sensitivity = cm[1,1] / (cm[1,1] + cm[1,0]) if (cm[1,1] + cm[1,0]) > 0 else 0
    specificity = cm[0,0] / (cm[0,0] + cm[0,1]) if (cm[0,0] + cm[0,1]) > 0 else 0
    
    print(f"\n🎯 临床指标:")
    print(f"  敏感性: {sensitivity:.3f} ({sensitivity*100:.1f}%)")
    print(f"  特异性: {specificity:.3f} ({specificity*100:.1f}%)")
    
    return balanced_acc, auc_score

def main():
    print("🎯 Corrected Task 1: Topographic Feature Classification")
    print("="*70)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    data_dirs = [
        "1252141/EEGs_Guinea-Bissau",
        "1252141/EEGs_Nigeria"
    ]
    metadata_files = [
        "1252141/metadata_guineabissau.csv",
        "1252141/metadata_nigeria.csv"
    ]
    
    try:
        train_dataset = CorrectedEEGDataset(data_dirs, metadata_files, split='train')
        test_dataset = CorrectedEEGDataset(data_dirs, metadata_files, split='test')
        
        train_loader = torch.utils.data.DataLoader(
            train_dataset, batch_size=8, shuffle=True, num_workers=0
        )
        test_loader = torch.utils.data.DataLoader(
            test_dataset, batch_size=8, shuffle=False, num_workers=0
        )
        
        print("✅ 修正的数据集创建成功")
        
    except Exception as e:
        print(f"❌ 数据集创建失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    model = EnhancedTopographicCNN(num_classes=2)
    param_count = sum(p.numel() for p in model.parameters())
    print(f"模型参数数量: {param_count:,}")
    
    trainer = CorrectedTrainer(model, device)
    history, val_preds, val_labels, val_probs = trainer.train(train_loader, test_loader, num_epochs=25)
    
    balanced_acc, auc_score = create_task1_report(val_labels, val_preds, val_probs, history)
    
    print(f"\n🎉 Corrected Task 1 完成！")
    print(f"📊 最终结果:")
    print(f"  - 平衡准确率: {balanced_acc*100:.1f}%")
    print(f"  - AUC分数: {auc_score:.3f}")
    print(f"📁 模型文件: corrected_task1_best_model.pth")

if __name__ == "__main__":
    main()
