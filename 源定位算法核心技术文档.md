# 源定位算法核心技术文档

## 算法概述

源定位算法核心 (`algorithms/inverse_solvers.py`) 是EEG源定位系统的核心计算模块，实现了完整的逆向求解算法链。该模块支持多种经典和现代源定位算法，包括LORETA、sLORETA、eLORETA、MNE、dSPM等，为准确的神经源定位提供强大的算法支持。

## 核心算法架构

### 1. SourceLocalizer (源定位器主类)

```python
class SourceLocalizer:
    def localize_sources(self, eeg_data, leadfield_matrix, noise_cov, method) -> Dict
    def _validate_inputs(self, eeg_data, leadfield_matrix, noise_cov)
    def _preprocess_leadfield(self, leadfield_matrix) -> np.ndarray
    def _estimate_noise_covariance(self, eeg_data) -> np.ndarray
    def _calculate_quality_metrics(self, eeg_data, leadfield_matrix, source_estimates) -> Dict
```

**核心功能：**
- 统一的源定位接口
- 多算法支持和切换
- 自动质量评估
- 结果验证和优化

**伪代码实现：**
```
源定位分析(EEG数据, 导联场矩阵, 噪声协方差, 方法):
    输入验证:
        检查数据维度匹配
        验证矩阵有效性
        确保数值稳定性
    
    导联场预处理:
        奇异值分解
        移除小奇异值
        重构稳定矩阵
    
    噪声协方差估计:
        如果未提供噪声协方差:
            使用数据尾部估计噪声
            计算经验协方差矩阵
            添加正则化确保正定性
    
    选择逆向求解器:
        根据方法参数选择算法
        初始化对应求解器
    
    正则化参数优化:
        如果启用自动优化:
            使用L-curve方法
            寻找最优正则化参数
        否则使用配置值
    
    执行逆向求解:
        调用选定算法求解
        获得源活动估计
    
    源活动重建:
        时间序列重建
        功率分布计算
        峰值源检测
        统计分析
    
    质量评估:
        拟合优度计算
        稀疏性评估
        时间一致性分析
        空间平滑性检查
    
    返回完整结果
```

### 2. 逆向求解算法实现

#### 2.1 LORETA (Low Resolution Electromagnetic Tomography)

```python
class LOREtaSolver(BaseSolver):
    def solve(self, eeg_data, leadfield_matrix, noise_cov, regularization) -> np.ndarray
    def _build_laplacian_operator(self, num_sources) -> np.ndarray
```

**算法原理：**
- 最小化源活动的拉普拉斯算子
- 假设相邻源具有相似的活动模式
- 产生平滑的源分布

**数学表达：**
```
LORETA解: J = (L^T L + λ∇²)^(-1) L^T M
其中：
- J: 源活动向量
- L: 导联场矩阵
- λ: 正则化参数
- ∇²: 拉普拉斯算子
- M: EEG测量数据
```

**伪代码实现：**
```
LORETA求解(EEG数据, 导联场矩阵, 噪声协方差, 正则化参数):
    应用白化变换:
        计算白化矩阵 W = C^(-1/2)
        白化EEG数据: M_w = W * M
        白化导联场: L_w = W * L
    
    构建拉普拉斯算子:
        创建二阶差分矩阵
        定义相邻源的平滑约束
    
    计算LORETA逆算子:
        A = L_w^T * L_w + λ * ∇²
        逆算子 = A^(-1) * L_w^T
    
    求解源活动:
        J = 逆算子 * M_w
    
    返回源估计
```

#### 2.2 sLORETA (Standardized LORETA)

```python
class sLOREtaSolver(BaseSolver):
    def solve(self, eeg_data, leadfield_matrix, noise_cov, regularization) -> np.ndarray
```

**算法特点：**
- 在最小范数解基础上进行标准化
- 消除深度偏差
- 提供统计显著性检验

**数学表达：**
```
sLORETA解: J_std = J / sqrt(diag(T))
其中：
- T = (L^T L + λI)^(-1) L^T L (L^T L + λI)^(-1)
- 标准化因子消除位置偏差
```

#### 2.3 eLORETA (Exact LORETA)

```python
class eLOREtaSolver(BaseSolver):
    def solve(self, eeg_data, leadfield_matrix, noise_cov, regularization) -> np.ndarray
    def _compute_eloreta_weights(self, leadfield_matrix, regularization) -> np.ndarray
```

**算法优势：**
- 零定位误差（在理想条件下）
- 精确的统计推断
- 最优的空间分辨率

**数学表达：**
```
eLORETA解: J = W * (L^T W L + λI)^(-1) L^T W M
其中：
- W: eLORETA权重矩阵
- W = diag(sqrt(diag((L^T L + λI)^(-1))))
```

#### 2.4 MNE (Minimum Norm Estimation)

```python
class MNESolver(BaseSolver):
    def solve(self, eeg_data, leadfield_matrix, noise_cov, regularization) -> np.ndarray
```

**算法特点：**
- 最小化源活动的L2范数
- 计算简单高效
- 偏向表面源

**数学表达：**
```
MNE解: J = (L^T L + λI)^(-1) L^T M
- 最小范数约束
- Tikhonov正则化
```

#### 2.5 dSPM (Dynamic Statistical Parametric Mapping)

```python
class dSPMSolver(BaseSolver):
    def solve(self, eeg_data, leadfield_matrix, noise_cov, regularization) -> np.ndarray
```

**算法特点：**
- 基于MNE的统计参数映射
- 提供统计显著性
- 噪声标准化

**数学表达：**
```
dSPM解: J_dSPM = J_MNE / sqrt(diag(R))
其中：
- R = (L^T L + λI)^(-1) L^T L (L^T L + λI)^(-1)
- 噪声标准化因子
```

### 3. RegularizationManager (正则化参数管理器)

```python
class RegularizationManager:
    def optimize_regularization(self, eeg_data, leadfield_matrix, noise_cov, method) -> float
    def _auto_optimize_lambda(self, eeg_data, leadfield_matrix, noise_cov, method) -> float
    def _find_lcurve_corner(self, residual_norms, solution_norms) -> int
```

**L-curve方法原理：**
- 在对数尺度上绘制解范数vs残差范数
- 寻找L形曲线的拐点
- 平衡拟合优度和解的平滑性

**伪代码实现：**
```
L-curve正则化参数优化(EEG数据, 导联场矩阵, 噪声协方差):
    定义候选正则化参数:
        λ_candidates = logspace(-8, -1, 20)
    
    对每个候选参数:
        计算源估计 J(λ)
        计算残差范数: ||M - L*J(λ)||
        计算解范数: ||J(λ)||
    
    构建L-curve:
        x轴: log(残差范数)
        y轴: log(解范数)
    
    寻找拐点:
        计算曲线曲率
        找到最大曲率点
        对应的λ为最优值
    
    返回最优正则化参数
```

### 4. SourceReconstructor (源活动重建器)

```python
class SourceReconstructor:
    def reconstruct_sources(self, source_estimates, leadfield_matrix) -> Dict
    def _reconstruct_time_series(self, source_estimates) -> Dict
    def _reconstruct_power(self, source_estimates) -> Dict
    def _detect_peak_sources(self, source_estimates) -> Dict
    def _time_frequency_analysis(self, source_estimates) -> Dict
```

**重建功能：**

#### 4.1 时间序列重建
```python
时间序列类型 = {
    'RMS时间序列': 'sqrt(mean(J²))',
    'PCA成分': '主成分分析降维',
    '峰值时间序列': 'max(|J|)',
    '统计时间序列': '均值、方差等'
}
```

#### 4.2 功率分布重建
```python
功率指标 = {
    '总功率': 'sum(J²)',
    '平均功率': 'mean(J²)',
    '峰值功率': 'max(J²)',
    '功率统计': '分布特征'
}
```

#### 4.3 峰值源检测
```python
峰值检测策略 = {
    '阈值设置': '95百分位数',
    '排序方法': '按活动强度降序',
    '时间定位': '峰值时间点',
    '空间聚类': '相邻峰值合并'
}
```

#### 4.4 时频分析
```python
时频分析方法 = {
    'STFT': '短时傅里叶变换',
    '小波变换': '时频局部化',
    '希尔伯特变换': '瞬时频率',
    '多分辨率分析': '不同时间尺度'
}
```

## 质量评估体系

### 1. 拟合优度 (Goodness of Fit)
```python
GOF = 1 - ||M - L*J||² / ||M||²
```
- 衡量重建EEG与原始EEG的匹配程度
- 值越接近1表示拟合越好

### 2. 源活动稀疏性
```python
稀疏性 = 1 - (||J||₁ / (√N * ||J||₂))
```
- 评估源分布的集中程度
- 生理上期望源活动相对稀疏

### 3. 时间一致性
```python
时间一致性 = mean(|corr(J(t), J(t+1))|)
```
- 评估相邻时间点源活动的相关性
- 反映源活动的时间连续性

### 4. 空间平滑性
```python
空间平滑性 = 1 / (1 + mean(|∇J|))
```
- 评估源分布的空间连续性
- 避免不合理的空间跳跃

## 算法性能优化

### 1. 数值稳定性
- **奇异值分解**：处理病态矩阵
- **正则化技术**：避免过拟合
- **条件数控制**：确保数值精度
- **白化变换**：去相关化处理

### 2. 计算效率
- **矩阵预分解**：SVD、Cholesky分解
- **稀疏矩阵技术**：利用矩阵稀疏性
- **并行计算**：多核CPU并行
- **内存优化**：减少内存拷贝

### 3. 算法鲁棒性
- **异常值检测**：识别和处理异常数据
- **参数自适应**：根据数据特征调整参数
- **多算法融合**：结合多种方法的优势
- **交叉验证**：验证结果可靠性

## 配置参数详解

### 源定位配置
```yaml
source_localization:
  # 源空间配置
  source_space:
    spacing: 5                          # mm，源点间距
    surface_type: "white"               # 表面类型
    
  # 逆向求解方法
  inverse_methods:
    - "loreta"                          # LORETA
    - "sloreta"                         # sLORETA  
    - "eloreta"                         # eLORETA
    - "mne"                             # 最小范数估计
    - "dspm"                            # 动态统计参数映射
    
  # 正则化参数
  regularization:
    lambda_auto: true                   # 自动优化
    lambda_value: 0.1                   # 固定值
    
  # 噪声协方差
  noise_cov:
    method: "empirical"                 # 估计方法
    regularization: 0.1                 # 正则化
```

## 算法验证和测试

### 1. 仿真验证
- **点源仿真**：已知位置的点源
- **分布源仿真**：扩展源区域
- **噪声鲁棒性**：不同信噪比测试
- **深度测试**：不同深度源的定位精度

### 2. 解析解对比
- **球形头模型**：与解析解精确对比
- **多层球模型**：验证多层边界条件
- **偶极子源**：测试点源响应
- **误差量化**：定位误差统计分析

### 3. 临床验证
- **癫痫数据**：与临床定位结果对比
- **感觉诱发电位**：已知源位置验证
- **运动皮层定位**：功能定位验证
- **跨被试一致性**：不同个体的稳定性

### 4. 算法对比
- **精度对比**：不同算法的定位精度
- **计算效率**：运行时间和内存使用
- **参数敏感性**：对参数变化的敏感程度
- **适用场景**：不同算法的最佳应用场景

## 扩展接口设计

### 1. 自定义求解器
```python
def register_custom_solver(name: str, solver_class: type):
    """注册自定义逆向求解器"""
    pass

def add_regularization_method(name: str, method_func: callable):
    """添加自定义正则化方法"""
    pass
```

### 2. 质量评估扩展
```python
def add_quality_metric(metric_name: str, metric_func: callable):
    """添加自定义质量评估指标"""
    pass

def set_quality_weights(weights: Dict[str, float]):
    """设置质量指标权重"""
    pass
```

### 3. 后处理扩展
```python
def register_postprocessor(name: str, processor_class: type):
    """注册自定义后处理器"""
    pass
```
