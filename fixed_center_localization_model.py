#!/usr/bin/env python3
"""
修复版中心定位模型
解决梯度爆炸和损失过大问题
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import time
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# 导入基础模块
from fixed_training_system import NormalizedEEGLesionDataset
from torch.utils.data import DataLoader

class FixedCenterLocalizationModel(nn.Module):
    """修复版中心定位模型 - 解决梯度爆炸问题"""
    
    def __init__(self, n_channels=14, feature_dim=256):  # 减小特征维度
        super().__init__()
        
        # EEG特征提取 - 添加更多正则化
        self.eeg_features = nn.Sequential(
            nn.Conv1d(n_channels, 32, kernel_size=7, padding=3),  # 减小通道数
            nn.BatchNorm1d(32),
            nn.<PERSON><PERSON><PERSON>(),
            nn.Dropout(0.2),
            nn.MaxPool1d(2),
            
            nn.Conv1d(32, 64, kernel_size=5, padding=2),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.MaxPool1d(2),
            
            nn.Conv1d(64, 128, kernel_size=3, padding=1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.AdaptiveAvgPool1d(1),
            
            nn.Flatten(),
            nn.Linear(128, feature_dim),
            nn.BatchNorm1d(feature_dim),  # 添加BN
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        # 时序特征提取 - 减小LSTM尺寸
        self.temporal_lstm = nn.LSTM(n_channels, 64, batch_first=True, bidirectional=True)
        self.temporal_fc = nn.Sequential(
            nn.Linear(128, feature_dim),
            nn.BatchNorm1d(feature_dim),  # 添加BN
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        # 特征融合 - 简化结构
        self.fusion = nn.Sequential(
            nn.Linear(feature_dim * 2, feature_dim),
            nn.BatchNorm1d(feature_dim),  # 添加BN
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        # 中心位置预测器 - 简化并添加正则化
        self.center_predictor = nn.Sequential(
            nn.Linear(feature_dim, 64),
            nn.BatchNorm1d(64),  # 添加BN
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(64, 3),  # 直接输出 (x, y, z) 坐标
            nn.Sigmoid()  # 归一化到 [0, 1]
        )
        
        # 权重初始化 - 使用更小的初始化
        self.apply(self._init_weights)
    
    def _init_weights(self, m):
        """更保守的权重初始化"""
        if isinstance(m, nn.Linear):
            # 使用更小的初始化范围
            torch.nn.init.xavier_uniform_(m.weight, gain=0.5)
            if m.bias is not None:
                torch.nn.init.zeros_(m.bias)
        elif isinstance(m, nn.Conv1d):
            torch.nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            torch.nn.init.constant_(m.weight, 0.1)  # 更小的初始值
        elif isinstance(m, nn.LSTM):
            for name, param in m.named_parameters():
                if 'weight' in name:
                    torch.nn.init.xavier_uniform_(param, gain=0.5)
                elif 'bias' in name:
                    torch.nn.init.zeros_(param)
    
    def forward(self, eeg_data, temporal_sequence):
        """前向传播"""
        # EEG特征提取
        eeg_feat = self.eeg_features(eeg_data)
        
        # 时序特征提取
        temporal_out, _ = self.temporal_lstm(temporal_sequence)
        temporal_feat = self.temporal_fc(temporal_out[:, -1, :])
        
        # 特征融合
        fused_features = torch.cat([eeg_feat, temporal_feat], dim=1)
        fused_features = self.fusion(fused_features)
        
        # 预测中心位置 (归一化坐标 [0, 1])
        center_normalized = self.center_predictor(fused_features)
        
        # 转换到实际坐标 [0, 255]
        center_coords = center_normalized * 255.0
        
        return {
            'center': center_coords,
            'center_normalized': center_normalized,
            'features': fused_features
        }

class FixedCenterLocalizationLoss(nn.Module):
    """修复版损失函数 - 解决数值过大问题"""
    
    def __init__(self, huber_delta=20.0, l1_weight=0.3):
        super().__init__()
        self.huber_delta = huber_delta  # Huber损失的阈值
        self.l1_weight = l1_weight
        self.huber_loss = nn.HuberLoss(delta=huber_delta)  # 使用Huber损失替代MSE
        self.l1_loss = nn.L1Loss()
    
    def forward(self, pred_center, target_masks):
        """计算中心定位损失"""
        batch_size = pred_center.shape[0]
        device = pred_center.device
        
        # 计算真实中心位置
        true_centers = []
        for i in range(batch_size):
            mask = target_masks[i]
            if torch.sum(mask) > 0:
                # 找到病灶的质心
                coords = torch.nonzero(mask, as_tuple=False).float()
                center = torch.mean(coords, dim=0)  # (z, y, x)
                # 重新排列为 (x, y, z)
                center = center[[2, 1, 0]]
            else:
                # 如果没有病灶，使用体积中心
                center = torch.tensor([127.5, 127.5, 127.5], device=device)
            
            true_centers.append(center)
        
        true_centers = torch.stack(true_centers).to(device)
        
        # 计算损失 - 使用Huber损失替代MSE
        huber_loss = self.huber_loss(pred_center, true_centers)
        l1_loss = self.l1_loss(pred_center, true_centers)
        
        # 总损失 - 降低权重
        total_loss = 0.7 * huber_loss + self.l1_weight * l1_loss
        
        # 计算欧几里得距离误差
        distances = torch.norm(pred_center - true_centers, dim=1)
        mean_distance = torch.mean(distances)
        
        return {
            'total_loss': total_loss,
            'huber_loss': huber_loss,
            'l1_loss': l1_loss,
            'mean_distance': mean_distance,
            'distances': distances,
            'true_centers': true_centers
        }

class FixedCenterLocalizationTrainer:
    """修复版训练器 - 解决梯度爆炸问题"""
    
    def __init__(self, model, device, learning_rate=5e-5):  # 降低学习率
        self.model = model.to(device)
        self.device = device
        self.criterion = FixedCenterLocalizationLoss(huber_delta=15.0)
        
        # 优化器 - 降低学习率和权重衰减
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=learning_rate,
            weight_decay=1e-6,  # 降低权重衰减
            eps=1e-8
        )
        
        # 学习率调度器
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='min', factor=0.7, patience=3, verbose=True, min_lr=1e-7
        )
        
        # 训练历史
        self.history = {
            'train_loss': [], 'val_loss': [],
            'train_distance': [], 'val_distance': [],
            'learning_rate': []
        }
    
    def train_epoch(self, train_loader):
        """训练一个epoch"""
        self.model.train()
        
        total_loss = 0.0
        total_distance = 0.0
        num_batches = 0
        
        pbar = tqdm(train_loader, desc="Training")
        for batch_idx, batch in enumerate(pbar):
            try:
                # 数据移到设备
                eeg_data = batch['eeg_data'].to(self.device)
                temporal_sequence = batch['temporal_sequence'].to(self.device)
                lesion_masks = batch['lesion_mask'].to(self.device)
                
                # 前向传播
                self.optimizer.zero_grad()
                outputs = self.model(eeg_data, temporal_sequence)
                
                # 计算损失
                loss_results = self.criterion(outputs['center'], lesion_masks)
                loss = loss_results['total_loss']
                
                # 检查损失有效性
                if torch.isnan(loss) or torch.isinf(loss):
                    print(f"Invalid loss at batch {batch_idx}: {loss.item()}")
                    continue
                
                # 反向传播
                loss.backward()
                
                # 梯度裁剪 - 更严格的裁剪
                grad_norm = torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=0.5)
                
                # 检查梯度范数
                if grad_norm > 10:
                    print(f"Large gradient norm: {grad_norm:.2f}")
                    continue
                
                self.optimizer.step()
                
                # 记录指标
                total_loss += loss.item()
                total_distance += loss_results['mean_distance'].item()
                num_batches += 1
                
                # 更新进度条
                pbar.set_postfix({
                    'Loss': f"{loss.item():.3f}",
                    'Distance': f"{loss_results['mean_distance'].item():.1f}",
                    'GradNorm': f"{grad_norm:.2f}"
                })
                
            except Exception as e:
                print(f"Error in batch {batch_idx}: {e}")
                continue
        
        avg_loss = total_loss / max(num_batches, 1)
        avg_distance = total_distance / max(num_batches, 1)
        
        return {'loss': avg_loss, 'distance': avg_distance}
    
    def validate_epoch(self, val_loader):
        """验证一个epoch"""
        self.model.eval()
        
        total_loss = 0.0
        total_distance = 0.0
        num_batches = 0
        all_distances = []
        
        with torch.no_grad():
            for batch_idx, batch in enumerate(val_loader):
                try:
                    # 数据移到设备
                    eeg_data = batch['eeg_data'].to(self.device)
                    temporal_sequence = batch['temporal_sequence'].to(self.device)
                    lesion_masks = batch['lesion_mask'].to(self.device)
                    
                    # 前向传播
                    outputs = self.model(eeg_data, temporal_sequence)
                    
                    # 计算损失
                    loss_results = self.criterion(outputs['center'], lesion_masks)
                    loss = loss_results['total_loss']
                    
                    if not torch.isnan(loss) and not torch.isinf(loss):
                        total_loss += loss.item()
                        total_distance += loss_results['mean_distance'].item()
                        all_distances.extend(loss_results['distances'].cpu().numpy())
                        num_batches += 1
                        
                except Exception as e:
                    print(f"Validation error in batch {batch_idx}: {e}")
                    continue
        
        avg_loss = total_loss / max(num_batches, 1)
        avg_distance = total_distance / max(num_batches, 1)
        
        return {
            'loss': avg_loss, 
            'distance': avg_distance,
            'all_distances': all_distances
        }
    
    def train(self, train_loader, val_loader, num_epochs=20):
        """完整训练循环"""
        print(f"开始修复版中心定位训练，共 {num_epochs} 个epoch...")
        
        best_val_distance = float('inf')
        patience_counter = 0
        max_patience = 8
        
        for epoch in range(num_epochs):
            start_time = time.time()
            
            # 训练
            train_metrics = self.train_epoch(train_loader)
            
            # 验证
            val_metrics = self.validate_epoch(val_loader)
            
            # 更新学习率
            self.scheduler.step(val_metrics['distance'])
            current_lr = self.optimizer.param_groups[0]['lr']
            
            # 记录历史
            self.history['train_loss'].append(train_metrics['loss'])
            self.history['val_loss'].append(val_metrics['loss'])
            self.history['train_distance'].append(train_metrics['distance'])
            self.history['val_distance'].append(val_metrics['distance'])
            self.history['learning_rate'].append(current_lr)
            
            # 打印epoch总结
            epoch_time = time.time() - start_time
            print(f"\nEpoch {epoch+1}/{num_epochs} ({epoch_time:.1f}s)")
            print(f"Train - Loss: {train_metrics['loss']:.3f}, Distance: {train_metrics['distance']:.1f}")
            print(f"Val   - Loss: {val_metrics['loss']:.3f}, Distance: {val_metrics['distance']:.1f}")
            print(f"LR: {current_lr:.2e}")
            
            # 早停和模型保存
            if val_metrics['distance'] < best_val_distance:
                best_val_distance = val_metrics['distance']
                patience_counter = 0
                
                # 保存最佳模型
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'val_distance': best_val_distance,
                    'history': self.history
                }, 'fixed_center_localization_best_model.pth')
                print(f"✓ 新的最佳模型已保存 (距离: {best_val_distance:.1f})")
            else:
                patience_counter += 1
                
            if patience_counter >= max_patience:
                print(f"早停触发，在第 {epoch+1} epoch停止训练")
                break
        
        print(f"\n训练完成！最佳验证距离: {best_val_distance:.1f} 体素")
        return self.history

def create_fixed_data_loaders(batch_size=2):  # 减小batch size
    """创建修复版数据加载器"""
    
    train_dataset = NormalizedEEGLesionDataset(
        "eeg_lesion_training_dataset/train_pairings.pkl", 
        augment=True
    )
    val_dataset = NormalizedEEGLesionDataset(
        "eeg_lesion_training_dataset/validation_pairings.pkl", 
        augment=False
    )
    
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    
    return train_loader, val_loader

def main():
    """主函数"""
    print("🔧 修复版癫痫病灶中心定位训练")
    print("="*50)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建数据加载器
    try:
        train_loader, val_loader = create_fixed_data_loaders(batch_size=2)
        print("✅ 数据加载器创建成功")
    except Exception as e:
        print(f"❌ 创建数据加载器失败: {e}")
        return
    
    # 创建修复版模型
    model = FixedCenterLocalizationModel(n_channels=14, feature_dim=256)
    print(f"修复版模型创建成功，参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 创建修复版训练器
    trainer = FixedCenterLocalizationTrainer(model, device, learning_rate=5e-5)
    
    # 开始训练
    history = trainer.train(train_loader, val_loader, num_epochs=15)
    
    # 绘制训练曲线
    plt.figure(figsize=(15, 5))
    
    plt.subplot(1, 3, 1)
    plt.plot(history['train_loss'], label='训练损失')
    plt.plot(history['val_loss'], label='验证损失')
    plt.title('损失曲线')
    plt.xlabel('Epoch')
    plt.ylabel('损失')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.subplot(1, 3, 2)
    plt.plot(history['train_distance'], label='训练距离')
    plt.plot(history['val_distance'], label='验证距离')
    plt.title('定位误差曲线')
    plt.xlabel('Epoch')
    plt.ylabel('距离 (体素)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.subplot(1, 3, 3)
    plt.plot(history['learning_rate'], label='学习率')
    plt.title('学习率曲线')
    plt.xlabel('Epoch')
    plt.ylabel('学习率')
    plt.yscale('log')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('fixed_center_training_curves.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("\n✅ 修复版中心定位训练完成！")
    print("📊 结果文件:")
    print("  - fixed_center_localization_best_model.pth: 最佳模型")
    print("  - fixed_center_training_curves.png: 训练曲线")
    
    # 显示最终性能
    if history['val_distance']:
        best_distance = min(history['val_distance'])
        print(f"\n🎯 最佳性能:")
        print(f"  最佳验证距离: {best_distance:.1f} 体素")
        print(f"  最终训练损失: {history['train_loss'][-1]:.3f}")
        print(f"  最终验证损失: {history['val_loss'][-1]:.3f}")

if __name__ == "__main__":
    main()
