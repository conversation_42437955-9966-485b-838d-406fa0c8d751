#!/usr/bin/env python3
"""
EEG-Lesion Intelligent Pairing System
Creates synthetic patient-lesion pairings for training epilepsy localization models
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import gzip
import pickle
import warnings
warnings.filterwarnings('ignore')

# Import required libraries
try:
    import nibabel as nib
    import mne
    from mne.channels import make_standard_montage
    from mne import create_info, EvokedArray
    from scipy import signal, stats
    from scipy.spatial.distance import cdist
    from sklearn.cluster import KMeans
    from sklearn.preprocessing import StandardScaler
    from sklearn.decomposition import PCA
    from sklearn.metrics import silhouette_score
    print("All required libraries loaded successfully")
except ImportError as e:
    print(f"Missing library: {e}")
    print("Installing required packages...")
    import subprocess
    import sys
    subprocess.check_call([sys.executable, "-m", "pip", "install", "nibabel", "scikit-learn"])
    import nibabel as nib
    from sklearn.cluster import KMeans
    from sklearn.preprocessing import StandardScaler
    from sklearn.decomposition import PCA
    from sklearn.metrics import silhouette_score

class EEGFeatureExtractor:
    """Extract comprehensive features from EEG recordings"""
    
    def __init__(self):
        self.sampling_rate = 128  # Hz
        self.eeg_channels = ['AF3', 'AF4', 'F3', 'F4', 'F7', 'F8', 'FC5', 'FC6', 
                            'O1', 'O2', 'P7', 'P8', 'T7', 'T8']
        
        # Frequency bands for analysis
        self.freq_bands = {
            'delta': (0.5, 4),
            'theta': (4, 8),
            'alpha': (8, 13),
            'beta': (13, 30),
            'gamma': (30, 50)
        }
        
    def load_eeg_data(self, filepath):
        """Load EEG data from compressed CSV"""
        with gzip.open(filepath, 'rt') as f:
            data = pd.read_csv(f)
        
        # Extract EEG channels only
        eeg_data = data[self.eeg_channels].values.T
        return eeg_data, data
    
    def compute_spectral_features(self, eeg_data):
        """Compute spectral power features for each frequency band"""
        features = {}
        
        for channel_idx, channel in enumerate(self.eeg_channels):
            channel_data = eeg_data[channel_idx, :]
            
            # Compute power spectral density
            freqs, psd = signal.welch(channel_data, fs=self.sampling_rate, nperseg=256)
            
            # Extract power in each frequency band
            for band_name, (low_freq, high_freq) in self.freq_bands.items():
                band_mask = (freqs >= low_freq) & (freqs <= high_freq)
                band_power = np.mean(psd[band_mask])
                features[f'{channel}_{band_name}_power'] = band_power
        
        return features
    
    def compute_connectivity_features(self, eeg_data):
        """Compute connectivity measures between electrode pairs"""
        features = {}
        n_channels = len(self.eeg_channels)
        
        # Compute correlation matrix
        corr_matrix = np.corrcoef(eeg_data)
        
        # Extract upper triangular correlations
        for i in range(n_channels):
            for j in range(i+1, n_channels):
                features[f'corr_{self.eeg_channels[i]}_{self.eeg_channels[j]}'] = corr_matrix[i, j]
        
        # Global connectivity measures
        features['mean_connectivity'] = np.mean(corr_matrix[np.triu_indices(n_channels, k=1)])
        features['max_connectivity'] = np.max(corr_matrix[np.triu_indices(n_channels, k=1)])
        features['connectivity_variance'] = np.var(corr_matrix[np.triu_indices(n_channels, k=1)])
        
        return features
    
    def compute_temporal_features(self, eeg_data):
        """Compute temporal characteristics of EEG signals"""
        features = {}
        
        for channel_idx, channel in enumerate(self.eeg_channels):
            channel_data = eeg_data[channel_idx, :]
            
            # Basic statistical features
            features[f'{channel}_mean'] = np.mean(channel_data)
            features[f'{channel}_std'] = np.std(channel_data)
            features[f'{channel}_skewness'] = stats.skew(channel_data)
            features[f'{channel}_kurtosis'] = stats.kurtosis(channel_data)
            
            # Variability measures
            features[f'{channel}_rms'] = np.sqrt(np.mean(channel_data**2))
            features[f'{channel}_peak_to_peak'] = np.ptp(channel_data)
            
            # Complexity measures
            features[f'{channel}_zero_crossings'] = np.sum(np.diff(np.sign(channel_data)) != 0)
            
        return features
    
    def compute_epilepsy_markers(self, eeg_data):
        """Compute features specifically related to epileptic activity"""
        features = {}
        
        # Spike detection using amplitude thresholding
        for channel_idx, channel in enumerate(self.eeg_channels):
            channel_data = eeg_data[channel_idx, :]
            
            # Detect high-amplitude events (potential spikes)
            threshold = np.mean(channel_data) + 3 * np.std(channel_data)
            spike_count = np.sum(np.abs(channel_data) > threshold)
            features[f'{channel}_spike_count'] = spike_count
            
            # Sharp wave detection using derivative
            derivative = np.diff(channel_data)
            sharp_wave_count = np.sum(np.abs(derivative) > 2 * np.std(derivative))
            features[f'{channel}_sharp_wave_count'] = sharp_wave_count
        
        # Global epilepsy markers
        all_data = eeg_data.flatten()
        features['global_spike_rate'] = np.sum(np.abs(all_data) > np.mean(all_data) + 3 * np.std(all_data)) / len(all_data)
        features['global_variability'] = np.std(all_data) / np.mean(np.abs(all_data))
        
        return features
    
    def extract_all_features(self, filepath):
        """Extract comprehensive feature set from EEG recording"""
        try:
            eeg_data, raw_data = self.load_eeg_data(filepath)
            
            # Extract different feature types
            spectral_features = self.compute_spectral_features(eeg_data)
            connectivity_features = self.compute_connectivity_features(eeg_data)
            temporal_features = self.compute_temporal_features(eeg_data)
            epilepsy_features = self.compute_epilepsy_markers(eeg_data)
            
            # Combine all features
            all_features = {
                **spectral_features,
                **connectivity_features,
                **temporal_features,
                **epilepsy_features
            }
            
            return all_features
            
        except Exception as e:
            print(f"Error processing {filepath}: {e}")
            return None

class LesionAnalyzer:
    """Analyze MRI lesion masks and extract spatial characteristics"""
    
    def __init__(self, masks_dir="masks-2"):
        self.masks_dir = Path(masks_dir)
        
    def load_lesion_mask(self, lesion_id):
        """Load lesion mask from NIfTI file"""
        mask_path = self.masks_dir / str(lesion_id) / f"{lesion_id}_MaskInOrig.nii.gz"
        
        if not mask_path.exists():
            return None
            
        try:
            nii_img = nib.load(mask_path)
            mask_data = nii_img.get_fdata()
            return mask_data, nii_img.affine
        except Exception as e:
            print(f"Error loading lesion {lesion_id}: {e}")
            return None
    
    def compute_lesion_features(self, lesion_id):
        """Extract comprehensive features from lesion mask"""
        result = self.load_lesion_mask(lesion_id)
        if result is None:
            return None
            
        mask_data, affine = result
        
        # Basic volume features
        lesion_voxels = mask_data > 0
        volume = np.sum(lesion_voxels)
        
        if volume == 0:
            return None
        
        features = {
            'lesion_id': lesion_id,
            'volume_voxels': volume,
            'volume_mm3': volume * np.abs(np.linalg.det(affine[:3, :3]))
        }
        
        # Spatial location features
        lesion_coords = np.where(lesion_voxels)
        centroid = [np.mean(coords) for coords in lesion_coords]
        
        features.update({
            'centroid_x': centroid[0],
            'centroid_y': centroid[1], 
            'centroid_z': centroid[2],
            'extent_x': np.ptp(lesion_coords[0]),
            'extent_y': np.ptp(lesion_coords[1]),
            'extent_z': np.ptp(lesion_coords[2])
        })
        
        # Shape features
        features.update({
            'compactness': volume / (np.sum(np.diff(lesion_coords, axis=1)**2) + 1e-6),
            'sphericity': (np.pi**(1/3) * (6*volume)**(2/3)) / np.sum(np.diff(lesion_coords, axis=1)**2),
        })
        
        # Anatomical region classification (simplified)
        # Based on centroid location in standard space
        if centroid[2] < 85:  # Lower brain regions
            if centroid[1] < 128:  # Anterior
                features['region'] = 'temporal'
            else:  # Posterior
                features['region'] = 'occipital'
        else:  # Upper brain regions
            if centroid[1] < 100:  # Anterior
                features['region'] = 'frontal'
            else:  # Posterior
                features['region'] = 'parietal'
        
        # Laterality
        features['laterality'] = 'left' if centroid[0] < 128 else 'right'
        features['bilateral'] = 1 if features['extent_x'] > 50 else 0
        
        return features

class EEGSourceLocalizer:
    """Perform EEG source localization using simplified dipole fitting"""
    
    def __init__(self):
        self.eeg_channels = ['AF3', 'AF4', 'F3', 'F4', 'F7', 'F8', 'FC5', 'FC6', 
                            'O1', 'O2', 'P7', 'P8', 'T7', 'T8']
        
        # Simplified electrode positions in MNI space (approximate)
        self.electrode_positions = {
            'AF3': [-39, 51, 12], 'AF4': [39, 51, 12],
            'F3': [-46, 20, 38], 'F4': [46, 20, 38],
            'F7': [-61, 11, -3], 'F8': [61, 11, -3],
            'FC5': [-57, 2, 23], 'FC6': [57, 2, 23],
            'O1': [-25, -87, 8], 'O2': [25, -87, 8],
            'P7': [-59, -25, 8], 'P8': [59, -25, 8],
            'T7': [-65, -13, -1], 'T8': [65, -13, -1]
        }
    
    def estimate_source_location(self, eeg_data):
        """Estimate source location using center of mass approach"""
        # Compute RMS power for each channel
        channel_powers = []
        for i, channel in enumerate(self.eeg_channels):
            power = np.sqrt(np.mean(eeg_data[i, :]**2))
            channel_powers.append(power)
        
        # Normalize powers
        channel_powers = np.array(channel_powers)
        channel_powers = channel_powers / np.sum(channel_powers)
        
        # Compute weighted center of mass
        source_location = np.zeros(3)
        for i, channel in enumerate(self.eeg_channels):
            pos = np.array(self.electrode_positions[channel])
            source_location += channel_powers[i] * pos
        
        # Convert to voxel coordinates (approximate)
        # Assuming 256x256x256 volume with 1mm isotropic voxels
        voxel_coords = [
            int(source_location[0] + 128),  # X: left-right
            int(source_location[1] + 128),  # Y: anterior-posterior  
            int(source_location[2] + 128)   # Z: inferior-superior
        ]
        
        # Ensure coordinates are within bounds
        voxel_coords = [max(0, min(255, coord)) for coord in voxel_coords]
        
        return {
            'source_x': voxel_coords[0],
            'source_y': voxel_coords[1],
            'source_z': voxel_coords[2],
            'source_strength': np.sum(channel_powers),
            'source_focality': 1.0 / (np.std(channel_powers) + 1e-6)
        }

class IntelligentPairingSystem:
    """Create intelligent EEG-lesion pairings using clinical knowledge"""

    def __init__(self):
        self.eeg_extractor = EEGFeatureExtractor()
        self.lesion_analyzer = LesionAnalyzer()
        self.source_localizer = EEGSourceLocalizer()

        # Clinical knowledge for pairing
        self.region_eeg_mapping = {
            'temporal': ['T7', 'T8', 'F7', 'F8'],  # Temporal electrodes
            'frontal': ['AF3', 'AF4', 'F3', 'F4', 'FC5', 'FC6'],  # Frontal electrodes
            'parietal': ['P7', 'P8'],  # Parietal electrodes
            'occipital': ['O1', 'O2']  # Occipital electrodes
        }

    def compute_spatial_compatibility(self, eeg_source, lesion_features):
        """Compute spatial compatibility between EEG source and lesion"""
        # Distance between estimated source and lesion centroid
        source_coords = np.array([eeg_source['source_x'], eeg_source['source_y'], eeg_source['source_z']])
        lesion_coords = np.array([lesion_features['centroid_x'], lesion_features['centroid_y'], lesion_features['centroid_z']])

        distance = np.linalg.norm(source_coords - lesion_coords)

        # Normalize distance (closer = higher compatibility)
        max_distance = np.sqrt(3 * 256**2)  # Maximum possible distance in 256^3 volume
        spatial_compatibility = 1.0 - (distance / max_distance)

        return spatial_compatibility

    def compute_clinical_compatibility(self, eeg_features, lesion_features):
        """Compute clinical compatibility based on epilepsy patterns"""
        compatibility_score = 0.0

        # Region-specific compatibility
        lesion_region = lesion_features['region']
        relevant_channels = self.region_eeg_mapping.get(lesion_region, [])

        # Check if EEG shows high activity in relevant channels
        region_activity = 0.0
        for channel in relevant_channels:
            # Sum spectral power across frequency bands
            for band in ['delta', 'theta', 'alpha', 'beta', 'gamma']:
                key = f'{channel}_{band}_power'
                if key in eeg_features:
                    region_activity += eeg_features[key]

        if len(relevant_channels) > 0:
            region_activity /= len(relevant_channels)
            compatibility_score += region_activity / 10000  # Normalize

        # Epilepsy marker compatibility
        # Larger lesions should correlate with more epileptic activity
        lesion_volume = lesion_features['volume_mm3']
        epilepsy_activity = eeg_features.get('global_spike_rate', 0) + eeg_features.get('global_variability', 0)

        # Volume-activity correlation (larger lesions -> more activity)
        volume_activity_match = min(1.0, (lesion_volume / 50000) * epilepsy_activity * 100)
        compatibility_score += volume_activity_match

        return min(1.0, compatibility_score)

    def compute_pairing_score(self, eeg_features, eeg_source, lesion_features, metadata=None):
        """Compute overall pairing compatibility score"""
        # Spatial compatibility (40% weight)
        spatial_score = self.compute_spatial_compatibility(eeg_source, lesion_features)

        # Clinical compatibility (40% weight)
        clinical_score = self.compute_clinical_compatibility(eeg_features, lesion_features)

        # Metadata compatibility (20% weight)
        metadata_score = 0.5  # Default neutral score
        if metadata is not None:
            # Epilepsy patients should pair with lesions
            if metadata.get('Group') == 'Epilepsy':
                metadata_score = 0.8
            else:  # Control subjects get lower lesion pairing scores
                metadata_score = 0.2

        # Weighted combination
        total_score = (0.4 * spatial_score + 0.4 * clinical_score + 0.2 * metadata_score)

        return {
            'total_score': total_score,
            'spatial_score': spatial_score,
            'clinical_score': clinical_score,
            'metadata_score': metadata_score
        }

def main():
    """Main function to demonstrate the system"""
    print("EEG-Lesion Intelligent Pairing System")
    print("="*50)

    # Initialize components
    pairing_system = IntelligentPairingSystem()

    print("System components initialized successfully")
    print(f"Found {len(list(Path('masks-2').iterdir()))} lesion cases")

    # Test with a sample EEG file
    sample_eeg = Path("1252141/EEGs_Guinea-Bissau/signal-1.csv.gz")
    if sample_eeg.exists():
        print(f"\nTesting EEG feature extraction with: {sample_eeg}")
        features = pairing_system.eeg_extractor.extract_all_features(sample_eeg)
        if features:
            print(f"Extracted {len(features)} EEG features")

            # Test source localization
            eeg_data, _ = pairing_system.eeg_extractor.load_eeg_data(sample_eeg)
            source_features = pairing_system.source_localizer.estimate_source_location(eeg_data)
            print(f"Estimated source location: {source_features}")

    # Test lesion analysis
    print(f"\nTesting lesion analysis with lesion ID 1")
    lesion_features = pairing_system.lesion_analyzer.compute_lesion_features(1)
    if lesion_features:
        print(f"Extracted {len(lesion_features)} lesion features")
        print(f"Lesion volume: {lesion_features['volume_mm3']:.1f} mm³")
        print(f"Lesion region: {lesion_features['region']}")
        print(f"Lesion laterality: {lesion_features['laterality']}")

        # Test pairing compatibility
        if features and source_features:
            metadata = {'Group': 'Epilepsy'}  # Sample metadata
            pairing_scores = pairing_system.compute_pairing_score(
                features, source_features, lesion_features, metadata
            )
            print(f"\nPairing compatibility scores:")
            for score_type, score_value in pairing_scores.items():
                print(f"  {score_type}: {score_value:.3f}")

    print("\nSystem ready for intelligent EEG-lesion pairing!")

if __name__ == "__main__":
    main()
