#!/usr/bin/env python3
"""
在原始T1数据上标出掩码位置的切片可视化
"""

import numpy as np
import matplotlib.pyplot as plt
import nibabel as nib
from matplotlib.colors import ListedColormap
import matplotlib.patches as patches
import os

def create_mask_overlay_slices():
    """
    创建T1原始数据上的掩码叠加切片可视化
    """
    print("=== 创建T1+掩码切片可视化 ===")
    
    # 加载数据
    t1_img = nib.load("sub-1_T1w.nii")
    mask_img = nib.load("1/1_MaskInRawData.nii.gz")
    
    t1_data = t1_img.get_fdata()
    mask_data = mask_img.get_fdata()
    
    print(f"T1数据形状: {t1_data.shape}")
    print(f"掩码数据形状: {mask_data.shape}")
    
    # 标准化T1数据到0-1范围
    t1_norm = (t1_data - t1_data.min()) / (t1_data.max() - t1_data.min())
    
    # 创建二值掩码
    mask_binary = mask_data > 0
    
    # 找到掩码的边界框
    coords = np.where(mask_binary)
    if len(coords[0]) == 0:
        print("错误: 掩码为空!")
        return
    
    min_x, max_x = coords[0].min(), coords[0].max()
    min_y, max_y = coords[1].min(), coords[1].max()
    min_z, max_z = coords[2].min(), coords[2].max()
    
    print(f"掩码边界框:")
    print(f"  X: {min_x} - {max_x}")
    print(f"  Y: {min_y} - {max_y}")
    print(f"  Z: {min_z} - {max_z}")
    
    # 创建多切片可视化
    create_axial_slices(t1_norm, mask_binary, min_z, max_z)
    create_coronal_slices(t1_norm, mask_binary, min_y, max_y)
    create_sagittal_slices(t1_norm, mask_binary, min_x, max_x)
    
    # 创建综合概览
    create_overview_figure(t1_norm, mask_binary)

def create_axial_slices(t1_norm, mask_binary, min_z, max_z):
    """
    创建轴位面切片（从上到下）
    """
    print("创建轴位面切片...")
    
    # 选择包含掩码的切片
    z_slices = np.linspace(min_z, max_z, 12, dtype=int)
    
    fig, axes = plt.subplots(3, 4, figsize=(20, 15))
    fig.suptitle('轴位面切片 - T1原始数据 + 掩码标记 (从下到上)', fontsize=16, fontweight='bold')
    
    for i, z in enumerate(z_slices):
        row = i // 4
        col = i % 4
        ax = axes[row, col]
        
        # 显示T1切片
        t1_slice = t1_norm[:, :, z]
        mask_slice = mask_binary[:, :, z]
        
        # 显示T1图像
        ax.imshow(t1_slice.T, cmap='gray', origin='lower', alpha=1.0)
        
        # 叠加掩码（红色轮廓）
        if np.any(mask_slice):
            # 创建掩码轮廓
            mask_contour = np.zeros((*mask_slice.shape, 4))
            mask_contour[mask_slice, :] = [1, 0, 0, 0.7]  # 红色，70%透明度
            ax.imshow(mask_contour.transpose(1, 0, 2), origin='lower')
            
            # 添加掩码边界框
            mask_coords = np.where(mask_slice)
            if len(mask_coords[0]) > 0:
                x_min, x_max = mask_coords[0].min(), mask_coords[0].max()
                y_min, y_max = mask_coords[1].min(), mask_coords[1].max()
                
                rect = patches.Rectangle((x_min, y_min), x_max-x_min, y_max-y_min,
                                       linewidth=2, edgecolor='yellow', facecolor='none')
                ax.add_patch(rect)
        
        ax.set_title(f'Z = {z} ({z/(t1_norm.shape[2]-1)*100:.0f}%)', fontweight='bold')
        ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('t1_mask_axial_slices.png', dpi=300, bbox_inches='tight')
    print("保存: t1_mask_axial_slices.png")
    plt.close(fig)

def create_coronal_slices(t1_norm, mask_binary, min_y, max_y):
    """
    创建冠状面切片（从前到后）
    """
    print("创建冠状面切片...")
    
    # 选择包含掩码的切片
    y_slices = np.linspace(min_y, max_y, 12, dtype=int)
    
    fig, axes = plt.subplots(3, 4, figsize=(20, 15))
    fig.suptitle('冠状面切片 - T1原始数据 + 掩码标记 (从前到后)', fontsize=16, fontweight='bold')
    
    for i, y in enumerate(y_slices):
        row = i // 4
        col = i % 4
        ax = axes[row, col]
        
        # 显示T1切片
        t1_slice = t1_norm[:, y, :]
        mask_slice = mask_binary[:, y, :]
        
        # 显示T1图像
        ax.imshow(t1_slice.T, cmap='gray', origin='lower', alpha=1.0)
        
        # 叠加掩码
        if np.any(mask_slice):
            mask_contour = np.zeros((*mask_slice.shape, 4))
            mask_contour[mask_slice, :] = [1, 0, 0, 0.7]
            ax.imshow(mask_contour.transpose(1, 0, 2), origin='lower')
            
            # 添加边界框
            mask_coords = np.where(mask_slice)
            if len(mask_coords[0]) > 0:
                x_min, x_max = mask_coords[0].min(), mask_coords[0].max()
                z_min, z_max = mask_coords[1].min(), mask_coords[1].max()
                
                rect = patches.Rectangle((x_min, z_min), x_max-x_min, z_max-z_min,
                                       linewidth=2, edgecolor='yellow', facecolor='none')
                ax.add_patch(rect)
        
        ax.set_title(f'Y = {y} ({y/(t1_norm.shape[1]-1)*100:.0f}%)', fontweight='bold')
        ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('t1_mask_coronal_slices.png', dpi=300, bbox_inches='tight')
    print("保存: t1_mask_coronal_slices.png")
    plt.close(fig)

def create_sagittal_slices(t1_norm, mask_binary, min_x, max_x):
    """
    创建矢状面切片（从左到右）
    """
    print("创建矢状面切片...")
    
    # 选择包含掩码的切片
    x_slices = np.linspace(min_x, max_x, 12, dtype=int)
    
    fig, axes = plt.subplots(3, 4, figsize=(20, 15))
    fig.suptitle('矢状面切片 - T1原始数据 + 掩码标记 (从左到右)', fontsize=16, fontweight='bold')
    
    for i, x in enumerate(x_slices):
        row = i // 4
        col = i % 4
        ax = axes[row, col]
        
        # 显示T1切片
        t1_slice = t1_norm[x, :, :]
        mask_slice = mask_binary[x, :, :]
        
        # 显示T1图像
        ax.imshow(t1_slice.T, cmap='gray', origin='lower', alpha=1.0)
        
        # 叠加掩码
        if np.any(mask_slice):
            mask_contour = np.zeros((*mask_slice.shape, 4))
            mask_contour[mask_slice, :] = [1, 0, 0, 0.7]
            ax.imshow(mask_contour.transpose(1, 0, 2), origin='lower')
            
            # 添加边界框
            mask_coords = np.where(mask_slice)
            if len(mask_coords[0]) > 0:
                y_min, y_max = mask_coords[0].min(), mask_coords[0].max()
                z_min, z_max = mask_coords[1].min(), mask_coords[1].max()
                
                rect = patches.Rectangle((y_min, z_min), y_max-y_min, z_max-z_min,
                                       linewidth=2, edgecolor='yellow', facecolor='none')
                ax.add_patch(rect)
        
        ax.set_title(f'X = {x} ({x/(t1_norm.shape[0]-1)*100:.0f}%)', fontweight='bold')
        ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('t1_mask_sagittal_slices.png', dpi=300, bbox_inches='tight')
    print("保存: t1_mask_sagittal_slices.png")
    plt.close(fig)

def create_overview_figure(t1_norm, mask_binary):
    """
    创建综合概览图
    """
    print("创建综合概览图...")
    
    # 找到掩码质心
    coords = np.where(mask_binary)
    centroid_x = int(np.mean(coords[0]))
    centroid_y = int(np.mean(coords[1]))
    centroid_z = int(np.mean(coords[2]))
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('T1 MRI + 掩码区域 - 综合概览 (质心切片)', fontsize=16, fontweight='bold')
    
    # 第一行：原始T1图像
    # 轴位面
    ax = axes[0, 0]
    t1_slice = t1_norm[:, :, centroid_z]
    im1 = ax.imshow(t1_slice.T, cmap='gray', origin='lower')
    ax.set_title(f'轴位面 - T1原始 (Z={centroid_z})', fontweight='bold')
    ax.axis('off')
    plt.colorbar(im1, ax=ax, shrink=0.8)
    
    # 冠状面
    ax = axes[0, 1]
    t1_slice = t1_norm[:, centroid_y, :]
    im2 = ax.imshow(t1_slice.T, cmap='gray', origin='lower')
    ax.set_title(f'冠状面 - T1原始 (Y={centroid_y})', fontweight='bold')
    ax.axis('off')
    plt.colorbar(im2, ax=ax, shrink=0.8)
    
    # 矢状面
    ax = axes[0, 2]
    t1_slice = t1_norm[centroid_x, :, :]
    im3 = ax.imshow(t1_slice.T, cmap='gray', origin='lower')
    ax.set_title(f'矢状面 - T1原始 (X={centroid_x})', fontweight='bold')
    ax.axis('off')
    plt.colorbar(im3, ax=ax, shrink=0.8)
    
    # 第二行：T1 + 掩码叠加
    # 轴位面叠加
    ax = axes[1, 0]
    t1_slice = t1_norm[:, :, centroid_z]
    mask_slice = mask_binary[:, :, centroid_z]
    ax.imshow(t1_slice.T, cmap='gray', origin='lower')
    if np.any(mask_slice):
        mask_contour = np.zeros((*mask_slice.shape, 4))
        mask_contour[mask_slice, :] = [1, 0, 0, 0.8]
        ax.imshow(mask_contour.transpose(1, 0, 2), origin='lower')
    ax.set_title('轴位面 - T1 + 掩码', fontweight='bold')
    ax.axis('off')
    
    # 冠状面叠加
    ax = axes[1, 1]
    t1_slice = t1_norm[:, centroid_y, :]
    mask_slice = mask_binary[:, centroid_y, :]
    ax.imshow(t1_slice.T, cmap='gray', origin='lower')
    if np.any(mask_slice):
        mask_contour = np.zeros((*mask_slice.shape, 4))
        mask_contour[mask_slice, :] = [1, 0, 0, 0.8]
        ax.imshow(mask_contour.transpose(1, 0, 2), origin='lower')
    ax.set_title('冠状面 - T1 + 掩码', fontweight='bold')
    ax.axis('off')
    
    # 矢状面叠加
    ax = axes[1, 2]
    t1_slice = t1_norm[centroid_x, :, :]
    mask_slice = mask_binary[centroid_x, :, :]
    ax.imshow(t1_slice.T, cmap='gray', origin='lower')
    if np.any(mask_slice):
        mask_contour = np.zeros((*mask_slice.shape, 4))
        mask_contour[mask_slice, :] = [1, 0, 0, 0.8]
        ax.imshow(mask_contour.transpose(1, 0, 2), origin='lower')
    ax.set_title('矢状面 - T1 + 掩码', fontweight='bold')
    ax.axis('off')
    
    # 添加图例
    from matplotlib.lines import Line2D
    legend_elements = [
        Line2D([0], [0], color='gray', lw=4, label='T1 MRI'),
        Line2D([0], [0], color='red', lw=4, label='掩码区域'),
        Line2D([0], [0], color='yellow', lw=2, label='边界框')
    ]
    fig.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(0.98, 0.98))
    
    plt.tight_layout()
    plt.savefig('t1_mask_overview.png', dpi=300, bbox_inches='tight')
    print("保存: t1_mask_overview.png")
    plt.close(fig)

def create_mask_statistics():
    """
    创建掩码统计信息图
    """
    print("创建掩码统计信息...")
    
    # 加载数据
    t1_img = nib.load("sub-1_T1w.nii")
    mask_img = nib.load("1/1_MaskInRawData.nii.gz")
    
    t1_data = t1_img.get_fdata()
    mask_data = mask_img.get_fdata()
    
    mask_binary = mask_data > 0
    coords = np.where(mask_binary)
    
    # 计算统计信息
    centroid = [np.mean(coords[0]), np.mean(coords[1]), np.mean(coords[2])]
    volume_mm3 = np.sum(mask_binary) * 1.0 * 1.0001361 * 1.0  # 体素尺寸
    
    # 计算掩码在各个方向的分布
    x_dist = np.sum(mask_binary, axis=(1, 2))
    y_dist = np.sum(mask_binary, axis=(0, 2))
    z_dist = np.sum(mask_binary, axis=(0, 1))
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('掩码区域统计分析', fontsize=16, fontweight='bold')
    
    # X方向分布
    axes[0, 0].plot(x_dist, 'r-', linewidth=2)
    axes[0, 0].axvline(centroid[0], color='blue', linestyle='--', label=f'质心 X={centroid[0]:.1f}')
    axes[0, 0].set_title('X方向分布 (左-右)')
    axes[0, 0].set_xlabel('X坐标')
    axes[0, 0].set_ylabel('体素数')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # Y方向分布
    axes[0, 1].plot(y_dist, 'g-', linewidth=2)
    axes[0, 1].axvline(centroid[1], color='blue', linestyle='--', label=f'质心 Y={centroid[1]:.1f}')
    axes[0, 1].set_title('Y方向分布 (前-后)')
    axes[0, 1].set_xlabel('Y坐标')
    axes[0, 1].set_ylabel('体素数')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # Z方向分布
    axes[1, 0].plot(z_dist, 'b-', linewidth=2)
    axes[1, 0].axvline(centroid[2], color='blue', linestyle='--', label=f'质心 Z={centroid[2]:.1f}')
    axes[1, 0].set_title('Z方向分布 (下-上)')
    axes[1, 0].set_xlabel('Z坐标')
    axes[1, 0].set_ylabel('体素数')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 统计信息文本
    stats_text = f"""
    掩码区域统计信息:
    
    • 总体素数: {np.sum(mask_binary):,}
    • 体积: {volume_mm3:.1f} mm³
    • 质心坐标: ({centroid[0]:.1f}, {centroid[1]:.1f}, {centroid[2]:.1f})
    
    边界范围:
    • X: {coords[0].min()} - {coords[0].max()} (跨度: {coords[0].max()-coords[0].min()+1})
    • Y: {coords[1].min()} - {coords[1].max()} (跨度: {coords[1].max()-coords[1].min()+1})
    • Z: {coords[2].min()} - {coords[2].max()} (跨度: {coords[2].max()-coords[2].min()+1})
    
    解剖位置推测:
    • X={centroid[0]:.0f}: {'左侧' if centroid[0] < 128 else '右侧'}半球
    • Y={centroid[1]:.0f}: {'前部' if centroid[1] < 114 else '后部'}脑区
    • Z={centroid[2]:.0f}: {'下部' if centroid[2] < 128 else '上部'}脑区
    """
    
    axes[1, 1].text(0.05, 0.95, stats_text, transform=axes[1, 1].transAxes,
                    fontsize=11, verticalalignment='top',
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow"))
    axes[1, 1].set_title('统计信息')
    axes[1, 1].axis('off')
    
    plt.tight_layout()
    plt.savefig('mask_statistics.png', dpi=300, bbox_inches='tight')
    print("保存: mask_statistics.png")
    plt.close(fig)

def main():
    """
    主函数
    """
    print("=== T1 MRI + 掩码切片可视化 ===")
    
    # 检查文件
    if not os.path.exists("sub-1_T1w.nii"):
        print("错误: 找不到 sub-1_T1w.nii")
        return
    if not os.path.exists("1/1_MaskInRawData.nii.gz"):
        print("错误: 找不到 1_MaskInRawData.nii.gz")
        return
    
    # 创建可视化
    create_mask_overlay_slices()
    create_mask_statistics()
    
    print("\n=== 可视化完成 ===")
    print("生成的文件:")
    print("- t1_mask_axial_slices.png: 轴位面切片序列")
    print("- t1_mask_coronal_slices.png: 冠状面切片序列")
    print("- t1_mask_sagittal_slices.png: 矢状面切片序列")
    print("- t1_mask_overview.png: 综合概览图")
    print("- mask_statistics.png: 掩码统计分析")
    
    print("\n图例说明:")
    print("- 灰色: T1 MRI原始数据")
    print("- 红色区域: 掩码标记的病灶区域")
    print("- 黄色框: 掩码边界框")

if __name__ == "__main__":
    main()
