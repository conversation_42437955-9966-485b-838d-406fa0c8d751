#!/usr/bin/env python3
"""
Complete Training System for Epilepsy Lesion Localization
with Adaptive Cubic Bounding Box Training

This script implements the full training pipeline with:
- Model training and validation
- Performance metrics tracking
- Model checkpointing and early stopping
- Training curve visualization
- Comprehensive logging
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.tensorboard import SummaryWriter
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json
import time
from tqdm import tqdm
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

from training_pipeline import (
    EpilepsyLocalizationModel, BoundingBoxLoss, create_data_loaders
)

# Set random seeds for reproducibility
torch.manual_seed(42)
np.random.seed(42)

class MetricsTracker:
    """Track and compute performance metrics for bounding box evaluation"""
    
    def __init__(self):
        self.reset()
    
    def reset(self):
        """Reset all metrics"""
        self.dice_scores = []
        self.iou_scores = []
        self.losses = []
        self.center_errors = []
        self.size_errors = []
    
    def compute_dice_score(self, pred_mask: torch.Tensor, target_mask: torch.Tensor) -> float:
        """Compute Dice similarity coefficient"""
        intersection = torch.sum(pred_mask * target_mask)
        union = torch.sum(pred_mask) + torch.sum(target_mask)
        
        if union == 0:
            return 1.0 if torch.sum(target_mask) == 0 else 0.0
        
        dice = (2.0 * intersection) / union
        return dice.item()
    
    def compute_iou_score(self, pred_mask: torch.Tensor, target_mask: torch.Tensor) -> float:
        """Compute 3D Intersection over Union"""
        intersection = torch.sum(pred_mask * target_mask)
        union = torch.sum(pred_mask) + torch.sum(target_mask) - intersection
        
        if union == 0:
            return 1.0 if torch.sum(target_mask) == 0 else 0.0
        
        iou = intersection / union
        return iou.item()
    
    def compute_center_error(self, pred_center: torch.Tensor, target_center: torch.Tensor) -> float:
        """Compute Euclidean distance between predicted and target centers"""
        error = torch.norm(pred_center - target_center, p=2)
        return error.item()
    
    def compute_target_center(self, target_mask: torch.Tensor) -> torch.Tensor:
        """Compute center of mass for target lesion"""
        coords = torch.nonzero(target_mask, as_tuple=False).float()
        if len(coords) == 0:
            return torch.tensor([128.0, 128.0, 128.0])  # Default center
        
        center = torch.mean(coords, dim=0)
        return center
    
    def update(self, pred_masks: torch.Tensor, target_masks: torch.Tensor, 
               pred_centers: torch.Tensor, losses: torch.Tensor):
        """Update metrics with batch results"""
        batch_size = pred_masks.shape[0]
        
        for i in range(batch_size):
            # Compute Dice and IoU scores
            dice = self.compute_dice_score(pred_masks[i], target_masks[i])
            iou = self.compute_iou_score(pred_masks[i], target_masks[i])
            
            self.dice_scores.append(dice)
            self.iou_scores.append(iou)
            
            # Compute center error
            target_center = self.compute_target_center(target_masks[i])
            center_error = self.compute_center_error(pred_centers[i], target_center)
            self.center_errors.append(center_error)
        
        # Store losses
        if isinstance(losses, torch.Tensor):
            if losses.dim() == 0:  # Scalar tensor
                self.losses.append(losses.item())
            else:  # Batch of losses
                self.losses.extend(losses.cpu().numpy().tolist())
        else:
            self.losses.append(losses)
    
    def get_metrics(self) -> Dict[str, float]:
        """Get current metrics summary"""
        if not self.dice_scores:
            return {
                'dice_mean': 0.0, 'dice_std': 0.0,
                'iou_mean': 0.0, 'iou_std': 0.0,
                'loss_mean': 0.0, 'loss_std': 0.0,
                'center_error_mean': 0.0, 'center_error_std': 0.0
            }
        
        return {
            'dice_mean': np.mean(self.dice_scores),
            'dice_std': np.std(self.dice_scores),
            'iou_mean': np.mean(self.iou_scores),
            'iou_std': np.std(self.iou_scores),
            'loss_mean': np.mean(self.losses),
            'loss_std': np.std(self.losses),
            'center_error_mean': np.mean(self.center_errors),
            'center_error_std': np.std(self.center_errors)
        }

class EarlyStopping:
    """Early stopping to prevent overfitting"""
    
    def __init__(self, patience: int = 10, min_delta: float = 0.001, 
                 restore_best_weights: bool = True):
        self.patience = patience
        self.min_delta = min_delta
        self.restore_best_weights = restore_best_weights
        
        self.best_score = None
        self.counter = 0
        self.best_weights = None
        
    def __call__(self, score: float, model: nn.Module) -> bool:
        """
        Check if training should stop
        Args:
            score: Current validation score (higher is better)
            model: Model to potentially save weights from
        Returns:
            True if training should stop
        """
        if self.best_score is None:
            self.best_score = score
            self.best_weights = model.state_dict().copy()
        elif score < self.best_score + self.min_delta:
            self.counter += 1
            if self.counter >= self.patience:
                if self.restore_best_weights:
                    model.load_state_dict(self.best_weights)
                return True
        else:
            self.best_score = score
            self.counter = 0
            self.best_weights = model.state_dict().copy()
        
        return False

class EpilepsyTrainer:
    """Complete training system for epilepsy localization model"""
    
    def __init__(self, model: nn.Module, device: torch.device, 
                 learning_rate: float = 1e-4, weight_decay: float = 1e-5):
        
        self.model = model.to(device)
        self.device = device
        
        # Loss function
        self.bbox_loss = BoundingBoxLoss()
        self.autoencoder_loss = nn.MSELoss()
        
        # Optimizer
        self.optimizer = optim.AdamW(
            self.model.parameters(), 
            lr=learning_rate, 
            weight_decay=weight_decay
        )
        
        # Learning rate scheduler
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='max', factor=0.5, patience=5, verbose=True
        )
        
        # Metrics tracking
        self.train_metrics = MetricsTracker()
        self.val_metrics = MetricsTracker()
        
        # Training history
        self.history = {
            'train_loss': [], 'val_loss': [],
            'train_dice': [], 'val_dice': [],
            'train_iou': [], 'val_iou': [],
            'learning_rate': []
        }
        
        # Early stopping
        self.early_stopping = EarlyStopping(patience=15, min_delta=0.001)
        
        # TensorBoard logging
        self.writer = SummaryWriter('runs/epilepsy_localization')
    
    def train_epoch(self, train_loader) -> Dict[str, float]:
        """Train for one epoch"""
        self.model.train()
        self.train_metrics.reset()
        
        epoch_loss = 0.0
        num_batches = 0
        
        pbar = tqdm(train_loader, desc="Training")
        for batch_idx, batch in enumerate(pbar):
            # Move data to device
            eeg_data = batch['eeg_data'].to(self.device)
            temporal_sequence = batch['temporal_sequence'].to(self.device)
            lesion_masks = batch['lesion_mask'].to(self.device)
            
            # Forward pass
            self.optimizer.zero_grad()
            
            outputs = self.model(eeg_data, temporal_sequence, return_autoencoder_loss=True)
            bbox_params = outputs['bbox_params']
            
            # Compute bounding box loss
            bbox_loss_results = self.bbox_loss(bbox_params, lesion_masks)
            bbox_loss_val = bbox_loss_results['total_loss']
            
            # Compute autoencoder loss (if available)
            autoencoder_loss_val = 0.0
            if 'reconstruction' in outputs:
                autoencoder_loss_val = self.autoencoder_loss(outputs['reconstruction'], eeg_data)
            
            # Total loss
            total_loss = bbox_loss_val + 0.1 * autoencoder_loss_val
            
            # Backward pass
            total_loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            self.optimizer.step()
            
            # Update metrics
            self.train_metrics.update(
                bbox_loss_results['pred_masks'], lesion_masks,
                bbox_params['center'], total_loss
            )
            
            epoch_loss += total_loss.item()
            num_batches += 1
            
            # Update progress bar
            pbar.set_postfix({
                'Loss': f"{total_loss.item():.4f}",
                'BBox': f"{bbox_loss_val.item():.4f}",
                'Auto': f"{autoencoder_loss_val:.4f}" if isinstance(autoencoder_loss_val, torch.Tensor) else f"{autoencoder_loss_val:.4f}"
            })
        
        # Get epoch metrics
        metrics = self.train_metrics.get_metrics()
        metrics['total_loss'] = epoch_loss / num_batches
        
        return metrics
    
    def validate_epoch(self, val_loader) -> Dict[str, float]:
        """Validate for one epoch"""
        self.model.eval()
        self.val_metrics.reset()
        
        epoch_loss = 0.0
        num_batches = 0
        
        with torch.no_grad():
            pbar = tqdm(val_loader, desc="Validation")
            for batch_idx, batch in enumerate(pbar):
                # Move data to device
                eeg_data = batch['eeg_data'].to(self.device)
                temporal_sequence = batch['temporal_sequence'].to(self.device)
                lesion_masks = batch['lesion_mask'].to(self.device)
                
                # Forward pass
                outputs = self.model(eeg_data, temporal_sequence, return_autoencoder_loss=True)
                bbox_params = outputs['bbox_params']
                
                # Compute losses
                bbox_loss_results = self.bbox_loss(bbox_params, lesion_masks)
                bbox_loss_val = bbox_loss_results['total_loss']
                
                autoencoder_loss_val = 0.0
                if 'reconstruction' in outputs:
                    autoencoder_loss_val = self.autoencoder_loss(outputs['reconstruction'], eeg_data)
                
                total_loss = bbox_loss_val + 0.1 * autoencoder_loss_val
                
                # Update metrics
                self.val_metrics.update(
                    bbox_loss_results['pred_masks'], lesion_masks,
                    bbox_params['center'], total_loss
                )
                
                epoch_loss += total_loss.item()
                num_batches += 1
                
                # Update progress bar
                pbar.set_postfix({
                    'Loss': f"{total_loss.item():.4f}",
                    'Dice': f"{self.val_metrics.get_metrics()['dice_mean']:.3f}",
                    'IoU': f"{self.val_metrics.get_metrics()['iou_mean']:.3f}"
                })
        
        # Get epoch metrics
        metrics = self.val_metrics.get_metrics()
        metrics['total_loss'] = epoch_loss / num_batches
        
        return metrics
    
    def train(self, train_loader, val_loader, num_epochs: int = 100, 
              save_dir: str = "models") -> Dict[str, List[float]]:
        """Complete training loop"""
        
        save_path = Path(save_dir)
        save_path.mkdir(exist_ok=True)
        
        print(f"Starting training for {num_epochs} epochs...")
        print(f"Device: {self.device}")
        print(f"Model parameters: {sum(p.numel() for p in self.model.parameters()):,}")
        
        best_val_iou = 0.0
        
        for epoch in range(num_epochs):
            start_time = time.time()
            
            # Train epoch
            train_metrics = self.train_epoch(train_loader)
            
            # Validate epoch
            val_metrics = self.validate_epoch(val_loader)
            
            # Update learning rate
            self.scheduler.step(val_metrics['iou_mean'])
            
            # Log metrics
            current_lr = self.optimizer.param_groups[0]['lr']
            
            # Update history
            self.history['train_loss'].append(train_metrics['total_loss'])
            self.history['val_loss'].append(val_metrics['total_loss'])
            self.history['train_dice'].append(train_metrics['dice_mean'])
            self.history['val_dice'].append(val_metrics['dice_mean'])
            self.history['train_iou'].append(train_metrics['iou_mean'])
            self.history['val_iou'].append(val_metrics['iou_mean'])
            self.history['learning_rate'].append(current_lr)
            
            # TensorBoard logging
            self.writer.add_scalar('Loss/Train', train_metrics['total_loss'], epoch)
            self.writer.add_scalar('Loss/Validation', val_metrics['total_loss'], epoch)
            self.writer.add_scalar('Dice/Train', train_metrics['dice_mean'], epoch)
            self.writer.add_scalar('Dice/Validation', val_metrics['dice_mean'], epoch)
            self.writer.add_scalar('IoU/Train', train_metrics['iou_mean'], epoch)
            self.writer.add_scalar('IoU/Validation', val_metrics['iou_mean'], epoch)
            self.writer.add_scalar('Learning_Rate', current_lr, epoch)
            
            # Print epoch summary
            epoch_time = time.time() - start_time
            print(f"\nEpoch {epoch+1}/{num_epochs} ({epoch_time:.1f}s)")
            print(f"Train - Loss: {train_metrics['total_loss']:.4f}, Dice: {train_metrics['dice_mean']:.3f}, IoU: {train_metrics['iou_mean']:.3f}")
            print(f"Val   - Loss: {val_metrics['total_loss']:.4f}, Dice: {val_metrics['dice_mean']:.3f}, IoU: {val_metrics['iou_mean']:.3f}")
            print(f"LR: {current_lr:.2e}")
            
            # Save best model
            if val_metrics['iou_mean'] > best_val_iou:
                best_val_iou = val_metrics['iou_mean']
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'val_iou': best_val_iou,
                    'history': self.history
                }, save_path / 'best_model.pth')
                print(f"✓ New best model saved (IoU: {best_val_iou:.3f})")
            
            # Early stopping check
            if self.early_stopping(val_metrics['iou_mean'], self.model):
                print(f"\nEarly stopping triggered after {epoch+1} epochs")
                break
            
            # Save checkpoint every 10 epochs
            if (epoch + 1) % 10 == 0:
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'history': self.history
                }, save_path / f'checkpoint_epoch_{epoch+1}.pth')
        
        self.writer.close()
        
        # Save final training history
        with open(save_path / 'training_history.json', 'w') as f:
            json.dump(self.history, f, indent=2)
        
        print(f"\nTraining completed! Best validation IoU: {best_val_iou:.3f}")
        
        return self.history

def main():
    """Main training function"""
    print("Epilepsy Lesion Localization - Cubic Bounding Box Training")
    print("="*70)
    
    # Check for GPU
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Create data loaders
    try:
        train_loader, val_loader, test_loader = create_data_loaders(
            train_file="eeg_lesion_training_dataset/train_pairings.pkl",
            val_file="eeg_lesion_training_dataset/validation_pairings.pkl",
            test_file="eeg_lesion_training_dataset/test_pairings.pkl",
            batch_size=2,  # Small batch size due to memory constraints
            num_workers=0  # Set to 0 to avoid multiprocessing issues
        )
        
        print(f"Data loaders created successfully")
        print(f"Train batches: {len(train_loader)}")
        print(f"Validation batches: {len(val_loader)}")
        print(f"Test batches: {len(test_loader)}")
        
    except Exception as e:
        print(f"Error creating data loaders: {e}")
        print("Please ensure the EEG-lesion training dataset exists.")
        return
    
    # Create model
    model = EpilepsyLocalizationModel(
        n_channels=14,
        feature_dim=512,
        fused_dim=1024,
        volume_size=256
    )
    
    # Create trainer
    trainer = EpilepsyTrainer(
        model=model,
        device=device,
        learning_rate=1e-4,
        weight_decay=1e-5
    )
    
    # Start training
    history = trainer.train(
        train_loader=train_loader,
        val_loader=val_loader,
        num_epochs=50  # Reduced for demonstration
    )
    
    print("\n✓ Training completed successfully!")

if __name__ == "__main__":
    main()
