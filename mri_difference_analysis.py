#!/usr/bin/env python3
"""
详细分析两个MRI掩码数据的区别
"""

import numpy as np
import matplotlib.pyplot as plt
import nibabel as nib
import os

def detailed_comparison():
    """
    详细比较两个MRI掩码的区别
    """
    print("=== 详细分析两个MRI掩码的区别 ===")
    
    # 加载数据
    img1 = nib.load("1/1_MaskInOrig.nii.gz")
    img2 = nib.load("1/1_MaskInRawData.nii.gz")
    
    data1 = img1.get_fdata()
    data2 = img2.get_fdata()
    
    header1 = img1.header
    header2 = img2.header
    
    affine1 = img1.affine
    affine2 = img2.affine
    
    print("\n=== 1. 基本属性对比 ===")
    print(f"MaskInOrig:")
    print(f"  - 数据形状: {data1.shape}")
    print(f"  - 体素尺寸: {header1.get_zooms()}")
    print(f"  - 数据类型: {data1.dtype}")
    print(f"  - 文件大小: {os.path.getsize('1/1_MaskInOrig.nii.gz')} bytes")
    
    print(f"\nMaskInRawData:")
    print(f"  - 数据形状: {data2.shape}")
    print(f"  - 体素尺寸: {header2.get_zooms()}")
    print(f"  - 数据类型: {data2.dtype}")
    print(f"  - 文件大小: {os.path.getsize('1/1_MaskInRawData.nii.gz')} bytes")
    
    print(f"\n=== 2. 形状差异分析 ===")
    shape_diff = np.array(data1.shape) - np.array(data2.shape[:3])  # 只比较前3维
    print(f"形状差异 (MaskInOrig - MaskInRawData): {shape_diff}")
    print(f"X轴差异: {shape_diff[0]} 体素")
    print(f"Y轴差异: {shape_diff[1]} 体素 ← 主要差异")
    print(f"Z轴差异: {shape_diff[2]} 体素")
    
    print(f"\n=== 3. 体素尺寸差异 ===")
    voxel1 = header1.get_zooms()
    voxel2 = header2.get_zooms()
    print(f"MaskInOrig体素尺寸: {voxel1}")
    print(f"MaskInRawData体素尺寸: {voxel2}")
    print(f"Y轴体素尺寸差异: {voxel2[1] - voxel1[1]:.6f} mm")
    
    print(f"\n=== 4. 仿射变换矩阵对比 ===")
    print(f"MaskInOrig仿射矩阵:")
    print(affine1)
    print(f"\nMaskInRawData仿射矩阵:")
    print(affine2)
    print(f"\n仿射矩阵差异:")
    print(affine1 - affine2)
    
    print(f"\n=== 5. 掩码内容对比 ===")
    mask1 = data1 > 0
    mask2 = data2 > 0
    
    print(f"MaskInOrig非零体素: {np.sum(mask1):,}")
    print(f"MaskInRawData非零体素: {np.sum(mask2):,}")
    print(f"体素数差异: {np.sum(mask1) - np.sum(mask2):,}")
    
    # 计算质心
    coords1 = np.where(mask1)
    coords2 = np.where(mask2)
    
    if len(coords1[0]) > 0 and len(coords2[0]) > 0:
        centroid1 = [np.mean(coords1[0]), np.mean(coords1[1]), np.mean(coords1[2])]
        centroid2 = [np.mean(coords2[0]), np.mean(coords2[1]), np.mean(coords2[2])]
        
        print(f"\n=== 6. 质心位置对比 ===")
        print(f"MaskInOrig质心: ({centroid1[0]:.1f}, {centroid1[1]:.1f}, {centroid1[2]:.1f})")
        print(f"MaskInRawData质心: ({centroid2[0]:.1f}, {centroid2[1]:.1f}, {centroid2[2]:.1f})")
        
        centroid_diff = np.array(centroid1) - np.array(centroid2)
        print(f"质心差异: ({centroid_diff[0]:.1f}, {centroid_diff[1]:.1f}, {centroid_diff[2]:.1f})")
        
        distance = np.sqrt(np.sum(centroid_diff**2))
        print(f"质心距离: {distance:.1f} 体素")
    
    return data1, data2, mask1, mask2

def analyze_spatial_relationship(data1, data2, mask1, mask2):
    """
    分析空间关系
    """
    print(f"\n=== 7. 空间关系分析 ===")
    
    # 由于形状不同，我们需要找到重叠区域
    min_shape = [min(data1.shape[i], data2.shape[i]) for i in range(3)]
    print(f"重叠区域形状: {min_shape}")
    
    # 提取重叠区域
    mask1_crop = mask1[:min_shape[0], :min_shape[1], :min_shape[2]]
    mask2_crop = mask2[:min_shape[0], :min_shape[1], :min_shape[2]]
    
    # 计算重叠统计
    intersection = mask1_crop & mask2_crop
    union = mask1_crop | mask2_crop
    only_mask1 = mask1_crop & ~mask2_crop
    only_mask2 = mask2_crop & ~mask1_crop
    
    print(f"重叠区域中:")
    print(f"  - 掩码1体素: {np.sum(mask1_crop):,}")
    print(f"  - 掩码2体素: {np.sum(mask2_crop):,}")
    print(f"  - 重叠体素: {np.sum(intersection):,}")
    print(f"  - 仅掩码1: {np.sum(only_mask1):,}")
    print(f"  - 仅掩码2: {np.sum(only_mask2):,}")
    
    if np.sum(union) > 0:
        dice = 2 * np.sum(intersection) / (np.sum(mask1_crop) + np.sum(mask2_crop))
        jaccard = np.sum(intersection) / np.sum(union)
        overlap_ratio = np.sum(intersection) / np.sum(union)
        
        print(f"  - Dice系数: {dice:.4f}")
        print(f"  - Jaccard指数: {jaccard:.4f}")
        print(f"  - 重叠率: {overlap_ratio*100:.1f}%")
    
    return mask1_crop, mask2_crop, intersection, only_mask1, only_mask2

def create_difference_visualization(data1, data2, mask1, mask2):
    """
    创建差异可视化
    """
    print(f"\n=== 创建差异可视化 ===")
    
    # 找到重叠区域
    min_shape = [min(data1.shape[i], data2.shape[i]) for i in range(3)]
    mask1_crop = mask1[:min_shape[0], :min_shape[1], :min_shape[2]]
    mask2_crop = mask2[:min_shape[0], :min_shape[1], :min_shape[2]]
    
    intersection = mask1_crop & mask2_crop
    only_mask1 = mask1_crop & ~mask2_crop
    only_mask2 = mask2_crop & ~mask1_crop
    
    # 创建差异分析图
    fig, axes = plt.subplots(3, 4, figsize=(20, 15))
    fig.suptitle('MRI掩码详细差异分析', fontsize=16, fontweight='bold')
    
    # 选择中心切片
    center_z = min_shape[2] // 2
    center_y = min_shape[1] // 2
    center_x = min_shape[0] // 2
    
    # 第一行：轴位面 (Z方向)
    axes[0, 0].imshow(mask1[:, :, center_z].T, cmap='Reds', origin='lower', alpha=0.8)
    axes[0, 0].set_title('MaskInOrig - 轴位面', fontweight='bold')
    axes[0, 0].axis('off')
    
    axes[0, 1].imshow(mask2_crop[:, :, center_z].T, cmap='Blues', origin='lower', alpha=0.8)
    axes[0, 1].set_title('MaskInRawData - 轴位面', fontweight='bold')
    axes[0, 1].axis('off')
    
    # 重叠显示
    overlap_img = np.zeros((*mask1_crop[:, :, center_z].shape, 3))
    overlap_img[only_mask1[:, :, center_z], 0] = 1  # 红色 - 仅掩码1
    overlap_img[only_mask2[:, :, center_z], 2] = 1  # 蓝色 - 仅掩码2
    overlap_img[intersection[:, :, center_z], 1] = 1  # 绿色 - 重叠
    
    axes[0, 2].imshow(overlap_img.transpose(1, 0, 2), origin='lower')
    axes[0, 2].set_title('重叠分析\n红:仅Orig 蓝:仅Raw 绿:重叠', fontweight='bold')
    axes[0, 2].axis('off')
    
    # 差异图
    diff_img = only_mask1[:, :, center_z].astype(int) - only_mask2[:, :, center_z].astype(int)
    im = axes[0, 3].imshow(diff_img.T, cmap='RdBu_r', origin='lower', vmin=-1, vmax=1)
    axes[0, 3].set_title('差异图\n红:Orig独有 蓝:Raw独有', fontweight='bold')
    axes[0, 3].axis('off')
    plt.colorbar(im, ax=axes[0, 3], shrink=0.8)
    
    # 第二行：冠状面 (Y方向)
    axes[1, 0].imshow(mask1[:, center_y, :].T, cmap='Reds', origin='lower', alpha=0.8)
    axes[1, 0].set_title('MaskInOrig - 冠状面', fontweight='bold')
    axes[1, 0].axis('off')
    
    axes[1, 1].imshow(mask2_crop[:, center_y, :].T, cmap='Blues', origin='lower', alpha=0.8)
    axes[1, 1].set_title('MaskInRawData - 冠状面', fontweight='bold')
    axes[1, 1].axis('off')
    
    # 重叠显示
    overlap_img_cor = np.zeros((*mask1_crop[:, center_y, :].shape, 3))
    overlap_img_cor[only_mask1[:, center_y, :], 0] = 1
    overlap_img_cor[only_mask2[:, center_y, :], 2] = 1
    overlap_img_cor[intersection[:, center_y, :], 1] = 1
    
    axes[1, 2].imshow(overlap_img_cor.transpose(1, 0, 2), origin='lower')
    axes[1, 2].set_title('重叠分析 - 冠状面', fontweight='bold')
    axes[1, 2].axis('off')
    
    # 差异图
    diff_img_cor = only_mask1[:, center_y, :].astype(int) - only_mask2[:, center_y, :].astype(int)
    im2 = axes[1, 3].imshow(diff_img_cor.T, cmap='RdBu_r', origin='lower', vmin=-1, vmax=1)
    axes[1, 3].set_title('差异图 - 冠状面', fontweight='bold')
    axes[1, 3].axis('off')
    plt.colorbar(im2, ax=axes[1, 3], shrink=0.8)
    
    # 第三行：统计和分析
    # 体积对比
    volumes = [np.sum(mask1), np.sum(mask2)]
    labels = ['MaskInOrig', 'MaskInRawData']
    colors = ['red', 'blue']
    
    axes[2, 0].bar(labels, volumes, color=colors, alpha=0.7)
    axes[2, 0].set_title('总体积对比', fontweight='bold')
    axes[2, 0].set_ylabel('体素数')
    for i, v in enumerate(volumes):
        axes[2, 0].text(i, v + v*0.01, f'{v:,}', ha='center', va='bottom')
    
    # 重叠区域统计
    overlap_stats = [np.sum(intersection), np.sum(only_mask1), np.sum(only_mask2)]
    overlap_labels = ['重叠', '仅Orig', '仅Raw']
    overlap_colors = ['green', 'red', 'blue']
    
    axes[2, 1].bar(overlap_labels, overlap_stats, color=overlap_colors, alpha=0.7)
    axes[2, 1].set_title('重叠区域统计', fontweight='bold')
    axes[2, 1].set_ylabel('体素数')
    for i, v in enumerate(overlap_stats):
        axes[2, 1].text(i, v + v*0.01, f'{v:,}', ha='center', va='bottom')
    
    # 形状对比
    shape_data = [data1.shape, data2.shape]
    x_pos = np.arange(3)
    width = 0.35
    
    axes[2, 2].bar(x_pos - width/2, data1.shape, width, label='MaskInOrig', color='red', alpha=0.7)
    axes[2, 2].bar(x_pos + width/2, data2.shape, width, label='MaskInRawData', color='blue', alpha=0.7)
    axes[2, 2].set_title('形状对比', fontweight='bold')
    axes[2, 2].set_ylabel('体素数')
    axes[2, 2].set_xticks(x_pos)
    axes[2, 2].set_xticklabels(['X', 'Y', 'Z'])
    axes[2, 2].legend()
    
    # 总结信息
    summary_text = f"""
    主要差异:
    
    1. 形状差异:
       • Y轴: {data1.shape[1]} vs {data2.shape[1]} 
       • 差异: {data1.shape[1] - data2.shape[1]} 体素
    
    2. 体积差异:
       • 总差异: {np.sum(mask1) - np.sum(mask2):,} 体素
       • 相对差异: {abs(np.sum(mask1) - np.sum(mask2))/max(np.sum(mask1), np.sum(mask2))*100:.1f}%
    
    3. 空间关系:
       • 重叠体素: {np.sum(intersection):,}
       • Dice系数: {2*np.sum(intersection)/(np.sum(mask1_crop)+np.sum(mask2_crop)):.3f}
    
    4. 可能原因:
       • 不同的图像配准
       • 不同的重采样方法
       • 原始vs处理后数据
    """
    
    axes[2, 3].text(0.05, 0.95, summary_text, transform=axes[2, 3].transAxes,
                    fontsize=10, verticalalignment='top',
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow"))
    axes[2, 3].set_title('差异总结', fontweight='bold')
    axes[2, 3].axis('off')
    
    plt.tight_layout()
    plt.savefig('mri_detailed_differences.png', dpi=300, bbox_inches='tight')
    print("保存详细差异图: mri_detailed_differences.png")
    plt.close(fig)

def analyze_header_differences():
    """
    分析头文件差异
    """
    print(f"\n=== 8. 头文件详细差异 ===")
    
    img1 = nib.load("1/1_MaskInOrig.nii.gz")
    img2 = nib.load("1/1_MaskInRawData.nii.gz")
    
    header1 = img1.header
    header2 = img2.header
    
    print("重要头文件字段对比:")
    
    # 数据类型
    print(f"数据类型:")
    print(f"  MaskInOrig: {header1.get_data_dtype()}")
    print(f"  MaskInRawData: {header2.get_data_dtype()}")
    
    # 体素尺寸
    print(f"体素尺寸:")
    print(f"  MaskInOrig: {header1.get_zooms()}")
    print(f"  MaskInRawData: {header2.get_zooms()}")
    
    # 数据范围
    print(f"数据范围:")
    print(f"  MaskInOrig: {header1.get_data_shape()}")
    print(f"  MaskInRawData: {header2.get_data_shape()}")
    
    # qform和sform
    print(f"qform代码:")
    print(f"  MaskInOrig: {header1['qform_code']}")
    print(f"  MaskInRawData: {header2['qform_code']}")
    
    print(f"sform代码:")
    print(f"  MaskInOrig: {header1['sform_code']}")
    print(f"  MaskInRawData: {header2['sform_code']}")

def main():
    """
    主分析函数
    """
    print("=== MRI掩码差异详细分析 ===")
    
    # 基本对比
    data1, data2, mask1, mask2 = detailed_comparison()
    
    # 空间关系分析
    mask1_crop, mask2_crop, intersection, only_mask1, only_mask2 = analyze_spatial_relationship(data1, data2, mask1, mask2)
    
    # 创建可视化
    create_difference_visualization(data1, data2, mask1, mask2)
    
    # 头文件分析
    analyze_header_differences()
    
    print(f"\n=== 结论 ===")
    print("主要差异:")
    print("1. 🔍 形状差异: Y轴维度不同 (256 vs 228)")
    print("2. 📏 体素尺寸: Y轴略有差异 (1.0 vs 1.0001361)")
    print("3. 🗂️ 空间坐标: 不同的仿射变换矩阵")
    print("4. 📊 体积: 相似但不完全相同")
    print("\n可能原因:")
    print("• MaskInOrig: 标准化/配准后的空间")
    print("• MaskInRawData: 原始采集空间")
    print("• 代表同一解剖区域在不同坐标系中的表示")

if __name__ == "__main__":
    main()
