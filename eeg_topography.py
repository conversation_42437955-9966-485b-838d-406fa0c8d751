#!/usr/bin/env python3
"""
EEG Topographic Mapping Script
Converts EEG data from EDF files to topographic brain maps
"""

import numpy as np
import matplotlib.pyplot as plt
import mne
from mne.viz import plot_topomap
import os
import warnings

# Suppress some MNE warnings for cleaner output
warnings.filterwarnings('ignore', category=RuntimeWarning)

def load_eeg_data(file_path):
    """
    Load EEG data from EDF file
    
    Parameters:
    file_path (str): Path to the EDF file
    
    Returns:
    raw: MNE Raw object containing EEG data
    """
    try:
        # Load the EDF file
        raw = mne.io.read_raw_edf(file_path, preload=True, verbose=False)
        print(f"Successfully loaded EEG data from {file_path}")
        print(f"Number of channels: {raw.info['nchan']}")
        print(f"Sampling frequency: {raw.info['sfreq']} Hz")
        print(f"Duration: {raw.times[-1]:.2f} seconds")
        
        return raw
    except Exception as e:
        print(f"Error loading EEG data: {e}")
        return None

def set_standard_montage(raw):
    """
    Set standard electrode positions for EEG data

    Parameters:
    raw: MNE Raw object

    Returns:
    raw: MNE Raw object with electrode positions set
    """
    try:
        # Try to set a standard 10-20 montage
        montage = mne.channels.make_standard_montage('standard_1020')
        raw.set_montage(montage, match_case=False, on_missing='ignore')
        print("Applied standard 10-20 electrode montage")
        return raw
    except Exception as e:
        print(f"Could not set standard montage: {e}")
        return raw

def create_topographic_map(raw, time_point=None, frequency_band=None, save_path=None):
    """
    Create topographic map from EEG data

    Parameters:
    raw: MNE Raw object
    time_point (float): Specific time point to plot (in seconds). If None, uses average
    frequency_band (tuple): Frequency band to filter (low, high). If None, uses broadband
    save_path (str): Path to save the figure. If None, displays the plot
    """

    # Make a copy to avoid modifying original data
    raw_copy = raw.copy()

    # Set standard montage if no electrode positions are available
    raw_copy = set_standard_montage(raw_copy)

    # Apply frequency filtering if specified
    if frequency_band:
        low_freq, high_freq = frequency_band
        raw_copy.filter(low_freq, high_freq, fir_design='firwin', verbose=False)
        print(f"Applied {low_freq}-{high_freq} Hz bandpass filter")

    # Get EEG channels only
    raw_copy.pick(picks='eeg', exclude='bads')

    # Get data
    data = raw_copy.get_data()

    # Select time point or compute average
    if time_point is not None:
        # Find the closest time index
        time_idx = np.argmin(np.abs(raw_copy.times - time_point))
        data_to_plot = data[:, time_idx]
        title_suffix = f" at {time_point:.2f}s"
    else:
        # Use RMS (root mean square) across time for average activity
        data_to_plot = np.sqrt(np.mean(data**2, axis=1))
        title_suffix = " (RMS average)"

    # Create the topographic map
    fig, ax = plt.subplots(1, 1, figsize=(10, 8))

    try:
        # Try to get channel positions from montage
        info = raw_copy.info

        # Create topographic plot
        im, _ = plot_topomap(data_to_plot, info,
                            ch_type='eeg',
                            contours=6,
                            cmap='RdBu_r',
                            axes=ax,
                            show=False)

        # Add title and colorbar
        title = f"EEG Topographic Map{title_suffix}"
        if frequency_band:
            title += f" ({frequency_band[0]}-{frequency_band[1]} Hz)"

        ax.set_title(title, fontsize=14, fontweight='bold')

        # Add colorbar
        cbar = plt.colorbar(im, ax=ax, shrink=0.8)
        cbar.set_label('Amplitude (µV)', rotation=270, labelpad=20)

        plt.tight_layout()

        # Save or display
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Topographic map saved to {save_path}")
        else:
            plt.show()

    except Exception as e:
        print(f"Error creating topographic map: {e}")
        print("Creating alternative visualization...")

        # Alternative: Create a simple channel activity plot
        ax.clear()
        channels = raw_copy.ch_names
        y_pos = np.arange(len(channels))

        ax.barh(y_pos, data_to_plot)
        ax.set_yticks(y_pos)
        ax.set_yticklabels(channels)
        ax.set_xlabel('Amplitude (µV)')
        ax.set_title(f"EEG Channel Activity{title_suffix}")
        ax.grid(True, alpha=0.3)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Channel activity plot saved to {save_path}")
        else:
            plt.show()

    return fig

def create_multiple_topomaps(raw, save_dir="topomaps"):
    """
    Create multiple topographic maps for different frequency bands and time points
    
    Parameters:
    raw: MNE Raw object
    save_dir (str): Directory to save the maps
    """
    
    # Create output directory
    os.makedirs(save_dir, exist_ok=True)
    
    # Define frequency bands
    freq_bands = {
        'delta': (0.5, 4),
        'theta': (4, 8),
        'alpha': (8, 13),
        'beta': (13, 30),
        'gamma': (30, 50),
        'broadband': None
    }
    
    # Create maps for different frequency bands (average across time)
    print("Creating frequency band topographic maps...")
    for band_name, freq_range in freq_bands.items():
        save_path = os.path.join(save_dir, f"topomap_{band_name}.png")
        fig = create_topographic_map(raw, frequency_band=freq_range, save_path=save_path)
        plt.close(fig)
    
    # Create maps at different time points (broadband)
    print("Creating time-specific topographic maps...")
    duration = raw.times[-1]
    time_points = np.linspace(0, duration, min(5, int(duration)))  # Max 5 time points
    
    for i, time_point in enumerate(time_points):
        save_path = os.path.join(save_dir, f"topomap_time_{time_point:.1f}s.png")
        fig = create_topographic_map(raw, time_point=time_point, save_path=save_path)
        plt.close(fig)
    
    print(f"All topographic maps saved in '{save_dir}' directory")

def main():
    """
    Main function to process EEG file and create topographic maps
    """
    
    # EDF file path
    edf_file = "PN00-1.edf"
    
    if not os.path.exists(edf_file):
        print(f"Error: EDF file '{edf_file}' not found!")
        return
    
    # Load EEG data
    print("Loading EEG data...")
    raw = load_eeg_data(edf_file)
    
    if raw is None:
        return
    
    # Create a simple topographic map (RMS average, broadband)
    print("\nCreating basic topographic map...")
    create_topographic_map(raw, save_path="basic_topomap.png")
    
    # Create multiple topographic maps
    print("\nCreating comprehensive topographic maps...")
    create_multiple_topomaps(raw)
    
    print("\nEEG topographic mapping completed!")
    print("Generated files:")
    print("- basic_topomap.png: Basic RMS topographic map")
    print("- topomaps/: Directory with frequency band and time-specific maps")

if __name__ == "__main__":
    main()
