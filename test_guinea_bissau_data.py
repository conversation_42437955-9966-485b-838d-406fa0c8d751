"""
几内亚比绍EEG数据测试脚本

该脚本用于验证几内亚比绍EEG数据的加载和基本处理功能
"""

import pandas as pd
import numpy as np
import gzip
import logging
from pathlib import Path
import time
import json

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_data_loading():
    """测试数据加载功能"""
    try:
        logger.info("开始测试几内亚比绍EEG数据加载")
        
        # 数据路径
        data_root = Path("1252141")
        metadata_path = data_root / "metadata_guineabissau.csv"
        eeg_dir = data_root / "EEGs_Guinea-Bissau"
        
        # 检查路径存在性
        if not data_root.exists():
            logger.error(f"数据根目录不存在: {data_root}")
            return False
            
        if not metadata_path.exists():
            logger.error(f"元数据文件不存在: {metadata_path}")
            return False
            
        if not eeg_dir.exists():
            logger.error(f"EEG数据目录不存在: {eeg_dir}")
            return False
            
        # 加载元数据
        logger.info("加载元数据...")
        metadata = pd.read_csv(metadata_path)
        logger.info(f"元数据加载成功，共 {len(metadata)} 个被试")
        
        # 显示元数据基本信息
        logger.info("元数据列名:")
        for col in metadata.columns:
            logger.info(f"  - {col}")
            
        # 显示组别分布
        if 'Group' in metadata.columns:
            group_counts = metadata['Group'].value_counts()
            logger.info("组别分布:")
            for group, count in group_counts.items():
                logger.info(f"  - {group}: {count}")
                
        # 测试加载几个EEG文件
        logger.info("测试EEG文件加载...")
        test_subjects = metadata['subject.id'].head(3).tolist()  # 测试前3个被试
        
        successful_loads = 0
        test_results = []
        
        for subject_id in test_subjects:
            try:
                logger.info(f"测试被试 {subject_id}...")
                
                # 构建文件路径
                eeg_file = eeg_dir / f"signal-{subject_id}.csv.gz"
                
                if not eeg_file.exists():
                    logger.warning(f"EEG文件不存在: {eeg_file}")
                    test_results.append({
                        'subject_id': subject_id,
                        'status': 'file_not_found',
                        'error': f"文件不存在: {eeg_file}"
                    })
                    continue
                    
                # 加载和分析文件
                start_time = time.time()
                
                with gzip.open(eeg_file, 'rt') as f:
                    # 读取前几行分析结构
                    header = f.readline().strip().split(',')
                    first_data_line = f.readline().strip().split(',')
                    
                    # 读取更多行进行分析
                    sample_lines = []
                    for i in range(100):  # 读取100行样本
                        line = f.readline()
                        if not line:
                            break
                        sample_lines.append(line.strip().split(','))
                        
                loading_time = time.time() - start_time
                
                # 分析EEG通道
                eeg_channels = ['AF3', 'AF4', 'F3', 'F4', 'F7', 'F8', 'FC5', 'FC6', 
                               'O1', 'O2', 'P7', 'P8', 'T7', 'T8']
                
                available_channels = [ch for ch in eeg_channels if ch.strip('"') in header]
                
                # 分析数据质量
                if sample_lines and available_channels:
                    try:
                        # 提取EEG数据样本
                        eeg_indices = [header.index(ch) for ch in available_channels if ch in header]
                        eeg_sample_data = []
                        
                        for line in sample_lines:
                            if len(line) > max(eeg_indices):
                                try:
                                    eeg_values = [float(line[idx]) for idx in eeg_indices]
                                    eeg_sample_data.append(eeg_values)
                                except ValueError:
                                    continue
                                    
                        if eeg_sample_data:
                            eeg_array = np.array(eeg_sample_data)
                            signal_std = np.std(eeg_array)
                            signal_mean = np.mean(eeg_array)
                            signal_range = np.max(eeg_array) - np.min(eeg_array)
                        else:
                            signal_std = signal_mean = signal_range = 0
                            
                    except Exception as e:
                        logger.warning(f"数据质量分析失败: {e}")
                        signal_std = signal_mean = signal_range = 0
                else:
                    signal_std = signal_mean = signal_range = 0
                    
                # 估计文件大小和数据量
                file_size = eeg_file.stat().st_size / (1024 * 1024)  # MB
                estimated_samples = len(sample_lines) * 100  # 粗略估计
                
                test_results.append({
                    'subject_id': subject_id,
                    'status': 'success',
                    'loading_time': loading_time,
                    'file_size_mb': file_size,
                    'total_columns': len(header),
                    'available_eeg_channels': len(available_channels),
                    'eeg_channels': available_channels,
                    'estimated_samples': estimated_samples,
                    'signal_std': signal_std,
                    'signal_mean': signal_mean,
                    'signal_range': signal_range
                })
                
                successful_loads += 1
                
                logger.info(f"  成功: {loading_time:.3f}秒, {file_size:.1f}MB, "
                          f"{len(available_channels)}个EEG通道")
                          
            except Exception as e:
                logger.error(f"被试 {subject_id} 测试失败: {e}")
                test_results.append({
                    'subject_id': subject_id,
                    'status': 'error',
                    'error': str(e)
                })
                
        # 生成测试报告
        logger.info("="*50)
        logger.info("测试结果摘要:")
        logger.info(f"测试被试数: {len(test_subjects)}")
        logger.info(f"成功加载: {successful_loads}")
        logger.info(f"成功率: {successful_loads/len(test_subjects)*100:.1f}%")
        
        # 分析成功的测试结果
        successful_results = [r for r in test_results if r['status'] == 'success']
        
        if successful_results:
            avg_channels = np.mean([r['available_eeg_channels'] for r in successful_results])
            avg_file_size = np.mean([r['file_size_mb'] for r in successful_results])
            avg_loading_time = np.mean([r['loading_time'] for r in successful_results])
            
            logger.info(f"平均EEG通道数: {avg_channels:.1f}")
            logger.info(f"平均文件大小: {avg_file_size:.1f}MB")
            logger.info(f"平均加载时间: {avg_loading_time:.3f}秒")
            
            # 显示数据质量统计
            valid_quality_results = [r for r in successful_results if r['signal_std'] > 0]
            if valid_quality_results:
                avg_std = np.mean([r['signal_std'] for r in valid_quality_results])
                avg_range = np.mean([r['signal_range'] for r in valid_quality_results])
                
                logger.info(f"平均信号标准差: {avg_std:.2e}")
                logger.info(f"平均信号范围: {avg_range:.2e}")
                
        # 保存详细测试结果
        output_file = "guinea_bissau_data_test_results.json"
        with open(output_file, 'w') as f:
            json.dump({
                'test_summary': {
                    'total_subjects_tested': len(test_subjects),
                    'successful_loads': successful_loads,
                    'success_rate': successful_loads/len(test_subjects),
                    'test_timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
                },
                'detailed_results': test_results
            }, f, indent=2, default=str)
            
        logger.info(f"详细测试结果已保存到: {output_file}")
        
        return successful_loads > 0
        
    except Exception as e:
        logger.error(f"数据加载测试失败: {e}")
        return False


def test_mne_integration():
    """测试MNE集成"""
    try:
        logger.info("测试MNE集成...")
        
        # 尝试导入MNE
        try:
            import mne
            logger.info(f"MNE版本: {mne.__version__}")
        except ImportError:
            logger.error("MNE库未安装，无法进行集成测试")
            return False
            
        # 测试加载一个EEG文件并转换为MNE格式
        data_root = Path("1252141")
        metadata_path = data_root / "metadata_guineabissau.csv"
        eeg_dir = data_root / "EEGs_Guinea-Bissau"
        
        if not metadata_path.exists():
            logger.error("元数据文件不存在，跳过MNE集成测试")
            return False
            
        # 加载元数据并选择一个测试被试
        metadata = pd.read_csv(metadata_path)
        test_subject_id = metadata['subject.id'].iloc[0]
        
        logger.info(f"使用被试 {test_subject_id} 进行MNE集成测试")
        
        # 加载EEG数据
        eeg_file = eeg_dir / f"signal-{test_subject_id}.csv.gz"
        
        if not eeg_file.exists():
            logger.error(f"测试EEG文件不存在: {eeg_file}")
            return False
            
        # 读取CSV数据
        with gzip.open(eeg_file, 'rt') as f:
            df = pd.read_csv(f, nrows=1000)  # 只读取前1000行进行测试
            
        # 提取EEG通道
        eeg_channels = ['AF3', 'AF4', 'F3', 'F4', 'F7', 'F8', 'FC5', 'FC6', 
                       'O1', 'O2', 'P7', 'P8', 'T7', 'T8']
        
        available_channels = [ch for ch in eeg_channels if ch in df.columns]
        
        if len(available_channels) < 5:
            logger.error(f"可用EEG通道数量不足: {len(available_channels)}")
            return False
            
        # 提取数据并转换单位
        data = df[available_channels].values.T * 1e-6  # 转换为伏特
        
        # 创建MNE Info对象
        info = mne.create_info(
            ch_names=available_channels,
            sfreq=128,  # 假设采样率为128Hz
            ch_types=['eeg'] * len(available_channels),
            verbose=False
        )
        
        # 创建Raw对象
        raw = mne.io.RawArray(data, info, verbose=False)
        
        # 基本验证
        logger.info(f"MNE Raw对象创建成功:")
        logger.info(f"  通道数: {raw.info['nchan']}")
        logger.info(f"  采样率: {raw.info['sfreq']}Hz")
        logger.info(f"  持续时间: {raw.times[-1]:.1f}秒")
        logger.info(f"  数据形状: {raw.get_data().shape}")
        
        # 测试基本操作
        try:
            # 测试滤波
            raw_filtered = raw.copy().filter(l_freq=0.5, h_freq=50, verbose=False)
            logger.info("滤波测试成功")
            
            # 测试数据提取
            data_array = raw.get_data()
            logger.info(f"数据提取成功，数据范围: [{np.min(data_array):.2e}, {np.max(data_array):.2e}]")
            
            # 测试电极位置设置
            try:
                montage = mne.channels.make_standard_montage('standard_1020')
                raw.set_montage(montage, match_case=False, on_missing='ignore', verbose=False)
                logger.info("电极位置设置成功")
            except Exception as e:
                logger.warning(f"电极位置设置失败: {e}")
                
        except Exception as e:
            logger.error(f"MNE基本操作测试失败: {e}")
            return False
            
        logger.info("MNE集成测试成功")
        return True
        
    except Exception as e:
        logger.error(f"MNE集成测试失败: {e}")
        return False


def main():
    """主函数"""
    logger.info("开始几内亚比绍EEG数据测试")
    logger.info("="*60)
    
    try:
        # 测试1: 数据加载
        logger.info("测试1: 数据加载功能")
        data_loading_success = test_data_loading()
        
        # 测试2: MNE集成
        logger.info("\n测试2: MNE集成")
        mne_integration_success = test_mne_integration()
        
        # 总结
        logger.info("\n" + "="*60)
        logger.info("测试总结:")
        logger.info(f"数据加载测试: {'通过' if data_loading_success else '失败'}")
        logger.info(f"MNE集成测试: {'通过' if mne_integration_success else '失败'}")
        
        if data_loading_success and mne_integration_success:
            logger.info("所有测试通过！系统可以处理几内亚比绍EEG数据")
            return True
        else:
            logger.error("部分测试失败，需要进一步调试")
            return False
            
    except Exception as e:
        logger.error(f"测试执行失败: {e}")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
