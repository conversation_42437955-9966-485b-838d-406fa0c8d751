#!/usr/bin/env python3
"""
Analyze EEG file electrode configuration
"""

import mne
import numpy as np
import pandas as pd

def analyze_eeg_file(file_path):
    """
    Comprehensive analysis of EEG file electrode configuration
    """
    print("=== EEG File Analysis ===")
    print(f"File: {file_path}")
    
    # Load the EDF file
    raw = mne.io.read_raw_edf(file_path, preload=True, verbose=False)
    
    print(f"\n=== Basic Information ===")
    print(f"Number of channels: {raw.info['nchan']}")
    print(f"Sampling frequency: {raw.info['sfreq']} Hz")
    print(f"Duration: {raw.times[-1]:.2f} seconds ({raw.times[-1]/60:.1f} minutes)")
    print(f"Number of samples: {len(raw.times)}")
    
    print(f"\n=== Channel Information ===")
    print("Channel names:")
    for i, ch_name in enumerate(raw.ch_names):
        print(f"  {i+1:2d}. {ch_name}")
    
    print(f"\n=== Channel Types ===")
    ch_types = raw.get_channel_types()
    unique_types = set(ch_types)
    for ch_type in unique_types:
        count = ch_types.count(ch_type)
        print(f"  {ch_type}: {count} channels")
    
    print(f"\n=== Electrode Positions ===")
    print(f"Has digitization info: {raw.info['dig'] is not None}")
    if raw.info['dig']:
        print(f"Number of digitization points: {len(raw.info['dig'])}")
    else:
        print("No digitization points found")
    
    # Check if channels have locations
    has_locations = []
    for ch in raw.info['chs']:
        loc = ch['loc']
        has_loc = not np.allclose(loc[:3], 0)
        has_locations.append(has_loc)
        
    print(f"Channels with 3D locations: {sum(has_locations)}/{len(has_locations)}")
    
    print(f"\n=== Channel Details ===")
    for i, ch in enumerate(raw.info['chs']):
        loc = ch['loc'][:3]
        has_loc = not np.allclose(loc, 0)
        print(f"  {ch['ch_name']:12s} | Type: {ch['kind']:2d} | Loc: {loc} | Has pos: {has_loc}")
    
    # Try to match with standard montages
    print(f"\n=== Standard Montage Matching ===")
    
    # Clean channel names for matching
    clean_names = []
    for name in raw.ch_names:
        # Remove 'EEG ' prefix if present
        clean_name = name.replace('EEG ', '').strip()
        clean_names.append(clean_name)
    
    print("Cleaned channel names:")
    for orig, clean in zip(raw.ch_names, clean_names):
        print(f"  {orig} -> {clean}")
    
    # Try different standard montages
    montage_names = ['standard_1020', 'standard_1005', 'biosemi64', 'easycap-M1']
    
    for montage_name in montage_names:
        try:
            montage = mne.channels.make_standard_montage(montage_name)
            print(f"\n--- {montage_name} montage ---")
            print(f"Available positions: {len(montage.ch_names)}")
            
            # Check matches
            matches = []
            for clean_name in clean_names:
                if clean_name in montage.ch_names:
                    matches.append(clean_name)
                else:
                    # Try case variations
                    for mont_ch in montage.ch_names:
                        if clean_name.lower() == mont_ch.lower():
                            matches.append(mont_ch)
                            break
            
            print(f"Matching channels: {len(matches)}/{len(clean_names)}")
            print(f"Matches: {matches}")
            
            if len(matches) > len(clean_names) * 0.5:  # If more than 50% match
                print(f"*** Good match with {montage_name} ***")
                
        except Exception as e:
            print(f"Error with {montage_name}: {e}")
    
    # Data statistics
    print(f"\n=== Data Statistics ===")
    data = raw.get_data()
    print(f"Data shape: {data.shape}")
    print(f"Data range: {data.min():.2e} to {data.max():.2e}")
    print(f"Data mean: {data.mean():.2e}")
    print(f"Data std: {data.std():.2e}")
    
    # Channel-wise statistics
    print(f"\n=== Channel Statistics ===")
    for i, ch_name in enumerate(raw.ch_names):
        ch_data = data[i, :]
        print(f"  {ch_name:12s}: mean={ch_data.mean():8.2e}, std={ch_data.std():8.2e}, "
              f"min={ch_data.min():8.2e}, max={ch_data.max():8.2e}")
    
    return raw

def main():
    file_path = "PN00-1.edf"
    raw = analyze_eeg_file(file_path)
    
    print(f"\n=== Recommendations ===")
    print("1. The file appears to use standard EEG channel names with 'EEG ' prefix")
    print("2. No electrode positions are embedded in the file")
    print("3. Need to manually set electrode positions using standard montage")
    print("4. Channel names suggest 10-20 system compatibility")

if __name__ == "__main__":
    main()
