"""
EEG源定位系统 - 真实数据科研级别系统测试

该测试模块使用几内亚比绍的真实EEG数据进行完整的系统验证，包括：
1. 数据准备和验证
2. 系统功能测试
3. 性能基准测试
4. 结果质量评估
5. 错误处理和鲁棒性测试
6. 科研级别验证
"""

import unittest
import pandas as pd
import numpy as np
import gzip
import time
import psutil
import os
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import tempfile
import shutil
import yaml
import json

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class GuineaBissauDataValidator:
    """几内亚比绍数据验证器"""
    
    def __init__(self, data_root: str):
        self.data_root = Path(data_root)
        self.metadata_path = self.data_root / "metadata_guineabissau.csv"
        self.eeg_dir = self.data_root / "EEGs_Guinea-Bissau"
        
    def load_metadata(self) -> pd.DataFrame:
        """加载元数据"""
        try:
            metadata = pd.read_csv(self.metadata_path)
            logger.info(f"加载元数据成功，共 {len(metadata)} 个被试")
            return metadata
        except Exception as e:
            logger.error(f"加载元数据失败: {e}")
            raise
            
    def validate_data_integrity(self) -> Dict:
        """验证数据完整性"""
        try:
            metadata = self.load_metadata()
            validation_results = {
                'total_subjects': len(metadata),
                'missing_files': [],
                'corrupted_files': [],
                'data_quality_issues': [],
                'subject_statistics': {}
            }
            
            for idx, row in metadata.iterrows():
                subject_id = row['subject.id']
                eeg_file = self.eeg_dir / f"signal-{subject_id}.csv.gz"
                
                # 检查文件存在性
                if not eeg_file.exists():
                    validation_results['missing_files'].append(subject_id)
                    continue
                    
                # 检查文件完整性和格式
                try:
                    data_info = self._analyze_eeg_file(eeg_file)
                    validation_results['subject_statistics'][subject_id] = data_info
                    
                    # 检查数据质量
                    if data_info['n_channels'] < 14:  # 期望至少14个EEG通道
                        validation_results['data_quality_issues'].append({
                            'subject_id': subject_id,
                            'issue': 'insufficient_channels',
                            'value': data_info['n_channels']
                        })
                        
                    if data_info['duration'] < 200:  # 期望至少200秒
                        validation_results['data_quality_issues'].append({
                            'subject_id': subject_id,
                            'issue': 'insufficient_duration',
                            'value': data_info['duration']
                        })
                        
                except Exception as e:
                    validation_results['corrupted_files'].append({
                        'subject_id': subject_id,
                        'error': str(e)
                    })
                    
            logger.info(f"数据完整性验证完成")
            logger.info(f"缺失文件: {len(validation_results['missing_files'])}")
            logger.info(f"损坏文件: {len(validation_results['corrupted_files'])}")
            logger.info(f"质量问题: {len(validation_results['data_quality_issues'])}")
            
            return validation_results
            
        except Exception as e:
            logger.error(f"数据完整性验证失败: {e}")
            raise
            
    def _analyze_eeg_file(self, file_path: Path) -> Dict:
        """分析单个EEG文件"""
        try:
            with gzip.open(file_path, 'rt') as f:
                # 读取前几行获取基本信息
                header = f.readline().strip().split(',')
                first_data_line = f.readline().strip().split(',')
                
                # 计算文件大小和估计行数
                file_size = file_path.stat().st_size
                
                # 读取更多行来估计采样率和数据质量
                sample_lines = []
                for i in range(min(1000, 100)):  # 读取最多1000行样本
                    line = f.readline()
                    if not line:
                        break
                    sample_lines.append(line.strip().split(','))
                        
            # 分析通道信息
            eeg_channels = [ch for ch in header if ch.strip('"') in 
                          ['AF3', 'AF4', 'F3', 'F4', 'F7', 'F8', 'FC5', 'FC6', 
                           'O1', 'O2', 'P7', 'P8', 'T7', 'T8']]
            
            # 估计数据统计
            if sample_lines:
                # 提取EEG通道数据进行质量分析
                eeg_data_sample = []
                for line in sample_lines:
                    if len(line) >= len(eeg_channels):
                        try:
                            eeg_values = [float(line[header.index(ch)]) for ch in eeg_channels]
                            eeg_data_sample.append(eeg_values)
                        except (ValueError, IndexError):
                            continue
                            
                if eeg_data_sample:
                    eeg_array = np.array(eeg_data_sample)
                    signal_std = np.std(eeg_array, axis=0)
                    signal_mean = np.mean(eeg_array, axis=0)
                else:
                    signal_std = np.zeros(len(eeg_channels))
                    signal_mean = np.zeros(len(eeg_channels))
            else:
                signal_std = np.zeros(len(eeg_channels))
                signal_mean = np.zeros(len(eeg_channels))
                
            # 估计总行数和时长
            estimated_lines = file_size // 200  # 粗略估计每行200字节
            estimated_duration = estimated_lines / 128  # 假设128Hz采样率
            
            return {
                'file_size_mb': file_size / (1024 * 1024),
                'n_channels': len(eeg_channels),
                'n_total_columns': len(header),
                'estimated_samples': estimated_lines,
                'duration': estimated_duration,
                'signal_std_mean': float(np.mean(signal_std)),
                'signal_mean_mean': float(np.mean(signal_mean)),
                'eeg_channels': eeg_channels
            }
            
        except Exception as e:
            logger.error(f"分析EEG文件失败 {file_path}: {e}")
            raise
            
    def select_test_subjects(self, n_subjects: int = 3) -> List[Dict]:
        """选择测试被试"""
        try:
            metadata = self.load_metadata()
            validation_results = self.validate_data_integrity()
            
            # 筛选高质量数据
            good_subjects = []
            for subject_id, stats in validation_results['subject_statistics'].items():
                if (stats['n_channels'] >= 14 and 
                    stats['duration'] >= 200 and
                    stats['signal_std_mean'] > 0):
                    
                    subject_row = metadata[metadata['subject.id'] == subject_id].iloc[0]
                    good_subjects.append({
                        'subject_id': subject_id,
                        'group': subject_row['Group'],
                        'eyes_condition': subject_row['Eyes.condition'],
                        'recorded_period': subject_row['recordedPeriod'],
                        'data_stats': stats
                    })
                    
            # 选择代表性被试：包含不同组别和条件
            selected_subjects = []
            
            # 优先选择癫痫组
            epilepsy_subjects = [s for s in good_subjects if s['group'] == 'Epilepsy']
            if epilepsy_subjects:
                selected_subjects.append(epilepsy_subjects[0])
                
            # 选择对照组
            control_subjects = [s for s in good_subjects if s['group'] == 'Control']
            if control_subjects:
                selected_subjects.append(control_subjects[0])
                
            # 如果需要更多被试，随机选择
            remaining_subjects = [s for s in good_subjects if s not in selected_subjects]
            while len(selected_subjects) < n_subjects and remaining_subjects:
                selected_subjects.append(remaining_subjects.pop(0))
                
            logger.info(f"选择了 {len(selected_subjects)} 个测试被试")
            for subject in selected_subjects:
                logger.info(f"  被试 {subject['subject_id']}: {subject['group']}, "
                          f"{subject['data_stats']['n_channels']}通道, "
                          f"{subject['data_stats']['duration']:.1f}秒")
                
            return selected_subjects
            
        except Exception as e:
            logger.error(f"选择测试被试失败: {e}")
            raise


class RealDataEEGLoader:
    """真实数据EEG加载器"""
    
    def __init__(self, data_root: str):
        self.data_root = Path(data_root)
        self.eeg_dir = self.data_root / "EEGs_Guinea-Bissau"
        
    def load_eeg_data(self, subject_id: int) -> 'mne.io.Raw':
        """加载EEG数据并转换为MNE格式"""
        try:
            import mne
            
            eeg_file = self.eeg_dir / f"signal-{subject_id}.csv.gz"
            
            # 读取CSV数据
            with gzip.open(eeg_file, 'rt') as f:
                df = pd.read_csv(f)
                
            # 提取EEG通道
            eeg_channels = ['AF3', 'AF4', 'F3', 'F4', 'F7', 'F8', 'FC5', 'FC6', 
                           'O1', 'O2', 'P7', 'P8', 'T7', 'T8']
            
            # 检查可用通道
            available_channels = [ch for ch in eeg_channels if ch in df.columns]
            
            if len(available_channels) < 10:
                raise ValueError(f"可用EEG通道数量不足: {len(available_channels)}")
                
            # 提取数据并转换单位（假设原始数据为微伏）
            data = df[available_channels].values.T * 1e-6  # 转换为伏特
            
            # 创建MNE Info对象
            info = mne.create_info(
                ch_names=available_channels,
                sfreq=128,  # 根据数据分析确定的采样率
                ch_types=['eeg'] * len(available_channels)
            )
            
            # 创建Raw对象
            raw = mne.io.RawArray(data, info)
            
            # 设置标准电极位置
            try:
                montage = mne.channels.make_standard_montage('standard_1020')
                raw.set_montage(montage, match_case=False, on_missing='ignore')
            except Exception as e:
                logger.warning(f"设置电极位置失败: {e}")
                
            logger.info(f"成功加载被试 {subject_id} 的EEG数据: "
                       f"{len(available_channels)}通道, {data.shape[1]}样本点")
            
            return raw
            
        except Exception as e:
            logger.error(f"加载EEG数据失败 (被试 {subject_id}): {e}")
            raise


class TestRealDataValidation(unittest.TestCase):
    """真实数据验证测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        cls.data_root = "1252141"  # 数据根目录
        cls.validator = GuineaBissauDataValidator(cls.data_root)
        cls.eeg_loader = RealDataEEGLoader(cls.data_root)
        
        # 创建临时测试目录
        cls.temp_dir = tempfile.mkdtemp()
        cls.test_config_path = Path(cls.temp_dir) / 'test_config.yaml'
        
        # 创建测试配置
        test_config = {
            'data_paths': {
                'guinea_bissau_dir': cls.data_root + '/EEGs_Guinea-Bissau',
                'output_dir': cls.temp_dir + '/results'
            },
            'eeg_processing': {
                'sampling_rate': 128,
                'filtering': {'highpass': 0.5, 'lowpass': 50, 'notch': 50},
                'preprocessing': {'remove_dc': True, 'detrend': True, 'baseline_correction': True},
                'artifact_removal': {'enable_ica': True, 'ica_components': 10},
                'channel_quality': {'bad_channel_threshold': 3.0, 'correlation_threshold': 0.3}
            },
            'head_modeling': {
                'tissue_segmentation': {'accuracy_threshold': 1.0},
                'individual_processing': {'enable_registration': True}
            },
            'bem_modeling': {
                'model_type': '3_layer',  # 使用3层模型加快测试
                'conductivity': {'scalp': 0.33, 'skull': 0.0042, 'brain': 0.33},
                'mesh': {'scalp_density': 1024, 'skull_density': 1024, 'brain_density': 1024},
                'numerical_accuracy': 1e-6
            },
            'source_localization': {
                'source_space': {'spacing': 10, 'surface_type': 'white'},
                'inverse_methods': ['sloreta', 'mne'],
                'regularization': {'lambda_auto': False, 'lambda_value': 0.1}
            },
            'visualization': {
                'brain_3d': {'backend': 'plotly', 'surface_alpha': 0.8, 'colormap': 'hot'},
                'output_formats': ['png', 'html']
            }
        }
        
        with open(cls.test_config_path, 'w') as f:
            yaml.dump(test_config, f)
            
        logger.info(f"测试环境初始化完成，数据根目录: {cls.data_root}")
        
    @classmethod
    def tearDownClass(cls):
        """测试类清理"""
        shutil.rmtree(cls.temp_dir)
        logger.info("测试环境清理完成")
        
    def test_01_data_integrity_validation(self):
        """测试1: 数据完整性验证"""
        logger.info("开始数据完整性验证测试")
        
        try:
            validation_results = self.validator.validate_data_integrity()
            
            # 验证基本统计
            self.assertGreater(validation_results['total_subjects'], 90)
            self.assertLess(len(validation_results['missing_files']), 5)  # 允许少量缺失
            self.assertLess(len(validation_results['corrupted_files']), 5)  # 允许少量损坏
            
            # 验证数据质量
            good_subjects = 0
            for subject_id, stats in validation_results['subject_statistics'].items():
                if stats['n_channels'] >= 14 and stats['duration'] >= 200:
                    good_subjects += 1
                    
            self.assertGreater(good_subjects, 50)  # 至少50个高质量被试
            
            logger.info(f"数据完整性验证通过: {good_subjects}个高质量被试")
            
        except Exception as e:
            self.fail(f"数据完整性验证失败: {e}")
            
    def test_02_eeg_data_loading(self):
        """测试2: EEG数据加载"""
        logger.info("开始EEG数据加载测试")
        
        try:
            # 选择测试被试
            test_subjects = self.validator.select_test_subjects(n_subjects=2)
            self.assertGreaterEqual(len(test_subjects), 1)
            
            for subject in test_subjects[:2]:  # 测试前2个被试
                subject_id = subject['subject_id']
                
                # 加载EEG数据
                raw = self.eeg_loader.load_eeg_data(subject_id)
                
                # 验证数据格式
                self.assertIsNotNone(raw)
                self.assertGreaterEqual(raw.info['nchan'], 10)
                self.assertEqual(raw.info['sfreq'], 128)
                self.assertGreater(raw.times[-1], 200)  # 至少200秒
                
                # 验证数据质量
                data = raw.get_data()
                self.assertFalse(np.any(np.isnan(data)))
                self.assertFalse(np.any(np.isinf(data)))
                self.assertGreater(np.std(data), 1e-8)  # 有意义的信号变化
                
                logger.info(f"被试 {subject_id} EEG数据加载成功: "
                          f"{raw.info['nchan']}通道, {raw.times[-1]:.1f}秒")
                
        except Exception as e:
            self.fail(f"EEG数据加载测试失败: {e}")
            
    def test_03_system_integration_with_real_data(self):
        """测试3: 真实数据系统集成测试"""
        logger.info("开始真实数据系统集成测试")
        
        try:
            # 导入系统模块
            import sys
            sys.path.append('..')
            from main import EEGSourceLocalizationSystem
            
            # 初始化系统
            system = EEGSourceLocalizationSystem(str(self.test_config_path))
            
            # 选择一个测试被试
            test_subjects = self.validator.select_test_subjects(n_subjects=1)
            self.assertGreaterEqual(len(test_subjects), 1)
            
            subject = test_subjects[0]
            subject_id = subject['subject_id']
            
            # 加载真实EEG数据
            real_eeg_data = self.eeg_loader.load_eeg_data(subject_id)
            
            # 创建模拟MRI数据（因为没有真实MRI数据）
            mock_mri_data = self._create_mock_mri_data()
            
            # 模拟数据加载
            from unittest.mock import patch
            
            start_time = time.time()
            
            with patch.object(system.data_manager.eeg_loader, 'load_eeg_data', return_value=real_eeg_data):
                with patch.object(system.data_manager.mri_processor, 'load_mri_data', return_value=mock_mri_data):
                    
                    result = system.run_complete_analysis(
                        subject_id=f'real_subject_{subject_id}',
                        data_type='guinea_bissau',
                        method='sloreta',
                        output_dir=str(Path(self.temp_dir) / f'real_test_{subject_id}')
                    )
                    
            processing_time = time.time() - start_time
            
            # 验证结果
            self.assertIn('subject_id', result)
            self.assertIn('eeg_result', result)
            self.assertIn('source_result', result)
            self.assertIn('visualization_result', result)
            
            # 验证处理质量
            eeg_quality = result['eeg_result']['final_quality']['overall_score']
            source_quality = result['source_result']['quality_metrics']['overall_score']
            
            self.assertGreater(eeg_quality, 0.3)  # 真实数据可能质量较低
            self.assertGreater(source_quality, 0.3)
            
            logger.info(f"真实数据系统集成测试通过:")
            logger.info(f"  被试: {subject_id} ({subject['group']})")
            logger.info(f"  处理时间: {processing_time:.2f}秒")
            logger.info(f"  EEG质量: {eeg_quality:.3f}")
            logger.info(f"  源定位质量: {source_quality:.3f}")
            
        except Exception as e:
            self.fail(f"真实数据系统集成测试失败: {e}")
            
    def test_04_performance_benchmark_real_data(self):
        """测试4: 真实数据性能基准测试"""
        logger.info("开始真实数据性能基准测试")
        
        try:
            # 选择不同规模的测试被试
            test_subjects = self.validator.select_test_subjects(n_subjects=3)
            
            performance_results = []
            
            for subject in test_subjects:
                subject_id = subject['subject_id']
                
                # 监控系统资源
                process = psutil.Process(os.getpid())
                initial_memory = process.memory_info().rss / 1024 / 1024  # MB
                
                start_time = time.time()
                
                try:
                    # 加载和基本处理
                    raw = self.eeg_loader.load_eeg_data(subject_id)
                    
                    # 简单的预处理测试
                    raw_filtered = raw.copy().filter(l_freq=0.5, h_freq=50)
                    
                    processing_time = time.time() - start_time
                    final_memory = process.memory_info().rss / 1024 / 1024  # MB
                    memory_usage = final_memory - initial_memory
                    
                    performance_results.append({
                        'subject_id': subject_id,
                        'n_channels': raw.info['nchan'],
                        'duration': raw.times[-1],
                        'processing_time': processing_time,
                        'memory_usage': memory_usage,
                        'throughput': raw.times[-1] / processing_time  # 秒数据/秒处理时间
                    })
                    
                    logger.info(f"被试 {subject_id}: {processing_time:.2f}秒, {memory_usage:.1f}MB")
                    
                except Exception as e:
                    logger.warning(f"被试 {subject_id} 性能测试失败: {e}")
                    
            # 验证性能指标
            if performance_results:
                avg_throughput = np.mean([r['throughput'] for r in performance_results])
                max_memory = max([r['memory_usage'] for r in performance_results])
                
                self.assertGreater(avg_throughput, 5)  # 至少5倍实时处理速度
                self.assertLess(max_memory, 1000)  # 内存使用不超过1GB
                
                logger.info(f"性能基准测试通过:")
                logger.info(f"  平均吞吐量: {avg_throughput:.1f}x实时")
                logger.info(f"  最大内存使用: {max_memory:.1f}MB")
                
        except Exception as e:
            self.fail(f"性能基准测试失败: {e}")
            
    def test_05_data_quality_assessment(self):
        """测试5: 数据质量评估"""
        logger.info("开始数据质量评估测试")
        
        try:
            test_subjects = self.validator.select_test_subjects(n_subjects=3)
            
            quality_metrics = []
            
            for subject in test_subjects:
                subject_id = subject['subject_id']
                
                try:
                    raw = self.eeg_loader.load_eeg_data(subject_id)
                    data = raw.get_data()
                    
                    # 计算质量指标
                    metrics = {
                        'subject_id': subject_id,
                        'group': subject['group'],
                        'snr': self._calculate_snr(data),
                        'signal_variance': np.mean(np.var(data, axis=1)),
                        'channel_correlation': self._calculate_channel_correlation(data),
                        'artifact_ratio': self._estimate_artifact_ratio(data),
                        'frequency_content': self._analyze_frequency_content(data, raw.info['sfreq'])
                    }
                    
                    quality_metrics.append(metrics)
                    
                    logger.info(f"被试 {subject_id} ({subject['group']}): "
                              f"SNR={metrics['snr']:.1f}dB, "
                              f"相关性={metrics['channel_correlation']:.3f}")
                              
                except Exception as e:
                    logger.warning(f"被试 {subject_id} 质量评估失败: {e}")
                    
            # 分析组间差异
            if quality_metrics:
                epilepsy_metrics = [m for m in quality_metrics if m['group'] == 'Epilepsy']
                control_metrics = [m for m in quality_metrics if m['group'] == 'Control']
                
                if epilepsy_metrics and control_metrics:
                    epilepsy_snr = np.mean([m['snr'] for m in epilepsy_metrics])
                    control_snr = np.mean([m['snr'] for m in control_metrics])
                    
                    logger.info(f"组间SNR对比: 癫痫组={epilepsy_snr:.1f}dB, 对照组={control_snr:.1f}dB")
                    
                # 验证数据质量合理性
                avg_snr = np.mean([m['snr'] for m in quality_metrics])
                avg_correlation = np.mean([m['channel_correlation'] for m in quality_metrics])
                
                self.assertGreater(avg_snr, 5)  # 平均SNR > 5dB
                self.assertGreater(avg_correlation, 0.2)  # 平均通道相关性 > 0.2
                
                logger.info(f"数据质量评估通过: 平均SNR={avg_snr:.1f}dB, 平均相关性={avg_correlation:.3f}")
                
        except Exception as e:
            self.fail(f"数据质量评估失败: {e}")
            
    def _create_mock_mri_data(self):
        """创建模拟MRI数据"""
        import nibabel as nib
        
        data = np.random.randint(0, 255, (64, 64, 64), dtype=np.uint8)
        affine = np.eye(4)
        nii_img = nib.Nifti1Image(data, affine)
        
        return {'T1': nii_img}
        
    def _calculate_snr(self, data: np.ndarray) -> float:
        """计算信噪比"""
        signal_power = np.mean(np.var(data, axis=1))
        noise_estimate = np.mean(np.abs(np.diff(data, axis=1)))
        noise_power = noise_estimate ** 2
        
        if noise_power > 0:
            snr_db = 10 * np.log10(signal_power / noise_power)
            return max(snr_db, 0)
        return 0
        
    def _calculate_channel_correlation(self, data: np.ndarray) -> float:
        """计算通道间平均相关性"""
        corr_matrix = np.corrcoef(data)
        np.fill_diagonal(corr_matrix, 0)
        return np.mean(np.abs(corr_matrix))
        
    def _estimate_artifact_ratio(self, data: np.ndarray) -> float:
        """估计伪迹比例"""
        # 简单的伪迹检测：基于幅度阈值
        threshold = 5 * np.std(data)
        artifact_samples = np.sum(np.abs(data) > threshold)
        return artifact_samples / data.size
        
    def _analyze_frequency_content(self, data: np.ndarray, sfreq: float) -> Dict:
        """分析频率内容"""
        from scipy import signal
        
        # 计算平均功率谱
        freqs, psd = signal.welch(data, fs=sfreq, nperseg=min(1024, data.shape[1]//4))
        avg_psd = np.mean(psd, axis=0)
        
        # 分析各频段功率
        bands = {
            'delta': (1, 4),
            'theta': (4, 8),
            'alpha': (8, 13),
            'beta': (13, 30)
        }
        
        band_powers = {}
        for band_name, (low, high) in bands.items():
            band_mask = (freqs >= low) & (freqs <= high)
            if np.any(band_mask):
                band_powers[band_name] = np.mean(avg_psd[band_mask])
            else:
                band_powers[band_name] = 0
                
        return band_powers


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
