"""
EEG源定位系统 - 系统集成测试

该测试模块验证整个EEG源定位系统的完整性和稳定性，包括：
1. 模块间集成测试
2. 端到端流程测试
3. 数据质量验证
4. 性能基准测试
5. 错误处理测试
"""

import unittest
import numpy as np
import tempfile
import shutil
from pathlib import Path
import yaml
import time
import logging
from unittest.mock import Mock, patch, MagicMock

# 导入系统模块
import sys
sys.path.append('..')

from main import EEGSourceLocalizationSystem
from core.data_manager import DataManager
from core.head_modeling import HeadModeling
from core.eeg_processing import EEGProcessor
from algorithms.tissue_segmentation import HighPrecisionTissueSegmenter
from algorithms.bem_modeling import BEMModeler
from algorithms.inverse_solvers import SourceLocalizer
from core.visualization import BrainActivityVisualizer

# 配置测试日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestSystemIntegration(unittest.TestCase):
    """系统集成测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        cls.test_config = {
            'data_paths': {
                'eeg_data_dir': 'test_data/eeg',
                'mri_data_dir': 'test_data/mri',
                'template_dir': 'test_data/templates',
                'output_dir': 'test_results',
                'cache_dir': 'test_cache'
            },
            'eeg_processing': {
                'sampling_rate': 250,
                'filtering': {'highpass': 0.5, 'lowpass': 100, 'notch': 50},
                'preprocessing': {'remove_dc': True, 'detrend': True, 'baseline_correction': True},
                'artifact_removal': {'enable_ica': True, 'ica_components': 10},
                'channel_quality': {'bad_channel_threshold': 3.0, 'correlation_threshold': 0.3}
            },
            'head_modeling': {
                'mni_template': {'resolution': 1, 'template_type': 'MNI152_T1_1mm'},
                'tissue_segmentation': {
                    'method': 'freesurfer',
                    'tissues': ['scalp', 'skull', 'csf', 'gray', 'white'],
                    'accuracy_threshold': 1.0
                },
                'individual_processing': {'enable_registration': True, 'registration_method': 'ants'}
            },
            'bem_modeling': {
                'model_type': '5_layer',
                'conductivity': {'scalp': 0.33, 'skull': 0.0042, 'csf': 1.79, 'gray': 0.33, 'white': 0.14},
                'mesh': {'scalp_density': 1024, 'skull_density': 1024, 'brain_density': 1024},
                'numerical_accuracy': 1e-6
            },
            'source_localization': {
                'source_space': {'spacing': 10, 'surface_type': 'white'},
                'inverse_methods': ['sloreta', 'mne'],
                'regularization': {'lambda_auto': True, 'lambda_value': 0.1},
                'noise_cov': {'method': 'empirical', 'regularization': 0.1}
            },
            'visualization': {
                'brain_3d': {'backend': 'plotly', 'surface_alpha': 0.8, 'colormap': 'hot'},
                'topography': {'interpolation': 'cubic', 'contour_lines': 10, 'colorbar': True},
                'output_formats': ['png', 'html']
            }
        }
        
        # 创建临时配置文件
        cls.temp_dir = tempfile.mkdtemp()
        cls.config_path = Path(cls.temp_dir) / 'test_config.yaml'
        with open(cls.config_path, 'w') as f:
            yaml.dump(cls.test_config, f)
            
        logger.info(f"测试环境初始化完成，临时目录: {cls.temp_dir}")
        
    @classmethod
    def tearDownClass(cls):
        """测试类清理"""
        shutil.rmtree(cls.temp_dir)
        logger.info("测试环境清理完成")
        
    def setUp(self):
        """每个测试方法的初始化"""
        self.system = None
        
    def tearDown(self):
        """每个测试方法的清理"""
        if self.system:
            del self.system
            
    def test_system_initialization(self):
        """测试系统初始化"""
        try:
            self.system = EEGSourceLocalizationSystem(str(self.config_path))
            
            # 验证各模块是否正确初始化
            self.assertIsInstance(self.system.data_manager, DataManager)
            self.assertIsInstance(self.system.head_modeler, HeadModeling)
            self.assertIsInstance(self.system.eeg_processor, EEGProcessor)
            self.assertIsInstance(self.system.tissue_segmenter, HighPrecisionTissueSegmenter)
            self.assertIsInstance(self.system.bem_modeler, BEMModeler)
            self.assertIsInstance(self.system.source_localizer, SourceLocalizer)
            self.assertIsInstance(self.system.visualizer, BrainActivityVisualizer)
            
            logger.info("系统初始化测试通过")
            
        except Exception as e:
            self.fail(f"系统初始化失败: {e}")
            
    def test_mock_data_processing_pipeline(self):
        """测试模拟数据处理流程"""
        try:
            self.system = EEGSourceLocalizationSystem(str(self.config_path))
            
            # 创建模拟数据
            mock_eeg_data = self._create_mock_eeg_data()
            mock_mri_data = self._create_mock_mri_data()
            
            # 模拟数据加载
            with patch.object(self.system.data_manager.eeg_loader, 'load_eeg_data', return_value=mock_eeg_data):
                with patch.object(self.system.data_manager.mri_processor, 'load_mri_data', return_value=mock_mri_data):
                    
                    # 运行完整分析流程
                    result = self.system.run_complete_analysis(
                        subject_id='test_subject',
                        data_type='test',
                        method='sloreta',
                        output_dir=str(Path(self.temp_dir) / 'test_output')
                    )
                    
                    # 验证结果结构
                    self.assertIn('subject_id', result)
                    self.assertIn('method', result)
                    self.assertIn('eeg_result', result)
                    self.assertIn('head_model', result)
                    self.assertIn('source_result', result)
                    self.assertIn('visualization_result', result)
                    self.assertIn('analysis_report', result)
                    self.assertIn('processing_time', result)
                    
                    # 验证处理时间合理
                    self.assertGreater(result['processing_time'], 0)
                    self.assertLess(result['processing_time'], 300)  # 不超过5分钟
                    
                    logger.info("模拟数据处理流程测试通过")
                    
        except Exception as e:
            self.fail(f"模拟数据处理流程测试失败: {e}")
            
    def test_error_handling(self):
        """测试错误处理机制"""
        try:
            self.system = EEGSourceLocalizationSystem(str(self.config_path))
            
            # 测试无效被试ID
            with self.assertRaises(Exception):
                self.system.run_complete_analysis(
                    subject_id='invalid_subject',
                    data_type='test',
                    method='sloreta'
                )
                
            # 测试无效方法
            with self.assertRaises(Exception):
                mock_eeg_data = self._create_mock_eeg_data()
                mock_mri_data = self._create_mock_mri_data()
                
                with patch.object(self.system.data_manager.eeg_loader, 'load_eeg_data', return_value=mock_eeg_data):
                    with patch.object(self.system.data_manager.mri_processor, 'load_mri_data', return_value=mock_mri_data):
                        self.system.run_complete_analysis(
                            subject_id='test_subject',
                            data_type='test',
                            method='invalid_method'
                        )
                        
            logger.info("错误处理测试通过")
            
        except Exception as e:
            self.fail(f"错误处理测试失败: {e}")
            
    def test_configuration_validation(self):
        """测试配置验证"""
        try:
            # 测试有效配置
            self.system = EEGSourceLocalizationSystem(str(self.config_path))
            self.assertIsNotNone(self.system.config)
            
            # 测试无效配置文件
            invalid_config_path = Path(self.temp_dir) / 'invalid_config.yaml'
            with open(invalid_config_path, 'w') as f:
                f.write("invalid: yaml: content:")
                
            with self.assertRaises(Exception):
                EEGSourceLocalizationSystem(str(invalid_config_path))
                
            logger.info("配置验证测试通过")
            
        except Exception as e:
            self.fail(f"配置验证测试失败: {e}")
            
    def test_memory_usage(self):
        """测试内存使用情况"""
        try:
            import psutil
            import os
            
            process = psutil.Process(os.getpid())
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            self.system = EEGSourceLocalizationSystem(str(self.config_path))
            
            # 创建较大的模拟数据
            mock_eeg_data = self._create_mock_eeg_data(n_channels=64, n_timepoints=10000)
            mock_mri_data = self._create_mock_mri_data(shape=(128, 128, 128))
            
            with patch.object(self.system.data_manager.eeg_loader, 'load_eeg_data', return_value=mock_eeg_data):
                with patch.object(self.system.data_manager.mri_processor, 'load_mri_data', return_value=mock_mri_data):
                    
                    result = self.system.run_complete_analysis(
                        subject_id='test_subject',
                        data_type='test',
                        method='sloreta',
                        output_dir=str(Path(self.temp_dir) / 'memory_test_output')
                    )
                    
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = final_memory - initial_memory
            
            # 验证内存使用合理（不超过2GB）
            self.assertLess(memory_increase, 2048)
            
            logger.info(f"内存使用测试通过，内存增长: {memory_increase:.2f} MB")
            
        except ImportError:
            logger.warning("psutil未安装，跳过内存使用测试")
        except Exception as e:
            self.fail(f"内存使用测试失败: {e}")
            
    def test_output_file_generation(self):
        """测试输出文件生成"""
        try:
            self.system = EEGSourceLocalizationSystem(str(self.config_path))
            
            output_dir = Path(self.temp_dir) / 'output_test'
            
            mock_eeg_data = self._create_mock_eeg_data()
            mock_mri_data = self._create_mock_mri_data()
            
            with patch.object(self.system.data_manager.eeg_loader, 'load_eeg_data', return_value=mock_eeg_data):
                with patch.object(self.system.data_manager.mri_processor, 'load_mri_data', return_value=mock_mri_data):
                    
                    result = self.system.run_complete_analysis(
                        subject_id='test_subject',
                        data_type='test',
                        method='sloreta',
                        output_dir=str(output_dir)
                    )
                    
            # 验证输出目录存在
            self.assertTrue(output_dir.exists())
            
            # 验证关键文件存在
            expected_files = ['analysis_summary.yaml']
            for filename in expected_files:
                file_path = output_dir / filename
                self.assertTrue(file_path.exists(), f"缺少输出文件: {filename}")
                
            logger.info("输出文件生成测试通过")
            
        except Exception as e:
            self.fail(f"输出文件生成测试失败: {e}")
            
    def _create_mock_eeg_data(self, n_channels=32, n_timepoints=1000):
        """创建模拟EEG数据"""
        try:
            import mne
            
            # 创建模拟数据
            data = np.random.randn(n_channels, n_timepoints) * 1e-6  # 微伏级别
            
            # 创建通道信息
            ch_names = [f'EEG{i:03d}' for i in range(n_channels)]
            ch_types = ['eeg'] * n_channels
            
            info = mne.create_info(ch_names=ch_names, sfreq=250, ch_types=ch_types)
            raw = mne.io.RawArray(data, info)
            
            return raw
            
        except Exception as e:
            logger.error(f"创建模拟EEG数据失败: {e}")
            raise
            
    def _create_mock_mri_data(self, shape=(64, 64, 64)):
        """创建模拟MRI数据"""
        try:
            import nibabel as nib
            
            # 创建模拟MRI数据
            data = np.random.randint(0, 255, shape, dtype=np.uint8)
            
            # 创建仿射变换矩阵
            affine = np.eye(4)
            affine[:3, :3] = np.diag([1.0, 1.0, 1.0])  # 1mm分辨率
            
            # 创建NIfTI图像
            nii_img = nib.Nifti1Image(data, affine)
            
            return {'T1': nii_img}
            
        except Exception as e:
            logger.error(f"创建模拟MRI数据失败: {e}")
            raise


class TestPerformanceBenchmark(unittest.TestCase):
    """性能基准测试类"""
    
    def setUp(self):
        """测试初始化"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_path = Path(self.temp_dir) / 'benchmark_config.yaml'
        
        # 创建基准测试配置
        benchmark_config = {
            'data_paths': {'output_dir': str(Path(self.temp_dir) / 'benchmark_results')},
            'eeg_processing': {'sampling_rate': 250},
            'head_modeling': {'tissue_segmentation': {'accuracy_threshold': 1.0}},
            'bem_modeling': {'model_type': '3_layer', 'numerical_accuracy': 1e-6},
            'source_localization': {'regularization': {'lambda_auto': False, 'lambda_value': 0.1}},
            'visualization': {'output_formats': ['png']}
        }
        
        with open(self.config_path, 'w') as f:
            yaml.dump(benchmark_config, f)
            
    def tearDown(self):
        """测试清理"""
        shutil.rmtree(self.temp_dir)
        
    def test_processing_speed_benchmark(self):
        """测试处理速度基准"""
        try:
            system = EEGSourceLocalizationSystem(str(self.config_path))
            
            # 不同数据规模的基准测试
            test_cases = [
                {'n_channels': 16, 'n_timepoints': 500, 'expected_time': 60},
                {'n_channels': 32, 'n_timepoints': 1000, 'expected_time': 120},
                {'n_channels': 64, 'n_timepoints': 2000, 'expected_time': 240}
            ]
            
            for case in test_cases:
                with self.subTest(case=case):
                    mock_eeg_data = self._create_mock_eeg_data(
                        case['n_channels'], case['n_timepoints']
                    )
                    mock_mri_data = self._create_mock_mri_data()
                    
                    start_time = time.time()
                    
                    with patch.object(system.data_manager.eeg_loader, 'load_eeg_data', return_value=mock_eeg_data):
                        with patch.object(system.data_manager.mri_processor, 'load_mri_data', return_value=mock_mri_data):
                            
                            result = system.run_complete_analysis(
                                subject_id=f"benchmark_{case['n_channels']}ch",
                                data_type='test',
                                method='mne',  # 使用较快的方法
                                output_dir=str(Path(self.temp_dir) / f"benchmark_{case['n_channels']}")
                            )
                            
                    processing_time = time.time() - start_time
                    
                    # 验证处理时间在合理范围内
                    self.assertLess(processing_time, case['expected_time'],
                                   f"处理时间过长: {processing_time:.2f}s > {case['expected_time']}s")
                    
                    logger.info(f"基准测试 {case['n_channels']}通道: {processing_time:.2f}秒")
                    
        except Exception as e:
            self.fail(f"处理速度基准测试失败: {e}")
            
    def _create_mock_eeg_data(self, n_channels, n_timepoints):
        """创建模拟EEG数据"""
        import mne
        
        data = np.random.randn(n_channels, n_timepoints) * 1e-6
        ch_names = [f'EEG{i:03d}' for i in range(n_channels)]
        ch_types = ['eeg'] * n_channels
        
        info = mne.create_info(ch_names=ch_names, sfreq=250, ch_types=ch_types)
        raw = mne.io.RawArray(data, info)
        
        return raw
        
    def _create_mock_mri_data(self):
        """创建模拟MRI数据"""
        import nibabel as nib
        
        data = np.random.randint(0, 255, (32, 32, 32), dtype=np.uint8)
        affine = np.eye(4)
        nii_img = nib.Nifti1Image(data, affine)
        
        return {'T1': nii_img}


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
