#!/usr/bin/env python3
"""
Fix Training Issues for Epilepsy Lesion Localization
Addresses the fundamental problems causing poor performance:
- Loss scaling issues
- Gradient flow problems
- Data preprocessing validation
- Learning rate optimization
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import json
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# Import our modules
from training_pipeline import EpilepsyLocalizationModel, create_data_loaders
from epilepsy_localization_network import BoundingBoxLoss

# Set random seeds for reproducibility
torch.manual_seed(42)
np.random.seed(42)

class TrainingFixer:
    """Fix fundamental training issues"""
    
    def __init__(self, device):
        self.device = device
        
    def validate_data_preprocessing(self, data_loader):
        """Validate data preprocessing and identify issues"""
        print("Validating data preprocessing...")
        
        eeg_stats = {'min': [], 'max': [], 'mean': [], 'std': []}
        mask_stats = {'volumes': [], 'non_zero_ratio': []}
        
        for batch_idx, batch in enumerate(data_loader):
            if batch_idx >= 5:  # Check first 5 batches
                break
            
            # EEG data analysis
            eeg_data = batch['eeg_data']
            eeg_stats['min'].append(eeg_data.min().item())
            eeg_stats['max'].append(eeg_data.max().item())
            eeg_stats['mean'].append(eeg_data.mean().item())
            eeg_stats['std'].append(eeg_data.std().item())
            
            # Lesion mask analysis
            lesion_masks = batch['lesion_mask']
            for i in range(lesion_masks.shape[0]):
                mask = lesion_masks[i]
                volume = torch.sum(mask > 0).item()
                total_voxels = mask.numel()
                
                mask_stats['volumes'].append(volume)
                mask_stats['non_zero_ratio'].append(volume / total_voxels)
        
        print("Data Analysis Results:")
        print(f"  EEG value range: [{np.min(eeg_stats['min']):.3f}, {np.max(eeg_stats['max']):.3f}]")
        print(f"  EEG mean: {np.mean(eeg_stats['mean']):.3f} ± {np.std(eeg_stats['mean']):.3f}")
        print(f"  EEG std: {np.mean(eeg_stats['std']):.3f}")
        print(f"  Lesion volume range: [{np.min(mask_stats['volumes'])}, {np.max(mask_stats['volumes'])}] voxels")
        print(f"  Non-zero ratio: {np.mean(mask_stats['non_zero_ratio']):.6f}")
        
        # Identify issues
        issues = []
        if np.max(eeg_stats['max']) > 1000 or np.min(eeg_stats['min']) < -1000:
            issues.append("EEG values are not normalized - consider z-score normalization")
        if np.mean(eeg_stats['std']) < 0.1:
            issues.append("EEG data has very low variance - check preprocessing")
        if np.mean(mask_stats['non_zero_ratio']) < 0.001:
            issues.append("Lesion masks are very sparse - may cause training instability")
        
        if issues:
            print("⚠️  Identified Issues:")
            for issue in issues:
                print(f"    - {issue}")
        else:
            print("✅ Data preprocessing looks good")
        
        return eeg_stats, mask_stats, issues
    
    def analyze_loss_scaling(self, model, data_loader):
        """Analyze loss component scaling"""
        print("Analyzing loss component scaling...")
        
        model.eval()
        bbox_loss = BoundingBoxLoss()
        
        dice_losses = []
        iou_losses = []
        focal_losses = []
        
        with torch.no_grad():
            for batch_idx, batch in enumerate(data_loader):
                if batch_idx >= 3:  # Analyze first 3 batches
                    break
                
                eeg_data = batch['eeg_data'].to(self.device)
                temporal_sequence = batch['temporal_sequence'].to(self.device)
                lesion_masks = batch['lesion_mask'].to(self.device)
                
                try:
                    outputs = model(eeg_data, temporal_sequence)
                    loss_results = bbox_loss(outputs['bbox_params'], lesion_masks)
                    
                    dice_losses.append(loss_results['dice_loss'].item())
                    iou_losses.append(loss_results['iou_loss'].item())
                    focal_losses.append(loss_results['focal_loss'].item())
                    
                except Exception as e:
                    print(f"Error in batch {batch_idx}: {e}")
                    continue
        
        if not dice_losses:
            print("❌ No valid loss computations - model may have issues")
            return None
        
        print("Loss Component Analysis:")
        print(f"  Dice loss: {np.mean(dice_losses):.6f} ± {np.std(dice_losses):.6f}")
        print(f"  IoU loss: {np.mean(iou_losses):.6f} ± {np.std(iou_losses):.6f}")
        print(f"  Focal loss: {np.mean(focal_losses):.6f} ± {np.std(focal_losses):.6f}")
        
        # Check for scaling issues
        max_loss = max(np.mean(dice_losses), np.mean(iou_losses), np.mean(focal_losses))
        min_loss = min(np.mean(dice_losses), np.mean(iou_losses), np.mean(focal_losses))
        
        if max_loss > 100 * min_loss:
            print("⚠️  Loss components are poorly scaled - need normalization")
            return True
        else:
            print("✅ Loss components are reasonably scaled")
            return False
    
    def create_normalized_loss_function(self):
        """Create properly normalized loss function"""
        
        class NormalizedBoundingBoxLoss(BoundingBoxLoss):
            def __init__(self):
                super().__init__(dice_weight=0.4, iou_weight=0.4, focal_weight=0.2)
                
            def forward(self, box_params, target_masks):
                # Create predicted box masks
                pred_masks = self.create_box_mask(box_params['center'], box_params['size'])
                
                # Compute individual losses with proper normalization
                dice_loss_val = self.dice_loss(pred_masks, target_masks)
                iou_loss_val = self.iou_3d_loss(pred_masks, target_masks)
                focal_loss_val = self.focal_loss(pred_masks, target_masks)
                
                # Ensure losses are in [0, 1] range
                dice_loss_val = torch.clamp(dice_loss_val, 0, 1)
                iou_loss_val = torch.clamp(iou_loss_val, 0, 1)
                focal_loss_val = torch.clamp(focal_loss_val, 0, 1)
                
                # Compute weighted total loss
                total_loss = (self.dice_weight * dice_loss_val + 
                             self.iou_weight * iou_loss_val + 
                             self.focal_weight * focal_loss_val)
                
                return {
                    'total_loss': total_loss,
                    'dice_loss': dice_loss_val,
                    'iou_loss': iou_loss_val,
                    'focal_loss': focal_loss_val,
                    'pred_masks': pred_masks
                }
        
        return NormalizedBoundingBoxLoss()
    
    def test_gradient_flow(self, model, data_loader, criterion):
        """Test if gradients are flowing properly"""
        print("Testing gradient flow...")
        
        model.train()
        optimizer = optim.Adam(model.parameters(), lr=1e-4)
        
        # Test single batch
        batch = next(iter(data_loader))
        eeg_data = batch['eeg_data'].to(self.device)
        temporal_sequence = batch['temporal_sequence'].to(self.device)
        lesion_masks = batch['lesion_mask'].to(self.device)
        
        optimizer.zero_grad()
        
        try:
            outputs = model(eeg_data, temporal_sequence)
            loss_results = criterion(outputs['bbox_params'], lesion_masks)
            loss = loss_results['total_loss']
            
            print(f"  Forward pass successful, loss: {loss.item():.6f}")
            
            loss.backward()
            
            # Check gradients
            total_norm = 0
            param_count = 0
            for name, param in model.named_parameters():
                if param.grad is not None:
                    param_norm = param.grad.data.norm(2)
                    total_norm += param_norm.item() ** 2
                    param_count += 1
                else:
                    print(f"  ⚠️  No gradient for parameter: {name}")
            
            total_norm = total_norm ** (1. / 2)
            
            print(f"  Gradient norm: {total_norm:.6f}")
            print(f"  Parameters with gradients: {param_count}")
            
            if total_norm > 0:
                print("✅ Gradients are flowing properly")
                return True
            else:
                print("❌ No gradients detected")
                return False
                
        except Exception as e:
            print(f"❌ Gradient flow test failed: {e}")
            return False
    
    def find_optimal_learning_rate(self, model, data_loader, criterion):
        """Simple learning rate finder"""
        print("Finding optimal learning rate...")
        
        # Test different learning rates
        test_lrs = [1e-6, 1e-5, 1e-4, 1e-3, 1e-2]
        lr_losses = []
        
        original_state = model.state_dict()
        
        for lr in test_lrs:
            # Restore model state
            model.load_state_dict(original_state)
            model.train()
            
            optimizer = optim.Adam(model.parameters(), lr=lr)
            
            losses = []
            for batch_idx, batch in enumerate(data_loader):
                if batch_idx >= 3:  # Test on 3 batches
                    break
                
                eeg_data = batch['eeg_data'].to(self.device)
                temporal_sequence = batch['temporal_sequence'].to(self.device)
                lesion_masks = batch['lesion_mask'].to(self.device)
                
                try:
                    optimizer.zero_grad()
                    outputs = model(eeg_data, temporal_sequence)
                    loss_results = criterion(outputs['bbox_params'], lesion_masks)
                    loss = loss_results['total_loss']
                    
                    if not torch.isnan(loss) and not torch.isinf(loss):
                        loss.backward()
                        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                        optimizer.step()
                        losses.append(loss.item())
                    
                except Exception as e:
                    print(f"  Error with LR {lr}: {e}")
                    losses.append(float('inf'))
                    break
            
            avg_loss = np.mean(losses) if losses else float('inf')
            lr_losses.append(avg_loss)
            print(f"  LR {lr:.1e}: Loss = {avg_loss:.6f}")
        
        # Find best learning rate
        best_idx = np.argmin(lr_losses)
        best_lr = test_lrs[best_idx]
        
        print(f"✅ Optimal learning rate: {best_lr:.1e}")
        
        # Restore original state
        model.load_state_dict(original_state)
        
        return best_lr
    
    def create_improved_training_config(self, optimal_lr):
        """Create improved training configuration"""
        
        config = {
            'learning_rate': optimal_lr,
            'optimizer': 'AdamW',
            'weight_decay': 1e-6,  # Reduced weight decay
            'batch_size': 2,
            'gradient_clip_norm': 1.0,
            'scheduler': 'ReduceLROnPlateau',
            'scheduler_params': {
                'mode': 'max',
                'factor': 0.5,
                'patience': 5,
                'min_lr': 1e-7
            },
            'early_stopping_patience': 15,
            'loss_weights': {
                'dice': 0.4,
                'iou': 0.4,
                'focal': 0.2
            }
        }
        
        return config

def main():
    """Main function to fix training issues"""
    print("Epilepsy Localization Training Issue Diagnosis & Fix")
    print("="*60)
    
    # Check device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Create data loaders
    try:
        train_loader, val_loader, _ = create_data_loaders(
            train_file="eeg_lesion_training_dataset/train_pairings.pkl",
            val_file="eeg_lesion_training_dataset/validation_pairings.pkl",
            test_file="eeg_lesion_training_dataset/test_pairings.pkl",
            batch_size=2,
            num_workers=0
        )
        print("✅ Data loaders created successfully")
    except Exception as e:
        print(f"❌ Error creating data loaders: {e}")
        return
    
    # Initialize fixer
    fixer = TrainingFixer(device)
    
    # Step 1: Validate data preprocessing
    print("\n" + "="*60)
    print("STEP 1: DATA PREPROCESSING VALIDATION")
    print("="*60)
    
    eeg_stats, mask_stats, issues = fixer.validate_data_preprocessing(train_loader)
    
    # Step 2: Create model and analyze loss scaling
    print("\n" + "="*60)
    print("STEP 2: LOSS SCALING ANALYSIS")
    print("="*60)
    
    model = EpilepsyLocalizationModel().to(device)
    needs_scaling = fixer.analyze_loss_scaling(model, train_loader)
    
    # Step 3: Create normalized loss function
    print("\n" + "="*60)
    print("STEP 3: CREATING NORMALIZED LOSS FUNCTION")
    print("="*60)
    
    normalized_criterion = fixer.create_normalized_loss_function()
    print("✅ Normalized loss function created")
    
    # Step 4: Test gradient flow
    print("\n" + "="*60)
    print("STEP 4: GRADIENT FLOW TEST")
    print("="*60)
    
    gradient_ok = fixer.test_gradient_flow(model, train_loader, normalized_criterion)
    
    # Step 5: Find optimal learning rate
    print("\n" + "="*60)
    print("STEP 5: LEARNING RATE OPTIMIZATION")
    print("="*60)
    
    optimal_lr = fixer.find_optimal_learning_rate(model, train_loader, normalized_criterion)
    
    # Step 6: Create improved configuration
    print("\n" + "="*60)
    print("STEP 6: IMPROVED TRAINING CONFIGURATION")
    print("="*60)
    
    improved_config = fixer.create_improved_training_config(optimal_lr)
    
    # Save results
    results = {
        'data_issues': issues,
        'loss_scaling_needed': needs_scaling,
        'gradient_flow_ok': gradient_ok,
        'optimal_learning_rate': optimal_lr,
        'improved_config': improved_config,
        'data_stats': {
            'eeg_stats': eeg_stats,
            'mask_stats': mask_stats
        }
    }
    
    with open('training_fix_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print("\n" + "="*60)
    print("DIAGNOSIS COMPLETED")
    print("="*60)
    print("✅ Results saved to training_fix_results.json")
    print(f"✅ Recommended learning rate: {optimal_lr:.1e}")
    print(f"✅ Gradient flow: {'OK' if gradient_ok else 'ISSUES DETECTED'}")
    print(f"✅ Loss scaling: {'NEEDED' if needs_scaling else 'OK'}")
    
    if issues:
        print("\n⚠️  Data preprocessing issues detected:")
        for issue in issues:
            print(f"   - {issue}")
    
    print("\nNext steps:")
    print("1. Use the normalized loss function")
    print(f"2. Set learning rate to {optimal_lr:.1e}")
    print("3. Apply gradient clipping (max_norm=1.0)")
    print("4. Monitor training closely for stability")

if __name__ == "__main__":
    main()
