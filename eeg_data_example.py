#!/usr/bin/env python3
"""
Example script showing how to load and work with individual EEG files
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import gzip
from pathlib import Path

def load_eeg_file(filepath):
    """Load a single EEG file and return the data"""
    print(f"Loading: {filepath}")
    
    with gzip.open(filepath, 'rt') as f:
        data = pd.read_csv(f)
    
    print(f"Data shape: {data.shape}")
    print(f"Columns: {list(data.columns)}")
    print(f"Duration: {len(data)/128:.1f} seconds (assuming 128 Hz)")
    
    return data

def plot_eeg_channels(data, subject_id, time_range=(0, 10)):
    """Plot EEG channels for a specific time range"""
    
    # EEG channel names
    eeg_channels = ['AF3', 'AF4', 'F3', 'F4', 'F7', 'F8', 'FC5', 'FC6', 
                    'O1', 'O2', 'P7', 'P8', 'T7', 'T8']
    
    # Calculate sample range
    sampling_rate = 128  # Hz
    start_sample = int(time_range[0] * sampling_rate)
    end_sample = int(time_range[1] * sampling_rate)
    
    # Create time vector
    time = np.arange(start_sample, end_sample) / sampling_rate
    
    # Create subplot for each channel
    fig, axes = plt.subplots(7, 2, figsize=(15, 20))
    axes = axes.flatten()
    
    for i, channel in enumerate(eeg_channels):
        channel_data = data[channel].iloc[start_sample:end_sample]
        axes[i].plot(time, channel_data, 'b-', linewidth=0.8)
        axes[i].set_title(f'{channel}', fontweight='bold')
        axes[i].set_ylabel('Amplitude (µV)')
        axes[i].grid(True, alpha=0.3)
        
        if i >= 12:  # Bottom row
            axes[i].set_xlabel('Time (seconds)')
    
    plt.suptitle(f'EEG Channels - Subject {subject_id} ({time_range[0]}-{time_range[1]}s)', 
                 fontsize=16, fontweight='bold')
    plt.tight_layout()
    
    # Save the plot
    filename = f"eeg_channels_subject_{subject_id}.png"
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"Channel plot saved as: {filename}")
    
    plt.show()

def analyze_single_subject():
    """Analyze a single subject's EEG data"""
    
    # Load Guinea-Bissau subject 1
    filepath = Path("1252141/EEGs_Guinea-Bissau/signal-1.csv.gz")
    
    if not filepath.exists():
        print(f"File not found: {filepath}")
        return
    
    # Load the data
    data = load_eeg_file(filepath)
    
    # Load metadata to get subject info
    metadata = pd.read_csv("1252141/metadata_guineabissau.csv")
    subject_info = metadata[metadata['subject.id'] == 1].iloc[0]
    
    print(f"\nSubject Information:")
    print(f"  Group: {subject_info['Group']}")
    print(f"  Eye condition: {subject_info['Eyes.condition']}")
    print(f"  Recording period: {subject_info['recordedPeriod']} seconds")
    print(f"  Start time: {subject_info['startTime']}")
    
    # Plot the first 10 seconds of EEG data
    plot_eeg_channels(data, subject_id=1, time_range=(0, 10))
    
    # Calculate basic statistics
    eeg_channels = ['AF3', 'AF4', 'F3', 'F4', 'F7', 'F8', 'FC5', 'FC6', 
                    'O1', 'O2', 'P7', 'P8', 'T7', 'T8']
    
    print(f"\nBasic Statistics:")
    for channel in eeg_channels:
        mean_val = data[channel].mean()
        std_val = data[channel].std()
        print(f"  {channel}: Mean = {mean_val:.1f} µV, Std = {std_val:.1f} µV")
    
    # Check data quality
    print(f"\nData Quality Indicators:")
    print(f"  Interpolated samples: {data['INTERPOLATED'].sum()}")
    print(f"  Raw contact quality: {data['RAW_CQ'].mean():.1f}")
    
    # Show contact quality for each channel
    cq_channels = [col for col in data.columns if col.startswith('CQ_') and col != 'CQ_CMS' and col != 'CQ_DRL']
    print(f"  Channel contact quality:")
    for cq_channel in cq_channels:
        channel_name = cq_channel.replace('CQ_', '')
        if channel_name in eeg_channels:
            cq_value = data[cq_channel].mean()
            print(f"    {channel_name}: {cq_value:.1f}")

if __name__ == "__main__":
    print("EEG Data Example - Individual File Analysis")
    print("="*50)
    analyze_single_subject()
