#!/usr/bin/env python3
"""
Proper Balanced Feature Extraction
正确的数据处理流程：
1. 先分割数据集（训练/测试）
2. 测试集保持平衡
3. 训练集再进行过采样
4. 充分利用两个数据集
5. 防止数据泄露
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score, roc_curve, balanced_accuracy_score
from sklearn.model_selection import train_test_split, StratifiedShuffleSplit
from imblearn.over_sampling import SMOTE, BorderlineSMOTE
import gzip
import os
from pathlib import Path
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# Import enhanced model components
from enhanced_feature_extraction import EnhancedMultiModalClassifier

class ProperBalancedEEGDataset(torch.utils.data.Dataset):
    """正确的平衡EEG数据集处理"""
    
    def __init__(self, data_dirs, metadata_files, split='train', test_size=0.25, random_state=42):
        self.data_dirs = [Path(d) for d in data_dirs]
        self.split = split
        self.random_state = random_state
        
        # 加载并分析数据分布
        self.analyze_data_distribution(metadata_files)
        
        # 正确的数据分割流程
        self.proper_data_split(test_size, random_state)
        
    def analyze_data_distribution(self, metadata_files):
        """分析数据分布"""
        print("📊 分析数据分布...")
        
        all_samples = []
        
        # Guinea-Bissau数据集
        gb_metadata = pd.read_csv(metadata_files[0])
        gb_metadata['dataset'] = 'Guinea-Bissau'
        gb_metadata['data_dir'] = str(self.data_dirs[0])
        
        for idx, row in gb_metadata.iterrows():
            subject_id = row['subject.id']
            group = row['Group']
            eeg_file = self.data_dirs[0] / f"signal-{subject_id}.csv.gz"
            
            if eeg_file.exists():
                all_samples.append({
                    'file_path': str(eeg_file),
                    'label': 1 if group == 'Epilepsy' else 0,
                    'group': group,
                    'dataset': 'Guinea-Bissau',
                    'subject_id': subject_id
                })
        
        # Nigeria数据集
        ng_metadata = pd.read_csv(metadata_files[1])
        ng_metadata['dataset'] = 'Nigeria'
        ng_metadata['data_dir'] = str(self.data_dirs[1])
        
        for idx, row in ng_metadata.iterrows():
            subject_id = row['subject.id']
            group = row['Group']
            
            # Nigeria数据集的文件命名格式
            if 'csv.file' in row and pd.notna(row['csv.file']):
                eeg_file = self.data_dirs[1] / row['csv.file']
            else:
                eeg_file = self.data_dirs[1] / f"signal-{subject_id}-1.csv.gz"
            
            if eeg_file.exists():
                all_samples.append({
                    'file_path': str(eeg_file),
                    'label': 1 if group == 'Epilepsy' else 0,
                    'group': group,
                    'dataset': 'Nigeria',
                    'subject_id': subject_id
                })
        
        self.all_samples = all_samples
        
        # 统计信息
        gb_samples = [s for s in all_samples if s['dataset'] == 'Guinea-Bissau']
        ng_samples = [s for s in all_samples if s['dataset'] == 'Nigeria']
        
        gb_epilepsy = sum([1 for s in gb_samples if s['label'] == 1])
        gb_control = len(gb_samples) - gb_epilepsy
        ng_epilepsy = sum([1 for s in ng_samples if s['label'] == 1])
        ng_control = len(ng_samples) - ng_epilepsy
        
        total_epilepsy = gb_epilepsy + ng_epilepsy
        total_control = gb_control + ng_control
        
        print(f"✅ 数据分布分析完成:")
        print(f"  Guinea-Bissau: {len(gb_samples)} 样本 (癫痫: {gb_epilepsy}, 对照: {gb_control})")
        print(f"  Nigeria: {len(ng_samples)} 样本 (癫痫: {ng_epilepsy}, 对照: {ng_control})")
        print(f"  总计: {len(all_samples)} 样本 (癫痫: {total_epilepsy}, 对照: {total_control})")
        print(f"  类别比例: {total_epilepsy/(total_epilepsy+total_control):.3f} : {total_control/(total_epilepsy+total_control):.3f}")
    
    def proper_data_split(self, test_size, random_state):
        """正确的数据分割：先分割，再平衡"""
        print(f"\n🔄 执行正确的数据分割流程...")
        
        # 提取特征用于分层分割
        file_paths = [s['file_path'] for s in self.all_samples]
        labels = [s['label'] for s in self.all_samples]
        datasets = [s['dataset'] for s in self.all_samples]
        
        # 创建分层标签：结合类别和数据集信息
        stratify_labels = [f"{label}_{dataset}" for label, dataset in zip(labels, datasets)]
        
        # 分层分割，确保训练集和测试集都有两个数据集的样本
        try:
            train_idx, test_idx = train_test_split(
                range(len(self.all_samples)),
                test_size=test_size,
                random_state=random_state,
                stratify=stratify_labels
            )
        except ValueError:
            # 如果分层失败，使用简单的随机分割
            print("⚠️  分层分割失败，使用随机分割")
            train_idx, test_idx = train_test_split(
                range(len(self.all_samples)),
                test_size=test_size,
                random_state=random_state
            )
        
        # 分割样本
        train_samples = [self.all_samples[i] for i in train_idx]
        test_samples = [self.all_samples[i] for i in test_idx]
        
        print(f"✅ 初始分割完成:")
        print(f"  训练集: {len(train_samples)} 样本")
        print(f"  测试集: {len(test_samples)} 样本")
        
        # 处理测试集：确保平衡
        balanced_test_samples = self.balance_test_set(test_samples)
        
        # 处理训练集：应用过采样
        if self.split == 'train':
            self.samples = self.apply_oversampling_to_train(train_samples)
        else:  # test
            self.samples = balanced_test_samples
        
        # 最终统计
        final_labels = [s['label'] for s in self.samples]
        final_datasets = [s['dataset'] for s in self.samples]
        
        epilepsy_count = sum(final_labels)
        control_count = len(final_labels) - epilepsy_count
        gb_count = sum([1 for d in final_datasets if d == 'Guinea-Bissau'])
        ng_count = sum([1 for d in final_datasets if d == 'Nigeria'])
        
        print(f"\n✅ {self.split.upper()}集最终分布:")
        print(f"  总样本: {len(self.samples)}")
        print(f"  癫痫: {epilepsy_count}, 对照: {control_count}")
        print(f"  Guinea-Bissau: {gb_count}, Nigeria: {ng_count}")
        print(f"  平衡比例: {epilepsy_count/len(final_labels):.3f} : {control_count/len(final_labels):.3f}")
    
    def balance_test_set(self, test_samples):
        """平衡测试集"""
        print(f"\n⚖️  平衡测试集...")
        
        epilepsy_samples = [s for s in test_samples if s['label'] == 1]
        control_samples = [s for s in test_samples if s['label'] == 0]
        
        print(f"  原始测试集: 癫痫 {len(epilepsy_samples)}, 对照 {len(control_samples)}")
        
        # 取较小类别的数量
        min_count = min(len(epilepsy_samples), len(control_samples))
        
        if min_count == 0:
            print("❌ 错误：测试集中缺少某一类别的样本")
            return test_samples
        
        # 随机选择相等数量的样本
        np.random.seed(self.random_state)
        
        selected_epilepsy = np.random.choice(len(epilepsy_samples), min_count, replace=False)
        selected_control = np.random.choice(len(control_samples), min_count, replace=False)
        
        balanced_samples = []
        balanced_samples.extend([epilepsy_samples[i] for i in selected_epilepsy])
        balanced_samples.extend([control_samples[i] for i in selected_control])
        
        print(f"  平衡后测试集: 癫痫 {min_count}, 对照 {min_count}")
        
        return balanced_samples
    
    def apply_oversampling_to_train(self, train_samples):
        """对训练集应用过采样"""
        print(f"\n🔄 对训练集应用过采样...")
        
        train_labels = [s['label'] for s in train_samples]
        epilepsy_count = sum(train_labels)
        control_count = len(train_labels) - epilepsy_count
        
        print(f"  过采样前: 癫痫 {epilepsy_count}, 对照 {control_count}")
        
        if epilepsy_count == 0 or control_count == 0:
            print("⚠️  训练集中缺少某一类别，跳过过采样")
            return train_samples
        
        # 提取简单特征用于SMOTE
        print("  提取特征用于过采样...")
        features_for_smote = []
        
        for sample in tqdm(train_samples, desc="提取特征"):
            eeg_data = self.load_eeg_signal(sample['file_path'])
            if eeg_data is not None:
                # 提取统计特征
                features = np.array([
                    np.mean(eeg_data, axis=1),      # 均值 [14]
                    np.std(eeg_data, axis=1),       # 标准差 [14]
                    np.max(eeg_data, axis=1),       # 最大值 [14]
                    np.min(eeg_data, axis=1),       # 最小值 [14]
                    np.median(eeg_data, axis=1),    # 中位数 [14]
                ]).flatten()  # 14*5 = 70维特征
            else:
                features = np.zeros(70)
            
            features_for_smote.append(features)
        
        features_array = np.array(features_for_smote)
        labels_array = np.array(train_labels)
        
        # 应用SMOTE过采样
        try:
            # 使用BorderlineSMOTE，更适合处理边界样本
            k_neighbors = min(5, epilepsy_count - 1) if epilepsy_count > 1 else 1
            smote = BorderlineSMOTE(random_state=self.random_state, k_neighbors=k_neighbors)
            features_resampled, labels_resampled = smote.fit_resample(features_array, labels_array)
            
            print(f"  过采样后: {len(labels_resampled)} 样本")
            
            # 创建新的样本列表
            resampled_samples = []
            
            # 保留原始样本
            resampled_samples.extend(train_samples)
            
            # 为新增的合成样本创建条目
            original_count = len(train_samples)
            new_samples_count = len(labels_resampled) - original_count
            
            if new_samples_count > 0:
                # 找到癫痫样本作为模板
                epilepsy_samples = [s for s in train_samples if s['label'] == 1]
                
                # 为新增样本随机选择模板
                np.random.seed(self.random_state)
                
                for i in range(new_samples_count):
                    template = np.random.choice(epilepsy_samples)
                    synthetic_sample = {
                        'file_path': template['file_path'],  # 使用模板文件
                        'label': 1,  # SMOTE只增加少数类（癫痫）
                        'group': 'Epilepsy',
                        'dataset': template['dataset'],
                        'subject_id': f"synthetic_{i}",
                        'is_synthetic': True
                    }
                    resampled_samples.append(synthetic_sample)
            
            final_labels = [s['label'] for s in resampled_samples]
            final_epilepsy = sum(final_labels)
            final_control = len(final_labels) - final_epilepsy
            
            print(f"  最终训练集: 癫痫 {final_epilepsy}, 对照 {final_control}")
            
            return resampled_samples
            
        except Exception as e:
            print(f"⚠️  过采样失败: {e}")
            print("    使用原始训练集")
            return train_samples
    
    def load_eeg_signal(self, file_path):
        """加载EEG信号"""
        try:
            with gzip.open(file_path, 'rt') as f:
                df = pd.read_csv(f)
            
            # 14个EEG通道
            eeg_channels = ['AF3', 'AF4', 'F3', 'F4', 'F7', 'F8', 'FC5', 'FC6', 
                           'O1', 'O2', 'P7', 'P8', 'T7', 'T8']
            
            available_channels = [ch for ch in eeg_channels if ch in df.columns]
            if len(available_channels) < 10:
                return None
            
            eeg_data = df[available_channels].values.T
            
            # 填充缺失通道
            if len(available_channels) < 14:
                padding = np.zeros((14 - len(available_channels), eeg_data.shape[1]))
                eeg_data = np.vstack([eeg_data, padding])
            
            # 标准化
            eeg_data = (eeg_data - np.mean(eeg_data, axis=1, keepdims=True)) / (np.std(eeg_data, axis=1, keepdims=True) + 1e-8)
            
            # 固定长度
            target_length = 2048
            if eeg_data.shape[1] > target_length:
                start_idx = np.random.randint(0, eeg_data.shape[1] - target_length + 1)
                eeg_data = eeg_data[:, start_idx:start_idx + target_length]
            elif eeg_data.shape[1] < target_length:
                pad_width = target_length - eeg_data.shape[1]
                eeg_data = np.pad(eeg_data, ((0, 0), (0, pad_width)), mode='reflect')
            
            return eeg_data.astype(np.float32)
            
        except Exception as e:
            return None
    
    def create_temporal_sequences(self, eeg_data, window_size=128, num_windows=16):
        """创建时间序列窗口"""
        total_length = eeg_data.shape[1]
        step_size = max(1, (total_length - window_size) // (num_windows - 1))
        
        sequences = []
        for i in range(num_windows):
            start_idx = min(i * step_size, total_length - window_size)
            end_idx = start_idx + window_size
            
            window = eeg_data[:, start_idx:end_idx]
            window_features = np.array([
                np.mean(window, axis=1),
                np.std(window, axis=1),
                np.max(window, axis=1),
                np.min(window, axis=1),
            ]).T.flatten()
            sequences.append(window_features)
        
        return np.array(sequences)
    
    def create_enhanced_topographic_map(self, eeg_data):
        """创建增强拓扑图"""
        from scipy import signal
        
        electrode_positions = {
            'AF3': (20, 25), 'AF4': (20, 39),
            'F3': (25, 20), 'F4': (25, 44), 'F7': (25, 10), 'F8': (25, 54),
            'FC5': (30, 15), 'FC6': (30, 49),
            'T7': (35, 5), 'T8': (35, 59),
            'P7': (45, 10), 'P8': (45, 54),
            'O1': (55, 25), 'O2': (55, 39)
        }
        
        channels = ['AF3', 'AF4', 'F3', 'F4', 'F7', 'F8', 'FC5', 'FC6', 
                   'O1', 'O2', 'P7', 'P8', 'T7', 'T8']
        
        fs = 256
        freq_bands = {
            'delta': (1, 4),
            'theta': (4, 8),
            'alpha': (8, 13),
            'beta': (13, 30)
        }
        
        topo_maps = []
        
        for band_name, (low_freq, high_freq) in freq_bands.items():
            try:
                sos = signal.butter(4, [low_freq, high_freq], btype='band', fs=fs, output='sos')
                filtered_data = signal.sosfilt(sos, eeg_data, axis=1)
                power_values = np.mean(filtered_data**2, axis=1)
            except:
                power_values = np.mean(eeg_data**2, axis=1)
            
            topo_map = np.zeros((64, 64))
            
            for i, channel in enumerate(channels):
                if channel in electrode_positions and i < len(power_values):
                    y, x = electrode_positions[channel]
                    for dy in range(-4, 5):
                        for dx in range(-4, 5):
                            ny, nx = y + dy, x + dx
                            if 0 <= ny < 64 and 0 <= nx < 64:
                                weight = np.exp(-(dy**2 + dx**2) / 8.0)
                                topo_map[ny, nx] += power_values[i] * weight
            
            topo_maps.append(topo_map)
        
        return np.stack(topo_maps)
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        sample = self.samples[idx]
        eeg_data = self.load_eeg_signal(sample['file_path'])
        
        if eeg_data is None:
            eeg_data = np.zeros((14, 2048), dtype=np.float32)
        
        # 对合成样本添加轻微噪声
        if sample.get('is_synthetic', False):
            noise = np.random.normal(0, 0.03, eeg_data.shape)
            eeg_data = eeg_data + noise
        
        topo_maps = self.create_enhanced_topographic_map(eeg_data)
        temporal_sequences = self.create_temporal_sequences(eeg_data)
        
        return {
            'topographic_maps': torch.FloatTensor(topo_maps),
            'temporal_sequences': torch.FloatTensor(temporal_sequences),
            'raw_eeg': torch.FloatTensor(eeg_data),
            'label': torch.LongTensor([sample['label']])[0],
            'dataset': sample['dataset'],
            'is_synthetic': sample.get('is_synthetic', False),
            'file_path': sample['file_path']
        }

class ProperBalancedTrainer:
    """正确的平衡训练器"""
    
    def __init__(self, model, device):
        self.model = model.to(device)
        self.device = device
        
        # 损失函数
        self.criterion = nn.CrossEntropyLoss()
        
        # 优化器
        self.optimizer = optim.AdamW(
            self.model.parameters(), 
            lr=1e-4, 
            weight_decay=1e-5
        )
        
        # 学习率调度器 - 基于平衡准确率
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='max', factor=0.5, patience=7, verbose=True
        )
        
        # 训练历史
        self.history = {
            'train_loss': [], 'val_loss': [],
            'train_acc': [], 'val_acc': [],
            'train_balanced_acc': [], 'val_balanced_acc': [],
            'val_auc': [], 'val_sensitivity': [], 'val_specificity': []
        }
    
    def train_epoch(self, train_loader):
        """训练一个epoch"""
        self.model.train()
        
        total_loss = 0.0
        all_predictions = []
        all_labels = []
        
        for batch in tqdm(train_loader, desc="Training"):
            topo_maps = batch['topographic_maps'].to(self.device)
            temporal_seq = batch['temporal_sequences'].to(self.device)
            labels = batch['label'].to(self.device)
            
            self.optimizer.zero_grad()
            
            outputs = self.model(topo_maps, temporal_seq)
            
            # 多任务损失
            fused_loss = self.criterion(outputs['fused_logits'], labels)
            topo_loss = self.criterion(outputs['topo_logits'], labels)
            total_loss_batch = 0.7 * fused_loss + 0.3 * topo_loss
            
            total_loss_batch.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            self.optimizer.step()
            
            total_loss += total_loss_batch.item()
            
            _, predictions = torch.max(outputs['fused_logits'], 1)
            all_predictions.extend(predictions.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
        
        avg_loss = total_loss / len(train_loader)
        accuracy = sum([1 for l, p in zip(all_labels, all_predictions) if l == p]) / len(all_labels)
        balanced_acc = balanced_accuracy_score(all_labels, all_predictions)
        
        return avg_loss, accuracy * 100, balanced_acc * 100
    
    def validate_epoch(self, val_loader):
        """验证一个epoch"""
        self.model.eval()
        
        total_loss = 0.0
        all_predictions = []
        all_labels = []
        all_probabilities = []
        
        with torch.no_grad():
            for batch in val_loader:
                topo_maps = batch['topographic_maps'].to(self.device)
                temporal_seq = batch['temporal_sequences'].to(self.device)
                labels = batch['label'].to(self.device)
                
                outputs = self.model(topo_maps, temporal_seq)
                
                fused_loss = self.criterion(outputs['fused_logits'], labels)
                topo_loss = self.criterion(outputs['topo_logits'], labels)
                total_loss_batch = 0.7 * fused_loss + 0.3 * topo_loss
                
                total_loss += total_loss_batch.item()
                
                probabilities = torch.softmax(outputs['fused_logits'], dim=1)
                _, predictions = torch.max(outputs['fused_logits'], 1)
                
                all_predictions.extend(predictions.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
                all_probabilities.extend(probabilities[:, 1].cpu().numpy())
        
        avg_loss = total_loss / len(val_loader)
        accuracy = sum([1 for l, p in zip(all_labels, all_predictions) if l == p]) / len(all_labels)
        balanced_acc = balanced_accuracy_score(all_labels, all_predictions)
        auc_score = roc_auc_score(all_labels, all_probabilities) if len(set(all_labels)) > 1 else 0.5
        
        # 计算敏感性和特异性
        cm = confusion_matrix(all_labels, all_predictions)
        if cm.shape == (2, 2):
            sensitivity = cm[1,1] / (cm[1,1] + cm[1,0]) if (cm[1,1] + cm[1,0]) > 0 else 0
            specificity = cm[0,0] / (cm[0,0] + cm[0,1]) if (cm[0,0] + cm[0,1]) > 0 else 0
        else:
            sensitivity = specificity = 0
        
        return (avg_loss, accuracy * 100, balanced_acc * 100, auc_score, 
                sensitivity * 100, specificity * 100, 
                all_predictions, all_labels, all_probabilities)
    
    def train(self, train_loader, val_loader, num_epochs=35):
        """完整训练循环"""
        print(f"🚀 开始正确的平衡训练，共 {num_epochs} 个epoch...")
        
        best_balanced_acc = 0.0
        
        for epoch in range(num_epochs):
            print(f"\nEpoch {epoch+1}/{num_epochs}")
            
            # 训练
            train_loss, train_acc, train_balanced_acc = self.train_epoch(train_loader)
            
            # 验证
            (val_loss, val_acc, val_balanced_acc, val_auc, 
             val_sensitivity, val_specificity, 
             val_preds, val_labels, val_probs) = self.validate_epoch(val_loader)
            
            # 更新学习率
            self.scheduler.step(val_balanced_acc)
            
            # 记录历史
            self.history['train_loss'].append(train_loss)
            self.history['val_loss'].append(val_loss)
            self.history['train_acc'].append(train_acc)
            self.history['val_acc'].append(val_acc)
            self.history['train_balanced_acc'].append(train_balanced_acc)
            self.history['val_balanced_acc'].append(val_balanced_acc)
            self.history['val_auc'].append(val_auc)
            self.history['val_sensitivity'].append(val_sensitivity)
            self.history['val_specificity'].append(val_specificity)
            
            # 打印结果
            print(f"训练 - 损失: {train_loss:.4f}, 准确率: {train_acc:.1f}%, 平衡准确率: {train_balanced_acc:.1f}%")
            print(f"验证 - 损失: {val_loss:.4f}, 准确率: {val_acc:.1f}%, 平衡准确率: {val_balanced_acc:.1f}%")
            print(f"     AUC: {val_auc:.3f}, 敏感性: {val_sensitivity:.1f}%, 特异性: {val_specificity:.1f}%")
            
            # 保存最佳模型
            if val_balanced_acc > best_balanced_acc:
                best_balanced_acc = val_balanced_acc
                torch.save({
                    'model_state_dict': self.model.state_dict(),
                    'val_balanced_acc': best_balanced_acc,
                    'history': self.history,
                    'final_predictions': val_preds,
                    'final_labels': val_labels,
                    'final_probabilities': val_probs
                }, 'proper_balanced_best_model.pth')
                print(f"✓ 保存最佳模型 (平衡准确率: {best_balanced_acc:.1f}%)")
        
        print(f"\n🎉 训练完成！最佳平衡准确率: {best_balanced_acc:.1f}%")
        return self.history, val_preds, val_labels, val_probs

def create_comprehensive_report(labels, predictions, probabilities, history):
    """创建综合性能报告"""
    
    print("\n" + "="*80)
    print("PROPER BALANCED ENHANCED FEATURE EXTRACTION - FINAL REPORT")
    print("="*80)
    
    # 基本指标
    accuracy = sum([1 for l, p in zip(labels, predictions) if l == p]) / len(labels)
    balanced_acc = balanced_accuracy_score(labels, predictions)
    auc_score = roc_auc_score(labels, probabilities)
    
    # 混淆矩阵
    cm = confusion_matrix(labels, predictions)
    sensitivity = cm[1,1] / (cm[1,1] + cm[1,0]) if (cm[1,1] + cm[1,0]) > 0 else 0
    specificity = cm[0,0] / (cm[0,0] + cm[0,1]) if (cm[0,0] + cm[0,1]) > 0 else 0
    
    print(f"\n📊 最终性能指标:")
    print(f"  测试样本数: {len(labels)}")
    print(f"  类别分布: 癫痫={sum(labels)}, 对照={len(labels)-sum(labels)}")
    print(f"  标准准确率: {accuracy:.3f} ({accuracy*100:.1f}%)")
    print(f"  平衡准确率: {balanced_acc:.3f} ({balanced_acc*100:.1f}%)")
    print(f"  AUC分数: {auc_score:.3f}")
    print(f"  敏感性: {sensitivity:.3f} ({sensitivity*100:.1f}%)")
    print(f"  特异性: {specificity:.3f} ({specificity*100:.1f}%)")
    
    print(f"\n📈 详细分类报告:")
    print(classification_report(labels, predictions, 
                              target_names=['Control', 'Epilepsy']))
    
    print(f"\n🔍 混淆矩阵:")
    print(f"              预测")
    print(f"实际    Control  Epilepsy")
    print(f"Control    {cm[0,0]:3d}      {cm[0,1]:3d}")
    print(f"Epilepsy   {cm[1,0]:3d}      {cm[1,1]:3d}")
    
    # 训练过程分析
    if history and 'val_balanced_acc' in history:
        best_epoch = np.argmax(history['val_balanced_acc']) + 1
        best_balanced_acc = max(history['val_balanced_acc'])
        final_balanced_acc = history['val_balanced_acc'][-1]
        
        print(f"\n📈 训练过程分析:")
        print(f"  最佳epoch: {best_epoch}")
        print(f"  最佳平衡准确率: {best_balanced_acc:.1f}%")
        print(f"  最终平衡准确率: {final_balanced_acc:.1f}%")
        print(f"  是否过拟合: {'是' if final_balanced_acc < best_balanced_acc - 5 else '否'}")
    
    # 改进建议
    print(f"\n💡 模型评估:")
    if balanced_acc >= 0.85:
        print("  ✅ 优秀 - 特征提取非常有效")
    elif balanced_acc >= 0.75:
        print("  ✅ 良好 - 特征提取有效")
    elif balanced_acc >= 0.65:
        print("  ⚠️  一般 - 需要进一步优化")
    else:
        print("  ❌ 较差 - 需要重新设计")
    
    print(f"\n🎯 下一步建议:")
    print(f"  1. 将此特征提取器集成到病灶定位模型")
    print(f"  2. 优化EEG-病灶空间映射算法")
    print(f"  3. 收集更多高质量的配对数据")
    
    return balanced_acc, auc_score, sensitivity, specificity

def main():
    """主函数"""
    print("🎯 Proper Balanced Enhanced Feature Extraction")
    print("="*70)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # 数据路径
    data_dirs = [
        "1252141/EEGs_Guinea-Bissau",
        "1252141/EEGs_Nigeria"
    ]
    metadata_files = [
        "1252141/metadata_guineabissau.csv",
        "1252141/metadata_nigeria.csv"
    ]
    
    try:
        # 创建正确的平衡数据集
        print("📊 创建训练集...")
        train_dataset = ProperBalancedEEGDataset(
            data_dirs, metadata_files, split='train', test_size=0.25
        )
        
        print("\n📊 创建测试集...")
        test_dataset = ProperBalancedEEGDataset(
            data_dirs, metadata_files, split='test', test_size=0.25
        )
        
        # 创建数据加载器
        train_loader = torch.utils.data.DataLoader(
            train_dataset, batch_size=8, shuffle=True, num_workers=0
        )
        test_loader = torch.utils.data.DataLoader(
            test_dataset, batch_size=8, shuffle=False, num_workers=0
        )
        
        print("\n✅ 正确的平衡数据集创建成功")
        
    except Exception as e:
        print(f"❌ 数据集创建失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 创建模型
    model = EnhancedMultiModalClassifier(num_classes=2)
    param_count = sum(p.numel() for p in model.parameters())
    print(f"模型参数数量: {param_count:,}")
    
    # 创建训练器
    trainer = ProperBalancedTrainer(model, device)
    
    # 开始训练
    history, val_preds, val_labels, val_probs = trainer.train(
        train_loader, test_loader, num_epochs=30
    )
    
    # 创建综合报告
    balanced_acc, auc_score, sensitivity, specificity = create_comprehensive_report(
        val_labels, val_preds, val_probs, history
    )
    
    print(f"\n🎉 Proper Balanced Enhanced Feature Extraction 完成！")
    print(f"📊 最终结果:")
    print(f"  - 平衡准确率: {balanced_acc*100:.1f}%")
    print(f"  - AUC分数: {auc_score:.3f}")
    print(f"  - 敏感性: {sensitivity*100:.1f}%")
    print(f"  - 特异性: {specificity*100:.1f}%")
    print(f"📁 模型文件: proper_balanced_best_model.pth")

if __name__ == "__main__":
    main()
