#!/usr/bin/env python3
"""
简化的MRI掩码查看器
"""

import numpy as np
import matplotlib.pyplot as plt
import nibabel as nib
import os

def create_summary_visualization():
    """
    创建MRI掩码的总结可视化
    """
    print("=== 创建MRI掩码总结可视化 ===")
    
    # 加载数据
    img1 = nib.load("1/1_MaskInOrig.nii.gz")
    img2 = nib.load("1/1_MaskInRawData.nii.gz")
    
    data1 = img1.get_fdata()
    data2 = img2.get_fdata()
    
    # 创建总结图
    fig = plt.figure(figsize=(20, 12))
    fig.suptitle('MRI掩码数据分析总结 - 患者PN00', fontsize=20, fontweight='bold')
    
    # 掩码1分析
    mask1 = data1 > 0
    center_z1 = data1.shape[2] // 2
    
    # 掩码2分析  
    mask2 = data2 > 0
    center_z2 = data2.shape[2] // 2
    
    # 第一行：掩码1 (MaskInOrig)
    ax1 = plt.subplot(3, 4, 1)
    ax1.imshow(mask1[:, :, center_z1].T, cmap='Reds', origin='lower')
    ax1.set_title('MaskInOrig - 轴位面', fontweight='bold')
    ax1.axis('off')
    
    ax2 = plt.subplot(3, 4, 2)
    ax2.imshow(mask1[data1.shape[0]//2, :, :].T, cmap='Reds', origin='lower')
    ax2.set_title('MaskInOrig - 矢状面', fontweight='bold')
    ax2.axis('off')
    
    ax3 = plt.subplot(3, 4, 3)
    ax3.imshow(mask1[:, data1.shape[1]//2, :].T, cmap='Reds', origin='lower')
    ax3.set_title('MaskInOrig - 冠状面', fontweight='bold')
    ax3.axis('off')
    
    ax4 = plt.subplot(3, 4, 4)
    info_text1 = f"""
    形状: {data1.shape}
    体素尺寸: {img1.header.get_zooms()}
    非零体素: {np.sum(mask1):,}
    覆盖率: {np.sum(mask1)/mask1.size*100:.2f}%
    """
    ax4.text(0.1, 0.5, info_text1, transform=ax4.transAxes, fontsize=12,
             verticalalignment='center', bbox=dict(boxstyle="round,pad=0.3", facecolor="lightcoral"))
    ax4.set_title('MaskInOrig - 信息', fontweight='bold')
    ax4.axis('off')
    
    # 第二行：掩码2 (MaskInRawData)
    ax5 = plt.subplot(3, 4, 5)
    ax5.imshow(mask2[:, :, center_z2].T, cmap='Blues', origin='lower')
    ax5.set_title('MaskInRawData - 轴位面', fontweight='bold')
    ax5.axis('off')
    
    ax6 = plt.subplot(3, 4, 6)
    ax6.imshow(mask2[data2.shape[0]//2, :, :].T, cmap='Blues', origin='lower')
    ax6.set_title('MaskInRawData - 矢状面', fontweight='bold')
    ax6.axis('off')
    
    ax7 = plt.subplot(3, 4, 7)
    ax7.imshow(mask2[:, data2.shape[1]//2, :].T, cmap='Blues', origin='lower')
    ax7.set_title('MaskInRawData - 冠状面', fontweight='bold')
    ax7.axis('off')
    
    ax8 = plt.subplot(3, 4, 8)
    info_text2 = f"""
    形状: {data2.shape}
    体素尺寸: {img2.header.get_zooms()}
    非零体素: {np.sum(mask2):,}
    覆盖率: {np.sum(mask2)/mask2.size*100:.2f}%
    """
    ax8.text(0.1, 0.5, info_text2, transform=ax8.transAxes, fontsize=12,
             verticalalignment='center', bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))
    ax8.set_title('MaskInRawData - 信息', fontweight='bold')
    ax8.axis('off')
    
    # 第三行：3D投影和统计
    ax9 = plt.subplot(3, 4, 9)
    xy_proj1 = np.sum(mask1, axis=2)
    im9 = ax9.imshow(xy_proj1.T, cmap='Reds', origin='lower')
    ax9.set_title('MaskInOrig - XY投影', fontweight='bold')
    ax9.axis('off')
    plt.colorbar(im9, ax=ax9, shrink=0.8)
    
    ax10 = plt.subplot(3, 4, 10)
    xy_proj2 = np.sum(mask2, axis=2)
    im10 = ax10.imshow(xy_proj2.T, cmap='Blues', origin='lower')
    ax10.set_title('MaskInRawData - XY投影', fontweight='bold')
    ax10.axis('off')
    plt.colorbar(im10, ax=ax10, shrink=0.8)
    
    # 体积比较
    ax11 = plt.subplot(3, 4, 11)
    volumes = [np.sum(mask1), np.sum(mask2)]
    labels = ['MaskInOrig', 'MaskInRawData']
    colors = ['red', 'blue']
    bars = ax11.bar(labels, volumes, color=colors, alpha=0.7)
    ax11.set_title('体积比较', fontweight='bold')
    ax11.set_ylabel('体素数')
    
    # 添加数值标签
    for bar, vol in zip(bars, volumes):
        height = bar.get_height()
        ax11.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                 f'{vol:,}', ha='center', va='bottom', fontweight='bold')
    
    # 总结信息
    ax12 = plt.subplot(3, 4, 12)
    volume_diff = abs(volumes[0] - volumes[1])
    volume_diff_percent = volume_diff / max(volumes) * 100
    
    summary_text = f"""
    患者: PN00
    
    掩码对比:
    • 体积差异: {volume_diff:,} 体素
    • 相对差异: {volume_diff_percent:.1f}%
    
    空间特征:
    • 掩码1: 立方体 (256³)
    • 掩码2: 矩形 (256×228×256)
    
    可能含义:
    • 癫痫灶定位掩码
    • 手术规划区域
    • 异常信号区域
    """
    
    ax12.text(0.05, 0.95, summary_text, transform=ax12.transAxes, fontsize=11,
             verticalalignment='top', bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow"))
    ax12.set_title('分析总结', fontweight='bold')
    ax12.axis('off')
    
    plt.tight_layout()
    plt.savefig('mri_mask_summary.png', dpi=300, bbox_inches='tight')
    print("保存总结图: mri_mask_summary.png")
    plt.close(fig)

def create_3d_comparison():
    """
    创建3D对比可视化
    """
    print("=== 创建3D对比可视化 ===")
    
    # 加载数据
    img1 = nib.load("1/1_MaskInOrig.nii.gz")
    img2 = nib.load("1/1_MaskInRawData.nii.gz")
    
    data1 = img1.get_fdata()
    data2 = img2.get_fdata()
    
    mask1 = data1 > 0
    mask2 = data2 > 0
    
    # 创建3D对比图
    fig = plt.figure(figsize=(18, 6))
    fig.suptitle('MRI掩码3D分布对比', fontsize=16, fontweight='bold')
    
    # 掩码1的3D分布
    ax1 = fig.add_subplot(131, projection='3d')
    coords1 = np.where(mask1)
    if len(coords1[0]) > 0:
        # 为性能考虑，只显示部分点
        step1 = max(1, len(coords1[0]) // 3000)
        ax1.scatter(coords1[0][::step1], coords1[1][::step1], coords1[2][::step1], 
                   c='red', alpha=0.6, s=0.5)
    ax1.set_title('MaskInOrig 3D分布', fontweight='bold')
    ax1.set_xlabel('X')
    ax1.set_ylabel('Y')
    ax1.set_zlabel('Z')
    
    # 掩码2的3D分布
    ax2 = fig.add_subplot(132, projection='3d')
    coords2 = np.where(mask2)
    if len(coords2[0]) > 0:
        step2 = max(1, len(coords2[0]) // 3000)
        ax2.scatter(coords2[0][::step2], coords2[1][::step2], coords2[2][::step2], 
                   c='blue', alpha=0.6, s=0.5)
    ax2.set_title('MaskInRawData 3D分布', fontweight='bold')
    ax2.set_xlabel('X')
    ax2.set_ylabel('Y')
    ax2.set_zlabel('Z')
    
    # 质心比较
    ax3 = fig.add_subplot(133)
    if len(coords1[0]) > 0 and len(coords2[0]) > 0:
        centroid1 = [np.mean(coords1[0]), np.mean(coords1[1]), np.mean(coords1[2])]
        centroid2 = [np.mean(coords2[0]), np.mean(coords2[1]), np.mean(coords2[2])]
        
        ax3.scatter(centroid1[0], centroid1[1], c='red', s=100, label='MaskInOrig质心', marker='o')
        ax3.scatter(centroid2[0], centroid2[1], c='blue', s=100, label='MaskInRawData质心', marker='s')
        
        # 连线显示差异
        ax3.plot([centroid1[0], centroid2[0]], [centroid1[1], centroid2[1]], 'k--', alpha=0.5)
        
        distance = np.sqrt(sum((np.array(centroid1) - np.array(centroid2))**2))
        ax3.set_title(f'质心对比\n距离: {distance:.1f} 体素', fontweight='bold')
        ax3.set_xlabel('X坐标')
        ax3.set_ylabel('Y坐标')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('mri_3d_comparison.png', dpi=300, bbox_inches='tight')
    print("保存3D对比图: mri_3d_comparison.png")
    plt.close(fig)

def main():
    """
    主函数
    """
    print("=== MRI掩码简化可视化 ===")
    
    # 检查文件
    if not os.path.exists("1/1_MaskInOrig.nii.gz"):
        print("错误: 找不到 1_MaskInOrig.nii.gz")
        return
    if not os.path.exists("1/1_MaskInRawData.nii.gz"):
        print("错误: 找不到 1_MaskInRawData.nii.gz")
        return
    
    # 创建可视化
    create_summary_visualization()
    create_3d_comparison()
    
    print("\n=== 可视化完成 ===")
    print("生成的文件:")
    print("- mri_mask_summary.png: 掩码总结分析")
    print("- mri_3d_comparison.png: 3D分布对比")
    print("- mri_analysis/: 详细分析文件夹")
    print("- mri_analysis_report.md: 详细分析报告")

if __name__ == "__main__":
    main()
