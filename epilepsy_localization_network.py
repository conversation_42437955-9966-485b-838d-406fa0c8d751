#!/usr/bin/env python3
"""
Comprehensive Neural Network Architecture for Epilepsy Lesion Localization
with Adaptive Cubic Bounding Box Training

This module implements a multi-modal approach combining:
1. Multi-Modal EEG Feature Extraction Network
2. 3D Lesion Localization Network with Cubic Bounding Box Training
3. Comprehensive Visualization and Analysis Tools
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
from torch.utils.data import Dataset, DataLoader
import torchvision.models as models
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# Set random seeds for reproducibility
torch.manual_seed(42)
np.random.seed(42)

class TopographicCNN(nn.Module):
    """
    Topographic CNN Branch: Converts EEG signals to 2D topographic maps
    and extracts spatial patterns using CNN backbone
    """
    
    def __init__(self, n_channels: int = 14, backbone: str = 'resnet18', feature_dim: int = 512):
        super(TopographicCNN, self).__init__()
        self.n_channels = n_channels
        self.feature_dim = feature_dim
        
        # Load pre-trained backbone
        if backbone == 'resnet18':
            self.backbone = models.resnet18(pretrained=True)
            # Modify first layer for single channel topographic maps
            self.backbone.conv1 = nn.Conv2d(1, 64, kernel_size=7, stride=2, padding=3, bias=False)
            # Modify final layer
            self.backbone.fc = nn.Linear(self.backbone.fc.in_features, feature_dim)
        elif backbone == 'efficientnet':
            from torchvision.models import efficientnet_b0
            self.backbone = efficientnet_b0(pretrained=True)
            # Modify for single channel input
            self.backbone.features[0][0] = nn.Conv2d(1, 32, kernel_size=3, stride=2, padding=1, bias=False)
            # Modify classifier
            self.backbone.classifier = nn.Linear(self.backbone.classifier[1].in_features, feature_dim)
        
        # Topographic map generation parameters
        self.topomap_size = 64  # Output topographic map size
        
    def eeg_to_topomap(self, eeg_data: torch.Tensor) -> torch.Tensor:
        """
        Convert EEG data to topographic maps using interpolation
        Args:
            eeg_data: [batch_size, n_channels, time_points]
        Returns:
            topomaps: [batch_size, 1, topomap_size, topomap_size]
        """
        batch_size = eeg_data.shape[0]
        
        # For now, use a simplified approach - in practice, use MNE-Python's topoplot
        # This creates a spatial representation based on electrode positions
        
        # Standard 10-20 electrode positions (normalized to [-1, 1])
        electrode_positions = {
            'AF3': [-0.3, 0.7], 'AF4': [0.3, 0.7],
            'F3': [-0.5, 0.3], 'F4': [0.5, 0.3],
            'F7': [-0.8, 0.1], 'F8': [0.8, 0.1],
            'FC5': [-0.6, 0.0], 'FC6': [0.6, 0.0],
            'O1': [-0.3, -0.8], 'O2': [0.3, -0.8],
            'P7': [-0.7, -0.4], 'P8': [0.7, -0.4],
            'T7': [-0.9, -0.1], 'T8': [0.9, -0.1]
        }
        
        # Create coordinate grids
        x = torch.linspace(-1, 1, self.topomap_size)
        y = torch.linspace(-1, 1, self.topomap_size)
        X, Y = torch.meshgrid(x, y, indexing='ij')
        
        # Initialize topomaps
        topomaps = torch.zeros(batch_size, 1, self.topomap_size, self.topomap_size)
        
        # Simple interpolation for each sample
        for b in range(batch_size):
            topomap = torch.zeros(self.topomap_size, self.topomap_size)
            
            # Average EEG values across time for each channel
            channel_values = torch.mean(eeg_data[b], dim=1)  # [n_channels]
            
            # Interpolate values to create topographic map
            for i, (channel, pos) in enumerate(electrode_positions.items()):
                if i < self.n_channels:
                    # Gaussian interpolation around electrode position
                    distances = torch.sqrt((X - pos[0])**2 + (Y - pos[1])**2)
                    weights = torch.exp(-distances**2 / 0.2)  # Gaussian kernel
                    topomap += channel_values[i] * weights
            
            topomaps[b, 0] = topomap
        
        return topomaps
    
    def forward(self, eeg_data: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through topographic CNN
        Args:
            eeg_data: [batch_size, n_channels, time_points]
        Returns:
            features: [batch_size, feature_dim]
        """
        # Convert EEG to topographic maps
        topomaps = self.eeg_to_topomap(eeg_data)
        
        # Extract features using CNN backbone
        features = self.backbone(topomaps)
        
        return features

class TemporalLSTM(nn.Module):
    """
    Temporal LSTM Branch: Processes sequences of topographic maps
    to model temporal dynamics and seizure progression
    """
    
    def __init__(self, input_dim: int = 512, hidden_dim: int = 256, 
                 num_layers: int = 2, feature_dim: int = 512):
        super(TemporalLSTM, self).__init__()
        
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        
        # LSTM layers
        self.lstm = nn.LSTM(
            input_size=input_dim,
            hidden_size=hidden_dim,
            num_layers=num_layers,
            batch_first=True,
            dropout=0.2,
            bidirectional=True
        )
        
        # Output projection
        self.fc = nn.Linear(hidden_dim * 2, feature_dim)  # *2 for bidirectional
        self.dropout = nn.Dropout(0.3)
        
    def forward(self, sequence_features: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through temporal LSTM
        Args:
            sequence_features: [batch_size, sequence_length, input_dim]
        Returns:
            features: [batch_size, feature_dim]
        """
        # LSTM forward pass
        lstm_out, (hidden, cell) = self.lstm(sequence_features)
        
        # Use the last output for classification
        last_output = lstm_out[:, -1, :]  # [batch_size, hidden_dim * 2]
        
        # Project to feature dimension
        features = self.fc(self.dropout(last_output))
        
        return features

class AttentionCNNAutoencoder(nn.Module):
    """
    1D Attention-CNN Autoencoder Branch: Processes raw multi-channel EEG
    with attention mechanisms for anomaly detection
    """
    
    def __init__(self, n_channels: int = 14, sequence_length: int = 1024, 
                 feature_dim: int = 512):
        super(AttentionCNNAutoencoder, self).__init__()
        
        self.n_channels = n_channels
        self.sequence_length = sequence_length
        self.feature_dim = feature_dim
        
        # Encoder
        self.encoder = nn.Sequential(
            nn.Conv1d(n_channels, 64, kernel_size=7, stride=2, padding=3),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Conv1d(64, 128, kernel_size=5, stride=2, padding=2),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Conv1d(128, 256, kernel_size=3, stride=2, padding=1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Conv1d(256, 512, kernel_size=3, stride=2, padding=1),
            nn.BatchNorm1d(512),
            nn.ReLU(),
        )
        
        # Attention mechanism
        self.attention = nn.MultiheadAttention(
            embed_dim=512,
            num_heads=8,
            dropout=0.1,
            batch_first=True
        )
        
        # Feature projection
        self.feature_projection = nn.Sequential(
            nn.AdaptiveAvgPool1d(1),
            nn.Flatten(),
            nn.Linear(512, feature_dim),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        # Decoder (for autoencoder training)
        self.decoder = nn.Sequential(
            nn.ConvTranspose1d(512, 256, kernel_size=3, stride=2, padding=1, output_padding=1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.ConvTranspose1d(256, 128, kernel_size=3, stride=2, padding=1, output_padding=1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.ConvTranspose1d(128, 64, kernel_size=5, stride=2, padding=2, output_padding=1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.ConvTranspose1d(64, n_channels, kernel_size=7, stride=2, padding=3, output_padding=1),
        )
    
    def forward(self, eeg_data: torch.Tensor, return_reconstruction: bool = False) -> torch.Tensor:
        """
        Forward pass through attention-CNN autoencoder
        Args:
            eeg_data: [batch_size, n_channels, time_points]
            return_reconstruction: Whether to return reconstruction for autoencoder loss
        Returns:
            features: [batch_size, feature_dim]
            reconstruction (optional): [batch_size, n_channels, time_points]
        """
        # Encode
        encoded = self.encoder(eeg_data)  # [batch_size, 512, reduced_time]
        
        # Apply attention
        # Transpose for attention: [batch_size, time, features]
        encoded_transposed = encoded.transpose(1, 2)
        attended, attention_weights = self.attention(
            encoded_transposed, encoded_transposed, encoded_transposed
        )
        
        # Transpose back: [batch_size, features, time]
        attended = attended.transpose(1, 2)
        
        # Extract features
        features = self.feature_projection(attended)
        
        if return_reconstruction:
            # Decode for autoencoder loss
            reconstruction = self.decoder(attended)
            return features, reconstruction
        
        return features

class FeatureFusionModule(nn.Module):
    """
    Feature Fusion Module: Integrates features from all three branches
    using learned attention weighting
    """
    
    def __init__(self, feature_dim: int = 512, fused_dim: int = 1024):
        super(FeatureFusionModule, self).__init__()
        
        self.feature_dim = feature_dim
        self.fused_dim = fused_dim
        
        # Attention weights for each branch
        self.attention_weights = nn.Parameter(torch.ones(3) / 3)  # Equal initial weights
        
        # Feature fusion layers
        self.fusion_layers = nn.Sequential(
            nn.Linear(feature_dim * 3, fused_dim),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(fused_dim, fused_dim),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
    def forward(self, topo_features: torch.Tensor, lstm_features: torch.Tensor, 
                auto_features: torch.Tensor) -> torch.Tensor:
        """
        Fuse features from all branches
        Args:
            topo_features: [batch_size, feature_dim] from topographic CNN
            lstm_features: [batch_size, feature_dim] from temporal LSTM
            auto_features: [batch_size, feature_dim] from attention autoencoder
        Returns:
            fused_features: [batch_size, fused_dim]
        """
        # Apply learned attention weights
        weights = F.softmax(self.attention_weights, dim=0)
        
        weighted_topo = weights[0] * topo_features
        weighted_lstm = weights[1] * lstm_features
        weighted_auto = weights[2] * auto_features
        
        # Concatenate weighted features
        concatenated = torch.cat([weighted_topo, weighted_lstm, weighted_auto], dim=1)
        
        # Apply fusion layers
        fused_features = self.fusion_layers(concatenated)
        
        return fused_features

class AdaptiveCubicBoundingBox(nn.Module):
    """
    Adaptive Cubic Bounding Box Trainer: Core network for training
    adaptive cubic bounding boxes with optimized size and position
    """

    def __init__(self, input_dim: int = 1024, volume_size: int = 256):
        super(AdaptiveCubicBoundingBox, self).__init__()

        self.volume_size = volume_size

        # Multi-scale bounding box sizes (as fractions of volume_size)
        self.box_scales = [
            (16, 26, 16),   # Small: 16x26x16 voxels
            (32, 32, 32),   # Medium: 32x32x32 voxels
            (64, 64, 64),   # Large: 64x64x64 voxels
        ]

        # Scale selection network
        self.scale_selector = nn.Sequential(
            nn.Linear(input_dim, 512),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, len(self.box_scales)),
            nn.Softmax(dim=1)
        )

        # Box center prediction network
        self.center_predictor = nn.Sequential(
            nn.Linear(input_dim, 512),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, 3),  # x, y, z coordinates
            nn.Sigmoid()  # Normalize to [0, 1] range
        )

        # Box size refinement network (for fine-tuning scale)
        self.size_refiner = nn.Sequential(
            nn.Linear(input_dim, 512),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, 3),  # width, height, depth refinements
            nn.Tanh()  # Allow ±1 scaling factor
        )

    def forward(self, features: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Forward pass to predict adaptive cubic bounding box
        Args:
            features: [batch_size, input_dim] fused EEG features
        Returns:
            dict with box parameters: center, size, scale_weights
        """
        batch_size = features.shape[0]

        # Predict scale weights
        scale_weights = self.scale_selector(features)  # [batch_size, n_scales]

        # Predict box center (normalized coordinates)
        center_norm = self.center_predictor(features)  # [batch_size, 3]
        center = center_norm * self.volume_size  # Scale to volume coordinates

        # Predict size refinements
        size_refinements = self.size_refiner(features)  # [batch_size, 3]

        # Compute final box sizes using weighted combination of scales
        final_sizes = torch.zeros(batch_size, 3, device=features.device)
        for i, (w, h, d) in enumerate(self.box_scales):
            scale_contribution = scale_weights[:, i:i+1] * torch.tensor([w, h, d], device=features.device)
            final_sizes += scale_contribution

        # Apply size refinements (±20% adjustment)
        size_adjustments = 1.0 + 0.2 * size_refinements
        final_sizes = final_sizes * size_adjustments

        # Ensure sizes are within reasonable bounds
        final_sizes = torch.clamp(final_sizes, min=8, max=128)

        return {
            'center': center,
            'size': final_sizes,
            'scale_weights': scale_weights,
            'size_refinements': size_refinements
        }

class BoundingBoxLoss(nn.Module):
    """
    Multi-objective loss function for cubic bounding box optimization
    Combines Dice, 3D IoU, and Focal losses
    """

    def __init__(self, dice_weight: float = 0.4, iou_weight: float = 0.4,
                 focal_weight: float = 0.2, focal_alpha: float = 0.25, focal_gamma: float = 2.0):
        super(BoundingBoxLoss, self).__init__()

        self.dice_weight = dice_weight
        self.iou_weight = iou_weight
        self.focal_weight = focal_weight
        self.focal_alpha = focal_alpha
        self.focal_gamma = focal_gamma

    def create_box_mask(self, center: torch.Tensor, size: torch.Tensor,
                       volume_size: int = 256) -> torch.Tensor:
        """
        Create binary mask for cubic bounding box
        Args:
            center: [batch_size, 3] box centers
            size: [batch_size, 3] box sizes
            volume_size: Size of the 3D volume
        Returns:
            box_masks: [batch_size, volume_size, volume_size, volume_size]
        """
        batch_size = center.shape[0]
        device = center.device

        # Create coordinate grids
        coords = torch.arange(volume_size, device=device, dtype=torch.float32)
        z, y, x = torch.meshgrid(coords, coords, coords, indexing='ij')

        # Expand for batch processing
        x = x.unsqueeze(0).expand(batch_size, -1, -1, -1)
        y = y.unsqueeze(0).expand(batch_size, -1, -1, -1)
        z = z.unsqueeze(0).expand(batch_size, -1, -1, -1)

        # Expand center and size for broadcasting
        center_x = center[:, 0].view(batch_size, 1, 1, 1)
        center_y = center[:, 1].view(batch_size, 1, 1, 1)
        center_z = center[:, 2].view(batch_size, 1, 1, 1)

        size_x = size[:, 0].view(batch_size, 1, 1, 1)
        size_y = size[:, 1].view(batch_size, 1, 1, 1)
        size_z = size[:, 2].view(batch_size, 1, 1, 1)

        # Create box masks
        mask_x = (torch.abs(x - center_x) <= size_x / 2)
        mask_y = (torch.abs(y - center_y) <= size_y / 2)
        mask_z = (torch.abs(z - center_z) <= size_z / 2)

        box_mask = mask_x & mask_y & mask_z

        return box_mask.float()

    def dice_loss(self, pred_mask: torch.Tensor, target_mask: torch.Tensor,
                  smooth: float = 1e-6) -> torch.Tensor:
        """
        Compute Dice coefficient loss
        """
        intersection = torch.sum(pred_mask * target_mask, dim=[1, 2, 3])
        union = torch.sum(pred_mask, dim=[1, 2, 3]) + torch.sum(target_mask, dim=[1, 2, 3])

        dice = (2.0 * intersection + smooth) / (union + smooth)
        return 1.0 - dice.mean()

    def iou_3d_loss(self, pred_mask: torch.Tensor, target_mask: torch.Tensor,
                    smooth: float = 1e-6) -> torch.Tensor:
        """
        Compute 3D Intersection over Union loss
        """
        intersection = torch.sum(pred_mask * target_mask, dim=[1, 2, 3])
        union = torch.sum(pred_mask, dim=[1, 2, 3]) + torch.sum(target_mask, dim=[1, 2, 3]) - intersection

        iou = (intersection + smooth) / (union + smooth)
        return 1.0 - iou.mean()

    def focal_loss(self, pred_mask: torch.Tensor, target_mask: torch.Tensor) -> torch.Tensor:
        """
        Compute Focal loss for handling class imbalance
        """
        # Flatten masks
        pred_flat = pred_mask.view(-1)
        target_flat = target_mask.view(-1)

        # Compute binary cross entropy
        bce = F.binary_cross_entropy(pred_flat, target_flat, reduction='none')

        # Compute focal weight
        pt = torch.where(target_flat == 1, pred_flat, 1 - pred_flat)
        focal_weight = self.focal_alpha * (1 - pt) ** self.focal_gamma

        focal_loss = focal_weight * bce
        return focal_loss.mean()

    def forward(self, box_params: Dict[str, torch.Tensor],
                target_masks: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Compute multi-objective bounding box loss
        Args:
            box_params: Dictionary with 'center' and 'size' tensors
            target_masks: [batch_size, volume_size, volume_size, volume_size]
        Returns:
            Dictionary with individual and total losses
        """
        # Create predicted box masks
        pred_masks = self.create_box_mask(box_params['center'], box_params['size'])

        # Compute individual losses
        dice_loss_val = self.dice_loss(pred_masks, target_masks)
        iou_loss_val = self.iou_3d_loss(pred_masks, target_masks)
        focal_loss_val = self.focal_loss(pred_masks, target_masks)

        # Compute weighted total loss
        total_loss = (self.dice_weight * dice_loss_val +
                     self.iou_weight * iou_loss_val +
                     self.focal_weight * focal_loss_val)

        return {
            'total_loss': total_loss,
            'dice_loss': dice_loss_val,
            'iou_loss': iou_loss_val,
            'focal_loss': focal_loss_val,
            'pred_masks': pred_masks
        }

def main():
    """Test the cubic bounding box components"""
    print("Testing Cubic Bounding Box Network Components")
    print("="*60)

    # Test parameters
    batch_size = 2
    input_dim = 1024
    volume_size = 256

    # Create test data
    features = torch.randn(batch_size, input_dim)
    target_masks = torch.zeros(batch_size, volume_size, volume_size, volume_size)

    # Add some lesion regions to target masks
    target_masks[0, 100:132, 120:152, 110:142] = 1.0  # 32x32x32 lesion
    target_masks[1, 80:96, 90:116, 85:101] = 1.0      # 16x26x16 lesion

    print(f"Input features shape: {features.shape}")
    print(f"Target masks shape: {target_masks.shape}")

    # Test Adaptive Cubic Bounding Box
    print("\n1. Testing Adaptive Cubic Bounding Box...")
    bbox_network = AdaptiveCubicBoundingBox(input_dim=input_dim, volume_size=volume_size)
    box_params = bbox_network(features)

    print(f"Predicted centers: {box_params['center']}")
    print(f"Predicted sizes: {box_params['size']}")
    print(f"Scale weights: {box_params['scale_weights']}")

    # Test Bounding Box Loss
    print("\n2. Testing Bounding Box Loss...")
    bbox_loss = BoundingBoxLoss()
    loss_results = bbox_loss(box_params, target_masks)

    print(f"Total loss: {loss_results['total_loss']:.4f}")
    print(f"Dice loss: {loss_results['dice_loss']:.4f}")
    print(f"IoU loss: {loss_results['iou_loss']:.4f}")
    print(f"Focal loss: {loss_results['focal_loss']:.4f}")

    print("\n✓ All cubic bounding box components working correctly!")

if __name__ == "__main__":
    main()
