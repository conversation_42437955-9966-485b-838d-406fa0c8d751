#!/usr/bin/env python3
"""
Fixed Training System for Epilepsy Lesion Localization
Addresses the identified issues:
1. EEG data normalization
2. Gradient flow problems
3. Proper loss scaling
4. Optimal learning rate
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import json
import time
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# Import our modules
from training_pipeline import EpilepsyLocalizationModel, EEGLesionDataset
from epilepsy_localization_network import BoundingBoxLoss
from torch.utils.data import DataLoader

# Set random seeds for reproducibility
torch.manual_seed(42)
np.random.seed(42)

class NormalizedEEGLesionDataset(EEGLesionDataset):
    """Enhanced dataset with proper EEG normalization"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.eeg_stats = None
        self._compute_normalization_stats()
    
    def _compute_normalization_stats(self):
        """Compute EEG normalization statistics"""
        print("Computing EEG normalization statistics...")
        
        all_eeg_values = []
        
        # Sample a few recordings to compute stats
        for i in range(min(10, len(self.pairings))):
            try:
                pairing = self.pairings[i]
                eeg_data = self.load_eeg_data(pairing)
                all_eeg_values.extend(eeg_data.flatten().tolist())
            except:
                continue
        
        if all_eeg_values:
            self.eeg_stats = {
                'mean': np.mean(all_eeg_values),
                'std': np.std(all_eeg_values)
            }
            print(f"EEG normalization stats: mean={self.eeg_stats['mean']:.2f}, std={self.eeg_stats['std']:.2f}")
        else:
            # Fallback values
            self.eeg_stats = {'mean': 4000.0, 'std': 500.0}
            print("Using fallback normalization stats")
    
    def normalize_eeg(self, eeg_data):
        """Apply z-score normalization to EEG data"""
        if self.eeg_stats is not None:
            normalized = (eeg_data - self.eeg_stats['mean']) / self.eeg_stats['std']
            return normalized
        return eeg_data
    
    def __getitem__(self, idx):
        """Get normalized EEG-lesion pair"""
        result = super().__getitem__(idx)
        
        # Normalize EEG data
        result['eeg_data'] = self.normalize_eeg(result['eeg_data'])
        
        # Also normalize temporal sequence
        if 'temporal_sequence' in result:
            # Normalize each time step
            for t in range(result['temporal_sequence'].shape[0]):
                result['temporal_sequence'][t] = self.normalize_eeg(result['temporal_sequence'][t])
        
        return result

class FixedBoundingBoxLoss(BoundingBoxLoss):
    """Fixed loss function with proper scaling and gradient handling"""
    
    def __init__(self, dice_weight=0.4, iou_weight=0.4, focal_weight=0.2):
        super().__init__(dice_weight, iou_weight, focal_weight)
        
    def create_box_mask(self, center, size, volume_size=256):
        """Create binary mask for cubic bounding box with gradient support"""
        batch_size = center.shape[0]
        device = center.device
        
        # Create coordinate grids
        coords = torch.arange(volume_size, device=device, dtype=torch.float32)
        z, y, x = torch.meshgrid(coords, coords, coords, indexing='ij')
        
        # Expand for batch processing
        x = x.unsqueeze(0).expand(batch_size, -1, -1, -1)
        y = y.unsqueeze(0).expand(batch_size, -1, -1, -1)
        z = z.unsqueeze(0).expand(batch_size, -1, -1, -1)
        
        # Expand center and size for broadcasting
        center_x = center[:, 0].view(batch_size, 1, 1, 1)
        center_y = center[:, 1].view(batch_size, 1, 1, 1)
        center_z = center[:, 2].view(batch_size, 1, 1, 1)
        
        size_x = size[:, 0].view(batch_size, 1, 1, 1)
        size_y = size[:, 1].view(batch_size, 1, 1, 1)
        size_z = size[:, 2].view(batch_size, 1, 1, 1)
        
        # Create soft box masks using sigmoid for differentiability
        sigma = 2.0  # Controls sharpness of boundaries
        
        mask_x = torch.sigmoid(sigma * (size_x / 2 - torch.abs(x - center_x)))
        mask_y = torch.sigmoid(sigma * (size_y / 2 - torch.abs(y - center_y)))
        mask_z = torch.sigmoid(sigma * (size_z / 2 - torch.abs(z - center_z)))
        
        # Combine masks
        box_mask = mask_x * mask_y * mask_z
        
        return box_mask
    
    def dice_loss(self, pred_mask, target_mask, smooth=1e-6):
        """Compute Dice coefficient loss with proper normalization"""
        # Flatten masks
        pred_flat = pred_mask.view(pred_mask.shape[0], -1)
        target_flat = target_mask.view(target_mask.shape[0], -1)
        
        intersection = torch.sum(pred_flat * target_flat, dim=1)
        union = torch.sum(pred_flat, dim=1) + torch.sum(target_flat, dim=1)
        
        dice = (2.0 * intersection + smooth) / (union + smooth)
        return 1.0 - dice.mean()
    
    def iou_3d_loss(self, pred_mask, target_mask, smooth=1e-6):
        """Compute 3D IoU loss with proper normalization"""
        # Flatten masks
        pred_flat = pred_mask.view(pred_mask.shape[0], -1)
        target_flat = target_mask.view(target_mask.shape[0], -1)
        
        intersection = torch.sum(pred_flat * target_flat, dim=1)
        union = torch.sum(pred_flat, dim=1) + torch.sum(target_flat, dim=1) - intersection
        
        iou = (intersection + smooth) / (union + smooth)
        return 1.0 - iou.mean()
    
    def focal_loss(self, pred_mask, target_mask):
        """Compute Focal loss with proper scaling"""
        # Flatten masks
        pred_flat = pred_mask.view(-1)
        target_flat = target_mask.view(-1)
        
        # Compute binary cross entropy
        bce = nn.functional.binary_cross_entropy(pred_flat, target_flat, reduction='none')
        
        # Compute focal weight
        pt = torch.where(target_flat == 1, pred_flat, 1 - pred_flat)
        focal_weight = self.focal_alpha * (1 - pt) ** self.focal_gamma
        
        focal_loss = focal_weight * bce
        return focal_loss.mean()

class FixedTrainer:
    """Fixed trainer with proper gradient handling"""
    
    def __init__(self, model, device, learning_rate=1e-5):
        self.model = model.to(device)
        self.device = device
        
        # Ensure all parameters require gradients
        for param in self.model.parameters():
            param.requires_grad = True
        
        # Loss function
        self.criterion = FixedBoundingBoxLoss()
        
        # Optimizer with lower learning rate
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=learning_rate,
            weight_decay=1e-6,
            eps=1e-8
        )
        
        # Learning rate scheduler
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='max', factor=0.5, patience=3, verbose=True, min_lr=1e-8
        )
        
        # Training history
        self.history = {
            'train_loss': [], 'val_loss': [],
            'train_dice': [], 'val_dice': [],
            'train_iou': [], 'val_iou': []
        }
    
    def train_epoch(self, train_loader):
        """Train for one epoch with proper error handling"""
        self.model.train()
        
        total_loss = 0.0
        dice_scores = []
        iou_scores = []
        num_batches = 0
        
        pbar = tqdm(train_loader, desc="Training")
        for batch_idx, batch in enumerate(pbar):
            try:
                # Move data to device
                eeg_data = batch['eeg_data'].to(self.device)
                temporal_sequence = batch['temporal_sequence'].to(self.device)
                lesion_masks = batch['lesion_mask'].to(self.device)
                
                # Ensure data requires gradients
                eeg_data.requires_grad_(True)
                temporal_sequence.requires_grad_(True)
                
                # Forward pass
                self.optimizer.zero_grad()
                
                outputs = self.model(eeg_data, temporal_sequence)
                bbox_params = outputs['bbox_params']
                
                # Compute loss
                loss_results = self.criterion(bbox_params, lesion_masks)
                loss = loss_results['total_loss']
                
                # Check for invalid loss
                if torch.isnan(loss) or torch.isinf(loss):
                    print(f"Invalid loss at batch {batch_idx}: {loss.item()}")
                    continue
                
                # Backward pass
                loss.backward()
                
                # Gradient clipping
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                
                # Check gradients
                total_norm = 0
                for param in self.model.parameters():
                    if param.grad is not None:
                        param_norm = param.grad.data.norm(2)
                        total_norm += param_norm.item() ** 2
                
                if total_norm > 0:
                    self.optimizer.step()
                    
                    # Compute metrics
                    pred_masks = loss_results['pred_masks']
                    for i in range(pred_masks.shape[0]):
                        pred_mask = pred_masks[i].detach().cpu().numpy()
                        target_mask = lesion_masks[i].cpu().numpy()
                        
                        # Dice score
                        intersection = np.sum(pred_mask * target_mask)
                        union = np.sum(pred_mask) + np.sum(target_mask)
                        dice = (2.0 * intersection) / (union + 1e-6)
                        dice_scores.append(dice)
                        
                        # IoU score
                        iou = intersection / (union - intersection + 1e-6)
                        iou_scores.append(iou)
                    
                    total_loss += loss.item()
                    num_batches += 1
                    
                    # Update progress bar
                    pbar.set_postfix({
                        'Loss': f"{loss.item():.4f}",
                        'Dice': f"{np.mean(dice_scores[-2:]):.3f}" if dice_scores else "0.000",
                        'IoU': f"{np.mean(iou_scores[-2:]):.3f}" if iou_scores else "0.000"
                    })
                else:
                    print(f"No gradients at batch {batch_idx}")
                    
            except Exception as e:
                print(f"Error in batch {batch_idx}: {e}")
                continue
        
        # Compute epoch metrics
        avg_loss = total_loss / max(num_batches, 1)
        avg_dice = np.mean(dice_scores) if dice_scores else 0.0
        avg_iou = np.mean(iou_scores) if iou_scores else 0.0
        
        return {
            'loss': avg_loss,
            'dice': avg_dice,
            'iou': avg_iou,
            'num_batches': num_batches
        }
    
    def validate_epoch(self, val_loader):
        """Validate for one epoch"""
        self.model.eval()
        
        total_loss = 0.0
        dice_scores = []
        iou_scores = []
        num_batches = 0
        
        with torch.no_grad():
            for batch_idx, batch in enumerate(val_loader):
                try:
                    # Move data to device
                    eeg_data = batch['eeg_data'].to(self.device)
                    temporal_sequence = batch['temporal_sequence'].to(self.device)
                    lesion_masks = batch['lesion_mask'].to(self.device)
                    
                    # Forward pass
                    outputs = self.model(eeg_data, temporal_sequence)
                    bbox_params = outputs['bbox_params']
                    
                    # Compute loss
                    loss_results = self.criterion(bbox_params, lesion_masks)
                    loss = loss_results['total_loss']
                    
                    if not torch.isnan(loss) and not torch.isinf(loss):
                        # Compute metrics
                        pred_masks = loss_results['pred_masks']
                        for i in range(pred_masks.shape[0]):
                            pred_mask = pred_masks[i].cpu().numpy()
                            target_mask = lesion_masks[i].cpu().numpy()
                            
                            # Dice score
                            intersection = np.sum(pred_mask * target_mask)
                            union = np.sum(pred_mask) + np.sum(target_mask)
                            dice = (2.0 * intersection) / (union + 1e-6)
                            dice_scores.append(dice)
                            
                            # IoU score
                            iou = intersection / (union - intersection + 1e-6)
                            iou_scores.append(iou)
                        
                        total_loss += loss.item()
                        num_batches += 1
                        
                except Exception as e:
                    print(f"Validation error in batch {batch_idx}: {e}")
                    continue
        
        # Compute epoch metrics
        avg_loss = total_loss / max(num_batches, 1)
        avg_dice = np.mean(dice_scores) if dice_scores else 0.0
        avg_iou = np.mean(iou_scores) if iou_scores else 0.0
        
        return {
            'loss': avg_loss,
            'dice': avg_dice,
            'iou': avg_iou,
            'num_batches': num_batches
        }
    
    def train(self, train_loader, val_loader, num_epochs=20):
        """Complete training loop"""
        print(f"Starting fixed training for {num_epochs} epochs...")
        
        best_val_iou = 0.0
        patience_counter = 0
        max_patience = 10
        
        for epoch in range(num_epochs):
            start_time = time.time()
            
            # Train epoch
            train_metrics = self.train_epoch(train_loader)
            
            # Validate epoch
            val_metrics = self.validate_epoch(val_loader)
            
            # Update learning rate
            self.scheduler.step(val_metrics['iou'])
            
            # Update history
            self.history['train_loss'].append(train_metrics['loss'])
            self.history['val_loss'].append(val_metrics['loss'])
            self.history['train_dice'].append(train_metrics['dice'])
            self.history['val_dice'].append(val_metrics['dice'])
            self.history['train_iou'].append(train_metrics['iou'])
            self.history['val_iou'].append(val_metrics['iou'])
            
            # Print epoch summary
            epoch_time = time.time() - start_time
            print(f"\nEpoch {epoch+1}/{num_epochs} ({epoch_time:.1f}s)")
            print(f"Train - Loss: {train_metrics['loss']:.4f}, Dice: {train_metrics['dice']:.3f}, IoU: {train_metrics['iou']:.3f}")
            print(f"Val   - Loss: {val_metrics['loss']:.4f}, Dice: {val_metrics['dice']:.3f}, IoU: {val_metrics['iou']:.3f}")
            
            # Early stopping and model saving
            if val_metrics['iou'] > best_val_iou:
                best_val_iou = val_metrics['iou']
                patience_counter = 0
                
                # Save best model
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'val_iou': best_val_iou,
                    'history': self.history
                }, 'fixed_best_model.pth')
                print(f"✓ New best model saved (IoU: {best_val_iou:.3f})")
            else:
                patience_counter += 1
                
            if patience_counter >= max_patience:
                print(f"Early stopping triggered after {epoch+1} epochs")
                break
        
        print(f"\nTraining completed! Best validation IoU: {best_val_iou:.3f}")
        return self.history

def create_fixed_data_loaders(batch_size=2):
    """Create data loaders with normalized EEG data"""
    
    # Create normalized datasets
    train_dataset = NormalizedEEGLesionDataset(
        "eeg_lesion_training_dataset/train_pairings.pkl", 
        augment=True
    )
    val_dataset = NormalizedEEGLesionDataset(
        "eeg_lesion_training_dataset/validation_pairings.pkl", 
        augment=False
    )
    
    # Create data loaders
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    
    return train_loader, val_loader

def main():
    """Main fixed training function"""
    print("Fixed Epilepsy Lesion Localization Training")
    print("="*50)
    
    # Check device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Create fixed data loaders
    try:
        train_loader, val_loader = create_fixed_data_loaders(batch_size=2)
        print("✅ Fixed data loaders created with EEG normalization")
    except Exception as e:
        print(f"❌ Error creating data loaders: {e}")
        return
    
    # Create model
    model = EpilepsyLocalizationModel()
    print(f"Model created with {sum(p.numel() for p in model.parameters()):,} parameters")
    
    # Create fixed trainer
    trainer = FixedTrainer(model, device, learning_rate=1e-5)
    
    # Start training
    history = trainer.train(train_loader, val_loader, num_epochs=15)
    
    # Plot training curves
    plt.figure(figsize=(15, 5))
    
    plt.subplot(1, 3, 1)
    plt.plot(history['train_loss'], label='Train Loss')
    plt.plot(history['val_loss'], label='Val Loss')
    plt.title('Loss Curves')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.subplot(1, 3, 2)
    plt.plot(history['train_dice'], label='Train Dice')
    plt.plot(history['val_dice'], label='Val Dice')
    plt.title('Dice Score Curves')
    plt.xlabel('Epoch')
    plt.ylabel('Dice Score')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.subplot(1, 3, 3)
    plt.plot(history['train_iou'], label='Train IoU')
    plt.plot(history['val_iou'], label='Val IoU')
    plt.title('IoU Score Curves')
    plt.xlabel('Epoch')
    plt.ylabel('IoU Score')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('fixed_training_curves.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("\n✅ Fixed training completed successfully!")
    print("✅ Best model saved as: fixed_best_model.pth")
    print("✅ Training curves saved as: fixed_training_curves.png")

if __name__ == "__main__":
    main()
