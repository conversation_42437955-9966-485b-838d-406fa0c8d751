#!/usr/bin/env python3
"""
EEG Dataset Analysis and Visualization Script
Analyzes EEG data from Guinea-Bissau and Nigeria datasets and creates topographic maps
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import gzip
import os
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Try to import MNE, install if not available
try:
    import mne
    from mne.channels import make_standard_montage
    from mne import create_info, EvokedArray
    print("MNE-Python is available")
except ImportError:
    print("MNE-Python not found. Installing...")
    import subprocess
    import sys
    subprocess.check_call([sys.executable, "-m", "pip", "install", "mne"])
    import mne
    from mne.channels import make_standard_montage
    from mne import create_info, EvokedArray
    print("MNE-Python installed and imported successfully")

# Set up matplotlib for better plots
plt.style.use('default')
mne.set_log_level('WARNING')

class EEGDatasetAnalyzer:
    def __init__(self, dataset_path="1252141"):
        self.dataset_path = Path(dataset_path)
        self.guinea_bissau_path = self.dataset_path / "EEGs_Guinea-Bissau"
        self.nigeria_path = self.dataset_path / "EEGs_Nigeria"
        
        # EEG channel names (14 channels)
        self.eeg_channels = ['AF3', 'AF4', 'F3', 'F4', 'F7', 'F8', 'FC5', 'FC6', 
                            'O1', 'O2', 'P7', 'P8', 'T7', 'T8']
        
        # Load metadata
        self.metadata_gb = self._load_metadata("metadata_guineabissau.csv")
        self.metadata_ng = self._load_metadata("metadata_nigeria.csv")
        
        print(f"Dataset Analysis Initialized")
        print(f"Guinea-Bissau subjects: {len(self.metadata_gb)}")
        print(f"Nigeria subjects: {len(self.metadata_ng)}")
        
    def _load_metadata(self, filename):
        """Load metadata CSV file"""
        filepath = self.dataset_path / filename
        return pd.read_csv(filepath)
    
    def analyze_dataset_structure(self):
        """Analyze and print dataset structure information"""
        print("\n" + "="*60)
        print("DATASET STRUCTURE ANALYSIS")
        print("="*60)
        
        # Guinea-Bissau Analysis
        print("\n--- GUINEA-BISSAU DATASET ---")
        print(f"Number of subjects: {len(self.metadata_gb)}")
        print(f"Groups: {self.metadata_gb['Group'].value_counts().to_dict()}")
        print(f"Eye conditions: {self.metadata_gb['Eyes.condition'].value_counts().to_dict()}")
        print(f"Recording periods (seconds): {self.metadata_gb['recordedPeriod'].describe()}")
        
        # Nigeria Analysis  
        print("\n--- NIGERIA DATASET ---")
        print(f"Number of subjects: {len(self.metadata_ng)}")
        print(f"Groups: {self.metadata_ng['Group'].value_counts().to_dict()}")
        print(f"First conditions: {self.metadata_ng['first_condition'].value_counts().to_dict()}")
        print(f"Recording periods (seconds): {self.metadata_ng['recordedPeriod'].describe()}")
        
        # EEG Channel Information
        print("\n--- EEG CHANNEL INFORMATION ---")
        print(f"Number of EEG channels: {len(self.eeg_channels)}")
        print(f"Channel names: {self.eeg_channels}")
        print("Channel locations based on 10-20 system:")
        channel_locations = {
            'AF3/AF4': 'Anterior Frontal (left/right)',
            'F3/F4': 'Frontal (left/right)', 
            'F7/F8': 'Frontal Temporal (left/right)',
            'FC5/FC6': 'Frontal Central (left/right)',
            'O1/O2': 'Occipital (left/right)',
            'P7/P8': 'Parietal Temporal (left/right)',
            'T7/T8': 'Temporal (left/right)'
        }
        for location, description in channel_locations.items():
            print(f"  {location}: {description}")
            
    def load_eeg_signal(self, dataset='guinea_bissau', subject_id=1):
        """Load EEG signal data for a specific subject"""
        if dataset == 'guinea_bissau':
            filepath = self.guinea_bissau_path / f"signal-{subject_id}.csv.gz"
        else:  # nigeria
            # Find the correct filename for Nigeria dataset
            matching_files = list(self.nigeria_path.glob(f"signal-{subject_id}-*.csv.gz"))
            if not matching_files:
                raise FileNotFoundError(f"No signal file found for subject {subject_id} in Nigeria dataset")
            filepath = matching_files[0]
        
        print(f"Loading: {filepath}")
        
        # Load compressed CSV
        with gzip.open(filepath, 'rt') as f:
            data = pd.read_csv(f)
        
        # Extract EEG channels only
        eeg_data = data[self.eeg_channels].values.T  # Transpose for MNE format (channels x time)
        
        # Get sampling rate (assuming 128 Hz based on typical EEG systems)
        sampling_rate = 128  # Hz
        
        return eeg_data, sampling_rate, data
    
    def create_mne_raw_object(self, eeg_data, sampling_rate):
        """Create MNE Raw object from EEG data"""
        # Create info object
        info = create_info(
            ch_names=self.eeg_channels,
            sfreq=sampling_rate,
            ch_types=['eeg'] * len(self.eeg_channels)
        )
        
        # Create Raw object
        raw = mne.io.RawArray(eeg_data, info)
        
        # Set standard montage for electrode positions
        montage = make_standard_montage('standard_1020')
        raw.set_montage(montage, match_case=False)
        
        return raw
    
    def create_topographic_maps(self, dataset='guinea_bissau', subject_ids=[1, 2], 
                               time_window=(60, 120), save_plots=True):
        """Create topographic maps showing spatial distribution of brain activity"""
        print(f"\n--- CREATING TOPOGRAPHIC MAPS ---")
        
        fig, axes = plt.subplots(2, len(subject_ids), figsize=(5*len(subject_ids), 10))
        if len(subject_ids) == 1:
            axes = axes.reshape(-1, 1)
        
        for idx, subject_id in enumerate(subject_ids):
            try:
                # Load EEG data
                eeg_data, sampling_rate, raw_data = self.load_eeg_signal(dataset, subject_id)
                
                # Create MNE Raw object
                raw = self.create_mne_raw_object(eeg_data, sampling_rate)
                
                # Extract time window (convert seconds to samples)
                start_sample = int(time_window[0] * sampling_rate)
                end_sample = int(time_window[1] * sampling_rate)
                
                if end_sample > eeg_data.shape[1]:
                    end_sample = eeg_data.shape[1]
                    print(f"Adjusted end time for subject {subject_id}")
                
                # Calculate mean amplitude in time window
                mean_data = np.mean(eeg_data[:, start_sample:end_sample], axis=1)
                
                # Create evoked object for topographic plotting
                evoked_data = mean_data.reshape(-1, 1)
                info = create_info(
                    ch_names=self.eeg_channels,
                    sfreq=sampling_rate,
                    ch_types=['eeg'] * len(self.eeg_channels)
                )
                evoked = EvokedArray(evoked_data, info, tmin=0)
                montage = make_standard_montage('standard_1020')
                evoked.set_montage(montage, match_case=False)
                
                # Plot topographic map - Mean Activity
                ax1 = axes[0, idx] if len(subject_ids) > 1 else axes[0]
                im1, _ = mne.viz.plot_topomap(
                    mean_data, evoked.info, axes=ax1, show=False,
                    cmap='RdBu_r', contours=6
                )
                ax1.set_title(f'Subject {subject_id}\nMean Activity ({time_window[0]}-{time_window[1]}s)')
                
                # Calculate and plot power (variance)
                power_data = np.var(eeg_data[:, start_sample:end_sample], axis=1)
                ax2 = axes[1, idx] if len(subject_ids) > 1 else axes[1]
                im2, _ = mne.viz.plot_topomap(
                    power_data, evoked.info, axes=ax2, show=False,
                    cmap='viridis', contours=6
                )
                ax2.set_title(f'Subject {subject_id}\nPower/Variance ({time_window[0]}-{time_window[1]}s)')
                
                # Get subject info from metadata
                if dataset == 'guinea_bissau':
                    subject_info = self.metadata_gb[self.metadata_gb['subject.id'] == subject_id]
                    if not subject_info.empty:
                        group = subject_info.iloc[0]['Group']
                        condition = subject_info.iloc[0]['Eyes.condition']
                        print(f"Subject {subject_id}: {group}, {condition}")
                
            except Exception as e:
                print(f"Error processing subject {subject_id}: {e}")
                continue
        
        plt.tight_layout()
        
        if save_plots:
            filename = f"topographic_maps_{dataset}_subjects_{'_'.join(map(str, subject_ids))}.png"
            plt.savefig(filename, dpi=300, bbox_inches='tight')
            print(f"Topographic maps saved as: {filename}")
        
        plt.show()
        
        return fig
    
    def analyze_channel_statistics(self, dataset='guinea_bissau', n_subjects=5):
        """Analyze statistics across channels for multiple subjects"""
        print(f"\n--- CHANNEL STATISTICS ANALYSIS ---")
        
        channel_stats = {channel: [] for channel in self.eeg_channels}
        
        subject_ids = range(1, n_subjects + 1)
        
        for subject_id in subject_ids:
            try:
                eeg_data, _, _ = self.load_eeg_signal(dataset, subject_id)
                
                for i, channel in enumerate(self.eeg_channels):
                    # Calculate mean absolute amplitude
                    mean_amp = np.mean(np.abs(eeg_data[i, :]))
                    channel_stats[channel].append(mean_amp)
                    
            except Exception as e:
                print(f"Skipping subject {subject_id}: {e}")
                continue
        
        # Create summary statistics
        stats_df = pd.DataFrame(channel_stats)
        
        print("\nChannel Statistics Summary (Mean Absolute Amplitude):")
        print(stats_df.describe())
        
        # Plot channel comparison
        plt.figure(figsize=(12, 6))
        stats_df.boxplot()
        plt.title(f'EEG Channel Amplitude Distribution ({dataset.replace("_", " ").title()})')
        plt.ylabel('Mean Absolute Amplitude (µV)')
        plt.xlabel('EEG Channels')
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        if True:  # save_plots
            filename = f"channel_statistics_{dataset}.png"
            plt.savefig(filename, dpi=300, bbox_inches='tight')
            print(f"Channel statistics plot saved as: {filename}")
        
        plt.show()
        
        return stats_df

def main():
    """Main analysis function"""
    print("EEG Dataset Analysis Starting...")
    
    # Initialize analyzer
    analyzer = EEGDatasetAnalyzer()
    
    # Analyze dataset structure
    analyzer.analyze_dataset_structure()
    
    # Create topographic maps for Guinea-Bissau dataset
    print("\n" + "="*60)
    print("CREATING VISUALIZATIONS")
    print("="*60)
    
    # Analyze a few subjects from Guinea-Bissau
    analyzer.create_topographic_maps(
        dataset='guinea_bissau', 
        subject_ids=[1, 2, 3], 
        time_window=(60, 120)
    )
    
    # Analyze channel statistics
    analyzer.analyze_channel_statistics(dataset='guinea_bissau', n_subjects=10)
    
    print("\nAnalysis completed successfully!")

if __name__ == "__main__":
    main()
