#!/usr/bin/env python3
"""
Task 2: Autoencoder Reconstruction Analysis
Train the 1D Attention-CNN Autoencoder branch independently and analyze reconstruction quality
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import gzip
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

class EEGAutoencoderDataset(torch.utils.data.Dataset):
    """EEG数据集用于自编码器训练"""
    
    def __init__(self, data_dir, metadata_file, split='train', test_size=0.2, random_state=42):
        self.data_dir = Path(data_dir)
        
        # 加载元数据
        self.metadata = pd.read_csv(metadata_file)
        print(f"Loaded metadata: {len(self.metadata)} subjects")
        
        # 创建标签映射
        self.label_map = {'Epilepsy': 1, 'Control': 0}
        
        # 准备数据
        self.prepare_data(split, test_size, random_state)
        
    def prepare_data(self, split, test_size, random_state):
        """准备训练/测试数据"""
        from sklearn.model_selection import train_test_split
        
        # 获取可用的EEG文件
        available_files = []
        labels = []
        groups = []
        
        for idx, row in self.metadata.iterrows():
            subject_id = row['subject.id']
            group = row['Group']
            
            # 构建文件路径
            eeg_file = self.data_dir / f"signal-{subject_id}.csv.gz"
            
            if eeg_file.exists():
                available_files.append(str(eeg_file))
                labels.append(self.label_map[group])
                groups.append(group)
        
        print(f"Found {len(available_files)} available EEG files")
        print(f"Epilepsy: {sum(labels)}, Control: {len(labels) - sum(labels)}")
        
        # 分割数据
        if split == 'all':
            self.file_paths = available_files
            self.labels = labels
            self.groups = groups
        else:
            train_files, test_files, train_labels, test_labels, train_groups, test_groups = train_test_split(
                available_files, labels, groups, test_size=test_size, 
                random_state=random_state, stratify=labels
            )
            
            if split == 'train':
                self.file_paths = train_files
                self.labels = train_labels
                self.groups = train_groups
            else:  # test
                self.file_paths = test_files
                self.labels = test_labels
                self.groups = test_groups
        
        print(f"{split.upper()} set: {len(self.file_paths)} samples")
        print(f"  Epilepsy: {sum(self.labels)}, Control: {len(self.labels) - sum(self.labels)}")
    
    def load_eeg_signal(self, file_path):
        """加载EEG信号"""
        try:
            with gzip.open(file_path, 'rt') as f:
                df = pd.read_csv(f)
            
            # 选择14个EEG通道
            eeg_channels = ['AF3', 'AF4', 'F3', 'F4', 'F7', 'F8', 'FC5', 'FC6', 
                           'O1', 'O2', 'P7', 'P8', 'T7', 'T8']
            
            eeg_data = df[eeg_channels].values.T  # [14, time_points]
            
            # 标准化
            eeg_data = (eeg_data - np.mean(eeg_data, axis=1, keepdims=True)) / (np.std(eeg_data, axis=1, keepdims=True) + 1e-8)
            
            # 截取或填充到固定长度
            target_length = 1024
            if eeg_data.shape[1] > target_length:
                eeg_data = eeg_data[:, :target_length]
            elif eeg_data.shape[1] < target_length:
                pad_width = target_length - eeg_data.shape[1]
                eeg_data = np.pad(eeg_data, ((0, 0), (0, pad_width)), mode='constant')
            
            return eeg_data.astype(np.float32)
            
        except Exception as e:
            print(f"Error loading {file_path}: {e}")
            return np.zeros((14, 1024), dtype=np.float32)
    
    def __len__(self):
        return len(self.file_paths)
    
    def __getitem__(self, idx):
        # 加载EEG数据
        eeg_data = self.load_eeg_signal(self.file_paths[idx])
        
        return {
            'eeg_data': torch.FloatTensor(eeg_data),
            'label': torch.LongTensor([self.labels[idx]])[0],
            'group': self.groups[idx],
            'file_path': self.file_paths[idx]
        }

class AttentionCNNAutoencoder(nn.Module):
    """1D Attention-CNN自编码器"""
    
    def __init__(self, n_channels=14, feature_dim=512):
        super().__init__()
        
        # 编码器
        self.encoder = nn.Sequential(
            # 第一层
            nn.Conv1d(n_channels, 64, kernel_size=7, padding=3),
            nn.BatchNorm1d(64),
            nn.ReLU(inplace=True),
            nn.MaxPool1d(2),  # 1024 -> 512
            
            # 第二层
            nn.Conv1d(64, 128, kernel_size=5, padding=2),
            nn.BatchNorm1d(128),
            nn.ReLU(inplace=True),
            nn.MaxPool1d(2),  # 512 -> 256
            
            # 第三层
            nn.Conv1d(128, 256, kernel_size=3, padding=1),
            nn.BatchNorm1d(256),
            nn.ReLU(inplace=True),
            nn.MaxPool1d(2),  # 256 -> 128
            
            # 第四层
            nn.Conv1d(256, feature_dim, kernel_size=3, padding=1),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(inplace=True),
            nn.MaxPool1d(2),  # 128 -> 64
        )
        
        # 多头注意力机制
        self.attention = nn.MultiheadAttention(
            embed_dim=feature_dim, 
            num_heads=8, 
            batch_first=True
        )
        
        # 解码器
        self.decoder = nn.Sequential(
            # 上采样层1
            nn.ConvTranspose1d(feature_dim, 256, kernel_size=4, stride=2, padding=1),  # 64 -> 128
            nn.BatchNorm1d(256),
            nn.ReLU(inplace=True),
            
            # 上采样层2
            nn.ConvTranspose1d(256, 128, kernel_size=4, stride=2, padding=1),  # 128 -> 256
            nn.BatchNorm1d(128),
            nn.ReLU(inplace=True),
            
            # 上采样层3
            nn.ConvTranspose1d(128, 64, kernel_size=4, stride=2, padding=1),  # 256 -> 512
            nn.BatchNorm1d(64),
            nn.ReLU(inplace=True),
            
            # 上采样层4
            nn.ConvTranspose1d(64, n_channels, kernel_size=4, stride=2, padding=1),  # 512 -> 1024
        )
        
        # 位置编码
        self.pos_encoding = nn.Parameter(torch.randn(1, 64, feature_dim) * 0.1)
    
    def forward(self, x, return_attention=False):
        # 编码
        encoded = self.encoder(x)  # [batch, feature_dim, 64]
        
        # 转换为注意力输入格式
        batch_size, feature_dim, seq_len = encoded.shape
        encoded_transposed = encoded.transpose(1, 2)  # [batch, 64, feature_dim]
        
        # 添加位置编码
        encoded_with_pos = encoded_transposed + self.pos_encoding
        
        # 多头注意力
        attended, attention_weights = self.attention(
            encoded_with_pos, encoded_with_pos, encoded_with_pos
        )
        
        # 转换回卷积格式
        attended = attended.transpose(1, 2)  # [batch, feature_dim, 64]
        
        # 解码
        reconstructed = self.decoder(attended)
        
        if return_attention:
            return reconstructed, attention_weights, encoded
        else:
            return reconstructed

class AutoencoderTrainer:
    """自编码器训练器"""
    
    def __init__(self, model, device):
        self.model = model.to(device)
        self.device = device
        
        # 损失函数和优化器
        self.criterion = nn.MSELoss()
        self.optimizer = optim.Adam(self.model.parameters(), lr=1e-4, weight_decay=1e-5)
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='min', factor=0.5, patience=5, verbose=True
        )
        
        # 训练历史
        self.history = {
            'train_loss': [], 'val_loss': [],
            'epilepsy_loss': [], 'control_loss': []
        }
    
    def train_epoch(self, train_loader):
        """训练一个epoch"""
        self.model.train()
        
        total_loss = 0.0
        num_batches = 0
        
        for batch in tqdm(train_loader, desc="Training"):
            eeg_data = batch['eeg_data'].to(self.device)
            
            self.optimizer.zero_grad()
            
            # 前向传播
            reconstructed = self.model(eeg_data)
            loss = self.criterion(reconstructed, eeg_data)
            
            # 反向传播
            loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            self.optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
        
        avg_loss = total_loss / num_batches
        return avg_loss
    
    def validate_epoch(self, val_loader):
        """验证一个epoch"""
        self.model.eval()
        
        total_loss = 0.0
        epilepsy_losses = []
        control_losses = []
        num_batches = 0
        
        with torch.no_grad():
            for batch in val_loader:
                eeg_data = batch['eeg_data'].to(self.device)
                labels = batch['label']
                
                # 前向传播
                reconstructed = self.model(eeg_data)
                loss = self.criterion(reconstructed, eeg_data)
                
                total_loss += loss.item()
                num_batches += 1
                
                # 按类别分别计算损失
                for i in range(eeg_data.shape[0]):
                    sample_loss = self.criterion(
                        reconstructed[i:i+1], eeg_data[i:i+1]
                    ).item()
                    
                    if labels[i].item() == 1:  # Epilepsy
                        epilepsy_losses.append(sample_loss)
                    else:  # Control
                        control_losses.append(sample_loss)
        
        avg_loss = total_loss / num_batches
        avg_epilepsy_loss = np.mean(epilepsy_losses) if epilepsy_losses else 0
        avg_control_loss = np.mean(control_losses) if control_losses else 0
        
        return avg_loss, avg_epilepsy_loss, avg_control_loss
    
    def train(self, train_loader, val_loader, num_epochs=30):
        """完整训练循环"""
        print(f"开始自编码器训练，共 {num_epochs} 个epoch...")
        
        best_val_loss = float('inf')
        
        for epoch in range(num_epochs):
            print(f"\nEpoch {epoch+1}/{num_epochs}")
            
            # 训练
            train_loss = self.train_epoch(train_loader)
            
            # 验证
            val_loss, epilepsy_loss, control_loss = self.validate_epoch(val_loader)
            
            # 更新学习率
            self.scheduler.step(val_loss)
            
            # 记录历史
            self.history['train_loss'].append(train_loss)
            self.history['val_loss'].append(val_loss)
            self.history['epilepsy_loss'].append(epilepsy_loss)
            self.history['control_loss'].append(control_loss)
            
            # 打印结果
            print(f"Train Loss: {train_loss:.6f}")
            print(f"Val Loss: {val_loss:.6f}")
            print(f"Epilepsy Loss: {epilepsy_loss:.6f}")
            print(f"Control Loss: {control_loss:.6f}")
            
            # 保存最佳模型
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                torch.save({
                    'model_state_dict': self.model.state_dict(),
                    'val_loss': best_val_loss,
                    'history': self.history
                }, 'attention_autoencoder_best_model.pth')
                print(f"✓ 保存最佳模型 (验证损失: {best_val_loss:.6f})")
        
        print(f"\n训练完成！最佳验证损失: {best_val_loss:.6f}")
        return self.history

def analyze_reconstructions(model, test_loader, device, num_samples=6):
    """分析重建质量"""
    model.eval()
    
    epilepsy_samples = []
    control_samples = []
    
    with torch.no_grad():
        for batch in test_loader:
            eeg_data = batch['eeg_data'].to(device)
            labels = batch['label']
            groups = batch['group']
            
            # 获取重建结果和注意力权重
            reconstructed, attention_weights, encoded = model(eeg_data, return_attention=True)
            
            for i in range(eeg_data.shape[0]):
                sample_data = {
                    'original': eeg_data[i].cpu().numpy(),
                    'reconstructed': reconstructed[i].cpu().numpy(),
                    'attention': attention_weights[i].cpu().numpy(),
                    'encoded': encoded[i].cpu().numpy(),
                    'group': groups[i]
                }
                
                if labels[i].item() == 1 and len(epilepsy_samples) < num_samples//2:
                    epilepsy_samples.append(sample_data)
                elif labels[i].item() == 0 and len(control_samples) < num_samples//2:
                    control_samples.append(sample_data)
                
                if len(epilepsy_samples) >= num_samples//2 and len(control_samples) >= num_samples//2:
                    break
            
            if len(epilepsy_samples) >= num_samples//2 and len(control_samples) >= num_samples//2:
                break
    
    return epilepsy_samples, control_samples

def visualize_reconstructions(epilepsy_samples, control_samples, history):
    """可视化重建结果"""
    
    # 创建大图
    fig = plt.figure(figsize=(20, 16))
    
    # 1. 训练损失曲线
    plt.subplot(3, 4, 1)
    epochs = range(1, len(history['train_loss']) + 1)
    plt.plot(epochs, history['train_loss'], 'b-', label='Training Loss')
    plt.plot(epochs, history['val_loss'], 'r-', label='Validation Loss')
    plt.title('Reconstruction Loss Curves')
    plt.xlabel('Epoch')
    plt.ylabel('MSE Loss')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 2. 按类别的损失对比
    plt.subplot(3, 4, 2)
    plt.plot(epochs, history['epilepsy_loss'], 'r-', label='Epilepsy Loss')
    plt.plot(epochs, history['control_loss'], 'b-', label='Control Loss')
    plt.title('Loss by Class')
    plt.xlabel('Epoch')
    plt.ylabel('MSE Loss')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 3-6. 癫痫样本重建对比
    for i, sample in enumerate(epilepsy_samples[:2]):
        # 原始信号
        plt.subplot(3, 4, 3 + i*2)
        plt.imshow(sample['original'], aspect='auto', cmap='viridis')
        plt.title(f'Epilepsy Original {i+1}')
        plt.ylabel('Channels')
        plt.xlabel('Time')
        
        # 重建信号
        plt.subplot(3, 4, 4 + i*2)
        plt.imshow(sample['reconstructed'], aspect='auto', cmap='viridis')
        plt.title(f'Epilepsy Reconstructed {i+1}')
        plt.ylabel('Channels')
        plt.xlabel('Time')
    
    # 7-10. 对照样本重建对比
    for i, sample in enumerate(control_samples[:2]):
        # 原始信号
        plt.subplot(3, 4, 7 + i*2)
        plt.imshow(sample['original'], aspect='auto', cmap='viridis')
        plt.title(f'Control Original {i+1}')
        plt.ylabel('Channels')
        plt.xlabel('Time')
        
        # 重建信号
        plt.subplot(3, 4, 8 + i*2)
        plt.imshow(sample['reconstructed'], aspect='auto', cmap='viridis')
        plt.title(f'Control Reconstructed {i+1}')
        plt.ylabel('Channels')
        plt.xlabel('Time')
    
    # 11. 注意力热图 - 癫痫
    plt.subplot(3, 4, 11)
    if epilepsy_samples:
        attention_map = np.mean(epilepsy_samples[0]['attention'], axis=0)  # 平均所有头
        plt.imshow(attention_map, aspect='auto', cmap='hot')
        plt.title('Epilepsy Attention Heatmap')
        plt.ylabel('Query Position')
        plt.xlabel('Key Position')
    
    # 12. 注意力热图 - 对照
    plt.subplot(3, 4, 12)
    if control_samples:
        attention_map = np.mean(control_samples[0]['attention'], axis=0)  # 平均所有头
        plt.imshow(attention_map, aspect='auto', cmap='hot')
        plt.title('Control Attention Heatmap')
        plt.ylabel('Query Position')
        plt.xlabel('Key Position')
    
    plt.tight_layout()
    plt.savefig('task2_autoencoder_analysis_results.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_reconstruction_report(history, epilepsy_samples, control_samples):
    """创建重建分析报告"""
    
    print("\n" + "="*60)
    print("AUTOENCODER RECONSTRUCTION ANALYSIS REPORT")
    print("="*60)
    
    # 最终损失
    final_train_loss = history['train_loss'][-1]
    final_val_loss = history['val_loss'][-1]
    final_epilepsy_loss = history['epilepsy_loss'][-1]
    final_control_loss = history['control_loss'][-1]
    
    print(f"\nFinal Reconstruction Losses:")
    print(f"  Training Loss: {final_train_loss:.6f}")
    print(f"  Validation Loss: {final_val_loss:.6f}")
    print(f"  Epilepsy Loss: {final_epilepsy_loss:.6f}")
    print(f"  Control Loss: {final_control_loss:.6f}")
    
    # 损失差异分析
    loss_ratio = final_epilepsy_loss / final_control_loss if final_control_loss > 0 else 1.0
    print(f"\nReconstruction Quality Analysis:")
    print(f"  Epilepsy/Control Loss Ratio: {loss_ratio:.3f}")
    
    if loss_ratio > 1.2:
        print("  ✓ Epilepsy signals are harder to reconstruct (expected)")
    elif loss_ratio < 0.8:
        print("  ⚠ Control signals are harder to reconstruct (unexpected)")
    else:
        print("  ≈ Similar reconstruction difficulty for both classes")
    
    # 样本级分析
    if epilepsy_samples and control_samples:
        epilepsy_mse = [np.mean((s['original'] - s['reconstructed'])**2) for s in epilepsy_samples]
        control_mse = [np.mean((s['original'] - s['reconstructed'])**2) for s in control_samples]
        
        print(f"\nSample-level MSE Analysis:")
        print(f"  Epilepsy samples: {np.mean(epilepsy_mse):.6f} ± {np.std(epilepsy_mse):.6f}")
        print(f"  Control samples: {np.mean(control_mse):.6f} ± {np.std(control_mse):.6f}")

def main():
    """主函数"""
    print("🔄 Task 2: Autoencoder Reconstruction Analysis")
    print("="*70)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # 创建数据集
    data_dir = "1252141/EEGs_Guinea-Bissau"
    metadata_file = "1252141/metadata_guineabissau.csv"
    
    try:
        train_dataset = EEGAutoencoderDataset(data_dir, metadata_file, split='train')
        test_dataset = EEGAutoencoderDataset(data_dir, metadata_file, split='test')
        
        # 创建数据加载器
        train_loader = torch.utils.data.DataLoader(
            train_dataset, batch_size=4, shuffle=True, num_workers=0
        )
        test_loader = torch.utils.data.DataLoader(
            test_dataset, batch_size=4, shuffle=False, num_workers=0
        )
        
        print("✅ 数据集创建成功")
        
    except Exception as e:
        print(f"❌ 数据集创建失败: {e}")
        return
    
    # 创建模型
    model = AttentionCNNAutoencoder(n_channels=14, feature_dim=512)
    param_count = sum(p.numel() for p in model.parameters())
    print(f"注意力CNN自编码器创建成功，参数数量: {param_count:,}")
    
    # 创建训练器
    trainer = AutoencoderTrainer(model, device)
    
    # 开始训练
    history = trainer.train(train_loader, test_loader, num_epochs=25)
    
    # 分析重建质量
    epilepsy_samples, control_samples = analyze_reconstructions(model, test_loader, device)
    
    # 可视化结果
    visualize_reconstructions(epilepsy_samples, control_samples, history)
    
    # 创建分析报告
    create_reconstruction_report(history, epilepsy_samples, control_samples)
    
    print("\n✅ Task 2 完成！")
    print("📊 结果文件:")
    print("  - attention_autoencoder_best_model.pth: 训练好的自编码器模型")
    print("  - task2_autoencoder_analysis_results.png: 重建分析图表")

if __name__ == "__main__":
    main()
