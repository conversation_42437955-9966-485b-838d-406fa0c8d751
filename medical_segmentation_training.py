#!/usr/bin/env python3
"""
完整的EEG-MRI医学分割训练系统
使用医学分割指标评估病灶定位准确率
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, random_split
import numpy as np
import pandas as pd
import nibabel as nib
import gzip
import glob
import os
import random
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from scipy.interpolate import griddata
from scipy.ndimage import zoom
import matplotlib.pyplot as plt
from tqdm import tqdm
import warnings
from collections import defaultdict

warnings.filterwarnings('ignore')

# 设置随机种子
torch.manual_seed(42)
np.random.seed(42)
random.seed(42)

class MedicalSegmentationMetrics:
    """医学分割评估指标"""
    
    @staticmethod
    def dice_coefficient(pred, target, smooth=1e-6):
        """Dice系数 (DSC)"""
        pred = pred.flatten()
        target = target.flatten()
        intersection = (pred * target).sum()
        return (2. * intersection + smooth) / (pred.sum() + target.sum() + smooth)
    
    @staticmethod
    def jaccard_index(pred, target, smooth=1e-6):
        """Jaccard指数 (IoU)"""
        pred = pred.flatten()
        target = target.flatten()
        intersection = (pred * target).sum()
        union = pred.sum() + target.sum() - intersection
        return (intersection + smooth) / (union + smooth)
    
    @staticmethod
    def sensitivity(pred, target, smooth=1e-6):
        """敏感性 (召回率/真阳性率)"""
        pred = pred.flatten()
        target = target.flatten()
        true_positive = (pred * target).sum()
        false_negative = (target * (1 - pred)).sum()
        return (true_positive + smooth) / (true_positive + false_negative + smooth)
    
    @staticmethod
    def specificity(pred, target, smooth=1e-6):
        """特异性 (真阴性率)"""
        pred = pred.flatten()
        target = target.flatten()
        true_negative = ((1 - pred) * (1 - target)).sum()
        false_positive = (pred * (1 - target)).sum()
        return (true_negative + smooth) / (true_negative + false_positive + smooth)
    
    @staticmethod
    def hausdorff_distance_95(pred, target):
        """95%豪斯多夫距离 (简化版本)"""
        # 简化实现，实际应用中需要更精确的计算
        pred_coords = np.where(pred > 0.5)
        target_coords = np.where(target > 0.5)
        
        if len(pred_coords[0]) == 0 or len(target_coords[0]) == 0:
            return float('inf')
        
        # 计算距离矩阵的简化版本
        pred_points = np.column_stack(pred_coords)
        target_points = np.column_stack(target_coords)
        
        # 简化的距离计算
        distances = []
        for p in pred_points[:100]:  # 限制计算量
            min_dist = np.min(np.sum((target_points - p)**2, axis=1))
            distances.append(np.sqrt(min_dist))
        
        return np.percentile(distances, 95) if distances else float('inf')
    
    @staticmethod
    def volume_similarity(pred, target, smooth=1e-6):
        """体积相似性"""
        pred_vol = pred.sum()
        target_vol = target.sum()
        return 1 - abs(pred_vol - target_vol) / (pred_vol + target_vol + smooth)

class DiceLoss(nn.Module):
    """Dice损失函数"""
    
    def __init__(self, smooth=1e-6):
        super(DiceLoss, self).__init__()
        self.smooth = smooth
    
    def forward(self, pred, target):
        pred = pred.view(-1)
        target = target.view(-1)
        intersection = (pred * target).sum()
        dice = (2. * intersection + self.smooth) / (pred.sum() + target.sum() + self.smooth)
        return 1 - dice

class CombinedLoss(nn.Module):
    """组合损失函数: Dice + BCE + Focal"""
    
    def __init__(self, dice_weight=0.5, bce_weight=0.3, focal_weight=0.2, alpha=0.25, gamma=2):
        super(CombinedLoss, self).__init__()
        self.dice_loss = DiceLoss()
        self.bce_loss = nn.BCELoss()
        self.dice_weight = dice_weight
        self.bce_weight = bce_weight
        self.focal_weight = focal_weight
        self.alpha = alpha
        self.gamma = gamma
    
    def focal_loss(self, pred, target):
        """Focal损失"""
        bce_loss = F.binary_cross_entropy(pred, target, reduction='none')
        pt = torch.exp(-bce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * bce_loss
        return focal_loss.mean()
    
    def forward(self, pred, target):
        dice = self.dice_loss(pred, target)
        bce = self.bce_loss(pred, target)
        focal = self.focal_loss(pred, target)
        
        return (self.dice_weight * dice + 
                self.bce_weight * bce + 
                self.focal_weight * focal)

class EEGMRIDataManager:
    """EEG-MRI数据管理器"""
    
    def __init__(self):
        self.eeg_files = []
        self.mask_files = []
        self.data_pairs = []
        
        # 电极位置
        self.electrode_positions = {
            'AF3': (-0.3, 0.7), 'AF4': (0.3, 0.7),
            'F3': (-0.5, 0.5), 'F4': (0.5, 0.5),
            'F7': (-0.7, 0.3), 'F8': (0.7, 0.3),
            'FC5': (-0.6, 0.2), 'FC6': (0.6, 0.2),
            'T7': (-0.8, 0.0), 'T8': (0.8, 0.0),
            'P7': (-0.7, -0.3), 'P8': (0.7, -0.3),
            'O1': (-0.3, -0.7), 'O2': (0.3, -0.7)
        }
        
        self.scaler = StandardScaler()
    
    def scan_all_data(self):
        """扫描所有数据文件"""
        print("=== 扫描所有数据文件 ===")
        
        # 扫描EEG文件
        nigeria_files = glob.glob("1252141/EEGs_Nigeria/*.csv.gz")
        guinea_files = glob.glob("1252141/EEGs_Guinea-Bissau/*.csv.gz")
        self.eeg_files = nigeria_files + guinea_files
        
        # 扫描MRI掩码文件
        self.mask_files = glob.glob("masks-2/*/[0-9]*_MaskInRawData.nii.gz")
        
        print(f"找到EEG文件: {len(self.eeg_files)}")
        print(f"找到MRI掩码: {len(self.mask_files)}")
        
        return len(self.eeg_files), len(self.mask_files)
    
    def create_data_pairs(self, strategy='random'):
        """创建EEG-MRI数据对"""
        print(f"=== 创建数据对 (策略: {strategy}) ===")
        
        if strategy == 'random':
            # 随机分配策略
            random.shuffle(self.mask_files)
            
            # 为每个EEG文件分配一个MRI掩码
            for i, eeg_file in enumerate(self.eeg_files):
                mask_file = self.mask_files[i % len(self.mask_files)]
                self.data_pairs.append({
                    'eeg_file': eeg_file,
                    'mask_file': mask_file,
                    'pair_id': i
                })
        
        elif strategy == 'balanced':
            # 平衡分配策略 - 确保每个掩码被使用相似次数
            mask_usage = defaultdict(int)
            
            for i, eeg_file in enumerate(self.eeg_files):
                # 选择使用次数最少的掩码
                min_usage = min(mask_usage.values()) if mask_usage else 0
                available_masks = [m for m in self.mask_files if mask_usage[m] == min_usage]
                
                if not available_masks:
                    available_masks = self.mask_files
                
                mask_file = random.choice(available_masks)
                mask_usage[mask_file] += 1
                
                self.data_pairs.append({
                    'eeg_file': eeg_file,
                    'mask_file': mask_file,
                    'pair_id': i
                })
        
        print(f"创建了 {len(self.data_pairs)} 个数据对")
        
        # 统计掩码使用情况
        mask_usage_stats = defaultdict(int)
        for pair in self.data_pairs:
            mask_usage_stats[pair['mask_file']] += 1
        
        print(f"掩码使用统计:")
        print(f"  平均使用次数: {np.mean(list(mask_usage_stats.values())):.2f}")
        print(f"  最大使用次数: {max(mask_usage_stats.values())}")
        print(f"  最小使用次数: {min(mask_usage_stats.values())}")
        
        return self.data_pairs
    
    def split_train_val_test(self, train_ratio=0.7, val_ratio=0.15, test_ratio=0.15):
        """划分训练集、验证集和测试集"""
        print("=== 划分数据集 ===")
        
        total_pairs = len(self.data_pairs)
        
        # 计算各集合大小
        train_size = int(total_pairs * train_ratio)
        val_size = int(total_pairs * val_ratio)
        test_size = total_pairs - train_size - val_size
        
        # 随机打乱数据对
        random.shuffle(self.data_pairs)
        
        # 划分数据集
        train_pairs = self.data_pairs[:train_size]
        val_pairs = self.data_pairs[train_size:train_size + val_size]
        test_pairs = self.data_pairs[train_size + val_size:]
        
        print(f"训练集: {len(train_pairs)} 对 ({len(train_pairs)/total_pairs*100:.1f}%)")
        print(f"验证集: {len(val_pairs)} 对 ({len(val_pairs)/total_pairs*100:.1f}%)")
        print(f"测试集: {len(test_pairs)} 对 ({len(test_pairs)/total_pairs*100:.1f}%)")
        
        return train_pairs, val_pairs, test_pairs

class MedicalEEGMRIDataset(Dataset):
    """医学EEG-MRI数据集"""
    
    def __init__(self, data_pairs, electrode_positions, scaler=None, is_training=True):
        self.data_pairs = data_pairs
        self.electrode_positions = electrode_positions
        self.scaler = scaler
        self.is_training = is_training
        
        if self.scaler is None:
            self.scaler = StandardScaler()
            self._fit_scaler()
    
    def _fit_scaler(self):
        """拟合标准化器"""
        print("拟合EEG数据标准化器...")
        
        sample_data = []
        sample_files = self.data_pairs[:min(20, len(self.data_pairs))]
        
        for pair in sample_files:
            eeg_data = self._load_eeg_file(pair['eeg_file'])
            if eeg_data is not None:
                sample_data.append(eeg_data)
        
        if sample_data:
            combined_data = np.vstack(sample_data)
            self.scaler.fit(combined_data)
            print(f"标准化器拟合完成，样本数: {len(combined_data)}")
    
    def _load_eeg_file(self, file_path):
        """加载EEG文件"""
        try:
            with gzip.open(file_path, 'rt') as f:
                data = pd.read_csv(f)
            
            eeg_channels = list(self.electrode_positions.keys())
            eeg_data = data[eeg_channels].values
            return eeg_data
        except Exception as e:
            print(f"加载EEG文件失败 {file_path}: {e}")
            return None
    
    def _preprocess_eeg(self, eeg_data):
        """预处理EEG数据"""
        # 归一化
        eeg_normalized = self.scaler.transform(eeg_data)
        
        # 分割为窗口
        window_size = 128
        stride = 64
        windows = []
        
        for i in range(0, len(eeg_normalized) - window_size + 1, stride):
            window = eeg_normalized[i:i + window_size]
            windows.append(window)
        
        return np.array(windows)
    
    def _eeg_to_topomap(self, eeg_window):
        """将EEG窗口转换为地形图"""
        grid_size = 64
        xi = np.linspace(-1, 1, grid_size)
        yi = np.linspace(-1, 1, grid_size)
        xi, yi = np.meshgrid(xi, yi)
        
        positions = np.array([self.electrode_positions[ch] for ch in self.electrode_positions.keys()])
        values = eeg_window.mean(axis=0)
        
        topomap = griddata(positions, values, (xi, yi), method='cubic', fill_value=0)
        mask = (xi**2 + yi**2) <= 1.0
        topomap = topomap * mask
        
        return topomap
    
    def _load_mask(self, mask_file, resolution=8):
        """加载并处理MRI掩码"""
        try:
            img = nib.load(mask_file)
            mask_data = img.get_fdata()
            
            # 下采样到指定分辨率
            target_shape = (resolution, resolution, resolution)
            zoom_factors = [target_shape[i] / mask_data.shape[i] for i in range(3)]
            mask_downsampled = zoom(mask_data, zoom_factors, order=1)
            mask_binary = (mask_downsampled > 0.5).astype(np.float32)
            
            return mask_binary
        except Exception as e:
            print(f"加载掩码文件失败 {mask_file}: {e}")
            return None
    
    def __len__(self):
        return len(self.data_pairs)
    
    def __getitem__(self, idx):
        pair = self.data_pairs[idx]
        
        # 加载EEG数据
        eeg_data = self._load_eeg_file(pair['eeg_file'])
        if eeg_data is None:
            return None
        
        # 预处理EEG
        eeg_windows = self._preprocess_eeg(eeg_data)
        
        # 转换为地形图序列（取前10个窗口）
        num_windows = min(10, len(eeg_windows))
        topomaps = []
        
        for i in range(num_windows):
            topomap = self._eeg_to_topomap(eeg_windows[i])
            topomaps.append(topomap)
        
        # 如果窗口不足10个，用零填充
        while len(topomaps) < 10:
            topomaps.append(np.zeros((64, 64)))
        
        topomaps = np.array(topomaps)
        
        # 加载对应的MRI掩码
        mask_8x8x8 = self._load_mask(pair['mask_file'], resolution=8)
        if mask_8x8x8 is None:
            return None
        
        return {
            'topomaps': torch.FloatTensor(topomaps).unsqueeze(1),  # (10, 1, 64, 64)
            'mask': torch.FloatTensor(mask_8x8x8),  # (8, 8, 8)
            'pair_id': pair['pair_id'],
            'eeg_file': pair['eeg_file'],
            'mask_file': pair['mask_file']
        }

def collate_fn(batch):
    """自定义批处理函数"""
    batch = [item for item in batch if item is not None]
    if len(batch) == 0:
        return None
    return torch.utils.data.dataloader.default_collate(batch)

class MedicalEEGMRIModel(nn.Module):
    """医学分割EEG-MRI模型"""

    def __init__(self):
        super(MedicalEEGMRIModel, self).__init__()

        # 空间注意力模块
        self.spatial_attention = nn.ModuleList([
            self._make_attention_layer(32),
            self._make_attention_layer(64),
            self._make_attention_layer(128),
            self._make_attention_layer(256)
        ])

        # CNN特征提取器
        self.feature_extractor = nn.Sequential(
            # 第一层
            nn.Conv2d(1, 32, 3, padding=1),
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2, 2),  # 64x64 -> 32x32

            # 第二层
            nn.Conv2d(32, 64, 3, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2, 2),  # 32x32 -> 16x16

            # 第三层
            nn.Conv2d(64, 128, 3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2, 2),  # 16x16 -> 8x8

            # 第四层
            nn.Conv2d(128, 256, 3, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.AdaptiveAvgPool2d((4, 4))  # 8x8 -> 4x4
        )

        # 时间序列LSTM
        self.temporal_lstm = nn.LSTM(
            input_size=256 * 4 * 4,
            hidden_size=512,
            num_layers=2,
            batch_first=True,
            dropout=0.3,
            bidirectional=True
        )

        # 特征融合层
        self.feature_fusion = nn.Sequential(
            nn.Linear(512 * 2, 256),  # 双向LSTM输出
            nn.ReLU(),
            nn.Dropout(0.4),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.3)
        )

        # 体素预测器
        self.voxel_predictor = nn.Sequential(
            nn.Linear(128, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, 512),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(512, 8 * 8 * 8),
            nn.Sigmoid()
        )

    def _make_attention_layer(self, channels):
        """创建注意力层"""
        return nn.Sequential(
            nn.Conv2d(channels, channels // 8, 1),
            nn.ReLU(),
            nn.Conv2d(channels // 8, 1, 1),
            nn.Sigmoid()
        )

    def forward(self, topomaps):
        batch_size, seq_len = topomaps.shape[:2]

        # 提取每个时间步的CNN特征
        cnn_features = []

        for t in range(seq_len):
            # CNN特征提取
            x = topomaps[:, t]  # (batch, 1, 64, 64)

            # 逐层提取特征并应用注意力
            x = self.feature_extractor[:4](x)  # 第一层
            attention = self.spatial_attention[0](x)
            x = x * attention

            x = self.feature_extractor[4:8](x)  # 第二层
            attention = self.spatial_attention[1](x)
            x = x * attention

            x = self.feature_extractor[8:12](x)  # 第三层
            attention = self.spatial_attention[2](x)
            x = x * attention

            x = self.feature_extractor[12:](x)  # 第四层
            attention = self.spatial_attention[3](x)
            x = x * attention

            # 展平特征
            features = x.view(batch_size, -1)
            cnn_features.append(features)

        # 堆叠时间序列特征
        temporal_features = torch.stack(cnn_features, dim=1)  # (batch, seq, features)

        # LSTM时间建模
        lstm_out, (h_n, c_n) = self.temporal_lstm(temporal_features)

        # 使用最后一个时间步的输出
        final_features = lstm_out[:, -1, :]  # (batch, hidden_size * 2)

        # 特征融合
        fused_features = self.feature_fusion(final_features)

        # 体素预测
        voxel_probs = self.voxel_predictor(fused_features)
        voxel_output = voxel_probs.view(batch_size, 8, 8, 8)

        return voxel_output

def train_model(train_pairs, val_pairs, device):
    """训练模型"""
    print("\n=== 开始模型训练 ===")

    # 创建数据集
    electrode_positions = {
        'AF3': (-0.3, 0.7), 'AF4': (0.3, 0.7),
        'F3': (-0.5, 0.5), 'F4': (0.5, 0.5),
        'F7': (-0.7, 0.3), 'F8': (0.7, 0.3),
        'FC5': (-0.6, 0.2), 'FC6': (0.6, 0.2),
        'T7': (-0.8, 0.0), 'T8': (0.8, 0.0),
        'P7': (-0.7, -0.3), 'P8': (0.7, -0.3),
        'O1': (-0.3, -0.7), 'O2': (0.3, -0.7)
    }

    # 创建训练和验证数据集
    train_dataset = MedicalEEGMRIDataset(train_pairs, electrode_positions, is_training=True)
    val_dataset = MedicalEEGMRIDataset(val_pairs, electrode_positions,
                                      scaler=train_dataset.scaler, is_training=False)

    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=4, shuffle=True,
                             collate_fn=collate_fn, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=4, shuffle=False,
                           collate_fn=collate_fn, num_workers=0)

    # 创建模型
    model = MedicalEEGMRIModel().to(device)

    # 损失函数和优化器
    criterion = CombinedLoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-5)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min',
                                                    factor=0.5, patience=5, verbose=True)

    # 训练循环
    num_epochs = 20
    train_losses = []
    val_losses = []
    best_val_loss = float('inf')

    metrics = MedicalSegmentationMetrics()

    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0
        train_dice = 0
        num_train_batches = 0

        train_pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{num_epochs} [Train]')
        for batch in train_pbar:
            if batch is None:
                continue

            topomaps = batch['topomaps'].to(device)
            targets = batch['mask'].to(device)

            optimizer.zero_grad()
            predictions = model(topomaps)

            loss = criterion(predictions, targets)
            loss.backward()
            optimizer.step()

            # 计算Dice系数
            with torch.no_grad():
                pred_binary = (predictions > 0.5).float()
                dice = metrics.dice_coefficient(pred_binary.cpu().numpy(),
                                              targets.cpu().numpy())
                train_dice += dice

            train_loss += loss.item()
            num_train_batches += 1

            train_pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Dice': f'{dice:.4f}'
            })

        avg_train_loss = train_loss / max(num_train_batches, 1)
        avg_train_dice = train_dice / max(num_train_batches, 1)
        train_losses.append(avg_train_loss)

        # 验证阶段
        model.eval()
        val_loss = 0
        val_dice = 0
        val_jaccard = 0
        val_sensitivity = 0
        val_specificity = 0
        num_val_batches = 0

        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc=f'Epoch {epoch+1}/{num_epochs} [Val]')
            for batch in val_pbar:
                if batch is None:
                    continue

                topomaps = batch['topomaps'].to(device)
                targets = batch['mask'].to(device)

                predictions = model(topomaps)
                loss = criterion(predictions, targets)

                # 计算医学分割指标
                pred_binary = (predictions > 0.5).float()
                pred_np = pred_binary.cpu().numpy()
                target_np = targets.cpu().numpy()

                dice = metrics.dice_coefficient(pred_np, target_np)
                jaccard = metrics.jaccard_index(pred_np, target_np)
                sensitivity = metrics.sensitivity(pred_np, target_np)
                specificity = metrics.specificity(pred_np, target_np)

                val_loss += loss.item()
                val_dice += dice
                val_jaccard += jaccard
                val_sensitivity += sensitivity
                val_specificity += specificity
                num_val_batches += 1

                val_pbar.set_postfix({
                    'Loss': f'{loss.item():.4f}',
                    'Dice': f'{dice:.4f}',
                    'IoU': f'{jaccard:.4f}'
                })

        avg_val_loss = val_loss / max(num_val_batches, 1)
        avg_val_dice = val_dice / max(num_val_batches, 1)
        avg_val_jaccard = val_jaccard / max(num_val_batches, 1)
        avg_val_sensitivity = val_sensitivity / max(num_val_batches, 1)
        avg_val_specificity = val_specificity / max(num_val_batches, 1)
        val_losses.append(avg_val_loss)

        # 学习率调度
        scheduler.step(avg_val_loss)

        # 保存最佳模型
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            torch.save(model.state_dict(), 'best_medical_eeg_mri_model.pth')

        # 打印epoch结果
        print(f'\nEpoch {epoch+1}/{num_epochs}:')
        print(f'  Train Loss: {avg_train_loss:.4f}, Train Dice: {avg_train_dice:.4f}')
        print(f'  Val Loss: {avg_val_loss:.4f}, Val Dice: {avg_val_dice:.4f}')
        print(f'  Val IoU: {avg_val_jaccard:.4f}, Val Sens: {avg_val_sensitivity:.4f}')
        print(f'  Val Spec: {avg_val_specificity:.4f}')

    return model, train_losses, val_losses

def evaluate_model(model, test_pairs, device):
    """评估模型"""
    print("\n=== 模型评估 ===")

    electrode_positions = {
        'AF3': (-0.3, 0.7), 'AF4': (0.3, 0.7),
        'F3': (-0.5, 0.5), 'F4': (0.5, 0.5),
        'F7': (-0.7, 0.3), 'F8': (0.7, 0.3),
        'FC5': (-0.6, 0.2), 'FC6': (0.6, 0.2),
        'T7': (-0.8, 0.0), 'T8': (0.8, 0.0),
        'P7': (-0.7, -0.3), 'P8': (0.7, -0.3),
        'O1': (-0.3, -0.7), 'O2': (0.3, -0.7)
    }

    # 加载最佳模型
    model.load_state_dict(torch.load('best_medical_eeg_mri_model.pth'))
    model.eval()

    # 创建测试数据集
    test_dataset = MedicalEEGMRIDataset(test_pairs, electrode_positions, is_training=False)
    test_loader = DataLoader(test_dataset, batch_size=4, shuffle=False,
                            collate_fn=collate_fn, num_workers=0)

    metrics = MedicalSegmentationMetrics()

    # 评估指标
    all_dice = []
    all_jaccard = []
    all_sensitivity = []
    all_specificity = []
    all_volume_sim = []

    with torch.no_grad():
        for batch in tqdm(test_loader, desc='Testing'):
            if batch is None:
                continue

            topomaps = batch['topomaps'].to(device)
            targets = batch['mask'].to(device)

            predictions = model(topomaps)
            pred_binary = (predictions > 0.5).float()

            # 计算批次指标
            for i in range(predictions.shape[0]):
                pred_np = pred_binary[i].cpu().numpy()
                target_np = targets[i].cpu().numpy()

                dice = metrics.dice_coefficient(pred_np, target_np)
                jaccard = metrics.jaccard_index(pred_np, target_np)
                sensitivity = metrics.sensitivity(pred_np, target_np)
                specificity = metrics.specificity(pred_np, target_np)
                volume_sim = metrics.volume_similarity(pred_np, target_np)

                all_dice.append(dice)
                all_jaccard.append(jaccard)
                all_sensitivity.append(sensitivity)
                all_specificity.append(specificity)
                all_volume_sim.append(volume_sim)

    # 计算最终指标
    final_metrics = {
        'dice': np.mean(all_dice),
        'dice_std': np.std(all_dice),
        'jaccard': np.mean(all_jaccard),
        'jaccard_std': np.std(all_jaccard),
        'sensitivity': np.mean(all_sensitivity),
        'sensitivity_std': np.std(all_sensitivity),
        'specificity': np.mean(all_specificity),
        'specificity_std': np.std(all_specificity),
        'volume_similarity': np.mean(all_volume_sim),
        'volume_similarity_std': np.std(all_volume_sim)
    }

    print("\n=== 最终评估结果 ===")
    print(f"Dice系数: {final_metrics['dice']:.4f} ± {final_metrics['dice_std']:.4f}")
    print(f"Jaccard指数(IoU): {final_metrics['jaccard']:.4f} ± {final_metrics['jaccard_std']:.4f}")
    print(f"敏感性(召回率): {final_metrics['sensitivity']:.4f} ± {final_metrics['sensitivity_std']:.4f}")
    print(f"特异性: {final_metrics['specificity']:.4f} ± {final_metrics['specificity_std']:.4f}")
    print(f"体积相似性: {final_metrics['volume_similarity']:.4f} ± {final_metrics['volume_similarity_std']:.4f}")

    return final_metrics

def main():
    """主训练函数"""
    print("=== 医学分割EEG-MRI训练系统 ===")

    # 设备设置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")

    # 数据管理
    data_manager = EEGMRIDataManager()

    # 扫描数据
    num_eeg, num_masks = data_manager.scan_all_data()

    if num_eeg == 0 or num_masks == 0:
        print("错误: 未找到足够的数据文件!")
        return

    # 创建数据对
    data_pairs = data_manager.create_data_pairs(strategy='balanced')

    # 划分数据集
    train_pairs, val_pairs, test_pairs = data_manager.split_train_val_test()

    print(f"\n=== 数据集统计 ===")
    print(f"总EEG文件: {num_eeg}")
    print(f"总MRI掩码: {num_masks}")
    print(f"训练对: {len(train_pairs)}")
    print(f"验证对: {len(val_pairs)}")
    print(f"测试对: {len(test_pairs)}")

    # 训练模型
    model, train_losses, val_losses = train_model(train_pairs, val_pairs, device)

    # 评估模型
    final_metrics = evaluate_model(model, test_pairs, device)

    # 保存结果
    results = {
        'train_losses': train_losses,
        'val_losses': val_losses,
        'final_metrics': final_metrics,
        'data_info': {
            'total_eeg_files': num_eeg,
            'total_mask_files': num_masks,
            'train_pairs': len(train_pairs),
            'val_pairs': len(val_pairs),
            'test_pairs': len(test_pairs)
        }
    }

    import pickle
    with open('medical_training_results.pkl', 'wb') as f:
        pickle.dump(results, f)

    print("\n=== 训练完成 ===")
    print("结果已保存:")
    print("- best_medical_eeg_mri_model.pth: 最佳模型")
    print("- medical_training_results.pkl: 训练结果")

    return results

if __name__ == "__main__":
    split_info = main()
