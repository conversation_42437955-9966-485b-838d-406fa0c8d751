#!/usr/bin/env python3
"""
完整的渐进式分辨率EEG-MRI病灶定位训练系统
严格按照6步要求实现
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import pandas as pd
import nibabel as nib
import gzip
import glob
import os
import random
from sklearn.preprocessing import StandardScaler
from scipy.interpolate import griddata
from scipy.ndimage import zoom
import matplotlib.pyplot as plt
from tqdm import tqdm
import warnings
from collections import defaultdict
import pickle

warnings.filterwarnings('ignore')

# 设置随机种子
torch.manual_seed(42)
np.random.seed(42)
random.seed(42)

class MedicalSegmentationMetrics:
    """医学分割评估指标"""
    
    @staticmethod
    def dice_coefficient(pred, target, smooth=1e-6):
        """Dice系数 (DSC) - 医学分割金标准"""
        pred = pred.flatten()
        target = target.flatten()
        intersection = (pred * target).sum()
        return (2. * intersection + smooth) / (pred.sum() + target.sum() + smooth)
    
    @staticmethod
    def jaccard_index(pred, target, smooth=1e-6):
        """Jaccard指数 (IoU) - 交并比"""
        pred = pred.flatten()
        target = target.flatten()
        intersection = (pred * target).sum()
        union = pred.sum() + target.sum() - intersection
        return (intersection + smooth) / (union + smooth)
    
    @staticmethod
    def sensitivity(pred, target, smooth=1e-6):
        """敏感性 (召回率/真阳性率) - 检测到病灶的能力"""
        pred = pred.flatten()
        target = target.flatten()
        true_positive = (pred * target).sum()
        false_negative = (target * (1 - pred)).sum()
        return (true_positive + smooth) / (true_positive + false_negative + smooth)
    
    @staticmethod
    def specificity(pred, target, smooth=1e-6):
        """特异性 (真阴性率) - 避免误报的能力"""
        pred = pred.flatten()
        target = target.flatten()
        true_negative = ((1 - pred) * (1 - target)).sum()
        false_positive = (pred * (1 - target)).sum()
        return (true_negative + smooth) / (true_negative + false_positive + smooth)
    
    @staticmethod
    def precision(pred, target, smooth=1e-6):
        """精确率 - 预测为阳性中真正为阳性的比例"""
        pred = pred.flatten()
        target = target.flatten()
        true_positive = (pred * target).sum()
        false_positive = (pred * (1 - target)).sum()
        return (true_positive + smooth) / (true_positive + false_positive + smooth)
    
    @staticmethod
    def f1_score(pred, target, smooth=1e-6):
        """F1分数 - 精确率和召回率的调和平均"""
        precision = MedicalSegmentationMetrics.precision(pred, target, smooth)
        sensitivity = MedicalSegmentationMetrics.sensitivity(pred, target, smooth)
        return (2 * precision * sensitivity + smooth) / (precision + sensitivity + smooth)

class DiceLoss(nn.Module):
    """Dice损失函数 - 医学分割专用"""
    
    def __init__(self, smooth=1e-6):
        super(DiceLoss, self).__init__()
        self.smooth = smooth
    
    def forward(self, pred, target):
        pred = pred.view(-1)
        target = target.view(-1)
        intersection = (pred * target).sum()
        dice = (2. * intersection + self.smooth) / (pred.sum() + target.sum() + self.smooth)
        return 1 - dice

class FocalLoss(nn.Module):
    """Focal损失函数 - 处理类别不平衡"""
    
    def __init__(self, alpha=0.25, gamma=2):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
    
    def forward(self, pred, target):
        bce_loss = F.binary_cross_entropy(pred, target, reduction='none')
        pt = torch.exp(-bce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * bce_loss
        return focal_loss.mean()

class CombinedMedicalLoss(nn.Module):
    """组合医学损失函数: Dice + Focal + BCE"""
    
    def __init__(self, dice_weight=0.5, focal_weight=0.3, bce_weight=0.2):
        super(CombinedMedicalLoss, self).__init__()
        self.dice_loss = DiceLoss()
        self.focal_loss = FocalLoss()
        self.bce_loss = nn.BCELoss()
        self.dice_weight = dice_weight
        self.focal_weight = focal_weight
        self.bce_weight = bce_weight
    
    def forward(self, pred, target):
        dice = self.dice_loss(pred, target)
        focal = self.focal_loss(pred, target)
        bce = self.bce_loss(pred, target)
        
        return (self.dice_weight * dice + 
                self.focal_weight * focal + 
                self.bce_weight * bce)

class EEGDataProcessor:
    """步骤1: EEG数据预处理器"""
    
    def __init__(self):
        # 标准10-20系统电极位置
        self.electrode_positions = {
            'AF3': (-0.3, 0.7), 'AF4': (0.3, 0.7),
            'F3': (-0.5, 0.5), 'F4': (0.5, 0.5),
            'F7': (-0.7, 0.3), 'F8': (0.7, 0.3),
            'FC5': (-0.6, 0.2), 'FC6': (0.6, 0.2),
            'T7': (-0.8, 0.0), 'T8': (0.8, 0.0),
            'P7': (-0.7, -0.3), 'P8': (0.7, -0.3),
            'O1': (-0.3, -0.7), 'O2': (0.3, -0.7)
        }
        self.scaler = StandardScaler()
        self.eeg_channels = list(self.electrode_positions.keys())
    
    def load_and_preprocess_eeg(self, file_path):
        """加载并预处理EEG数据"""
        try:
            with gzip.open(file_path, 'rt') as f:
                data = pd.read_csv(f)
            
            # 提取EEG通道数据
            eeg_data = data[self.eeg_channels].values
            
            # 步骤1: 归一化处理
            eeg_normalized = self.scaler.fit_transform(eeg_data)
            
            # 滑动窗口分割
            window_size = 128  # 1秒窗口
            stride = 64
            windows = []
            
            for i in range(0, len(eeg_normalized) - window_size + 1, stride):
                window = eeg_normalized[i:i + window_size]
                windows.append(window)
            
            return np.array(windows)
        except Exception as e:
            print(f"EEG预处理失败 {file_path}: {e}")
            return None
    
    def eeg_to_topomap(self, eeg_window):
        """步骤3: 将EEG数据转化为2D地形图"""
        grid_size = 64
        xi = np.linspace(-1, 1, grid_size)
        yi = np.linspace(-1, 1, grid_size)
        xi, yi = np.meshgrid(xi, yi)
        
        positions = np.array([self.electrode_positions[ch] for ch in self.eeg_channels])
        values = eeg_window.mean(axis=0)  # 对时间维度取平均
        
        # 三次样条插值生成地形图
        topomap = griddata(positions, values, (xi, yi), method='cubic', fill_value=0)
        
        # 创建头部掩码
        mask = (xi**2 + yi**2) <= 1.0
        topomap = topomap * mask
        
        return topomap

class MRIProcessor:
    """步骤2: MRI数据处理器"""
    
    @staticmethod
    def blur_mri_to_resolution(mask_data, target_resolution):
        """步骤2: 将MRI模糊化到指定分辨率"""
        target_shape = (target_resolution, target_resolution, target_resolution)
        zoom_factors = [target_shape[i] / mask_data.shape[i] for i in range(3)]
        
        # 使用三线性插值进行下采样
        mask_downsampled = zoom(mask_data, zoom_factors, order=1)
        
        # 二值化处理
        mask_binary = (mask_downsampled > 0.5).astype(np.float32)
        
        return mask_binary

class SpatialAttention(nn.Module):
    """空间注意力模块"""
    
    def __init__(self, channels):
        super(SpatialAttention, self).__init__()
        self.conv1 = nn.Conv2d(channels, channels // 8, 1)
        self.conv2 = nn.Conv2d(channels // 8, 1, 1)
        self.sigmoid = nn.Sigmoid()
        
    def forward(self, x):
        attention = F.relu(self.conv1(x))
        attention = self.sigmoid(self.conv2(attention))
        return x * attention

class MultiLayerAttentionCNN(nn.Module):
    """步骤3: 多层注意力CNN"""
    
    def __init__(self, input_channels=1):
        super(MultiLayerAttentionCNN, self).__init__()
        
        # CNN层
        self.conv1 = nn.Conv2d(input_channels, 32, 3, padding=1)
        self.bn1 = nn.BatchNorm2d(32)
        self.attention1 = SpatialAttention(32)
        
        self.conv2 = nn.Conv2d(32, 64, 3, padding=1)
        self.bn2 = nn.BatchNorm2d(64)
        self.attention2 = SpatialAttention(64)
        
        self.conv3 = nn.Conv2d(64, 128, 3, padding=1)
        self.bn3 = nn.BatchNorm2d(128)
        self.attention3 = SpatialAttention(128)
        
        self.conv4 = nn.Conv2d(128, 256, 3, padding=1)
        self.bn4 = nn.BatchNorm2d(256)
        self.attention4 = SpatialAttention(256)
        
        self.pool = nn.MaxPool2d(2, 2)
        self.adaptive_pool = nn.AdaptiveAvgPool2d((4, 4))
        self.dropout = nn.Dropout(0.3)
        
    def forward(self, x):
        features = []
        
        # 第一层: 64x64 -> 32x32
        x = F.relu(self.bn1(self.conv1(x)))
        x = self.attention1(x)
        features.append(x)
        x = self.pool(x)
        
        # 第二层: 32x32 -> 16x16
        x = F.relu(self.bn2(self.conv2(x)))
        x = self.attention2(x)
        features.append(x)
        x = self.pool(x)
        
        # 第三层: 16x16 -> 8x8
        x = F.relu(self.bn3(self.conv3(x)))
        x = self.attention3(x)
        features.append(x)
        x = self.pool(x)
        
        # 第四层: 8x8 -> 4x4
        x = F.relu(self.bn4(self.conv4(x)))
        x = self.attention4(x)
        features.append(x)
        x = self.adaptive_pool(x)
        
        return x, features

class TemporalLSTM(nn.Module):
    """步骤3: LSTM学习时间序列关系"""
    
    def __init__(self, input_size=256*4*4, hidden_size=512, num_layers=2):
        super(TemporalLSTM, self).__init__()
        
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            batch_first=True,
            dropout=0.3,
            bidirectional=True
        )
        
        self.fc = nn.Linear(hidden_size * 2, 256)  # 双向LSTM
        self.dropout = nn.Dropout(0.4)
        
    def forward(self, x):
        # x shape: (batch, sequence, features)
        lstm_out, (h_n, c_n) = self.lstm(x)
        
        # 使用最后一个时间步的输出
        output = self.fc(lstm_out[:, -1, :])
        output = self.dropout(output)
        
        return output

class ProgressiveVoxelPredictor(nn.Module):
    """步骤4&5: 渐进式体素预测器"""
    
    def __init__(self, eeg_features=256):
        super(ProgressiveVoxelPredictor, self).__init__()
        
        self.eeg_features = eeg_features
        
        # 不同分辨率的预测器
        self.predictors = nn.ModuleDict({
            '8': self._make_predictor(eeg_features, 8),
            '16': self._make_predictor(eeg_features + 64, 16),  # 融合CNN第2层特征
            '32': self._make_predictor(eeg_features + 128, 32), # 融合CNN第3层特征
            '64': self._make_predictor(eeg_features + 256, 64), # 融合CNN第4层特征
        })
        
    def _make_predictor(self, input_features, resolution):
        """创建指定分辨率的预测器"""
        total_voxels = resolution ** 3
        
        return nn.Sequential(
            nn.Linear(input_features, 512),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(512, 1024),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(1024, total_voxels),
            nn.Sigmoid()
        )
    
    def forward(self, eeg_features, cnn_features=None, resolution=8):
        resolution_str = str(resolution)
        
        if resolution > 8 and cnn_features is not None:
            # 步骤5: 融合EEG的前几层特征
            if resolution == 16:
                combined_features = torch.cat([eeg_features, cnn_features[1].mean(dim=[2,3])], dim=1)
            elif resolution == 32:
                combined_features = torch.cat([eeg_features, cnn_features[2].mean(dim=[2,3])], dim=1)
            elif resolution == 64:
                combined_features = torch.cat([eeg_features, cnn_features[3].mean(dim=[2,3])], dim=1)
            else:
                combined_features = eeg_features
        else:
            combined_features = eeg_features
        
        voxel_probs = self.predictors[resolution_str](combined_features)
        return voxel_probs.view(-1, resolution, resolution, resolution)

class ProgressiveEEGMRIModel(nn.Module):
    """完整的渐进式EEG-MRI模型"""
    
    def __init__(self):
        super(ProgressiveEEGMRIModel, self).__init__()
        
        self.cnn = MultiLayerAttentionCNN()
        self.lstm = TemporalLSTM()
        self.voxel_predictor = ProgressiveVoxelPredictor()
        
    def forward(self, topomaps, resolution=8, return_features=False):
        batch_size, seq_len = topomaps.shape[:2]
        
        # CNN特征提取
        cnn_features_list = []
        cnn_feature_maps = []
        
        for i in range(seq_len):
            features, feature_maps = self.cnn(topomaps[:, i])
            cnn_features_list.append(features.view(batch_size, -1))
            if i == 0:  # 只保存第一个时间步的特征图用于融合
                cnn_feature_maps = feature_maps
        
        # LSTM时间序列学习
        cnn_features = torch.stack(cnn_features_list, dim=1)
        eeg_features = self.lstm(cnn_features)
        
        # 体素预测
        voxel_output = self.voxel_predictor(eeg_features, cnn_feature_maps, resolution)
        
        if return_features:
            return voxel_output, eeg_features, cnn_feature_maps
        return voxel_output

class ProgressiveDataset(Dataset):
    """渐进式训练数据集"""

    def __init__(self, data_pairs, eeg_processor, current_resolution=8):
        self.data_pairs = data_pairs
        self.eeg_processor = eeg_processor
        self.current_resolution = current_resolution

    def __len__(self):
        return len(self.data_pairs)

    def __getitem__(self, idx):
        pair = self.data_pairs[idx]

        # 加载并预处理EEG
        eeg_windows = self.eeg_processor.load_and_preprocess_eeg(pair['eeg_file'])
        if eeg_windows is None:
            return None

        # 转换为地形图序列
        num_windows = min(10, len(eeg_windows))
        topomaps = []

        for i in range(num_windows):
            topomap = self.eeg_processor.eeg_to_topomap(eeg_windows[i])
            topomaps.append(topomap)

        # 填充到10个时间步
        while len(topomaps) < 10:
            topomaps.append(np.zeros((64, 64)))

        topomaps = np.array(topomaps)

        # 加载MRI掩码
        try:
            img = nib.load(pair['mask_file'])
            mask_data = img.get_fdata()

            # 步骤2: 模糊化到当前分辨率
            mask_blurred = MRIProcessor.blur_mri_to_resolution(mask_data, self.current_resolution)

        except Exception as e:
            print(f"加载掩码失败 {pair['mask_file']}: {e}")
            return None

        return {
            'topomaps': torch.FloatTensor(topomaps).unsqueeze(1),  # (10, 1, 64, 64)
            'mask': torch.FloatTensor(mask_blurred),
            'pair_id': pair['pair_id']
        }

def create_all_data_pairs():
    """为所有318个EEG文件分配MRI掩码"""
    print("=== 创建完整数据对 ===")

    # 扫描所有EEG文件
    nigeria_files = glob.glob("1252141/EEGs_Nigeria/*.csv.gz")
    guinea_files = glob.glob("1252141/EEGs_Guinea-Bissau/*.csv.gz")
    all_eeg_files = nigeria_files + guinea_files

    # 扫描所有MRI掩码
    all_mask_files = glob.glob("masks-2/*/[0-9]*_MaskInRawData.nii.gz")

    print(f"找到EEG文件: {len(all_eeg_files)}")
    print(f"找到MRI掩码: {len(all_mask_files)}")

    # 为每个EEG文件分配一个MRI掩码（平衡分配）
    data_pairs = []
    mask_usage = defaultdict(int)

    for i, eeg_file in enumerate(all_eeg_files):
        # 选择使用次数最少的掩码
        min_usage = min(mask_usage.values()) if mask_usage else 0
        available_masks = [m for m in all_mask_files if mask_usage[m] == min_usage]

        if not available_masks:
            available_masks = all_mask_files

        mask_file = random.choice(available_masks)
        mask_usage[mask_file] += 1

        data_pairs.append({
            'eeg_file': eeg_file,
            'mask_file': mask_file,
            'pair_id': i
        })

    print(f"创建了 {len(data_pairs)} 个数据对")
    print(f"掩码平均使用次数: {np.mean(list(mask_usage.values())):.2f}")

    return data_pairs

def split_train_val_test(data_pairs, train_ratio=0.7, val_ratio=0.15):
    """划分训练集、验证集和测试集"""
    print("=== 划分数据集 ===")

    total_pairs = len(data_pairs)
    random.shuffle(data_pairs)

    train_size = int(total_pairs * train_ratio)
    val_size = int(total_pairs * val_ratio)

    train_pairs = data_pairs[:train_size]
    val_pairs = data_pairs[train_size:train_size + val_size]
    test_pairs = data_pairs[train_size + val_size:]

    print(f"训练集: {len(train_pairs)} 对")
    print(f"验证集: {len(val_pairs)} 对")
    print(f"测试集: {len(test_pairs)} 对")

    return train_pairs, val_pairs, test_pairs

def train_progressive_resolution(train_pairs, val_pairs, device):
    """步骤4&5: 渐进式分辨率训练"""
    print("\n=== 开始渐进式分辨率训练 ===")

    # 创建EEG处理器
    eeg_processor = EEGDataProcessor()

    # 创建模型
    model = ProgressiveEEGMRIModel().to(device)

    # 损失函数
    criterion = CombinedMedicalLoss()

    # 渐进式训练配置
    resolution_schedule = [
        {'resolution': 8, 'epochs': 15, 'lr': 0.001},
        {'resolution': 16, 'epochs': 12, 'lr': 0.0008},
        {'resolution': 32, 'epochs': 10, 'lr': 0.0005},
        {'resolution': 64, 'epochs': 8, 'lr': 0.0003}
    ]

    all_train_losses = []
    all_val_losses = []
    all_metrics = []

    for stage, config in enumerate(resolution_schedule):
        resolution = config['resolution']
        epochs = config['epochs']
        lr = config['lr']

        print(f"\n--- 阶段 {stage+1}: 训练分辨率 {resolution}x{resolution}x{resolution} ---")

        # 创建当前分辨率的数据集
        train_dataset = ProgressiveDataset(train_pairs, eeg_processor, resolution)
        val_dataset = ProgressiveDataset(val_pairs, eeg_processor, resolution)

        train_loader = DataLoader(train_dataset, batch_size=4, shuffle=True,
                                 collate_fn=lambda x: [item for item in x if item is not None])
        val_loader = DataLoader(val_dataset, batch_size=4, shuffle=False,
                               collate_fn=lambda x: [item for item in x if item is not None])

        # 优化器
        optimizer = optim.Adam(model.parameters(), lr=lr, weight_decay=1e-5)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min',
                                                        factor=0.5, patience=3, verbose=True)

        stage_train_losses = []
        stage_val_losses = []
        best_val_loss = float('inf')

        for epoch in range(epochs):
            # 训练阶段
            model.train()
            train_loss = 0
            train_dice = 0
            num_train_batches = 0

            train_pbar = tqdm(train_loader, desc=f'分辨率{resolution} Epoch {epoch+1}/{epochs} [Train]')
            for batch in train_pbar:
                if len(batch) == 0:
                    continue

                topomaps = torch.stack([item['topomaps'] for item in batch]).to(device)
                targets = torch.stack([item['mask'] for item in batch]).to(device)

                optimizer.zero_grad()
                predictions = model(topomaps, resolution=resolution)

                loss = criterion(predictions, targets)
                loss.backward()
                optimizer.step()

                # 计算Dice系数
                with torch.no_grad():
                    pred_binary = (predictions > 0.5).float()
                    dice = MedicalSegmentationMetrics.dice_coefficient(
                        pred_binary.cpu().numpy(), targets.cpu().numpy())
                    train_dice += dice

                train_loss += loss.item()
                num_train_batches += 1

                train_pbar.set_postfix({
                    'Loss': f'{loss.item():.4f}',
                    'Dice': f'{dice:.4f}'
                })

            avg_train_loss = train_loss / max(num_train_batches, 1)
            avg_train_dice = train_dice / max(num_train_batches, 1)
            stage_train_losses.append(avg_train_loss)

            # 验证阶段
            model.eval()
            val_loss = 0
            val_dice = 0
            val_jaccard = 0
            val_sensitivity = 0
            val_specificity = 0
            num_val_batches = 0

            with torch.no_grad():
                val_pbar = tqdm(val_loader, desc=f'分辨率{resolution} Epoch {epoch+1}/{epochs} [Val]')
                for batch in val_pbar:
                    if len(batch) == 0:
                        continue

                    topomaps = torch.stack([item['topomaps'] for item in batch]).to(device)
                    targets = torch.stack([item['mask'] for item in batch]).to(device)

                    predictions = model(topomaps, resolution=resolution)
                    loss = criterion(predictions, targets)

                    # 计算医学分割指标
                    pred_binary = (predictions > 0.5).float()
                    pred_np = pred_binary.cpu().numpy()
                    target_np = targets.cpu().numpy()

                    dice = MedicalSegmentationMetrics.dice_coefficient(pred_np, target_np)
                    jaccard = MedicalSegmentationMetrics.jaccard_index(pred_np, target_np)
                    sensitivity = MedicalSegmentationMetrics.sensitivity(pred_np, target_np)
                    specificity = MedicalSegmentationMetrics.specificity(pred_np, target_np)

                    val_loss += loss.item()
                    val_dice += dice
                    val_jaccard += jaccard
                    val_sensitivity += sensitivity
                    val_specificity += specificity
                    num_val_batches += 1

                    val_pbar.set_postfix({
                        'Loss': f'{loss.item():.4f}',
                        'Dice': f'{dice:.4f}',
                        'IoU': f'{jaccard:.4f}'
                    })

            avg_val_loss = val_loss / max(num_val_batches, 1)
            avg_val_dice = val_dice / max(num_val_batches, 1)
            avg_val_jaccard = val_jaccard / max(num_val_batches, 1)
            avg_val_sensitivity = val_sensitivity / max(num_val_batches, 1)
            avg_val_specificity = val_specificity / max(num_val_batches, 1)
            stage_val_losses.append(avg_val_loss)

            scheduler.step(avg_val_loss)

            # 保存最佳模型
            if avg_val_loss < best_val_loss:
                best_val_loss = avg_val_loss
                torch.save(model.state_dict(), f'best_model_resolution_{resolution}.pth')

            print(f'分辨率{resolution} Epoch {epoch+1}/{epochs}:')
            print(f'  Train Loss: {avg_train_loss:.4f}, Train Dice: {avg_train_dice:.4f}')
            print(f'  Val Loss: {avg_val_loss:.4f}, Val Dice: {avg_val_dice:.4f}')
            print(f'  Val IoU: {avg_val_jaccard:.4f}, Val Sens: {avg_val_sensitivity:.4f}')
            print(f'  Val Spec: {avg_val_specificity:.4f}')

        all_train_losses.extend(stage_train_losses)
        all_val_losses.extend(stage_val_losses)

        # 记录当前阶段的最终指标
        stage_metrics = {
            'resolution': resolution,
            'final_val_dice': avg_val_dice,
            'final_val_jaccard': avg_val_jaccard,
            'final_val_sensitivity': avg_val_sensitivity,
            'final_val_specificity': avg_val_specificity
        }
        all_metrics.append(stage_metrics)

        print(f"分辨率 {resolution} 训练完成!")
        print(f"最终验证Dice: {avg_val_dice:.4f}")

    return model, all_train_losses, all_val_losses, all_metrics

def evaluate_final_model(model, test_pairs, device):
    """步骤6: 最终模型评估"""
    print("\n=== 最终模型评估 ===")

    eeg_processor = EEGDataProcessor()

    # 在所有分辨率上评估
    resolutions = [8, 16, 32, 64]
    final_results = {}

    for resolution in resolutions:
        print(f"\n评估分辨率 {resolution}x{resolution}x{resolution}:")

        # 加载对应分辨率的最佳模型
        try:
            model.load_state_dict(torch.load(f'best_model_resolution_{resolution}.pth'))
            model.eval()
        except:
            print(f"未找到分辨率 {resolution} 的模型，跳过...")
            continue

        test_dataset = ProgressiveDataset(test_pairs, eeg_processor, resolution)
        test_loader = DataLoader(test_dataset, batch_size=4, shuffle=False,
                                collate_fn=lambda x: [item for item in x if item is not None])

        all_dice = []
        all_jaccard = []
        all_sensitivity = []
        all_specificity = []
        all_precision = []
        all_f1 = []

        with torch.no_grad():
            for batch in tqdm(test_loader, desc=f'测试分辨率{resolution}'):
                if len(batch) == 0:
                    continue

                topomaps = torch.stack([item['topomaps'] for item in batch]).to(device)
                targets = torch.stack([item['mask'] for item in batch]).to(device)

                predictions = model(topomaps, resolution=resolution)
                pred_binary = (predictions > 0.5).float()

                # 计算每个样本的指标
                for i in range(predictions.shape[0]):
                    pred_np = pred_binary[i].cpu().numpy()
                    target_np = targets[i].cpu().numpy()

                    dice = MedicalSegmentationMetrics.dice_coefficient(pred_np, target_np)
                    jaccard = MedicalSegmentationMetrics.jaccard_index(pred_np, target_np)
                    sensitivity = MedicalSegmentationMetrics.sensitivity(pred_np, target_np)
                    specificity = MedicalSegmentationMetrics.specificity(pred_np, target_np)
                    precision = MedicalSegmentationMetrics.precision(pred_np, target_np)
                    f1 = MedicalSegmentationMetrics.f1_score(pred_np, target_np)

                    all_dice.append(dice)
                    all_jaccard.append(jaccard)
                    all_sensitivity.append(sensitivity)
                    all_specificity.append(specificity)
                    all_precision.append(precision)
                    all_f1.append(f1)

        # 计算最终指标
        resolution_results = {
            'dice_mean': np.mean(all_dice),
            'dice_std': np.std(all_dice),
            'jaccard_mean': np.mean(all_jaccard),
            'jaccard_std': np.std(all_jaccard),
            'sensitivity_mean': np.mean(all_sensitivity),
            'sensitivity_std': np.std(all_sensitivity),
            'specificity_mean': np.mean(all_specificity),
            'specificity_std': np.std(all_specificity),
            'precision_mean': np.mean(all_precision),
            'precision_std': np.std(all_precision),
            'f1_mean': np.mean(all_f1),
            'f1_std': np.std(all_f1),
            'num_samples': len(all_dice)
        }

        final_results[resolution] = resolution_results

        print(f"分辨率 {resolution} 结果:")
        print(f"  Dice系数: {resolution_results['dice_mean']:.4f} ± {resolution_results['dice_std']:.4f}")
        print(f"  Jaccard指数: {resolution_results['jaccard_mean']:.4f} ± {resolution_results['jaccard_std']:.4f}")
        print(f"  敏感性: {resolution_results['sensitivity_mean']:.4f} ± {resolution_results['sensitivity_std']:.4f}")
        print(f"  特异性: {resolution_results['specificity_mean']:.4f} ± {resolution_results['specificity_std']:.4f}")
        print(f"  精确率: {resolution_results['precision_mean']:.4f} ± {resolution_results['precision_std']:.4f}")
        print(f"  F1分数: {resolution_results['f1_mean']:.4f} ± {resolution_results['f1_std']:.4f}")
        print(f"  测试样本数: {resolution_results['num_samples']}")

    return final_results

def main():
    """主函数 - 完整的6步训练流程"""
    print("=== 完整渐进式EEG-MRI病灶定位训练系统 ===")
    print("严格按照6步要求实现:")
    print("1. 预处理并归一化EEG数据")
    print("2. 将MRI模糊化到8x8x8")
    print("3. EEG转2D地形图，多层注意力CNN+LSTM")
    print("4. 体素概率计算并训练一定轮数")
    print("5. 逐渐提高分辨率并融合EEG前几层特征")
    print("6. 终了")

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"\n使用设备: {device}")

    # 创建所有数据对
    data_pairs = create_all_data_pairs()

    # 划分数据集
    train_pairs, val_pairs, test_pairs = split_train_val_test(data_pairs)

    # 渐进式训练
    model, train_losses, val_losses, stage_metrics = train_progressive_resolution(
        train_pairs, val_pairs, device)

    # 最终评估
    final_results = evaluate_final_model(model, test_pairs, device)

    # 保存完整结果
    complete_results = {
        'train_losses': train_losses,
        'val_losses': val_losses,
        'stage_metrics': stage_metrics,
        'final_results': final_results,
        'data_info': {
            'total_pairs': len(data_pairs),
            'train_pairs': len(train_pairs),
            'val_pairs': len(val_pairs),
            'test_pairs': len(test_pairs)
        }
    }

    with open('complete_progressive_results.pkl', 'wb') as f:
        pickle.dump(complete_results, f)

    print("\n=== 训练完成 ===")
    print("生成的文件:")
    for resolution in [8, 16, 32, 64]:
        print(f"- best_model_resolution_{resolution}.pth")
    print("- complete_progressive_results.pkl")

    # 打印最终总结
    print(f"\n=== 最终病灶定位准确率总结 ===")
    for resolution, results in final_results.items():
        print(f"分辨率 {resolution}x{resolution}x{resolution}:")
        print(f"  病灶定位准确率(Dice): {results['dice_mean']:.1%} ± {results['dice_std']:.1%}")
        print(f"  交并比(IoU): {results['jaccard_mean']:.1%} ± {results['jaccard_std']:.1%}")
        print(f"  敏感性: {results['sensitivity_mean']:.1%} ± {results['sensitivity_std']:.1%}")

    return complete_results

if __name__ == "__main__":
    results = main()
