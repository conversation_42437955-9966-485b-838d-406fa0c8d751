#!/usr/bin/env python3
"""
Improved EEG Topographic Mapping Script
Creates proper brain topographic maps from EEG data
"""

import numpy as np
import matplotlib.pyplot as plt
import mne
from mne.viz import plot_topomap
import os
import warnings

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore', category=RuntimeWarning)
mne.set_log_level('WARNING')

def load_and_prepare_eeg(file_path):
    """
    Load EEG data and prepare it for topographic mapping
    """
    # Load the EDF file
    raw = mne.io.read_raw_edf(file_path, preload=True, verbose=False)
    
    print(f"Loaded EEG data: {raw.info['nchan']} channels, {raw.info['sfreq']} Hz")
    print(f"Duration: {raw.times[-1]:.1f} seconds")
    print(f"Channel names: {raw.ch_names[:10]}..." if len(raw.ch_names) > 10 else f"Channel names: {raw.ch_names}")
    
    # Set channel types to EEG
    raw.set_channel_types({ch: 'eeg' for ch in raw.ch_names})
    
    # Try to set standard montage
    try:
        montage = mne.channels.make_standard_montage('standard_1020')
        raw.set_montage(montage, match_case=False, on_missing='ignore')
        print("Applied standard 10-20 montage")
    except:
        print("Could not apply standard montage, will use available channel positions")
    
    return raw

def create_topomap_figure(data, info, title, save_path=None):
    """
    Create a single topographic map figure
    """
    fig, ax = plt.subplots(figsize=(10, 8))
    
    try:
        # Create the topographic map
        im, _ = plot_topomap(data, info, 
                           ch_type='eeg',
                           contours=8,
                           cmap='RdBu_r',
                           axes=ax,
                           show=False,
                           sphere='auto')
        
        # Add title
        ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
        
        # Add colorbar
        cbar = plt.colorbar(im, ax=ax, shrink=0.8, aspect=20)
        cbar.set_label('Amplitude (µV)', rotation=270, labelpad=20, fontsize=12)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
            print(f"Saved: {save_path}")
        
        return fig
        
    except Exception as e:
        print(f"Error creating topographic map: {e}")
        plt.close(fig)
        return None

def analyze_frequency_bands(raw):
    """
    Analyze different frequency bands and create topographic maps
    """
    # Define frequency bands
    freq_bands = {
        'Delta (0.5-4 Hz)': (0.5, 4),
        'Theta (4-8 Hz)': (4, 8),
        'Alpha (8-13 Hz)': (8, 13),
        'Beta (13-30 Hz)': (13, 30),
        'Gamma (30-50 Hz)': (30, 50)
    }
    
    # Create output directory
    os.makedirs('frequency_topomaps', exist_ok=True)
    
    print("\nCreating frequency band topographic maps...")
    
    for band_name, (low_freq, high_freq) in freq_bands.items():
        print(f"Processing {band_name}...")
        
        # Filter the data
        raw_filtered = raw.copy()
        raw_filtered.filter(low_freq, high_freq, fir_design='firwin', verbose=False)
        
        # Get data and compute power
        data = raw_filtered.get_data()
        power = np.mean(data**2, axis=1)  # Power across time
        
        # Create filename
        filename = f"frequency_topomaps/topomap_{band_name.split()[0].lower()}.png"
        
        # Create topographic map
        fig = create_topomap_figure(power, raw_filtered.info, 
                                  f"EEG Power - {band_name}", filename)
        if fig:
            plt.close(fig)

def analyze_time_evolution(raw, num_timepoints=6):
    """
    Create topographic maps at different time points
    """
    os.makedirs('time_topomaps', exist_ok=True)
    
    print(f"\nCreating time evolution topographic maps ({num_timepoints} time points)...")
    
    duration = raw.times[-1]
    time_points = np.linspace(0, duration, num_timepoints)
    
    for i, time_point in enumerate(time_points):
        print(f"Processing time point {i+1}/{num_timepoints}: {time_point:.1f}s")
        
        # Find the closest time index
        time_idx = np.argmin(np.abs(raw.times - time_point))
        
        # Get data at this time point
        data = raw.get_data()[:, time_idx]
        
        # Create filename
        filename = f"time_topomaps/topomap_time_{time_point:.1f}s.png"
        
        # Create topographic map
        fig = create_topomap_figure(data, raw.info, 
                                  f"EEG Activity at {time_point:.1f}s", filename)
        if fig:
            plt.close(fig)

def create_summary_figure(raw):
    """
    Create a summary figure with multiple topographic maps
    """
    print("\nCreating summary figure...")
    
    # Create a 2x3 subplot figure
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('EEG Topographic Analysis Summary', fontsize=20, fontweight='bold')
    
    # Frequency bands for summary
    freq_bands = [
        ('Delta', (0.5, 4)),
        ('Theta', (4, 8)),
        ('Alpha', (8, 13)),
        ('Beta', (13, 30)),
        ('Gamma', (30, 50))
    ]
    
    # Create frequency band maps
    for i, (band_name, (low_freq, high_freq)) in enumerate(freq_bands):
        if i >= 5:  # Only 5 frequency bands
            break
            
        ax = axes[i//3, i%3]
        
        # Filter and get power
        raw_filtered = raw.copy()
        raw_filtered.filter(low_freq, high_freq, fir_design='firwin', verbose=False)
        data = raw_filtered.get_data()
        power = np.mean(data**2, axis=1)
        
        try:
            im, _ = plot_topomap(power, raw_filtered.info,
                               ch_type='eeg',
                               contours=6,
                               cmap='RdBu_r',
                               axes=ax,
                               show=False,
                               sphere='auto')
            ax.set_title(f'{band_name} ({low_freq}-{high_freq} Hz)', fontweight='bold')
        except:
            ax.text(0.5, 0.5, f'{band_name}\nMapping Error', 
                   ha='center', va='center', transform=ax.transAxes)
    
    # Add overall RMS map
    ax = axes[1, 2]
    data = raw.get_data()
    rms_data = np.sqrt(np.mean(data**2, axis=1))
    
    try:
        im, _ = plot_topomap(rms_data, raw.info,
                           ch_type='eeg',
                           contours=6,
                           cmap='RdBu_r',
                           axes=ax,
                           show=False,
                           sphere='auto')
        ax.set_title('Overall RMS Activity', fontweight='bold')
    except:
        ax.text(0.5, 0.5, 'RMS Activity\nMapping Error', 
               ha='center', va='center', transform=ax.transAxes)
    
    plt.tight_layout()
    plt.savefig('eeg_summary_topomaps.png', dpi=300, bbox_inches='tight', facecolor='white')
    print("Saved: eeg_summary_topomaps.png")
    plt.close(fig)

def main():
    """
    Main function
    """
    edf_file = "PN00-1.edf"
    
    if not os.path.exists(edf_file):
        print(f"Error: File '{edf_file}' not found!")
        return
    
    print("=== EEG Topographic Mapping Analysis ===")
    
    # Load and prepare data
    raw = load_and_prepare_eeg(edf_file)
    
    # Create basic RMS topographic map
    print("\nCreating basic RMS topographic map...")
    data = raw.get_data()
    rms_data = np.sqrt(np.mean(data**2, axis=1))
    fig = create_topomap_figure(rms_data, raw.info, 
                              "EEG RMS Activity (All Frequencies)", 
                              "basic_rms_topomap.png")
    if fig:
        plt.close(fig)
    
    # Analyze frequency bands
    analyze_frequency_bands(raw)
    
    # Analyze time evolution
    analyze_time_evolution(raw)
    
    # Create summary figure
    create_summary_figure(raw)
    
    print("\n=== Analysis Complete ===")
    print("Generated files:")
    print("- basic_rms_topomap.png: Basic RMS topographic map")
    print("- frequency_topomaps/: Frequency band analysis")
    print("- time_topomaps/: Time evolution analysis")
    print("- eeg_summary_topomaps.png: Summary figure with all frequency bands")

if __name__ == "__main__":
    main()
