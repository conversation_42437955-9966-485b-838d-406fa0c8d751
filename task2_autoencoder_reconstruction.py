#!/usr/bin/env python3
"""
Task 2: Autoencoder Reconstruction Analysis
基于发作间期EEG数据的自编码器重建分析：
1. 训练1D Attention-CNN自编码器
2. 分析重建质量差异（癫痫 vs 对照）
3. 生成注意力热图
4. 可视化重建结果
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import gzip
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

class InterictalEEGDataset(torch.utils.data.Dataset):
    """发作间期EEG数据集用于自编码器训练"""
    
    def __init__(self, data_dirs, metadata_files, split='train', test_size=0.25, random_state=42):
        self.data_dirs = [Path(d) for d in data_dirs]
        self.split = split
        
        # 加载数据
        self.load_interictal_data(metadata_files, test_size, random_state)
        
    def load_interictal_data(self, metadata_files, test_size, random_state):
        """加载发作间期EEG数据"""
        print("📊 加载发作间期EEG数据...")
        
        all_samples = []
        
        # Guinea-Bissau数据集（包含癫痫患者）
        gb_metadata = pd.read_csv(metadata_files[0])
        for idx, row in gb_metadata.iterrows():
            subject_id = row['subject.id']
            group = row['Group']
            eeg_file = self.data_dirs[0] / f"signal-{subject_id}.csv.gz"
            
            if eeg_file.exists():
                all_samples.append({
                    'file_path': str(eeg_file),
                    'label': 1 if group == 'Epilepsy' else 0,
                    'group': group,
                    'dataset': 'Guinea-Bissau',
                    'subject_id': subject_id
                })
        
        # Nigeria数据集（全部对照组）
        ng_metadata = pd.read_csv(metadata_files[1])
        for idx, row in ng_metadata.iterrows():
            subject_id = row['subject.id']
            group = row['Group']
            
            if 'csv.file' in row and pd.notna(row['csv.file']):
                eeg_file = self.data_dirs[1] / row['csv.file']
            else:
                eeg_file = self.data_dirs[1] / f"signal-{subject_id}-1.csv.gz"
            
            if eeg_file.exists():
                all_samples.append({
                    'file_path': str(eeg_file),
                    'label': 0,  # 全部是对照组
                    'group': 'Control',
                    'dataset': 'Nigeria',
                    'subject_id': subject_id
                })
        
        # 统计信息
        epilepsy_samples = [s for s in all_samples if s['label'] == 1]
        control_samples = [s for s in all_samples if s['label'] == 0]
        
        print(f"✅ 数据加载完成:")
        print(f"  总样本: {len(all_samples)}")
        print(f"  癫痫患者: {len(epilepsy_samples)} (发作间期)")
        print(f"  对照组: {len(control_samples)}")
        
        # 分割数据
        from sklearn.model_selection import train_test_split
        
        labels = [s['label'] for s in all_samples]
        train_idx, test_idx = train_test_split(
            range(len(all_samples)), test_size=test_size, 
            random_state=random_state, stratify=labels
        )
        
        if self.split == 'train':
            self.samples = [all_samples[i] for i in train_idx]
        else:
            self.samples = [all_samples[i] for i in test_idx]
        
        # 最终统计
        final_labels = [s['label'] for s in self.samples]
        print(f"\n{self.split.upper()}集分布:")
        print(f"  样本数: {len(self.samples)}")
        print(f"  癫痫: {sum(final_labels)}, 对照: {len(final_labels) - sum(final_labels)}")
    
    def load_eeg_signal(self, file_path):
        """加载EEG信号"""
        try:
            with gzip.open(file_path, 'rt') as f:
                df = pd.read_csv(f)
            
            # 14个EEG通道
            eeg_channels = ['AF3', 'AF4', 'F3', 'F4', 'F7', 'F8', 'FC5', 'FC6', 
                           'O1', 'O2', 'P7', 'P8', 'T7', 'T8']
            
            available_channels = [ch for ch in eeg_channels if ch in df.columns]
            if len(available_channels) < 10:
                return None
            
            eeg_data = df[available_channels].values.T
            
            # 填充缺失通道
            if len(available_channels) < 14:
                padding = np.zeros((14 - len(available_channels), eeg_data.shape[1]))
                eeg_data = np.vstack([eeg_data, padding])
            
            # 标准化
            eeg_data = (eeg_data - np.mean(eeg_data, axis=1, keepdims=True)) / (np.std(eeg_data, axis=1, keepdims=True) + 1e-8)
            
            # 固定长度 - 使用更长的序列以捕获更多时序信息
            target_length = 1536  # 6秒@256Hz
            if eeg_data.shape[1] > target_length:
                # 随机选择起始点
                start_idx = np.random.randint(0, eeg_data.shape[1] - target_length + 1)
                eeg_data = eeg_data[:, start_idx:start_idx + target_length]
            elif eeg_data.shape[1] < target_length:
                pad_width = target_length - eeg_data.shape[1]
                eeg_data = np.pad(eeg_data, ((0, 0), (0, pad_width)), mode='reflect')
            
            return eeg_data.astype(np.float32)
            
        except Exception as e:
            return None
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        sample = self.samples[idx]
        eeg_data = self.load_eeg_signal(sample['file_path'])
        
        if eeg_data is None:
            eeg_data = np.zeros((14, 1536), dtype=np.float32)
        
        return {
            'eeg_data': torch.FloatTensor(eeg_data),
            'label': torch.LongTensor([sample['label']])[0],
            'group': sample['group'],
            'dataset': sample['dataset'],
            'file_path': sample['file_path']
        }

class AttentionCNNAutoencoder(nn.Module):
    """1D Attention-CNN自编码器"""
    
    def __init__(self, n_channels=14, latent_dim=256):
        super().__init__()
        
        # 编码器
        self.encoder = nn.Sequential(
            # 第一层: 1536 -> 768
            nn.Conv1d(n_channels, 32, kernel_size=7, stride=2, padding=3),
            nn.BatchNorm1d(32),
            nn.ReLU(inplace=True),
            
            # 第二层: 768 -> 384
            nn.Conv1d(32, 64, kernel_size=5, stride=2, padding=2),
            nn.BatchNorm1d(64),
            nn.ReLU(inplace=True),
            
            # 第三层: 384 -> 192
            nn.Conv1d(64, 128, kernel_size=3, stride=2, padding=1),
            nn.BatchNorm1d(128),
            nn.ReLU(inplace=True),
            
            # 第四层: 192 -> 96
            nn.Conv1d(128, latent_dim, kernel_size=3, stride=2, padding=1),
            nn.BatchNorm1d(latent_dim),
            nn.ReLU(inplace=True),
        )
        
        # 多头注意力机制
        self.attention = nn.MultiheadAttention(
            embed_dim=latent_dim, 
            num_heads=8, 
            batch_first=True,
            dropout=0.1
        )
        
        # 位置编码
        self.pos_encoding = nn.Parameter(torch.randn(1, 96, latent_dim) * 0.1)
        
        # 解码器
        self.decoder = nn.Sequential(
            # 上采样层1: 96 -> 192
            nn.ConvTranspose1d(latent_dim, 128, kernel_size=4, stride=2, padding=1),
            nn.BatchNorm1d(128),
            nn.ReLU(inplace=True),
            
            # 上采样层2: 192 -> 384
            nn.ConvTranspose1d(128, 64, kernel_size=4, stride=2, padding=1),
            nn.BatchNorm1d(64),
            nn.ReLU(inplace=True),
            
            # 上采样层3: 384 -> 768
            nn.ConvTranspose1d(64, 32, kernel_size=4, stride=2, padding=1),
            nn.BatchNorm1d(32),
            nn.ReLU(inplace=True),
            
            # 上采样层4: 768 -> 1536
            nn.ConvTranspose1d(32, n_channels, kernel_size=4, stride=2, padding=1),
        )
    
    def forward(self, x, return_attention=False):
        batch_size = x.shape[0]
        
        # 编码
        encoded = self.encoder(x)  # [batch, latent_dim, 96]
        
        # 转换为注意力输入格式
        encoded_transposed = encoded.transpose(1, 2)  # [batch, 96, latent_dim]
        
        # 添加位置编码
        encoded_with_pos = encoded_transposed + self.pos_encoding
        
        # 多头注意力
        attended, attention_weights = self.attention(
            encoded_with_pos, encoded_with_pos, encoded_with_pos
        )
        
        # 残差连接
        attended = attended + encoded_transposed
        
        # 转换回卷积格式
        attended = attended.transpose(1, 2)  # [batch, latent_dim, 96]
        
        # 解码
        reconstructed = self.decoder(attended)
        
        if return_attention:
            return reconstructed, attention_weights, encoded
        else:
            return reconstructed

class AutoencoderTrainer:
    """自编码器训练器"""
    
    def __init__(self, model, device):
        self.model = model.to(device)
        self.device = device
        
        # 损失函数
        self.criterion = nn.MSELoss()
        
        # 优化器
        self.optimizer = optim.AdamW(
            self.model.parameters(), 
            lr=1e-4, 
            weight_decay=1e-5
        )
        
        # 学习率调度器
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='min', factor=0.5, patience=7, verbose=True
        )
        
        # 训练历史
        self.history = {
            'train_loss': [], 'val_loss': [],
            'epilepsy_loss': [], 'control_loss': []
        }
    
    def train_epoch(self, train_loader):
        """训练一个epoch"""
        self.model.train()
        
        total_loss = 0.0
        num_batches = 0
        
        for batch in tqdm(train_loader, desc="Training"):
            eeg_data = batch['eeg_data'].to(self.device)
            
            self.optimizer.zero_grad()
            
            # 前向传播
            reconstructed = self.model(eeg_data)
            loss = self.criterion(reconstructed, eeg_data)
            
            # 反向传播
            loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            self.optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
        
        avg_loss = total_loss / num_batches
        return avg_loss
    
    def validate_epoch(self, val_loader):
        """验证一个epoch"""
        self.model.eval()
        
        total_loss = 0.0
        epilepsy_losses = []
        control_losses = []
        num_batches = 0
        
        with torch.no_grad():
            for batch in val_loader:
                eeg_data = batch['eeg_data'].to(self.device)
                labels = batch['label']
                
                # 前向传播
                reconstructed = self.model(eeg_data)
                loss = self.criterion(reconstructed, eeg_data)
                
                total_loss += loss.item()
                num_batches += 1
                
                # 按类别分别计算损失
                for i in range(eeg_data.shape[0]):
                    sample_loss = self.criterion(
                        reconstructed[i:i+1], eeg_data[i:i+1]
                    ).item()
                    
                    if labels[i].item() == 1:  # Epilepsy
                        epilepsy_losses.append(sample_loss)
                    else:  # Control
                        control_losses.append(sample_loss)
        
        avg_loss = total_loss / num_batches
        avg_epilepsy_loss = np.mean(epilepsy_losses) if epilepsy_losses else 0
        avg_control_loss = np.mean(control_losses) if control_losses else 0
        
        return avg_loss, avg_epilepsy_loss, avg_control_loss
    
    def train(self, train_loader, val_loader, num_epochs=30):
        """完整训练循环"""
        print(f"🔄 开始自编码器训练，共 {num_epochs} 个epoch...")
        
        best_val_loss = float('inf')
        
        for epoch in range(num_epochs):
            print(f"\nEpoch {epoch+1}/{num_epochs}")
            
            # 训练
            train_loss = self.train_epoch(train_loader)
            
            # 验证
            val_loss, epilepsy_loss, control_loss = self.validate_epoch(val_loader)
            
            # 更新学习率
            self.scheduler.step(val_loss)
            
            # 记录历史
            self.history['train_loss'].append(train_loss)
            self.history['val_loss'].append(val_loss)
            self.history['epilepsy_loss'].append(epilepsy_loss)
            self.history['control_loss'].append(control_loss)
            
            # 打印结果
            print(f"训练损失: {train_loss:.6f}")
            print(f"验证损失: {val_loss:.6f}")
            print(f"癫痫重建损失: {epilepsy_loss:.6f}")
            print(f"对照重建损失: {control_loss:.6f}")
            
            # 保存最佳模型
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                torch.save({
                    'model_state_dict': self.model.state_dict(),
                    'val_loss': best_val_loss,
                    'history': self.history
                }, 'interictal_autoencoder_best_model.pth')
                print(f"✓ 保存最佳模型 (验证损失: {best_val_loss:.6f})")
        
        print(f"\n🎉 训练完成！最佳验证损失: {best_val_loss:.6f}")
        return self.history

def analyze_reconstructions(model, test_loader, device, num_samples=8):
    """分析重建质量"""
    print("🔍 分析重建质量...")
    
    model.eval()
    
    epilepsy_samples = []
    control_samples = []
    
    with torch.no_grad():
        for batch in test_loader:
            eeg_data = batch['eeg_data'].to(device)
            labels = batch['label']
            groups = batch['group']
            
            # 获取重建结果和注意力权重
            reconstructed, attention_weights, encoded = model(eeg_data, return_attention=True)
            
            for i in range(eeg_data.shape[0]):
                sample_data = {
                    'original': eeg_data[i].cpu().numpy(),
                    'reconstructed': reconstructed[i].cpu().numpy(),
                    'attention': attention_weights[i].cpu().numpy(),
                    'encoded': encoded[i].cpu().numpy(),
                    'group': groups[i],
                    'mse': torch.nn.functional.mse_loss(
                        reconstructed[i], eeg_data[i]
                    ).item()
                }
                
                if labels[i].item() == 1 and len(epilepsy_samples) < num_samples//2:
                    epilepsy_samples.append(sample_data)
                elif labels[i].item() == 0 and len(control_samples) < num_samples//2:
                    control_samples.append(sample_data)
                
                if len(epilepsy_samples) >= num_samples//2 and len(control_samples) >= num_samples//2:
                    break
            
            if len(epilepsy_samples) >= num_samples//2 and len(control_samples) >= num_samples//2:
                break
    
    return epilepsy_samples, control_samples

def visualize_autoencoder_results(epilepsy_samples, control_samples, history):
    """可视化自编码器结果"""
    
    fig = plt.figure(figsize=(20, 16))
    
    # 1. 训练损失曲线
    plt.subplot(4, 4, 1)
    epochs = range(1, len(history['train_loss']) + 1)
    plt.plot(epochs, history['train_loss'], 'b-', label='训练损失')
    plt.plot(epochs, history['val_loss'], 'r-', label='验证损失')
    plt.title('重建损失曲线')
    plt.xlabel('Epoch')
    plt.ylabel('MSE损失')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 2. 按类别的损失对比
    plt.subplot(4, 4, 2)
    plt.plot(epochs, history['epilepsy_loss'], 'r-', label='癫痫损失')
    plt.plot(epochs, history['control_loss'], 'b-', label='对照损失')
    plt.title('按类别的重建损失')
    plt.xlabel('Epoch')
    plt.ylabel('MSE损失')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 3. 损失分布对比
    plt.subplot(4, 4, 3)
    epilepsy_mse = [s['mse'] for s in epilepsy_samples]
    control_mse = [s['mse'] for s in control_samples]
    
    plt.boxplot([control_mse, epilepsy_mse], labels=['对照', '癫痫'])
    plt.title('重建误差分布')
    plt.ylabel('MSE损失')
    plt.grid(True, alpha=0.3)
    
    # 4. 重建质量统计
    plt.subplot(4, 4, 4)
    categories = ['对照组', '癫痫组']
    mean_losses = [np.mean(control_mse), np.mean(epilepsy_mse)]
    std_losses = [np.std(control_mse), np.std(epilepsy_mse)]
    
    bars = plt.bar(categories, mean_losses, yerr=std_losses, capsize=5, 
                   color=['blue', 'red'], alpha=0.7)
    plt.title('平均重建误差')
    plt.ylabel('MSE损失')
    
    for bar, loss in zip(bars, mean_losses):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001,
                f'{loss:.4f}', ha='center', va='bottom')
    
    # 5-8. 癫痫样本重建对比
    for i, sample in enumerate(epilepsy_samples[:2]):
        # 原始信号
        plt.subplot(4, 4, 5 + i*2)
        plt.imshow(sample['original'], aspect='auto', cmap='viridis', vmin=-3, vmax=3)
        plt.title(f'癫痫原始信号 {i+1}')
        plt.ylabel('通道')
        plt.colorbar()
        
        # 重建信号
        plt.subplot(4, 4, 6 + i*2)
        plt.imshow(sample['reconstructed'], aspect='auto', cmap='viridis', vmin=-3, vmax=3)
        plt.title(f'癫痫重建信号 {i+1} (MSE: {sample["mse"]:.4f})')
        plt.ylabel('通道')
        plt.colorbar()
    
    # 9-12. 对照样本重建对比
    for i, sample in enumerate(control_samples[:2]):
        # 原始信号
        plt.subplot(4, 4, 9 + i*2)
        plt.imshow(sample['original'], aspect='auto', cmap='viridis', vmin=-3, vmax=3)
        plt.title(f'对照原始信号 {i+1}')
        plt.ylabel('通道')
        plt.colorbar()
        
        # 重建信号
        plt.subplot(4, 4, 10 + i*2)
        plt.imshow(sample['reconstructed'], aspect='auto', cmap='viridis', vmin=-3, vmax=3)
        plt.title(f'对照重建信号 {i+1} (MSE: {sample["mse"]:.4f})')
        plt.ylabel('通道')
        plt.colorbar()
    
    # 13. 注意力热图 - 癫痫
    plt.subplot(4, 4, 13)
    if epilepsy_samples:
        # 注意力权重是 [num_heads, seq_len, seq_len]，取平均
        attention_map = np.mean(epilepsy_samples[0]['attention'], axis=0)
        if attention_map.ndim == 2:
            plt.imshow(attention_map, aspect='auto', cmap='hot')
            plt.title('癫痫注意力热图')
            plt.ylabel('查询位置')
            plt.xlabel('键位置')
            plt.colorbar()
        else:
            # 如果维度不对，显示一维注意力权重
            plt.plot(attention_map, 'r-')
            plt.title('癫痫注意力权重')
            plt.xlabel('位置')
            plt.ylabel('权重')

    # 14. 注意力热图 - 对照
    plt.subplot(4, 4, 14)
    if control_samples:
        attention_map = np.mean(control_samples[0]['attention'], axis=0)
        if attention_map.ndim == 2:
            plt.imshow(attention_map, aspect='auto', cmap='hot')
            plt.title('对照注意力热图')
            plt.ylabel('查询位置')
            plt.xlabel('键位置')
            plt.colorbar()
        else:
            # 如果维度不对，显示一维注意力权重
            plt.plot(attention_map, 'b-')
            plt.title('对照注意力权重')
            plt.xlabel('位置')
            plt.ylabel('权重')
    
    # 15. 时序注意力权重
    plt.subplot(4, 4, 15)
    if epilepsy_samples and control_samples:
        epilepsy_attention = np.mean(epilepsy_samples[0]['attention'], axis=(0, 1))
        control_attention = np.mean(control_samples[0]['attention'], axis=(0, 1))
        
        x = np.arange(len(epilepsy_attention))
        plt.plot(x, epilepsy_attention, 'r-', label='癫痫', alpha=0.7)
        plt.plot(x, control_attention, 'b-', label='对照', alpha=0.7)
        plt.title('时序注意力权重')
        plt.xlabel('时间位置')
        plt.ylabel('注意力权重')
        plt.legend()
        plt.grid(True, alpha=0.3)
    
    # 16. 编码特征分布
    plt.subplot(4, 4, 16)
    if epilepsy_samples and control_samples:
        epilepsy_features = np.mean(epilepsy_samples[0]['encoded'], axis=0)
        control_features = np.mean(control_samples[0]['encoded'], axis=0)
        
        plt.plot(epilepsy_features, 'r-', label='癫痫编码', alpha=0.7)
        plt.plot(control_features, 'b-', label='对照编码', alpha=0.7)
        plt.title('编码特征对比')
        plt.xlabel('特征维度')
        plt.ylabel('特征值')
        plt.legend()
        plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('task2_autoencoder_reconstruction_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_reconstruction_report(history, epilepsy_samples, control_samples):
    """创建重建分析报告"""
    
    print("\n" + "="*80)
    print("TASK 2: AUTOENCODER RECONSTRUCTION ANALYSIS REPORT")
    print("="*80)
    
    # 最终损失
    final_train_loss = history['train_loss'][-1]
    final_val_loss = history['val_loss'][-1]
    final_epilepsy_loss = history['epilepsy_loss'][-1]
    final_control_loss = history['control_loss'][-1]
    
    print(f"\n📊 重建损失分析:")
    print(f"  最终训练损失: {final_train_loss:.6f}")
    print(f"  最终验证损失: {final_val_loss:.6f}")
    print(f"  癫痫重建损失: {final_epilepsy_loss:.6f}")
    print(f"  对照重建损失: {final_control_loss:.6f}")
    
    # 损失比较
    loss_ratio = final_epilepsy_loss / final_control_loss if final_control_loss > 0 else 1.0
    print(f"\n🔍 重建质量差异:")
    print(f"  癫痫/对照损失比: {loss_ratio:.3f}")
    
    if loss_ratio > 1.2:
        print("  ✓ 癫痫信号更难重建 - 符合预期（病理信号复杂性更高）")
        quality_assessment = "优秀"
    elif loss_ratio > 1.05:
        print("  ≈ 癫痫信号略难重建 - 存在轻微差异")
        quality_assessment = "良好"
    elif loss_ratio < 0.9:
        print("  ⚠ 对照信号更难重建 - 意外结果，需要进一步分析")
        quality_assessment = "需要改进"
    else:
        print("  ≈ 两类信号重建难度相似")
        quality_assessment = "一般"
    
    # 样本级分析
    if epilepsy_samples and control_samples:
        epilepsy_mse = [s['mse'] for s in epilepsy_samples]
        control_mse = [s['mse'] for s in control_samples]
        
        print(f"\n📈 样本级重建误差:")
        print(f"  癫痫样本: {np.mean(epilepsy_mse):.6f} ± {np.std(epilepsy_mse):.6f}")
        print(f"  对照样本: {np.mean(control_mse):.6f} ± {np.std(control_mse):.6f}")
        
        # 统计显著性检验
        from scipy import stats
        t_stat, p_value = stats.ttest_ind(epilepsy_mse, control_mse)
        print(f"  t检验 p值: {p_value:.4f}")
        
        if p_value < 0.05:
            print("  ✓ 两组重建误差存在显著差异")
        else:
            print("  ≈ 两组重建误差无显著差异")
    
    # 注意力机制分析
    print(f"\n🎯 注意力机制分析:")
    if epilepsy_samples and control_samples:
        epilepsy_attention_var = np.var(epilepsy_samples[0]['attention'])
        control_attention_var = np.var(control_samples[0]['attention'])
        
        print(f"  癫痫注意力方差: {epilepsy_attention_var:.6f}")
        print(f"  对照注意力方差: {control_attention_var:.6f}")
        
        if epilepsy_attention_var > control_attention_var * 1.2:
            print("  ✓ 癫痫样本注意力更加分散 - 反映病理复杂性")
        else:
            print("  ≈ 两组注意力模式相似")
    
    # 特征提取有效性评估
    print(f"\n🧠 特征提取有效性评估:")
    print(f"  重建质量: {quality_assessment}")
    
    if final_val_loss < 0.01:
        print("  ✅ 自编码器重建质量优秀")
    elif final_val_loss < 0.05:
        print("  ✅ 自编码器重建质量良好")
    else:
        print("  ⚠️ 自编码器重建质量需要改进")
    
    # 对病灶定位的启示
    print(f"\n💡 对病灶定位任务的启示:")
    print(f"  1. 自编码器能够学习到EEG信号的有效表示")
    print(f"  2. 癫痫和对照信号存在可区分的特征模式")
    print(f"  3. 注意力机制能够捕获时序依赖关系")
    print(f"  4. 建议将自编码器特征集成到定位模型中")
    
    return loss_ratio, quality_assessment

def main():
    """主函数"""
    print("🔄 Task 2: Autoencoder Reconstruction Analysis")
    print("="*70)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # 数据路径
    data_dirs = [
        "1252141/EEGs_Guinea-Bissau",
        "1252141/EEGs_Nigeria"
    ]
    metadata_files = [
        "1252141/metadata_guineabissau.csv",
        "1252141/metadata_nigeria.csv"
    ]
    
    try:
        # 创建数据集
        train_dataset = InterictalEEGDataset(
            data_dirs, metadata_files, split='train'
        )
        test_dataset = InterictalEEGDataset(
            data_dirs, metadata_files, split='test'
        )
        
        # 创建数据加载器
        train_loader = torch.utils.data.DataLoader(
            train_dataset, batch_size=8, shuffle=True, num_workers=0
        )
        test_loader = torch.utils.data.DataLoader(
            test_dataset, batch_size=8, shuffle=False, num_workers=0
        )
        
        print("✅ 发作间期EEG数据集创建成功")
        
    except Exception as e:
        print(f"❌ 数据集创建失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 创建自编码器模型
    model = AttentionCNNAutoencoder(n_channels=14, latent_dim=256)
    param_count = sum(p.numel() for p in model.parameters())
    print(f"注意力CNN自编码器创建成功，参数数量: {param_count:,}")
    
    # 创建训练器
    trainer = AutoencoderTrainer(model, device)
    
    # 开始训练
    history = trainer.train(train_loader, test_loader, num_epochs=25)
    
    # 分析重建质量
    epilepsy_samples, control_samples = analyze_reconstructions(model, test_loader, device)
    
    # 可视化结果
    visualize_autoencoder_results(epilepsy_samples, control_samples, history)
    
    # 创建分析报告
    loss_ratio, quality_assessment = create_reconstruction_report(history, epilepsy_samples, control_samples)
    
    print(f"\n🎉 Task 2 完成！")
    print(f"📊 关键结果:")
    print(f"  - 重建质量: {quality_assessment}")
    print(f"  - 癫痫/对照损失比: {loss_ratio:.3f}")
    print(f"📁 结果文件:")
    print(f"  - interictal_autoencoder_best_model.pth: 训练好的自编码器")
    print(f"  - task2_autoencoder_reconstruction_analysis.png: 分析图表")

if __name__ == "__main__":
    main()
