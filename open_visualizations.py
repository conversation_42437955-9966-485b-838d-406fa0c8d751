#!/usr/bin/env python3
"""
Script to open the generated EEG visualizations
"""

import subprocess
import sys
import os
from pathlib import Path

def open_file(filepath):
    """Open a file using the system default application"""
    try:
        if sys.platform == "darwin":  # macOS
            subprocess.run(["open", filepath])
        elif sys.platform == "win32":  # Windows
            os.startfile(filepath)
        else:  # Linux
            subprocess.run(["xdg-open", filepath])
        print(f"✓ Opened: {filepath}")
        return True
    except Exception as e:
        print(f"✗ Error opening {filepath}: {e}")
        return False

def main():
    """Open all generated visualization files"""
    
    print("Opening EEG Analysis Visualizations...")
    print("="*50)
    
    # List of visualization files to open
    files_to_open = [
        "topographic_maps_guinea_bissau_subjects_1_2_3.png",
        "channel_statistics_guinea_bissau.png", 
        "eeg_channels_subject_1.png",
        "EEG_Dataset_Analysis_Report.md"
    ]
    
    opened_count = 0
    
    for filename in files_to_open:
        if os.path.exists(filename):
            if open_file(filename):
                opened_count += 1
        else:
            print(f"✗ File not found: {filename}")
    
    print(f"\nSuccessfully opened {opened_count}/{len(files_to_open)} files")
    
    if opened_count > 0:
        print("\nVisualization files opened in your default applications:")
        print("• Topographic maps show spatial brain activity distribution")
        print("• Channel statistics show amplitude patterns across electrodes") 
        print("• Individual channel plots show raw EEG time series")
        print("• Analysis report contains comprehensive documentation")
    else:
        print("\nNo files could be opened. Please check that the analysis has been run.")

if __name__ == "__main__":
    main()
