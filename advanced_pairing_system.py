#!/usr/bin/env python3
"""
Advanced EEG-Lesion Pairing System with Machine Learning Clustering
Enhanced pairing strategies using temporal analysis and clustering
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import pickle
import warnings
warnings.filterwarnings('ignore')

from sklearn.cluster import KMeans, DBSCAN
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.metrics import silhouette_score
from scipy.spatial.distance import cdist
from scipy import signal

from eeg_lesion_pairing_system import IntelligentPairingSystem

class AdvancedPairingSystem(IntelligentPairingSystem):
    """Advanced pairing system with clustering and temporal analysis"""
    
    def __init__(self):
        super().__init__()
        self.scaler = StandardScaler()
        
    def extract_temporal_signatures(self, eeg_data):
        """Extract advanced temporal signatures from EEG data"""
        signatures = {}
        
        # Time-frequency analysis
        for i, channel in enumerate(self.eeg_channels):
            channel_data = eeg_data[i, :]
            
            # Compute spectrogram
            f, t, Sxx = signal.spectrogram(channel_data, fs=128, nperseg=256)
            
            # Extract temporal evolution of frequency bands
            for band_name, (low_freq, high_freq) in self.freq_bands.items():
                band_mask = (f >= low_freq) & (f <= high_freq)
                band_power_evolution = np.mean(Sxx[band_mask, :], axis=0)
                
                # Temporal features
                signatures[f'{channel}_{band_name}_temporal_mean'] = np.mean(band_power_evolution)
                signatures[f'{channel}_{band_name}_temporal_std'] = np.std(band_power_evolution)
                signatures[f'{channel}_{band_name}_temporal_trend'] = np.polyfit(range(len(band_power_evolution)), band_power_evolution, 1)[0]
        
        # Cross-channel temporal coherence
        coherence_features = self.compute_temporal_coherence(eeg_data)
        signatures.update(coherence_features)
        
        return signatures
    
    def compute_temporal_coherence(self, eeg_data):
        """Compute temporal coherence between channel pairs"""
        features = {}
        n_channels = len(self.eeg_channels)
        
        for i in range(n_channels):
            for j in range(i+1, n_channels):
                # Compute coherence
                f, Cxy = signal.coherence(eeg_data[i, :], eeg_data[j, :], fs=128)
                
                # Extract coherence in different frequency bands
                for band_name, (low_freq, high_freq) in self.freq_bands.items():
                    band_mask = (f >= low_freq) & (f <= high_freq)
                    band_coherence = np.mean(Cxy[band_mask])
                    features[f'coherence_{self.eeg_channels[i]}_{self.eeg_channels[j]}_{band_name}'] = band_coherence
        
        return features
    
    def cluster_eeg_patterns(self, eeg_data_list, n_clusters=5):
        """Cluster EEG patterns using machine learning"""
        print(f"Clustering EEG patterns into {n_clusters} groups...")
        
        # Extract features for all EEG recordings
        feature_matrix = []
        for eeg_record in eeg_data_list:
            # Extract standard features
            eeg_features = {k: v for k, v in eeg_record.items() 
                           if k not in ['filepath', 'subject_id', 'dataset', 'group', 'condition', 'duration']}
            
            # Convert to feature vector
            feature_vector = list(eeg_features.values())
            feature_matrix.append(feature_vector)
        
        # Standardize features
        feature_matrix = np.array(feature_matrix)
        feature_matrix_scaled = self.scaler.fit_transform(feature_matrix)
        
        # Perform clustering
        kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        cluster_labels = kmeans.fit_predict(feature_matrix_scaled)
        
        # Compute silhouette score
        silhouette_avg = silhouette_score(feature_matrix_scaled, cluster_labels)
        print(f"Average silhouette score: {silhouette_avg:.3f}")
        
        # Assign cluster labels to EEG records
        for i, eeg_record in enumerate(eeg_data_list):
            eeg_record['eeg_cluster'] = cluster_labels[i]
        
        return eeg_data_list, cluster_labels, kmeans
    
    def cluster_lesion_patterns(self, lesion_data_list, n_clusters=4):
        """Cluster lesion patterns based on spatial and morphological features"""
        print(f"Clustering lesion patterns into {n_clusters} groups...")
        
        # Extract lesion features
        feature_matrix = []
        feature_names = ['volume_mm3', 'centroid_x', 'centroid_y', 'centroid_z', 
                        'extent_x', 'extent_y', 'extent_z', 'compactness', 'sphericity']
        
        for lesion_record in lesion_data_list:
            feature_vector = [lesion_record.get(fname, 0) for fname in feature_names]
            feature_matrix.append(feature_vector)
        
        # Standardize features
        feature_matrix = np.array(feature_matrix)
        feature_matrix_scaled = self.scaler.fit_transform(feature_matrix)
        
        # Perform clustering
        kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        cluster_labels = kmeans.fit_predict(feature_matrix_scaled)
        
        # Compute silhouette score
        silhouette_avg = silhouette_score(feature_matrix_scaled, cluster_labels)
        print(f"Average silhouette score: {silhouette_avg:.3f}")
        
        # Assign cluster labels to lesion records
        for i, lesion_record in enumerate(lesion_data_list):
            lesion_record['lesion_cluster'] = cluster_labels[i]
        
        return lesion_data_list, cluster_labels, kmeans
    
    def compute_cluster_compatibility(self, eeg_cluster, lesion_cluster, cluster_compatibility_matrix=None):
        """Compute compatibility between EEG and lesion clusters"""
        if cluster_compatibility_matrix is None:
            # Default compatibility matrix (can be learned from data)
            # Higher values indicate better compatibility
            cluster_compatibility_matrix = np.array([
                [0.9, 0.7, 0.5, 0.3],  # EEG cluster 0 with lesion clusters 0,1,2,3
                [0.7, 0.9, 0.6, 0.4],  # EEG cluster 1 with lesion clusters 0,1,2,3
                [0.5, 0.6, 0.9, 0.7],  # EEG cluster 2 with lesion clusters 0,1,2,3
                [0.3, 0.4, 0.7, 0.9],  # EEG cluster 3 with lesion clusters 0,1,2,3
                [0.6, 0.5, 0.8, 0.6]   # EEG cluster 4 with lesion clusters 0,1,2,3
            ])
        
        # Ensure indices are within bounds
        eeg_idx = min(eeg_cluster, cluster_compatibility_matrix.shape[0] - 1)
        lesion_idx = min(lesion_cluster, cluster_compatibility_matrix.shape[1] - 1)
        
        return cluster_compatibility_matrix[eeg_idx, lesion_idx]
    
    def create_cluster_based_pairings(self, eeg_data_list, lesion_data_list, top_k=3):
        """Create pairings based on cluster compatibility"""
        print("Creating cluster-based pairings...")
        
        # Cluster EEG and lesion data
        eeg_data_clustered, eeg_clusters, eeg_kmeans = self.cluster_eeg_patterns(eeg_data_list)
        lesion_data_clustered, lesion_clusters, lesion_kmeans = self.cluster_lesion_patterns(lesion_data_list)
        
        # Create enhanced pairings
        pairings = []
        
        for eeg_record in eeg_data_clustered:
            eeg_cluster = eeg_record['eeg_cluster']
            
            # Compute compatibility with all lesions
            lesion_scores = []
            for lesion_record in lesion_data_clustered:
                lesion_cluster = lesion_record['lesion_cluster']
                
                # Standard compatibility score
                eeg_features = {k: v for k, v in eeg_record.items() 
                               if k not in ['filepath', 'subject_id', 'dataset', 'group', 'condition', 'duration', 'eeg_cluster']}
                source_features = {k: v for k, v in eeg_record.items() if k.startswith('source_')}
                metadata = {'Group': eeg_record['group']}
                
                standard_scores = self.compute_pairing_score(eeg_features, source_features, lesion_record, metadata)
                
                # Cluster compatibility score
                cluster_score = self.compute_cluster_compatibility(eeg_cluster, lesion_cluster)
                
                # Combined score (weighted average)
                combined_score = 0.7 * standard_scores['total_score'] + 0.3 * cluster_score
                
                lesion_scores.append({
                    'lesion_id': lesion_record['lesion_id'],
                    'lesion_features': lesion_record,
                    'combined_score': combined_score,
                    'cluster_score': cluster_score,
                    **standard_scores
                })
            
            # Sort by combined score and take top-k
            lesion_scores.sort(key=lambda x: x['combined_score'], reverse=True)
            top_lesions = lesion_scores[:top_k]
            
            # Create pairings
            for rank, lesion_match in enumerate(top_lesions):
                pairing = {
                    'eeg_subject_id': eeg_record['subject_id'],
                    'eeg_dataset': eeg_record['dataset'],
                    'eeg_group': eeg_record['group'],
                    'eeg_cluster': eeg_cluster,
                    'lesion_id': lesion_match['lesion_id'],
                    'lesion_cluster': lesion_match['lesion_features']['lesion_cluster'],
                    'pairing_rank': rank + 1,
                    'compatibility_score': lesion_match['combined_score'],
                    'cluster_compatibility': lesion_match['cluster_score'],
                    'spatial_score': lesion_match['spatial_score'],
                    'clinical_score': lesion_match['clinical_score'],
                    'metadata_score': lesion_match['metadata_score'],
                    'eeg_features': eeg_features,
                    'lesion_features': lesion_match['lesion_features']
                }
                pairings.append(pairing)
        
        print(f"Created {len(pairings)} cluster-based pairings")
        return pairings, eeg_kmeans, lesion_kmeans
    
    def visualize_clusters(self, eeg_data_list, lesion_data_list, eeg_kmeans, lesion_kmeans):
        """Visualize EEG and lesion clusters"""
        print("Creating cluster visualizations...")
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # EEG cluster visualization (PCA)
        eeg_features = []
        eeg_labels = []
        for eeg_record in eeg_data_list:
            features = {k: v for k, v in eeg_record.items() 
                       if k not in ['filepath', 'subject_id', 'dataset', 'group', 'condition', 'duration', 'eeg_cluster']}
            eeg_features.append(list(features.values()))
            eeg_labels.append(eeg_record['eeg_cluster'])
        
        eeg_features_scaled = self.scaler.fit_transform(np.array(eeg_features))
        pca_eeg = PCA(n_components=2)
        eeg_pca = pca_eeg.fit_transform(eeg_features_scaled)
        
        scatter = axes[0, 0].scatter(eeg_pca[:, 0], eeg_pca[:, 1], c=eeg_labels, cmap='viridis')
        axes[0, 0].set_title('EEG Pattern Clusters (PCA)')
        axes[0, 0].set_xlabel(f'PC1 ({pca_eeg.explained_variance_ratio_[0]:.2f})')
        axes[0, 0].set_ylabel(f'PC2 ({pca_eeg.explained_variance_ratio_[1]:.2f})')
        plt.colorbar(scatter, ax=axes[0, 0])
        
        # Lesion cluster visualization
        lesion_features = []
        lesion_labels = []
        for lesion_record in lesion_data_list:
            features = [lesion_record.get(fname, 0) for fname in 
                       ['volume_mm3', 'centroid_x', 'centroid_y', 'centroid_z']]
            lesion_features.append(features)
            lesion_labels.append(lesion_record['lesion_cluster'])
        
        lesion_features_scaled = self.scaler.fit_transform(np.array(lesion_features))
        pca_lesion = PCA(n_components=2)
        lesion_pca = pca_lesion.fit_transform(lesion_features_scaled)
        
        scatter = axes[0, 1].scatter(lesion_pca[:, 0], lesion_pca[:, 1], c=lesion_labels, cmap='plasma')
        axes[0, 1].set_title('Lesion Pattern Clusters (PCA)')
        axes[0, 1].set_xlabel(f'PC1 ({pca_lesion.explained_variance_ratio_[0]:.2f})')
        axes[0, 1].set_ylabel(f'PC2 ({pca_lesion.explained_variance_ratio_[1]:.2f})')
        plt.colorbar(scatter, ax=axes[0, 1])
        
        # Cluster distribution
        eeg_cluster_counts = pd.Series(eeg_labels).value_counts().sort_index()
        axes[1, 0].bar(eeg_cluster_counts.index, eeg_cluster_counts.values)
        axes[1, 0].set_title('EEG Cluster Distribution')
        axes[1, 0].set_xlabel('Cluster ID')
        axes[1, 0].set_ylabel('Count')
        
        lesion_cluster_counts = pd.Series(lesion_labels).value_counts().sort_index()
        axes[1, 1].bar(lesion_cluster_counts.index, lesion_cluster_counts.values)
        axes[1, 1].set_title('Lesion Cluster Distribution')
        axes[1, 1].set_xlabel('Cluster ID')
        axes[1, 1].set_ylabel('Count')
        
        plt.tight_layout()
        
        # Save visualization
        output_file = "cluster_analysis_visualization.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"Cluster visualization saved as: {output_file}")
        
        plt.show()
        
        return fig

def main():
    """Demonstrate the advanced pairing system"""
    print("Advanced EEG-Lesion Pairing System")
    print("="*50)
    
    # Initialize advanced system
    advanced_system = AdvancedPairingSystem()
    
    print("Advanced pairing system initialized with clustering capabilities")
    print("Ready for enhanced EEG-lesion matching!")

if __name__ == "__main__":
    main()
