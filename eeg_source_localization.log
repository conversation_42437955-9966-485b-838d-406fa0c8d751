2025-07-31 15:33:07,115 - INFO - 初始化高精度EEG源定位分析系统
2025-07-31 15:33:07,124 - INFO - 使用MNE内置fsaverage模板
2025-07-31 15:33:07,125 - INFO - 系统初始化完成
2025-07-31 15:33:07,125 - INFO - 电导率参数: {'scalp': 0.33, 'skull': 0.0042, 'csf': 1.79, 'gray_matter': 0.33, 'white_matter': 0.14}
2025-07-31 15:33:07,125 - INFO - EEG通道数: 14
2025-07-31 15:33:07,125 - INFO - 系统初始化成功
2025-07-31 15:33:07,132 - INFO - 选择患者: 1 (Group: Epilepsy)
2025-07-31 15:33:07,132 - INFO - 开始执行完整的高精度EEG源定位分析
2025-07-31 15:33:07,132 - INFO - 开始完整的高精度EEG源定位分析
2025-07-31 15:33:07,132 - INFO - ================================================================================
2025-07-31 15:33:07,132 - INFO - 步骤1: 加载EEG数据
2025-07-31 15:33:07,132 - INFO - 加载EEG数据: 1252141/EEGs_Guinea-Bissau/signal-1.csv.gz
2025-07-31 15:33:07,234 - INFO - 验证EEG数据质量
2025-07-31 15:33:07,237 - INFO - EEG数据质量检查完成
2025-07-31 15:33:07,237 - INFO - 平均信号范围: 4.09e+03, 平均相关性: 0.450
2025-07-31 15:33:07,246 - INFO - EEG数据加载成功
2025-07-31 15:33:07,608 - INFO - 通道数: 14, 采样频率: 256.0 Hz, 时长: 150.5秒
2025-07-31 15:33:07,609 - INFO - 步骤2: EEG数据预处理
2025-07-31 15:33:07,609 - INFO - 开始EEG数据预处理
2025-07-31 15:33:07,611 - INFO - 设置平均参考电极
2025-07-31 15:33:07,624 - INFO - 应用带通滤波: 1.0-40.0 Hz
2025-07-31 15:33:07,643 - INFO - 预处理数据质量: 平均功率=7.74e+03, 平均标准差=8.04e+01
2025-07-31 15:33:07,643 - INFO - EEG数据预处理完成
2025-07-31 15:33:07,643 - INFO - 步骤3: 加载MRI数据
2025-07-31 15:33:07,643 - INFO - 加载MRI数据
2025-07-31 15:33:07,643 - INFO - 使用MNI152标准模板
2025-07-31 15:33:07,643 - INFO - 使用标准模板，跳过个体MRI质量检查
2025-07-31 15:33:07,643 - INFO - 步骤4: 头部组织分割
2025-07-31 15:33:07,643 - INFO - 开始5层头部组织分割
2025-07-31 15:33:07,643 - INFO - 使用fsaverage模板分割
2025-07-31 15:33:07,643 - INFO - 验证组织分割质量
2025-07-31 15:33:07,643 - INFO - 使用模板分割，跳过质量检查
2025-07-31 15:33:07,643 - INFO - 头部组织分割完成
2025-07-31 15:33:07,643 - INFO - 步骤5: 构建BEM模型
2025-07-31 15:33:07,643 - INFO - 构建高精度BEM模型
2025-07-31 15:33:07,643 - INFO - 构建fsaverage模板BEM模型
2025-07-31 15:33:07,643 - WARNING - 标准BEM模型构建失败，使用球形模型: conductivity must be a float or a 1D array-like with 1 or 3 elements
2025-07-31 15:33:07,644 - INFO - 构建球形BEM模型
2025-07-31 15:33:07,730 - INFO - 验证BEM模型质量
2025-07-31 15:33:07,730 - INFO - BEM模型质量检查完成
2025-07-31 15:33:07,730 - INFO - BEM模型构建完成
2025-07-31 15:33:07,730 - INFO - 步骤6: 设置源空间
2025-07-31 15:33:07,730 - INFO - 设置源空间
2025-07-31 15:33:07,731 - ERROR - 源空间设置失败: Could not find the LH surface /Users/<USER>/mne_data/MNE-fsaverage-data/fsaverage/fsaverage/surf/lh.white
2025-07-31 15:33:07,731 - ERROR - 分析过程中发生错误: Could not find the LH surface /Users/<USER>/mne_data/MNE-fsaverage-data/fsaverage/fsaverage/surf/lh.white
2025-07-31 15:33:07,731 - ERROR - 分析执行失败: Could not find the LH surface /Users/<USER>/mne_data/MNE-fsaverage-data/fsaverage/fsaverage/surf/lh.white
2025-07-31 15:34:21,156 - INFO - 初始化高精度EEG源定位分析系统
2025-07-31 15:34:21,164 - INFO - 使用MNE内置fsaverage模板
2025-07-31 15:34:21,164 - INFO - 系统初始化完成
2025-07-31 15:34:21,164 - INFO - 电导率参数: {'scalp': 0.33, 'skull': 0.0042, 'csf': 1.79, 'gray_matter': 0.33, 'white_matter': 0.14}
2025-07-31 15:34:21,164 - INFO - EEG通道数: 14
2025-07-31 15:34:21,164 - INFO - 系统初始化成功
2025-07-31 15:34:21,171 - INFO - 选择患者: 1 (Group: Epilepsy)
2025-07-31 15:34:21,171 - INFO - 开始执行完整的高精度EEG源定位分析
2025-07-31 15:34:21,171 - INFO - 开始完整的高精度EEG源定位分析
2025-07-31 15:34:21,171 - INFO - ================================================================================
2025-07-31 15:34:21,171 - INFO - 步骤1: 加载EEG数据
2025-07-31 15:34:21,171 - INFO - 加载EEG数据: 1252141/EEGs_Guinea-Bissau/signal-1.csv.gz
2025-07-31 15:34:21,263 - INFO - 验证EEG数据质量
2025-07-31 15:34:21,266 - INFO - EEG数据质量检查完成
2025-07-31 15:34:21,266 - INFO - 平均信号范围: 4.09e+03, 平均相关性: 0.450
2025-07-31 15:34:21,282 - INFO - EEG数据加载成功
2025-07-31 15:34:21,625 - INFO - 通道数: 14, 采样频率: 256.0 Hz, 时长: 150.5秒
2025-07-31 15:34:21,625 - INFO - 步骤2: EEG数据预处理
2025-07-31 15:34:21,625 - INFO - 开始EEG数据预处理
2025-07-31 15:34:21,627 - INFO - 设置平均参考电极
2025-07-31 15:34:21,639 - INFO - 应用带通滤波: 1.0-40.0 Hz
2025-07-31 15:34:21,665 - INFO - 预处理数据质量: 平均功率=7.74e+03, 平均标准差=8.04e+01
2025-07-31 15:34:21,666 - INFO - EEG数据预处理完成
2025-07-31 15:34:21,666 - INFO - 步骤3: 加载MRI数据
2025-07-31 15:34:21,666 - INFO - 加载MRI数据
2025-07-31 15:34:21,666 - INFO - 使用MNI152标准模板
2025-07-31 15:34:21,666 - INFO - 使用标准模板，跳过个体MRI质量检查
2025-07-31 15:34:21,666 - INFO - 步骤4: 头部组织分割
2025-07-31 15:34:21,666 - INFO - 开始5层头部组织分割
2025-07-31 15:34:21,666 - INFO - 使用fsaverage模板分割
2025-07-31 15:34:21,666 - INFO - 验证组织分割质量
2025-07-31 15:34:21,666 - INFO - 使用模板分割，跳过质量检查
2025-07-31 15:34:21,666 - INFO - 头部组织分割完成
2025-07-31 15:34:21,667 - INFO - 步骤5: 构建BEM模型
2025-07-31 15:34:21,667 - INFO - 构建高精度BEM模型
2025-07-31 15:34:21,667 - INFO - 构建fsaverage模板BEM模型
2025-07-31 15:34:21,667 - WARNING - 标准BEM模型构建失败，使用球形模型: conductivity must be a float or a 1D array-like with 1 or 3 elements
2025-07-31 15:34:21,667 - INFO - 构建球形BEM模型
2025-07-31 15:34:21,741 - INFO - 验证BEM模型质量
2025-07-31 15:34:21,741 - INFO - BEM模型质量检查完成
2025-07-31 15:34:21,741 - INFO - BEM模型构建完成
2025-07-31 15:34:21,741 - INFO - 步骤6: 设置源空间
2025-07-31 15:34:21,741 - INFO - 设置体积源空间
2025-07-31 15:34:21,742 - INFO - 体积源空间设置完成
2025-07-31 15:34:21,742 - INFO - 源点数: 506
2025-07-31 15:34:21,742 - INFO - 步骤7: 计算正向解
2025-07-31 15:34:21,742 - INFO - 计算正向解
2025-07-31 15:34:21,839 - INFO - 验证正向解质量
2025-07-31 15:34:21,842 - INFO - 正向解质量检查完成
2025-07-31 15:34:21,842 - INFO - 条件数: 107.87360876561813, 平均导联场强度: 2.43e+02
2025-07-31 15:34:21,842 - INFO - 正向解计算完成
2025-07-31 15:34:21,842 - INFO - 源点数: 506, 通道数: 14, 导联场矩阵: (14, 1518)
2025-07-31 15:34:21,842 - INFO - 步骤8: 计算噪声协方差矩阵
2025-07-31 15:34:21,842 - INFO - 计算噪声协方差矩阵
2025-07-31 15:34:21,884 - INFO - 验证噪声协方差矩阵质量
2025-07-31 15:34:21,886 - INFO - 噪声协方差质量: 条件数=3.68e+15, 秩=14, 迹=1.08e+05
2025-07-31 15:34:21,886 - INFO - 噪声协方差矩阵计算完成
2025-07-31 15:34:21,886 - INFO - 步骤9: 计算多种逆解
2025-07-31 15:34:21,886 - INFO - 计算多种逆解方法的源定位结果
2025-07-31 15:34:22,239 - INFO - 计算dSPM逆解
2025-07-31 15:34:22,318 - INFO - 验证dSPM逆解质量
2025-07-31 15:34:22,326 - INFO - dSPM逆解质量: 最大活动=9.18e-02, 活跃源数=460, 空间集中度=144.0
2025-07-31 15:34:22,326 - INFO - dSPM逆解计算完成
2025-07-31 15:34:22,326 - INFO - 计算sLORETA逆解
2025-07-31 15:34:22,410 - INFO - 验证sLORETA逆解质量
2025-07-31 15:34:22,410 - INFO - sLORETA逆解质量: 最大活动=4.23e-02, 活跃源数=329, 空间集中度=141.2
2025-07-31 15:34:22,411 - INFO - sLORETA逆解计算完成
2025-07-31 15:34:22,411 - INFO - 计算eLORETA逆解
2025-07-31 15:34:23,184 - INFO - 验证eLORETA逆解质量
2025-07-31 15:34:23,184 - INFO - eLORETA逆解质量: 最大活动=1.02e-03, 活跃源数=379, 空间集中度=138.4
2025-07-31 15:34:23,184 - INFO - eLORETA逆解计算完成
2025-07-31 15:34:23,184 - INFO - 比较逆解方法一致性
2025-07-31 15:34:23,185 - INFO - 方法一致性: 峰值位置标准差=76.2, 平均相关性=0.916
2025-07-31 15:34:23,185 - INFO - 所有逆解方法计算完成
2025-07-31 15:34:23,185 - INFO - 步骤10: 融合多通道结果
2025-07-31 15:34:23,185 - INFO - 融合多通道源定位结果
2025-07-31 15:34:23,187 - INFO - dSPM方法融合完成: 峰值位置=70, 峰值强度=9.18e-02, 活跃区域大小=506
2025-07-31 15:34:23,187 - INFO - sLORETA方法融合完成: 峰值位置=79, 峰值强度=4.23e-02, 活跃区域大小=506
2025-07-31 15:34:23,187 - INFO - eLORETA方法融合完成: 峰值位置=236, 峰值强度=1.02e-03, 活跃区域大小=502
2025-07-31 15:34:23,187 - INFO - 步骤11: 生成可视化
2025-07-31 15:34:23,247 - INFO - 生成综合可视化结果
2025-07-31 15:34:37,968 - INFO - 综合可视化完成
2025-07-31 15:34:37,970 - INFO - 步骤12: 生成统计报告
2025-07-31 15:34:37,970 - INFO - 生成统计分析报告
2025-07-31 15:34:37,973 - INFO - 统计分析报告生成完成
2025-07-31 15:34:37,974 - INFO - 步骤13: 质量控制评估
2025-07-31 15:34:37,975 - INFO - ================================================================================
2025-07-31 15:34:37,975 - INFO - 高精度EEG源定位分析完成!
2025-07-31 15:34:37,975 - INFO - ================================================================================
2025-07-31 15:34:37,978 - INFO - 分析成功完成!
