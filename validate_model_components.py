#!/usr/bin/env python3
"""
逐个验证模型组件的有效性
检查每个组件是否正常工作，找出损失过大的原因
"""

import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 导入模型组件
from center_localization_model import CenterLocalizationModel, CenterLocalizationLoss
from fixed_training_system import NormalizedEEGLesionDataset
from torch.utils.data import DataLoader

class ComponentValidator:
    """组件验证器"""
    
    def __init__(self, device=None):
        self.device = device or torch.device('cpu')
        print(f"组件验证器初始化，使用设备: {self.device}")
    
    def validate_data_loading(self):
        """验证数据加载"""
        print("\n" + "="*60)
        print("1. 验证数据加载组件")
        print("="*60)
        
        try:
            # 创建数据集
            dataset = NormalizedEEGLesionDataset(
                "eeg_lesion_training_dataset/validation_pairings.pkl", 
                augment=False
            )
            
            print(f"✅ 数据集创建成功，样本数: {len(dataset)}")
            
            # 检查单个样本
            sample = dataset[0]
            
            print(f"📊 样本数据结构:")
            for key, value in sample.items():
                if isinstance(value, torch.Tensor):
                    print(f"  {key}: {value.shape}, dtype: {value.dtype}")
                    print(f"    范围: [{value.min():.3f}, {value.max():.3f}]")
                    print(f"    均值: {value.mean():.3f}, 标准差: {value.std():.3f}")
                else:
                    print(f"  {key}: {value} (type: {type(value)})")
            
            # 检查病灶中心计算
            lesion_mask = sample['lesion_mask']
            if torch.sum(lesion_mask) > 0:
                coords = torch.nonzero(lesion_mask, as_tuple=False).float()
                center = torch.mean(coords, dim=0)
                center_xyz = center[[2, 1, 0]]  # 转换为 (x, y, z)
                print(f"📍 真实病灶中心: [{center_xyz[0]:.1f}, {center_xyz[1]:.1f}, {center_xyz[2]:.1f}]")
                print(f"📏 病灶体积: {torch.sum(lesion_mask).item()} 体素")
            else:
                print("⚠️  该样本无病灶标注")
            
            return True, sample
            
        except Exception as e:
            print(f"❌ 数据加载验证失败: {e}")
            return False, None
    
    def validate_eeg_feature_extraction(self, sample):
        """验证EEG特征提取"""
        print("\n" + "="*60)
        print("2. 验证EEG特征提取组件")
        print("="*60)
        
        try:
            # 创建简化的EEG特征提取器
            eeg_features = nn.Sequential(
                nn.Conv1d(14, 64, kernel_size=7, padding=3),
                nn.BatchNorm1d(64),
                nn.ReLU(),
                nn.MaxPool1d(2),
                
                nn.Conv1d(64, 128, kernel_size=5, padding=2),
                nn.BatchNorm1d(128),
                nn.ReLU(),
                nn.MaxPool1d(2),
                
                nn.Conv1d(128, 256, kernel_size=3, padding=1),
                nn.BatchNorm1d(256),
                nn.ReLU(),
                nn.AdaptiveAvgPool1d(1),
                
                nn.Flatten(),
                nn.Linear(256, 512),
                nn.ReLU()
            ).to(self.device)
            
            # 测试输入
            eeg_data = sample['eeg_data'].unsqueeze(0).to(self.device)  # 添加batch维度
            print(f"📥 EEG输入形状: {eeg_data.shape}")
            
            # 逐层检查
            x = eeg_data
            layer_names = ['Conv1d_1', 'BatchNorm1d_1', 'ReLU_1', 'MaxPool1d_1',
                          'Conv1d_2', 'BatchNorm1d_2', 'ReLU_2', 'MaxPool1d_2',
                          'Conv1d_3', 'BatchNorm1d_3', 'ReLU_3', 'AdaptiveAvgPool1d',
                          'Flatten', 'Linear', 'ReLU_final']
            
            for i, (layer, name) in enumerate(zip(eeg_features, layer_names)):
                x = layer(x)
                print(f"  {name}: {x.shape}, 范围: [{x.min():.3f}, {x.max():.3f}]")
                
                # 检查是否有异常值
                if torch.isnan(x).any():
                    print(f"    ⚠️  检测到NaN值!")
                if torch.isinf(x).any():
                    print(f"    ⚠️  检测到Inf值!")
            
            eeg_feat = x
            print(f"✅ EEG特征提取成功，输出形状: {eeg_feat.shape}")
            
            return True, eeg_feat
            
        except Exception as e:
            print(f"❌ EEG特征提取验证失败: {e}")
            return False, None
    
    def validate_temporal_feature_extraction(self, sample):
        """验证时序特征提取"""
        print("\n" + "="*60)
        print("3. 验证时序特征提取组件")
        print("="*60)
        
        try:
            # 创建LSTM特征提取器
            temporal_lstm = nn.LSTM(14, 128, batch_first=True, bidirectional=True).to(self.device)
            temporal_fc = nn.Sequential(
                nn.Linear(256, 512),
                nn.ReLU()
            ).to(self.device)
            
            # 测试输入
            temporal_sequence = sample['temporal_sequence'].unsqueeze(0).to(self.device)
            print(f"📥 时序输入形状: {temporal_sequence.shape}")
            
            # LSTM处理
            temporal_out, (h_n, c_n) = temporal_lstm(temporal_sequence)
            print(f"  LSTM输出形状: {temporal_out.shape}")
            print(f"  LSTM输出范围: [{temporal_out.min():.3f}, {temporal_out.max():.3f}]")
            
            # 使用最后一个时间步
            last_output = temporal_out[:, -1, :]
            print(f"  最后时间步形状: {last_output.shape}")
            
            # 全连接层
            temporal_feat = temporal_fc(last_output)
            print(f"  时序特征形状: {temporal_feat.shape}")
            print(f"  时序特征范围: [{temporal_feat.min():.3f}, {temporal_feat.max():.3f}]")
            
            print(f"✅ 时序特征提取成功")
            
            return True, temporal_feat
            
        except Exception as e:
            print(f"❌ 时序特征提取验证失败: {e}")
            return False, None
    
    def validate_feature_fusion(self, eeg_feat, temporal_feat):
        """验证特征融合"""
        print("\n" + "="*60)
        print("4. 验证特征融合组件")
        print("="*60)
        
        try:
            # 创建融合模块
            fusion = nn.Sequential(
                nn.Linear(512 * 2, 512),
                nn.ReLU(),
                nn.Dropout(0.3),
                nn.Linear(512, 256),
                nn.ReLU()
            ).to(self.device)
            
            print(f"📥 EEG特征形状: {eeg_feat.shape}")
            print(f"📥 时序特征形状: {temporal_feat.shape}")
            
            # 特征拼接
            fused_input = torch.cat([eeg_feat, temporal_feat], dim=1)
            print(f"  拼接后形状: {fused_input.shape}")
            print(f"  拼接后范围: [{fused_input.min():.3f}, {fused_input.max():.3f}]")
            
            # 融合处理
            fused_features = fusion(fused_input)
            print(f"  融合特征形状: {fused_features.shape}")
            print(f"  融合特征范围: [{fused_features.min():.3f}, {fused_features.max():.3f}]")
            
            print(f"✅ 特征融合成功")
            
            return True, fused_features
            
        except Exception as e:
            print(f"❌ 特征融合验证失败: {e}")
            return False, None
    
    def validate_center_prediction(self, fused_features):
        """验证中心预测"""
        print("\n" + "="*60)
        print("5. 验证中心预测组件")
        print("="*60)
        
        try:
            # 创建中心预测器
            center_predictor = nn.Sequential(
                nn.Linear(256, 128),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(128, 64),
                nn.ReLU(),
                nn.Linear(64, 3),
                nn.Sigmoid()
            ).to(self.device)
            
            print(f"📥 融合特征形状: {fused_features.shape}")
            
            # 预测中心
            center_normalized = center_predictor(fused_features)
            print(f"  归一化中心: {center_normalized.shape}")
            print(f"  归一化中心值: {center_normalized.squeeze().detach().cpu().numpy()}")
            
            # 转换到实际坐标
            center_coords = center_normalized * 255.0
            print(f"  实际坐标: {center_coords.squeeze().detach().cpu().numpy()}")
            
            print(f"✅ 中心预测成功")
            
            return True, center_coords
            
        except Exception as e:
            print(f"❌ 中心预测验证失败: {e}")
            return False, None
    
    def validate_loss_function(self, pred_center, sample):
        """验证损失函数"""
        print("\n" + "="*60)
        print("6. 验证损失函数组件")
        print("="*60)
        
        try:
            # 创建损失函数
            criterion = CenterLocalizationLoss()
            
            # 准备数据
            lesion_mask = sample['lesion_mask'].unsqueeze(0).to(self.device)
            
            print(f"📥 预测中心: {pred_center.squeeze().detach().cpu().numpy()}")
            
            # 计算真实中心
            if torch.sum(lesion_mask) > 0:
                coords = torch.nonzero(lesion_mask[0], as_tuple=False).float()
                true_center = torch.mean(coords, dim=0)
                true_center_xyz = true_center[[2, 1, 0]]  # 转换为 (x, y, z)
                print(f"📍 真实中心: {true_center_xyz.detach().cpu().numpy()}")
            else:
                true_center_xyz = torch.tensor([127.5, 127.5, 127.5])
                print(f"📍 默认中心: {true_center_xyz.numpy()}")
            
            # 计算损失
            loss_results = criterion(pred_center, lesion_mask)
            
            print(f"📊 损失分析:")
            print(f"  总损失: {loss_results['total_loss'].item():.4f}")
            print(f"  MSE损失: {loss_results['mse_loss'].item():.4f}")
            print(f"  L1损失: {loss_results['l1_loss'].item():.4f}")
            print(f"  平均距离: {loss_results['mean_distance'].item():.2f} 体素")
            
            # 分析损失大小
            distance = loss_results['mean_distance'].item()
            if distance > 100:
                print(f"⚠️  距离过大 ({distance:.2f})，可能存在问题")
            elif distance > 50:
                print(f"⚠️  距离较大 ({distance:.2f})，需要优化")
            else:
                print(f"✅ 距离合理 ({distance:.2f})")
            
            return True, loss_results
            
        except Exception as e:
            print(f"❌ 损失函数验证失败: {e}")
            return False, None
    
    def validate_complete_model(self, sample):
        """验证完整模型"""
        print("\n" + "="*60)
        print("7. 验证完整模型")
        print("="*60)
        
        try:
            # 创建完整模型
            model = CenterLocalizationModel(n_channels=14, feature_dim=512).to(self.device)
            model.eval()
            
            # 准备输入
            eeg_data = sample['eeg_data'].unsqueeze(0).to(self.device)
            temporal_sequence = sample['temporal_sequence'].unsqueeze(0).to(self.device)
            lesion_mask = sample['lesion_mask'].unsqueeze(0).to(self.device)
            
            print(f"📥 模型输入:")
            print(f"  EEG数据: {eeg_data.shape}")
            print(f"  时序数据: {temporal_sequence.shape}")
            
            # 前向传播
            with torch.no_grad():
                outputs = model(eeg_data, temporal_sequence)
            
            print(f"📤 模型输出:")
            for key, value in outputs.items():
                if isinstance(value, torch.Tensor):
                    print(f"  {key}: {value.shape}")
                    if key == 'center':
                        print(f"    预测中心: {value.squeeze().cpu().numpy()}")
            
            # 计算损失
            criterion = CenterLocalizationLoss()
            loss_results = criterion(outputs['center'], lesion_mask)
            
            print(f"📊 完整模型损失:")
            print(f"  总损失: {loss_results['total_loss'].item():.4f}")
            print(f"  定位误差: {loss_results['mean_distance'].item():.2f} 体素")
            
            print(f"✅ 完整模型验证成功")
            
            return True, outputs, loss_results
            
        except Exception as e:
            print(f"❌ 完整模型验证失败: {e}")
            return False, None, None
    
    def analyze_gradient_flow(self, model, sample):
        """分析梯度流"""
        print("\n" + "="*60)
        print("8. 分析梯度流")
        print("="*60)
        
        try:
            model.train()
            
            # 准备数据
            eeg_data = sample['eeg_data'].unsqueeze(0).to(self.device)
            temporal_sequence = sample['temporal_sequence'].unsqueeze(0).to(self.device)
            lesion_mask = sample['lesion_mask'].unsqueeze(0).to(self.device)
            
            # 确保需要梯度
            eeg_data.requires_grad_(True)
            temporal_sequence.requires_grad_(True)
            
            # 前向传播
            outputs = model(eeg_data, temporal_sequence)
            
            # 计算损失
            criterion = CenterLocalizationLoss()
            loss_results = criterion(outputs['center'], lesion_mask)
            loss = loss_results['total_loss']
            
            # 反向传播
            loss.backward()
            
            # 检查梯度
            print(f"📊 梯度分析:")
            total_norm = 0
            param_count = 0
            zero_grad_count = 0
            
            for name, param in model.named_parameters():
                if param.grad is not None:
                    param_norm = param.grad.data.norm(2).item()
                    total_norm += param_norm ** 2
                    param_count += 1
                    
                    if param_norm < 1e-8:
                        zero_grad_count += 1
                        
                    print(f"  {name}: 梯度范数 = {param_norm:.6f}")
                else:
                    print(f"  {name}: 无梯度")
            
            total_norm = total_norm ** 0.5
            
            print(f"📈 梯度统计:")
            print(f"  总梯度范数: {total_norm:.6f}")
            print(f"  有梯度的参数: {param_count}")
            print(f"  零梯度参数: {zero_grad_count}")
            
            if total_norm > 0:
                print(f"✅ 梯度流正常")
            else:
                print(f"❌ 梯度流异常")
            
            return True, total_norm
            
        except Exception as e:
            print(f"❌ 梯度流分析失败: {e}")
            return False, 0

def main():
    """主验证函数"""
    print("🔍 模型组件逐个验证")
    print("="*70)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    validator = ComponentValidator(device)
    
    # 1. 验证数据加载
    data_ok, sample = validator.validate_data_loading()
    if not data_ok:
        print("❌ 数据加载失败，停止验证")
        return
    
    # 2. 验证EEG特征提取
    eeg_ok, eeg_feat = validator.validate_eeg_feature_extraction(sample)
    if not eeg_ok:
        print("❌ EEG特征提取失败")
        return
    
    # 3. 验证时序特征提取
    temporal_ok, temporal_feat = validator.validate_temporal_feature_extraction(sample)
    if not temporal_ok:
        print("❌ 时序特征提取失败")
        return
    
    # 4. 验证特征融合
    fusion_ok, fused_features = validator.validate_feature_fusion(eeg_feat, temporal_feat)
    if not fusion_ok:
        print("❌ 特征融合失败")
        return
    
    # 5. 验证中心预测
    pred_ok, pred_center = validator.validate_center_prediction(fused_features)
    if not pred_ok:
        print("❌ 中心预测失败")
        return
    
    # 6. 验证损失函数
    loss_ok, loss_results = validator.validate_loss_function(pred_center, sample)
    if not loss_ok:
        print("❌ 损失函数失败")
        return
    
    # 7. 验证完整模型
    model_ok, outputs, full_loss = validator.validate_complete_model(sample)
    if not model_ok:
        print("❌ 完整模型失败")
        return
    
    # 8. 分析梯度流
    model = CenterLocalizationModel(n_channels=14, feature_dim=512).to(device)
    grad_ok, grad_norm = validator.analyze_gradient_flow(model, sample)
    
    # 总结
    print("\n" + "="*70)
    print("🎯 验证总结")
    print("="*70)
    
    print("✅ 通过的组件:")
    components = [
        ("数据加载", data_ok),
        ("EEG特征提取", eeg_ok),
        ("时序特征提取", temporal_ok),
        ("特征融合", fusion_ok),
        ("中心预测", pred_ok),
        ("损失函数", loss_ok),
        ("完整模型", model_ok),
        ("梯度流", grad_ok)
    ]
    
    for name, status in components:
        status_str = "✅" if status else "❌"
        print(f"  {status_str} {name}")
    
    if full_loss:
        distance = full_loss['mean_distance'].item()
        print(f"\n📊 关键指标:")
        print(f"  定位误差: {distance:.2f} 体素")
        print(f"  总损失: {full_loss['total_loss'].item():.4f}")
        print(f"  梯度范数: {grad_norm:.6f}")
        
        # 问题诊断
        print(f"\n🔍 问题诊断:")
        if distance > 100:
            print("  ⚠️  定位误差过大，可能是:")
            print("     - 模型初始化问题")
            print("     - 损失函数设计问题")
            print("     - 数据预处理问题")
        elif distance > 50:
            print("  ⚠️  定位误差较大，需要优化")
        else:
            print("  ✅ 定位误差在合理范围内")
        
        if grad_norm < 1e-6:
            print("  ⚠️  梯度过小，可能存在梯度消失")
        elif grad_norm > 10:
            print("  ⚠️  梯度过大，可能存在梯度爆炸")
        else:
            print("  ✅ 梯度范数正常")

if __name__ == "__main__":
    main()
