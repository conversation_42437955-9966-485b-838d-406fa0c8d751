#!/usr/bin/env python3
"""
EEG数据探索和分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import gzip
import os
import glob

def explore_eeg_data():
    """
    探索EEG数据结构
    """
    print("=== 探索EEG数据结构 ===")
    
    # 查看metadata
    print("\n1. 分析metadata...")
    nigeria_meta = pd.read_csv("1252141/metadata_nigeria.csv")
    guinea_meta = pd.read_csv("1252141/metadata_guineabissau.csv")
    
    print(f"Nigeria metadata shape: {nigeria_meta.shape}")
    print(f"Guinea-Bissau metadata shape: {guinea_meta.shape}")
    
    print("\nNigeria metadata columns:")
    print(nigeria_meta.columns.tolist())
    print("\nNigeria metadata sample:")
    print(nigeria_meta.head())
    
    print("\nGuinea-Bissau metadata columns:")
    print(guinea_meta.columns.tolist())
    print("\nGuinea-Bissau metadata sample:")
    print(guinea_meta.head())
    
    # 分析EEG文件
    print("\n2. 分析EEG文件...")
    nigeria_files = glob.glob("1252141/EEGs_Nigeria/*.csv.gz")
    guinea_files = glob.glob("1252141/EEGs_Guinea-Bissau/*.csv.gz")
    
    print(f"Nigeria EEG files: {len(nigeria_files)}")
    print(f"Guinea-Bissau EEG files: {len(guinea_files)}")
    
    # 读取一个示例文件
    if nigeria_files:
        sample_file = nigeria_files[0]
        print(f"\n3. 分析示例文件: {sample_file}")
        
        with gzip.open(sample_file, 'rt') as f:
            sample_data = pd.read_csv(f)
        
        print(f"Sample data shape: {sample_data.shape}")
        print(f"Columns: {sample_data.columns.tolist()}")
        print(f"Data types: {sample_data.dtypes}")
        print("\nFirst few rows:")
        print(sample_data.head())
        
        print(f"\nData statistics:")
        print(sample_data.describe())
        
        return sample_data, nigeria_meta, guinea_meta
    
    return None, nigeria_meta, guinea_meta

def analyze_eeg_structure(sample_data):
    """
    分析EEG数据结构
    """
    print("\n=== 分析EEG数据结构 ===")
    
    # 检查是否有时间列
    time_cols = [col for col in sample_data.columns if 'time' in col.lower()]
    print(f"Time columns: {time_cols}")
    
    # 检查电极列
    electrode_cols = [col for col in sample_data.columns if col not in time_cols]
    print(f"Electrode columns ({len(electrode_cols)}): {electrode_cols}")
    
    # 分析采样率
    if time_cols:
        time_col = time_cols[0]
        time_diff = sample_data[time_col].diff().dropna()
        sampling_rate = 1 / time_diff.mean() if time_diff.mean() > 0 else None
        print(f"Estimated sampling rate: {sampling_rate:.2f} Hz")
    
    # 分析数据范围
    if electrode_cols:
        electrode_data = sample_data[electrode_cols]
        print(f"\nElectrode data statistics:")
        print(f"Min value: {electrode_data.min().min():.6f}")
        print(f"Max value: {electrode_data.max().max():.6f}")
        print(f"Mean value: {electrode_data.mean().mean():.6f}")
        print(f"Std value: {electrode_data.std().mean():.6f}")
    
    return electrode_cols, time_cols

def main():
    """
    主函数
    """
    # 探索数据
    sample_data, nigeria_meta, guinea_meta = explore_eeg_data()
    
    if sample_data is not None:
        electrode_cols, time_cols = analyze_eeg_structure(sample_data)
        
        # 创建简单可视化
        print("\n=== 创建数据可视化 ===")
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('EEG Data Exploration', fontsize=16)
        
        # 1. 时间序列示例
        if electrode_cols and len(electrode_cols) > 0:
            sample_channels = electrode_cols[:4] if len(electrode_cols) >= 4 else electrode_cols
            
            ax = axes[0, 0]
            for i, ch in enumerate(sample_channels):
                ax.plot(sample_data[ch][:1000], label=ch, alpha=0.7)
            ax.set_title('Sample EEG Channels (first 1000 points)')
            ax.set_xlabel('Time points')
            ax.set_ylabel('Amplitude')
            ax.legend()
            ax.grid(True, alpha=0.3)
        
        # 2. 数据分布
        if electrode_cols:
            ax = axes[0, 1]
            sample_data[electrode_cols[0]].hist(bins=50, ax=ax, alpha=0.7)
            ax.set_title(f'Distribution of {electrode_cols[0]}')
            ax.set_xlabel('Amplitude')
            ax.set_ylabel('Frequency')
            ax.grid(True, alpha=0.3)
        
        # 3. 相关性矩阵
        if len(electrode_cols) >= 4:
            ax = axes[1, 0]
            corr_data = sample_data[electrode_cols[:8]].corr()
            im = ax.imshow(corr_data, cmap='coolwarm', vmin=-1, vmax=1)
            ax.set_title('Channel Correlation Matrix')
            ax.set_xticks(range(len(corr_data.columns)))
            ax.set_yticks(range(len(corr_data.columns)))
            ax.set_xticklabels(corr_data.columns, rotation=45)
            ax.set_yticklabels(corr_data.columns)
            plt.colorbar(im, ax=ax)
        
        # 4. 统计信息
        ax = axes[1, 1]
        if electrode_cols:
            stats_text = f"""
            EEG Data Summary:
            
            • Total files: {len(glob.glob("1252141/EEGs_Nigeria/*.csv.gz")) + len(glob.glob("1252141/EEGs_Guinea-Bissau/*.csv.gz"))}
            • Channels: {len(electrode_cols)}
            • Sample length: {len(sample_data)}
            • Data range: {sample_data[electrode_cols].min().min():.3f} to {sample_data[electrode_cols].max().max():.3f}
            • Mean amplitude: {sample_data[electrode_cols].mean().mean():.6f}
            
            Channel names:
            {', '.join(electrode_cols[:10])}
            {'...' if len(electrode_cols) > 10 else ''}
            """
            
            ax.text(0.05, 0.95, stats_text, transform=ax.transAxes,
                   fontsize=10, verticalalignment='top',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))
        ax.set_title('Data Summary')
        ax.axis('off')
        
        plt.tight_layout()
        plt.savefig('eeg_data_exploration.png', dpi=300, bbox_inches='tight')
        print("Saved: eeg_data_exploration.png")
        plt.close()
        
        return electrode_cols, sample_data
    
    return None, None

if __name__ == "__main__":
    electrode_cols, sample_data = main()
