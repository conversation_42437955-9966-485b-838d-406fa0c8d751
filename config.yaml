# EEG源定位系统配置文件

# 系统基本配置
system:
  name: "EEG多通道源定位分析系统"
  version: "1.0.0"
  author: "神经影像分析团队"
  python_version: ">=3.8"
  
# 数据路径配置
data_paths:
  eeg_data_dir: "1252141"
  mri_data_dir: "masks-2"
  template_dir: "templates"
  output_dir: "results"
  cache_dir: "cache"
  
# EEG数据处理配置
eeg_processing:
  # 采样率配置
  sampling_rate: 250  # Hz
  
  # 滤波参数
  filtering:
    highpass: 0.5     # Hz
    lowpass: 100      # Hz
    notch: 50         # Hz (工频滤波)
    
  # 预处理参数
  preprocessing:
    remove_dc: true
    detrend: true
    baseline_correction: true
    
  # 伪迹去除
  artifact_removal:
    enable_ica: true
    ica_components: 20
    enable_ssp: true
    
  # 通道质量评估
  channel_quality:
    bad_channel_threshold: 3.0  # 标准差倍数
    correlation_threshold: 0.3
    
# MRI头部建模配置
head_modeling:
  # MNI模板配置
  mni_template:
    resolution: 1  # mm
    template_type: "MNI152_T1_1mm"
    
  # 组织分割配置
  tissue_segmentation:
    method: "freesurfer"  # freesurfer, fsl, simpleitk
    tissues:
      - "scalp"      # 头皮
      - "skull"      # 颅骨
      - "csf"        # 脑脊液
      - "gray"       # 灰质
      - "white"      # 白质
    
    # 分割精度要求
    accuracy_threshold: 1.0  # mm
    
  # 个体化处理
  individual_processing:
    enable_registration: true
    registration_method: "ants"  # ants, fsl, spm
    
# BEM建模配置
bem_modeling:
  # 模型类型
  model_type: "5_layer"  # 3_layer, 5_layer
  
  # 导电率设置 (S/m)
  conductivity:
    scalp: 0.33
    skull: 0.0042
    csf: 1.79
    gray: 0.33
    white: 0.14
    
  # 网格参数
  mesh:
    scalp_density: 5120   # 三角形数量
    skull_density: 5120
    brain_density: 5120
    
  # 数值精度
  numerical_accuracy: 1e-6
  
# 源定位配置
source_localization:
  # 源空间配置
  source_space:
    spacing: 5  # mm
    surface_type: "white"  # white, pial, inflated
    
  # 逆向求解方法
  inverse_methods:
    - "loreta"
    - "sloreta"
    - "eloreta"
    - "mne"
    - "dspm"
    
  # 正则化参数
  regularization:
    lambda_auto: true
    lambda_value: 0.1
    
  # 噪声协方差
  noise_cov:
    method: "empirical"
    regularization: 0.1
    
# 可视化配置
visualization:
  # 3D渲染配置
  brain_3d:
    backend: "matplotlib"  # matplotlib, plotly, mayavi
    surface_alpha: 0.8
    colormap: "hot"
    
  # 拓扑图配置
  topography:
    interpolation: "cubic"
    contour_lines: 10
    colorbar: true
    
  # 输出格式
  output_formats:
    - "png"
    - "svg"
    - "html"
    
# 性能优化配置
performance:
  # 并行计算
  parallel:
    n_jobs: -1  # 使用所有可用CPU核心
    backend: "loky"
    
  # 内存管理
  memory:
    max_memory_usage: "8GB"
    use_memmap: true
    chunk_size: 1000
    
  # 缓存配置
  cache:
    enable: true
    max_size: "2GB"
    compression: "lz4"
    
# 质量控制配置
quality_control:
  # 验证阈值
  validation:
    geometry_check: true
    conductivity_check: true
    source_space_check: true
    
  # 统计分析
  statistics:
    confidence_level: 0.95
    multiple_comparison_correction: "fdr"
    
# 日志配置
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "eeg_source_localization.log"
  
# 输出配置
output:
  # 结果保存格式
  save_formats:
    - "nifti"    # 3D体积数据
    - "gifti"    # 表面数据
    - "json"     # 元数据
    - "csv"      # 表格数据
    
  # 报告生成
  report:
    include_plots: true
    include_statistics: true
    format: "html"  # html, pdf, markdown
