#!/usr/bin/env python3
"""
简化的EEG源定位分析
使用基本的偶极子拟合和电场分布分析

参考文献：
1. <PERSON><PERSON><PERSON> & Von <PERSON>on (1985). Two bilateral sources of the late AEP as identified by a spatio-temporal dipole model.
2. <PERSON><PERSON> et al. (1992). EEG and MEG: forward solutions for inverse methods.
3. <PERSON> & <PERSON> (2006). Electric fields of the brain: the neurophysics of EEG.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import gzip
from pathlib import Path
from scipy.optimize import minimize
from scipy.spatial.distance import cdist
import warnings
warnings.filterwarnings('ignore')

class SimplifiedEEGSourceLocalizer:
    """简化的EEG源定位分析器"""
    
    def __init__(self):
        """初始化源定位分析器"""
        
        # 14通道EEG电极位置 (10-20系统，球坐标转换为笛卡尔坐标)
        # 基于标准10-20系统位置，头部半径 = 9cm
        self.electrode_positions = {
            'AF3': np.array([-0.03, 0.08, 0.04]),   # 左前额
            'AF4': np.array([0.03, 0.08, 0.04]),    # 右前额
            'F3': np.array([-0.05, 0.06, 0.05]),    # 左额
            'F4': np.array([0.05, 0.06, 0.05]),     # 右额
            'F7': np.array([-0.07, 0.04, 0.03]),    # 左颞前
            'F8': np.array([0.07, 0.04, 0.03]),     # 右颞前
            'FC5': np.array([-0.06, 0.02, 0.06]),   # 左中央前
            'FC6': np.array([0.06, 0.02, 0.06]),    # 右中央前
            'T7': np.array([-0.08, 0.00, 0.02]),    # 左颞
            'T8': np.array([0.08, 0.00, 0.02]),     # 右颞
            'P7': np.array([-0.07, -0.04, 0.03]),   # 左颞后
            'P8': np.array([0.07, -0.04, 0.03]),    # 右颞后
            'O1': np.array([-0.03, -0.08, 0.04]),   # 左枕
            'O2': np.array([0.03, -0.08, 0.04])     # 右枕
        }
        
        # 14通道名称
        self.eeg_channels = ['AF3', 'AF4', 'F3', 'F4', 'F7', 'F8', 'FC5', 'FC6', 
                            'O1', 'O2', 'P7', 'P8', 'T7', 'T8']
        
        # 生物组织电导率 (S/m)
        self.conductivity = 0.33  # 简化为单一电导率
        
        # 头部几何参数
        self.head_radius = 0.09  # 9cm
        
        print("🧠 简化EEG源定位分析器初始化完成")
        print(f"📍 电极数量: {len(self.eeg_channels)}")
        print(f"🔬 头部半径: {self.head_radius*1000:.0f} mm")
        print(f"⚡ 电导率: {self.conductivity} S/m")
    
    def load_eeg_data(self, file_path):
        """加载EEG数据"""
        print(f"📊 加载EEG数据: {file_path}")
        
        try:
            with gzip.open(file_path, 'rt') as f:
                df = pd.read_csv(f)
            
            # 检查通道可用性
            available_channels = [ch for ch in self.eeg_channels if ch in df.columns]
            if len(available_channels) < 10:
                raise ValueError(f"可用通道数量不足: {len(available_channels)}/14")
            
            # 提取EEG数据
            eeg_data = np.zeros((14, len(df)))
            for i, ch in enumerate(self.eeg_channels):
                if ch in available_channels:
                    eeg_data[i] = df[ch].values
            
            # 基本预处理
            # 1. 去除直流分量
            eeg_data = eeg_data - np.mean(eeg_data, axis=1, keepdims=True)
            
            # 2. 标准化
            eeg_data = eeg_data / (np.std(eeg_data, axis=1, keepdims=True) + 1e-8)
            
            # 3. 简单滤波 (移动平均)
            window_size = 5
            for i in range(14):
                eeg_data[i] = np.convolve(eeg_data[i], np.ones(window_size)/window_size, mode='same')
            
            print(f"✅ EEG数据加载成功")
            print(f"   通道数: {len(available_channels)}")
            print(f"   数据点数: {eeg_data.shape[1]}")
            print(f"   时间长度: {eeg_data.shape[1]/256:.1f} 秒")
            
            return eeg_data, available_channels
            
        except Exception as e:
            print(f"❌ EEG数据加载失败: {e}")
            raise
    
    def compute_electrode_matrix(self):
        """计算电极位置矩阵"""
        electrode_matrix = np.zeros((14, 3))
        for i, ch in enumerate(self.eeg_channels):
            if ch in self.electrode_positions:
                electrode_matrix[i] = self.electrode_positions[ch]
        
        return electrode_matrix
    
    def dipole_forward_model(self, dipole_pos, dipole_moment, electrode_pos):
        """
        偶极子正向模型
        计算给定偶极子在各电极处产生的电位
        
        Parameters:
        -----------
        dipole_pos : array (3,)
            偶极子位置 [x, y, z]
        dipole_moment : array (3,)
            偶极子矩 [mx, my, mz]
        electrode_pos : array (n_electrodes, 3)
            电极位置矩阵
            
        Returns:
        --------
        potentials : array (n_electrodes,)
            各电极处的电位
        """
        n_electrodes = electrode_pos.shape[0]
        potentials = np.zeros(n_electrodes)
        
        # 球形头模型中的偶极子电位公式
        # V = (1/4πσr²) * (p·r) / r³
        
        for i in range(n_electrodes):
            r_vec = electrode_pos[i] - dipole_pos  # 从偶极子到电极的向量
            r_dist = np.linalg.norm(r_vec)
            
            if r_dist > 1e-6:  # 避免除零
                # 简化的偶极子电位公式
                potential = np.dot(dipole_moment, r_vec) / (4 * np.pi * self.conductivity * r_dist**3)
                potentials[i] = potential
        
        return potentials
    
    def fit_single_dipole(self, measured_potentials, electrode_pos):
        """
        单偶极子拟合
        
        Parameters:
        -----------
        measured_potentials : array (n_electrodes,)
            测量的电极电位
        electrode_pos : array (n_electrodes, 3)
            电极位置
            
        Returns:
        --------
        best_dipole : dict
            最佳偶极子参数
        """
        
        def objective_function(params):
            """目标函数：最小化预测电位与测量电位的差异"""
            dipole_pos = params[:3]
            dipole_moment = params[3:6]
            
            # 约束偶极子在头部内部
            if np.linalg.norm(dipole_pos) > self.head_radius * 0.8:
                return 1e6
            
            predicted_potentials = self.dipole_forward_model(dipole_pos, dipole_moment, electrode_pos)
            
            # 计算相关系数作为拟合度量
            correlation = np.corrcoef(measured_potentials, predicted_potentials)[0, 1]
            
            # 最大化相关系数 = 最小化负相关系数
            return -correlation if not np.isnan(correlation) else 1e6
        
        # 多次随机初始化寻找全局最优解
        best_result = None
        best_score = 1e6
        
        for _ in range(20):  # 20次随机初始化
            # 随机初始化偶极子位置（在头部内部）
            init_pos = np.random.uniform(-0.05, 0.05, 3)
            # 随机初始化偶极子矩
            init_moment = np.random.uniform(-1e-9, 1e-9, 3)
            
            initial_params = np.concatenate([init_pos, init_moment])
            
            try:
                result = minimize(
                    objective_function,
                    initial_params,
                    method='L-BFGS-B',
                    bounds=[(-0.08, 0.08)] * 6  # 位置和矩的边界
                )
                
                if result.fun < best_score:
                    best_score = result.fun
                    best_result = result
                    
            except:
                continue
        
        if best_result is not None:
            dipole_pos = best_result.x[:3]
            dipole_moment = best_result.x[3:6]
            goodness_of_fit = -best_result.fun
            
            return {
                'position': dipole_pos,
                'moment': dipole_moment,
                'goodness_of_fit': goodness_of_fit,
                'residual_variance': 1 - goodness_of_fit**2
            }
        else:
            return None
    
    def analyze_source_activity(self, eeg_data, time_window=None):
        """
        分析源活动
        
        Parameters:
        -----------
        eeg_data : array (n_channels, n_times)
            EEG数据
        time_window : tuple or None
            分析的时间窗口 (start_idx, end_idx)
            
        Returns:
        --------
        source_results : dict
            源分析结果
        """
        print(f"🎯 分析源活动...")
        
        # 获取电极位置
        electrode_pos = self.compute_electrode_matrix()
        
        # 选择时间窗口
        if time_window is None:
            # 选择中间5秒
            n_times = eeg_data.shape[1]
            start_idx = max(0, n_times//2 - int(2.5*256))
            end_idx = min(n_times, n_times//2 + int(2.5*256))
        else:
            start_idx, end_idx = time_window
        
        # 计算时间平均的电位分布
        avg_potentials = np.mean(eeg_data[:, start_idx:end_idx], axis=1)
        
        print(f"   分析时间窗口: {start_idx/256:.1f} - {end_idx/256:.1f} 秒")
        print(f"   电位范围: {np.min(avg_potentials):.2e} - {np.max(avg_potentials):.2e}")
        
        # 单偶极子拟合
        dipole_result = self.fit_single_dipole(avg_potentials, electrode_pos)
        
        if dipole_result is not None:
            print(f"✅ 偶极子拟合成功")
            print(f"   位置: ({dipole_result['position'][0]*1000:.1f}, {dipole_result['position'][1]*1000:.1f}, {dipole_result['position'][2]*1000:.1f}) mm")
            print(f"   拟合优度: {dipole_result['goodness_of_fit']:.3f}")
            print(f"   残差方差: {dipole_result['residual_variance']:.3f}")
        else:
            print(f"❌ 偶极子拟合失败")
        
        # 创建3D源活动分布
        source_grid = self.create_source_grid()
        source_activity = self.compute_source_distribution(avg_potentials, electrode_pos, source_grid)
        
        return {
            'dipole_result': dipole_result,
            'avg_potentials': avg_potentials,
            'electrode_positions': electrode_pos,
            'source_grid': source_grid,
            'source_activity': source_activity,
            'time_window': (start_idx, end_idx)
        }
    
    def create_source_grid(self, resolution=0.01):
        """创建3D源网格"""
        # 在头部内部创建3D网格
        x = np.arange(-0.06, 0.07, resolution)
        y = np.arange(-0.06, 0.07, resolution)
        z = np.arange(-0.02, 0.08, resolution)
        
        X, Y, Z = np.meshgrid(x, y, z)
        grid_points = np.column_stack([X.ravel(), Y.ravel(), Z.ravel()])
        
        # 只保留头部内部的点
        distances = np.linalg.norm(grid_points, axis=1)
        inside_head = distances < self.head_radius * 0.8
        
        return grid_points[inside_head]
    
    def compute_source_distribution(self, measured_potentials, electrode_pos, source_grid):
        """计算源活动分布"""
        n_sources = source_grid.shape[0]
        source_activity = np.zeros(n_sources)
        
        # 简化的源活动估计：基于距离加权的反投影
        for i, source_pos in enumerate(source_grid):
            # 计算该源点到各电极的距离
            distances = np.linalg.norm(electrode_pos - source_pos, axis=1)
            
            # 距离加权
            weights = 1.0 / (distances + 1e-6)
            weights = weights / np.sum(weights)
            
            # 加权平均电位作为源活动强度
            source_activity[i] = np.abs(np.sum(weights * measured_potentials))
        
        return source_activity
    
    def visualize_results(self, results):
        """可视化源定位结果"""
        print(f"📊 可视化源定位结果...")
        
        fig = plt.figure(figsize=(20, 15))
        
        # 1. 电极电位分布
        ax1 = plt.subplot(2, 3, 1)
        potentials = results['avg_potentials']
        electrode_pos = results['electrode_positions']
        
        scatter = ax1.scatter(electrode_pos[:, 0]*1000, electrode_pos[:, 1]*1000, 
                             c=potentials, cmap='RdBu_r', s=100)
        ax1.set_xlabel('X (mm)')
        ax1.set_ylabel('Y (mm)')
        ax1.set_title('电极电位分布 (俯视图)')
        plt.colorbar(scatter, ax=ax1, label='电位 (标准化)')
        
        # 添加电极标签
        for i, ch in enumerate(self.eeg_channels):
            ax1.annotate(ch, (electrode_pos[i, 0]*1000, electrode_pos[i, 1]*1000), 
                        xytext=(5, 5), textcoords='offset points', fontsize=8)
        
        # 2. 3D电极位置和偶极子
        ax2 = fig.add_subplot(2, 3, 2, projection='3d')
        
        # 绘制电极
        ax2.scatter(electrode_pos[:, 0]*1000, electrode_pos[:, 1]*1000, electrode_pos[:, 2]*1000, 
                   c=potentials, cmap='RdBu_r', s=100)
        
        # 绘制偶极子（如果拟合成功）
        if results['dipole_result'] is not None:
            dipole_pos = results['dipole_result']['position']
            dipole_moment = results['dipole_result']['moment']
            
            ax2.scatter(dipole_pos[0]*1000, dipole_pos[1]*1000, dipole_pos[2]*1000, 
                       c='red', s=200, marker='*', label='偶极子')
            
            # 绘制偶极子方向
            scale = 20
            ax2.quiver(dipole_pos[0]*1000, dipole_pos[1]*1000, dipole_pos[2]*1000,
                      dipole_moment[0]*scale, dipole_moment[1]*scale, dipole_moment[2]*scale,
                      color='red', arrow_length_ratio=0.1)
        
        ax2.set_xlabel('X (mm)')
        ax2.set_ylabel('Y (mm)')
        ax2.set_zlabel('Z (mm)')
        ax2.set_title('3D电极位置和偶极子')
        ax2.legend()
        
        # 3. 源活动分布 (XY平面)
        ax3 = plt.subplot(2, 3, 3)
        source_grid = results['source_grid']
        source_activity = results['source_activity']
        
        # 选择Z=0平面附近的源点
        z_slice = np.abs(source_grid[:, 2]) < 0.01
        if np.sum(z_slice) > 0:
            scatter3 = ax3.scatter(source_grid[z_slice, 0]*1000, source_grid[z_slice, 1]*1000,
                                  c=source_activity[z_slice], cmap='hot', s=20)
            plt.colorbar(scatter3, ax=ax3, label='源活动强度')
        
        ax3.set_xlabel('X (mm)')
        ax3.set_ylabel('Y (mm)')
        ax3.set_title('源活动分布 (Z≈0平面)')
        
        # 4. 电位时间序列（如果有时间数据）
        ax4 = plt.subplot(2, 3, 4)
        ax4.plot(potentials, 'o-')
        ax4.set_xlabel('电极索引')
        ax4.set_ylabel('电位 (标准化)')
        ax4.set_title('各电极平均电位')
        ax4.grid(True, alpha=0.3)
        
        # 5. 源活动强度分布
        ax5 = plt.subplot(2, 3, 5)
        ax5.hist(source_activity, bins=50, alpha=0.7, color='orange')
        ax5.set_xlabel('源活动强度')
        ax5.set_ylabel('频次')
        ax5.set_title('源活动强度分布')
        ax5.grid(True, alpha=0.3)
        
        # 6. 拟合质量评估
        ax6 = plt.subplot(2, 3, 6)
        if results['dipole_result'] is not None:
            dipole_result = results['dipole_result']
            predicted_potentials = self.dipole_forward_model(
                dipole_result['position'], 
                dipole_result['moment'], 
                electrode_pos
            )
            
            ax6.scatter(potentials, predicted_potentials, alpha=0.7)
            ax6.plot([np.min(potentials), np.max(potentials)], 
                    [np.min(potentials), np.max(potentials)], 'r--', label='完美拟合')
            ax6.set_xlabel('测量电位')
            ax6.set_ylabel('预测电位')
            ax6.set_title(f'偶极子拟合质量 (R={dipole_result["goodness_of_fit"]:.3f})')
            ax6.legend()
            ax6.grid(True, alpha=0.3)
        else:
            ax6.text(0.5, 0.5, '偶极子拟合失败', ha='center', va='center', transform=ax6.transAxes)
            ax6.set_title('拟合结果')
        
        plt.tight_layout()
        plt.savefig('simplified_eeg_source_localization.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"✅ 可视化完成")
        print(f"   图像保存: simplified_eeg_source_localization.png")

def main():
    """主函数"""
    print("🚀 简化EEG源定位分析")
    print("="*70)
    
    # 初始化分析器
    localizer = SimplifiedEEGSourceLocalizer()
    
    # 选择癫痫患者样本
    data_dir = Path("1252141/EEGs_Guinea-Bissau")
    metadata = pd.read_csv("1252141/metadata_guineabissau.csv")
    epilepsy_patients = metadata[metadata['Group'] == 'Epilepsy']
    
    if len(epilepsy_patients) == 0:
        print("❌ 未找到癫痫患者数据")
        return
    
    # 选择第一个癫痫患者
    patient = epilepsy_patients.iloc[0]
    subject_id = patient['subject.id']
    eeg_file = data_dir / f"signal-{subject_id}.csv.gz"
    
    if not eeg_file.exists():
        print(f"❌ EEG文件不存在: {eeg_file}")
        return
    
    print(f"📋 选择患者: {subject_id}")
    
    try:
        # 1. 加载EEG数据
        eeg_data, available_channels = localizer.load_eeg_data(str(eeg_file))
        
        # 2. 源活动分析
        results = localizer.analyze_source_activity(eeg_data)
        
        # 3. 可视化结果
        localizer.visualize_results(results)
        
        # 4. 输出结果摘要
        print("\n🎉 简化EEG源定位分析完成！")
        print("="*70)
        print("📊 主要结果:")
        print(f"   患者ID: {subject_id}")
        
        if results['dipole_result'] is not None:
            dipole = results['dipole_result']
            print(f"   偶极子位置: ({dipole['position'][0]*1000:.1f}, {dipole['position'][1]*1000:.1f}, {dipole['position'][2]*1000:.1f}) mm")
            print(f"   拟合优度: {dipole['goodness_of_fit']:.3f}")
            print(f"   残差方差: {dipole['residual_variance']:.3f}")
        else:
            print("   偶极子拟合: 失败")
        
        max_activity_idx = np.argmax(results['source_activity'])
        max_source_pos = results['source_grid'][max_activity_idx]
        print(f"   最强源位置: ({max_source_pos[0]*1000:.1f}, {max_source_pos[1]*1000:.1f}, {max_source_pos[2]*1000:.1f}) mm")
        print(f"   最大源活动: {np.max(results['source_activity']):.2e}")
        
        print("\n📁 输出文件:")
        print("   - simplified_eeg_source_localization.png: 源定位可视化结果")
        
    except Exception as e:
        print(f"❌ 源定位分析失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
