#!/usr/bin/env python3
"""
Task 1: Topographic Feature Classification Training
Train the topographic CNN branch to classify epilepsy vs non-epilepsy from EEG data
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score, roc_curve
from sklearn.model_selection import train_test_split
import gzip
import os
from pathlib import Path
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

class EEGTopographicDataset(torch.utils.data.Dataset):
    """EEG数据集用于拓扑特征分类"""
    
    def __init__(self, data_dir, metadata_file, split='train', test_size=0.2, random_state=42):
        self.data_dir = Path(data_dir)
        
        # 加载元数据
        self.metadata = pd.read_csv(metadata_file)
        print(f"Loaded metadata: {len(self.metadata)} subjects")
        
        # 创建标签映射
        self.label_map = {'Epilepsy': 1, 'Control': 0}
        
        # 准备数据
        self.prepare_data(split, test_size, random_state)
        
    def prepare_data(self, split, test_size, random_state):
        """准备训练/测试数据"""
        # 获取可用的EEG文件
        available_files = []
        labels = []
        
        for idx, row in self.metadata.iterrows():
            subject_id = row['subject.id']
            group = row['Group']
            
            # 构建文件路径
            eeg_file = self.data_dir / f"signal-{subject_id}.csv.gz"
            
            if eeg_file.exists():
                available_files.append(str(eeg_file))
                labels.append(self.label_map[group])
        
        print(f"Found {len(available_files)} available EEG files")
        print(f"Epilepsy: {sum(labels)}, Control: {len(labels) - sum(labels)}")
        
        # 分割数据
        if split == 'all':
            self.file_paths = available_files
            self.labels = labels
        else:
            train_files, test_files, train_labels, test_labels = train_test_split(
                available_files, labels, test_size=test_size, 
                random_state=random_state, stratify=labels
            )
            
            if split == 'train':
                self.file_paths = train_files
                self.labels = train_labels
            else:  # test
                self.file_paths = test_files
                self.labels = test_labels
        
        print(f"{split.upper()} set: {len(self.file_paths)} samples")
        print(f"  Epilepsy: {sum(self.labels)}, Control: {len(self.labels) - sum(self.labels)}")
    
    def load_eeg_signal(self, file_path):
        """加载EEG信号"""
        try:
            with gzip.open(file_path, 'rt') as f:
                df = pd.read_csv(f)
            
            # 选择14个EEG通道
            eeg_channels = ['AF3', 'AF4', 'F3', 'F4', 'F7', 'F8', 'FC5', 'FC6', 
                           'O1', 'O2', 'P7', 'P8', 'T7', 'T8']
            
            eeg_data = df[eeg_channels].values.T  # [14, time_points]
            
            # 标准化
            eeg_data = (eeg_data - np.mean(eeg_data, axis=1, keepdims=True)) / (np.std(eeg_data, axis=1, keepdims=True) + 1e-8)
            
            # 截取或填充到固定长度
            target_length = 1024
            if eeg_data.shape[1] > target_length:
                eeg_data = eeg_data[:, :target_length]
            elif eeg_data.shape[1] < target_length:
                pad_width = target_length - eeg_data.shape[1]
                eeg_data = np.pad(eeg_data, ((0, 0), (0, pad_width)), mode='constant')
            
            return eeg_data.astype(np.float32)
            
        except Exception as e:
            print(f"Error loading {file_path}: {e}")
            # 返回零数据
            return np.zeros((14, 1024), dtype=np.float32)
    
    def create_topographic_map(self, eeg_data):
        """将14通道EEG数据转换为64x64拓扑图"""
        # 简化的电极位置映射到64x64网格
        electrode_positions = {
            'AF3': (20, 25), 'AF4': (20, 39),
            'F3': (25, 20), 'F4': (25, 44), 'F7': (25, 10), 'F8': (25, 54),
            'FC5': (30, 15), 'FC6': (30, 49),
            'T7': (35, 5), 'T8': (35, 59),
            'P7': (45, 10), 'P8': (45, 54),
            'O1': (55, 25), 'O2': (55, 39)
        }
        
        channels = ['AF3', 'AF4', 'F3', 'F4', 'F7', 'F8', 'FC5', 'FC6', 
                   'O1', 'O2', 'P7', 'P8', 'T7', 'T8']
        
        # 对时间维度求平均，得到每个电极的平均活动
        channel_values = np.mean(eeg_data, axis=1)
        
        # 创建64x64的拓扑图
        topo_map = np.zeros((64, 64))
        
        for i, channel in enumerate(channels):
            if channel in electrode_positions:
                y, x = electrode_positions[channel]
                # 在电极位置周围创建高斯分布
                for dy in range(-3, 4):
                    for dx in range(-3, 4):
                        ny, nx = y + dy, x + dx
                        if 0 <= ny < 64 and 0 <= nx < 64:
                            weight = np.exp(-(dy**2 + dx**2) / 6.0)
                            topo_map[ny, nx] += channel_values[i] * weight
        
        return topo_map
    
    def __len__(self):
        return len(self.file_paths)
    
    def __getitem__(self, idx):
        # 加载EEG数据
        eeg_data = self.load_eeg_signal(self.file_paths[idx])
        
        # 创建拓扑图
        topo_map = self.create_topographic_map(eeg_data)
        
        # 转换为tensor并添加通道维度
        topo_tensor = torch.FloatTensor(topo_map).unsqueeze(0)  # [1, 64, 64]
        
        label = torch.LongTensor([self.labels[idx]])[0]
        
        return {
            'topographic_map': topo_tensor,
            'label': label,
            'file_path': self.file_paths[idx]
        }

class TopographicCNN(nn.Module):
    """基于ResNet18的拓扑CNN分类器"""
    
    def __init__(self, num_classes=2):
        super().__init__()
        
        # 简化的ResNet-like架构
        self.features = nn.Sequential(
            # 第一层
            nn.Conv2d(1, 32, kernel_size=7, stride=2, padding=3),
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=3, stride=2, padding=1),
            
            # 残差块1
            self._make_layer(32, 32, 2),
            
            # 残差块2
            self._make_layer(32, 64, 2, stride=2),
            
            # 残差块3
            self._make_layer(64, 128, 2, stride=2),
            
            # 全局平均池化
            nn.AdaptiveAvgPool2d((1, 1))
        )
        
        # 分类头
        self.classifier = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(128, 64),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(64, num_classes)
        )
        
        # 特征提取器（用于可视化）
        self.feature_extractor = nn.Sequential(
            self.features,
            nn.Flatten(),
            nn.Dropout(0.5),
            nn.Linear(128, 64),
            nn.ReLU(inplace=True)
        )
    
    def _make_layer(self, in_channels, out_channels, blocks, stride=1):
        """创建残差层"""
        layers = []
        
        # 第一个块可能需要下采样
        layers.append(self._make_block(in_channels, out_channels, stride))
        
        # 其余块
        for _ in range(1, blocks):
            layers.append(self._make_block(out_channels, out_channels))
        
        return nn.Sequential(*layers)
    
    def _make_block(self, in_channels, out_channels, stride=1):
        """创建残差块"""
        return nn.Sequential(
            nn.Conv2d(in_channels, out_channels, kernel_size=3, stride=stride, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )
    
    def forward(self, x):
        features = self.features(x)
        features_flat = features.view(features.size(0), -1)
        logits = self.classifier(features_flat)
        return logits
    
    def extract_features(self, x):
        """提取特征用于可视化"""
        return self.feature_extractor(x)

class TopographicTrainer:
    """拓扑特征分类训练器"""
    
    def __init__(self, model, device):
        self.model = model.to(device)
        self.device = device
        
        # 损失函数和优化器
        self.criterion = nn.CrossEntropyLoss()
        self.optimizer = optim.Adam(self.model.parameters(), lr=1e-4, weight_decay=1e-5)
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='max', factor=0.5, patience=5, verbose=True
        )
        
        # 训练历史
        self.history = {
            'train_loss': [], 'val_loss': [],
            'train_acc': [], 'val_acc': []
        }
    
    def train_epoch(self, train_loader):
        """训练一个epoch"""
        self.model.train()
        
        total_loss = 0.0
        correct = 0
        total = 0
        
        for batch in tqdm(train_loader, desc="Training"):
            topo_maps = batch['topographic_map'].to(self.device)
            labels = batch['label'].to(self.device)
            
            self.optimizer.zero_grad()
            
            # 前向传播
            logits = self.model(topo_maps)
            loss = self.criterion(logits, labels)
            
            # 反向传播
            loss.backward()
            self.optimizer.step()
            
            # 统计
            total_loss += loss.item()
            _, predicted = torch.max(logits.data, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
        
        avg_loss = total_loss / len(train_loader)
        accuracy = 100.0 * correct / total
        
        return avg_loss, accuracy
    
    def validate_epoch(self, val_loader):
        """验证一个epoch"""
        self.model.eval()
        
        total_loss = 0.0
        correct = 0
        total = 0
        all_predictions = []
        all_labels = []
        all_probabilities = []
        
        with torch.no_grad():
            for batch in val_loader:
                topo_maps = batch['topographic_map'].to(self.device)
                labels = batch['label'].to(self.device)
                
                # 前向传播
                logits = self.model(topo_maps)
                loss = self.criterion(logits, labels)
                
                # 统计
                total_loss += loss.item()
                probabilities = torch.softmax(logits, dim=1)
                _, predicted = torch.max(logits.data, 1)
                
                total += labels.size(0)
                correct += (predicted == labels).sum().item()
                
                # 收集预测结果
                all_predictions.extend(predicted.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
                all_probabilities.extend(probabilities[:, 1].cpu().numpy())  # 癫痫类概率
        
        avg_loss = total_loss / len(val_loader)
        accuracy = 100.0 * correct / total
        
        return avg_loss, accuracy, all_predictions, all_labels, all_probabilities
    
    def train(self, train_loader, val_loader, num_epochs=30):
        """完整训练循环"""
        print(f"开始拓扑特征分类训练，共 {num_epochs} 个epoch...")
        
        best_val_acc = 0.0
        
        for epoch in range(num_epochs):
            print(f"\nEpoch {epoch+1}/{num_epochs}")
            
            # 训练
            train_loss, train_acc = self.train_epoch(train_loader)
            
            # 验证
            val_loss, val_acc, val_preds, val_labels, val_probs = self.validate_epoch(val_loader)
            
            # 更新学习率
            self.scheduler.step(val_acc)
            
            # 记录历史
            self.history['train_loss'].append(train_loss)
            self.history['val_loss'].append(val_loss)
            self.history['train_acc'].append(train_acc)
            self.history['val_acc'].append(val_acc)
            
            # 打印结果
            print(f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%")
            print(f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%")
            
            # 保存最佳模型
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                torch.save({
                    'model_state_dict': self.model.state_dict(),
                    'val_acc': best_val_acc,
                    'history': self.history
                }, 'topographic_cnn_best_model.pth')
                print(f"✓ 保存最佳模型 (验证准确率: {best_val_acc:.2f}%)")
        
        print(f"\n训练完成！最佳验证准确率: {best_val_acc:.2f}%")
        return self.history, val_preds, val_labels, val_probs

def create_performance_report(y_true, y_pred, y_prob, class_names=['Control', 'Epilepsy']):
    """创建性能报告"""
    
    # 分类报告
    print("\n" + "="*60)
    print("CLASSIFICATION PERFORMANCE REPORT")
    print("="*60)
    
    print("\nClassification Report:")
    print(classification_report(y_true, y_pred, target_names=class_names))
    
    # 混淆矩阵
    cm = confusion_matrix(y_true, y_pred)
    print(f"\nConfusion Matrix:")
    print(cm)
    
    # ROC AUC
    auc_score = roc_auc_score(y_true, y_prob)
    print(f"\nROC AUC Score: {auc_score:.4f}")
    
    return cm, auc_score

def visualize_results(history, cm, y_true, y_prob, class_names=['Control', 'Epilepsy']):
    """可视化训练结果"""
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 训练曲线
    epochs = range(1, len(history['train_loss']) + 1)
    
    axes[0, 0].plot(epochs, history['train_loss'], 'b-', label='Training Loss')
    axes[0, 0].plot(epochs, history['val_loss'], 'r-', label='Validation Loss')
    axes[0, 0].set_title('Training and Validation Loss')
    axes[0, 0].set_xlabel('Epoch')
    axes[0, 0].set_ylabel('Loss')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    axes[0, 1].plot(epochs, history['train_acc'], 'b-', label='Training Accuracy')
    axes[0, 1].plot(epochs, history['val_acc'], 'r-', label='Validation Accuracy')
    axes[0, 1].set_title('Training and Validation Accuracy')
    axes[0, 1].set_xlabel('Epoch')
    axes[0, 1].set_ylabel('Accuracy (%)')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 混淆矩阵
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                xticklabels=class_names, yticklabels=class_names, ax=axes[1, 0])
    axes[1, 0].set_title('Confusion Matrix')
    axes[1, 0].set_xlabel('Predicted')
    axes[1, 0].set_ylabel('Actual')
    
    # ROC曲线
    fpr, tpr, _ = roc_curve(y_true, y_prob)
    auc_score = roc_auc_score(y_true, y_prob)
    
    axes[1, 1].plot(fpr, tpr, 'b-', label=f'ROC Curve (AUC = {auc_score:.3f})')
    axes[1, 1].plot([0, 1], [0, 1], 'r--', label='Random Classifier')
    axes[1, 1].set_title('ROC Curve')
    axes[1, 1].set_xlabel('False Positive Rate')
    axes[1, 1].set_ylabel('True Positive Rate')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('task1_topographic_classification_results.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """主函数"""
    print("🧠 Task 1: Topographic Feature Classification Training")
    print("="*70)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # 创建数据集
    data_dir = "1252141/EEGs_Guinea-Bissau"
    metadata_file = "1252141/metadata_guineabissau.csv"
    
    try:
        train_dataset = EEGTopographicDataset(data_dir, metadata_file, split='train')
        test_dataset = EEGTopographicDataset(data_dir, metadata_file, split='test')
        
        # 创建数据加载器
        train_loader = torch.utils.data.DataLoader(
            train_dataset, batch_size=8, shuffle=True, num_workers=0
        )
        test_loader = torch.utils.data.DataLoader(
            test_dataset, batch_size=8, shuffle=False, num_workers=0
        )
        
        print("✅ 数据集创建成功")
        
    except Exception as e:
        print(f"❌ 数据集创建失败: {e}")
        return
    
    # 创建模型
    model = TopographicCNN(num_classes=2)
    param_count = sum(p.numel() for p in model.parameters())
    print(f"拓扑CNN模型创建成功，参数数量: {param_count:,}")
    
    # 创建训练器
    trainer = TopographicTrainer(model, device)
    
    # 开始训练
    history, val_preds, val_labels, val_probs = trainer.train(
        train_loader, test_loader, num_epochs=25
    )
    
    # 创建性能报告
    cm, auc_score = create_performance_report(val_labels, val_preds, val_probs)
    
    # 可视化结果
    visualize_results(history, cm, val_labels, val_probs)
    
    print("\n✅ Task 1 完成！")
    print("📊 结果文件:")
    print("  - topographic_cnn_best_model.pth: 训练好的拓扑CNN模型")
    print("  - task1_topographic_classification_results.png: 性能分析图表")
    
    # 保存特征提取器权重
    torch.save(model.feature_extractor.state_dict(), 'topographic_feature_extractor.pth')
    print("  - topographic_feature_extractor.pth: 拓扑特征提取器权重")

if __name__ == "__main__":
    main()
