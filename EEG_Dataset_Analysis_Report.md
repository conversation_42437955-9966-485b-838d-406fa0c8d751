# EEG Dataset Analysis Report

## Dataset Overview

This report presents a comprehensive analysis of the EEG dataset located in the `1252141` folder, which contains EEG recordings from two different locations: Guinea-Bissau and Nigeria. The analysis was performed using MNE-Python for proper EEG data handling and visualization.

## Dataset Structure

### 1. **Data Organization**
- **Guinea-Bissau Dataset**: `EEGs_Guinea-Bissau/` folder
- **Nigeria Dataset**: `EEGs_Nigeria/` folder
- **Metadata Files**: 
  - `metadata_guineabissau.csv`
  - `metadata_nigeria.csv`

### 2. **File Format**
- **Signal Files**: Compressed CSV files (`.csv.gz`)
- **Naming Convention**: 
  - Guinea-Bissau: `signal-{subject_id}.csv.gz`
  - Nigeria: `signal-{subject_id}-{session}.csv.gz`

## Key Dataset Properties

### 1. **Sample Sizes**
- **Guinea-Bissau**: 97 subjects
- **Nigeria**: 225 subjects
- **Total**: 322 subjects

### 2. **Subject Groups**
#### Guinea-Bissau:
- **Epilepsy patients**: 51 subjects (52.6%)
- **Control subjects**: 46 subjects (47.4%)

#### Nigeria:
- **Epilepsy patients**: 128 subjects (56.9%)
- **Control subjects**: 97 subjects (43.1%)

### 3. **Recording Conditions**
#### Guinea-Bissau:
- **Eyes closed-3min-then-open-2min**: 84 recordings (86.6%)
- **Eyes open-3min-then-closed-2min**: 13 recordings (13.4%)

#### Nigeria:
- **Eyes closed first**: 114 recordings (50.7%)
- **Eyes open first**: 111 recordings (49.3%)

### 4. **Recording Duration**
#### Guinea-Bissau:
- **Mean duration**: 310.1 seconds (~5.2 minutes)
- **Range**: 286-478 seconds
- **Standard deviation**: 27.8 seconds

#### Nigeria:
- **Mean duration**: 257.7 seconds (~4.3 minutes)
- **Range**: 1-327 seconds
- **Standard deviation**: 40.9 seconds

## EEG Channel Configuration

### 1. **Number of Channels**
- **14 EEG channels** (standard across both datasets)

### 2. **Channel Names and Locations**
Based on the international 10-20 electrode placement system:

| Channel | Location | Brain Region |
|---------|----------|--------------|
| AF3/AF4 | Anterior Frontal (left/right) | Prefrontal cortex |
| F3/F4 | Frontal (left/right) | Frontal cortex |
| F7/F8 | Frontal Temporal (left/right) | Anterior temporal |
| FC5/FC6 | Frontal Central (left/right) | Motor/premotor cortex |
| O1/O2 | Occipital (left/right) | Visual cortex |
| P7/P8 | Parietal Temporal (left/right) | Posterior temporal |
| T7/T8 | Temporal (left/right) | Temporal cortex |

### 3. **Additional Data Columns**
- **COUNTER**: Sample counter
- **INTERPOLATED**: Interpolation flags
- **GYROX/GYROY**: Gyroscope data (head movement)
- **RAW_CQ**: Raw contact quality
- **CQ_[channel]**: Contact quality for each channel
- **CQ_CMS/CQ_DRL**: Reference electrode quality

## Data Format and Sampling

### 1. **Sampling Rate**
- **Estimated**: 128 Hz (typical for EEG systems)
- **Data points per subject**: ~38,529 samples (for 5-minute recordings)

### 2. **Data Units**
- **Amplitude values**: Likely in microvolts (µV)
- **Typical range**: 3,700-4,300 µV (based on channel statistics)

### 3. **Data Quality Indicators**
- Contact quality metrics for each electrode
- Interpolation flags for artifact correction
- Gyroscope data for motion artifact detection

## Channel Statistics Analysis

### 1. **Amplitude Distribution** (Guinea-Bissau, n=10 subjects)

| Channel | Mean (µV) | Std Dev (µV) | Min (µV) | Max (µV) |
|---------|-----------|--------------|----------|----------|
| AF3 | 4,138.96 | 57.35 | 4,056.65 | 4,258.44 |
| AF4 | 3,879.91 | 83.64 | 3,778.77 | 4,087.99 |
| F3 | 4,297.95 | 65.89 | 4,186.15 | 4,408.21 |
| F4 | 3,847.69 | 59.36 | 3,735.80 | 3,944.49 |
| O1 | 4,049.74 | 93.45 | 3,848.81 | 4,198.34 |
| O2 | 4,297.95 | 65.89 | 4,186.15 | 4,408.21 |

### 2. **Key Observations**
- **Frontal channels (F3, AF3)** show higher amplitudes
- **Right hemisphere channels** tend to have slightly lower amplitudes
- **Occipital channels (O1, O2)** show moderate amplitudes
- **Standard deviations** are relatively small, indicating stable recordings

## Visualizations Generated

### 1. **Topographic Maps**
- **File**: `topographic_maps_guinea_bissau_subjects_1_2_3.png`
- **Content**: 2D spatial distribution of brain activity
- **Subjects analyzed**: 3 subjects (1 epilepsy, 1 control, 1 epilepsy)
- **Time window**: 60-120 seconds
- **Maps generated**:
  - Mean activity topoplots
  - Power/variance topoplots

### 2. **Channel Statistics**
- **File**: `channel_statistics_guinea_bissau.png`
- **Content**: Box plots showing amplitude distribution across all 14 channels
- **Sample size**: 10 subjects from Guinea-Bissau dataset

## Technical Implementation

### 1. **Analysis Tools Used**
- **MNE-Python**: Professional EEG analysis library
- **Standard 10-20 montage**: Proper electrode positioning
- **Pandas/NumPy**: Data manipulation and statistics
- **Matplotlib**: Visualization

### 2. **Data Processing Steps**
1. Load compressed CSV files
2. Extract EEG channels (14 channels)
3. Create MNE Raw objects with proper electrode positions
4. Calculate mean amplitudes and power/variance
5. Generate topographic maps using spherical spline interpolation

### 3. **Code Structure**
- **Main script**: `eeg_analysis.py`
- **Class-based design**: `EEGDatasetAnalyzer`
- **Modular functions**: Data loading, analysis, visualization

## Research Applications

### 1. **Clinical Studies**
- **Epilepsy vs. Control comparison**: Large sample sizes for statistical power
- **Cross-cultural validation**: Two different populations (Guinea-Bissau, Nigeria)
- **Resting-state analysis**: Eyes open/closed conditions

### 2. **Signal Processing Research**
- **Artifact detection**: Motion and contact quality data available
- **Preprocessing validation**: Interpolation flags for quality assessment
- **Spatial analysis**: 14-channel coverage of major brain regions

### 3. **Machine Learning Applications**
- **Classification tasks**: Epilepsy detection
- **Feature extraction**: Spatial and temporal patterns
- **Cross-dataset validation**: Training on one dataset, testing on another

## Recommendations for Further Analysis

### 1. **Frequency Domain Analysis**
- Compute power spectral density for different frequency bands
- Analyze alpha (8-12 Hz), beta (13-30 Hz), theta (4-8 Hz) rhythms
- Compare frequency patterns between epilepsy and control groups

### 2. **Connectivity Analysis**
- Calculate coherence between electrode pairs
- Analyze functional connectivity networks
- Compare network properties between groups

### 3. **Time-Frequency Analysis**
- Wavelet analysis for time-varying spectral content
- Event-related spectral perturbations
- Analysis of transient epileptiform activity

### 4. **Cross-Dataset Validation**
- Train classifiers on Guinea-Bissau data, test on Nigeria data
- Analyze cultural/demographic differences in EEG patterns
- Develop robust, generalizable epilepsy detection algorithms

## Conclusion

This EEG dataset represents a valuable resource for epilepsy research with:
- **Large sample size**: 322 subjects total
- **Balanced groups**: Roughly equal epilepsy/control subjects
- **Multi-site validation**: Two different populations
- **Standard format**: 14-channel 10-20 system
- **Quality metrics**: Contact quality and artifact detection data

The successful analysis demonstrates that the data is well-structured and suitable for advanced EEG research applications, including machine learning, connectivity analysis, and clinical studies.
