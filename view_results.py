#!/usr/bin/env python3
"""
View the generated EEG topographic maps
"""

import matplotlib.pyplot as plt
import matplotlib.image as mpimg
import os

def display_image(image_path, title):
    """Display a single image"""
    if os.path.exists(image_path):
        img = mpimg.imread(image_path)
        plt.figure(figsize=(12, 8))
        plt.imshow(img)
        plt.axis('off')
        plt.title(title, fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.show()
        print(f"Displayed: {title}")
    else:
        print(f"File not found: {image_path}")

def main():
    """
    Display all generated topographic maps
    """
    print("=== EEG Topographic Maps Generated ===")
    
    # Display main summary
    print("\n1. Comprehensive Summary:")
    display_image('eeg_summary_analysis.png', 'EEG Frequency Band Analysis Summary')
    
    # Display basic RMS map
    print("\n2. Basic RMS Activity Map:")
    display_image('basic_rms_topomap.png', 'EEG RMS Activity (Broadband)')
    
    # Display frequency band maps
    print("\n3. Frequency Band Maps:")
    freq_bands = ['delta', 'theta', 'alpha', 'beta', 'gamma']
    for band in freq_bands:
        image_path = f'frequency_maps/{band}_topomap.png'
        display_image(image_path, f'{band.capitalize()} Band Topographic Map')
    
    # Display time evolution maps
    print("\n4. Time Evolution Maps:")
    time_files = [f for f in os.listdir('time_maps') if f.endswith('.png')]
    time_files.sort()
    for time_file in time_files:
        time_point = time_file.replace('time_', '').replace('s_topomap.png', '')
        image_path = f'time_maps/{time_file}'
        display_image(image_path, f'EEG Activity at {time_point}s')
    
    print("\n=== All Maps Displayed ===")
    print("\nGenerated Files Summary:")
    print("- eeg_summary_analysis.png: Complete frequency analysis")
    print("- basic_rms_topomap.png: Overall brain activity")
    print("- frequency_maps/: Individual frequency band maps")
    print("- time_maps/: Brain activity at different time points")

if __name__ == "__main__":
    main()
