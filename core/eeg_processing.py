"""
EEG源定位系统 - 多通道EEG数据处理引擎

该模块实现完整的EEG数据预处理、滤波、伪迹去除和通道质量评估系统。
支持多种EEG数据格式，确保数据质量满足源定位分析要求。

主要功能：
1. 多格式EEG数据加载和预处理
2. 高质量数字滤波器设计和应用
3. 智能伪迹检测和去除
4. 通道质量评估和坏通道检测
5. 参考电极处理和重参考
6. 时频分析和特征提取
"""

import numpy as np
import mne
from scipy import signal, stats
from sklearn.decomposition import FastICA
from typing import Dict, List, Optional, Tuple, Union
import logging
from pathlib import Path
import pandas as pd
import yaml

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class EEGProcessor:
    """EEG数据处理引擎主类"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化EEG处理引擎
        
        参数:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.eeg_config = self.config['eeg_processing']
        
        # 初始化子模块
        self.preprocessor = Preprocessor(self.config)
        self.filter_bank = FilterBank(self.config)
        self.artifact_remover = ArtifactRemover(self.config)
        self.channel_quality_assessor = ChannelQualityAssessor(self.config)
        
        logger.info("EEG数据处理引擎初始化完成")
        
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            raise
            
    def process_eeg_data(self, raw_data: mne.io.Raw, subject_id: str) -> Dict:
        """
        完整的EEG数据处理流程
        
        参数:
            raw_data: 原始EEG数据
            subject_id: 被试ID
            
        返回:
            Dict: 处理后的EEG数据和质量报告
        """
        try:
            logger.info(f"开始处理被试 {subject_id} 的EEG数据")
            
            # 步骤1: 数据预处理
            preprocessed_data = self.preprocessor.preprocess(raw_data)
            
            # 步骤2: 通道质量评估
            channel_quality = self.channel_quality_assessor.assess_channels(preprocessed_data)
            
            # 步骤3: 坏通道处理
            cleaned_data = self._handle_bad_channels(preprocessed_data, channel_quality)
            
            # 步骤4: 数字滤波
            filtered_data = self.filter_bank.apply_filters(cleaned_data)
            
            # 步骤5: 伪迹去除
            artifact_free_data = self.artifact_remover.remove_artifacts(filtered_data)
            
            # 步骤6: 最终质量检查
            final_quality = self._final_quality_check(artifact_free_data)
            
            # 步骤7: 生成处理报告
            processing_report = self._generate_processing_report(
                subject_id, channel_quality, final_quality
            )
            
            result = {
                'processed_data': artifact_free_data,
                'channel_quality': channel_quality,
                'final_quality': final_quality,
                'processing_report': processing_report,
                'subject_id': subject_id
            }
            
            logger.info(f"EEG数据处理完成，最终质量评分: {final_quality['overall_score']:.3f}")
            return result
            
        except Exception as e:
            logger.error(f"EEG数据处理失败: {e}")
            raise
            
    def _handle_bad_channels(self, raw_data: mne.io.Raw, 
                           channel_quality: Dict) -> mne.io.Raw:
        """处理坏通道"""
        try:
            bad_channels = channel_quality.get('bad_channels', [])
            
            if bad_channels:
                logger.info(f"检测到坏通道: {bad_channels}")
                
                # 标记坏通道
                raw_data.info['bads'] = bad_channels
                
                # 插值坏通道
                raw_interpolated = raw_data.copy()
                raw_interpolated.interpolate_bads(reset_bads=True)
                
                logger.info(f"已插值 {len(bad_channels)} 个坏通道")
                return raw_interpolated
            else:
                logger.info("未检测到坏通道")
                return raw_data
                
        except Exception as e:
            logger.error(f"坏通道处理失败: {e}")
            return raw_data
            
    def _final_quality_check(self, processed_data: mne.io.Raw) -> Dict:
        """最终质量检查"""
        try:
            quality_metrics = {}
            
            # 获取数据
            data = processed_data.get_data()
            
            # 信噪比评估
            snr = self._calculate_snr(data)
            quality_metrics['snr'] = snr
            
            # 数据完整性检查
            completeness = self._check_data_completeness(data)
            quality_metrics['completeness'] = completeness
            
            # 频谱质量评估
            spectral_quality = self._assess_spectral_quality(data, processed_data.info['sfreq'])
            quality_metrics['spectral_quality'] = spectral_quality
            
            # 计算总体质量评分
            overall_score = np.mean([snr, completeness, spectral_quality])
            quality_metrics['overall_score'] = overall_score
            
            return quality_metrics
            
        except Exception as e:
            logger.error(f"最终质量检查失败: {e}")
            return {'overall_score': 0.0}
            
    def _calculate_snr(self, data: np.ndarray) -> float:
        """计算信噪比"""
        try:
            # 简化的SNR计算：信号功率与噪声功率的比值
            signal_power = np.mean(np.var(data, axis=1))
            
            # 估计噪声（使用高频成分）
            noise_estimate = np.mean(np.abs(np.diff(data, axis=1)))
            noise_power = noise_estimate ** 2
            
            if noise_power > 0:
                snr = 10 * np.log10(signal_power / noise_power)
                # 归一化到[0,1]范围
                snr_normalized = np.clip((snr + 20) / 40, 0, 1)  # 假设SNR范围[-20, 20]dB
                return snr_normalized
            else:
                return 1.0
                
        except Exception as e:
            logger.warning(f"SNR计算失败: {e}")
            return 0.5
            
    def _check_data_completeness(self, data: np.ndarray) -> float:
        """检查数据完整性"""
        try:
            # 检查NaN和无穷大值
            nan_ratio = np.sum(np.isnan(data)) / data.size
            inf_ratio = np.sum(np.isinf(data)) / data.size
            
            # 检查零值比例
            zero_ratio = np.sum(data == 0) / data.size
            
            # 完整性评分
            completeness = 1.0 - (nan_ratio + inf_ratio + min(zero_ratio, 0.1))
            return max(completeness, 0.0)
            
        except Exception as e:
            logger.warning(f"数据完整性检查失败: {e}")
            return 0.5
            
    def _assess_spectral_quality(self, data: np.ndarray, sfreq: float) -> float:
        """评估频谱质量"""
        try:
            # 计算功率谱密度
            freqs, psd = signal.welch(data, fs=sfreq, nperseg=min(1024, data.shape[1]//4))
            
            # 评估不同频段的质量
            delta_band = (1, 4)
            theta_band = (4, 8)
            alpha_band = (8, 13)
            beta_band = (13, 30)
            
            quality_scores = []
            
            for low, high in [delta_band, theta_band, alpha_band, beta_band]:
                band_mask = (freqs >= low) & (freqs <= high)
                if np.any(band_mask):
                    band_power = np.mean(psd[:, band_mask])
                    # 简化的质量评估：基于功率的合理性
                    if band_power > 0:
                        quality_scores.append(1.0)
                    else:
                        quality_scores.append(0.0)
                        
            return np.mean(quality_scores) if quality_scores else 0.5
            
        except Exception as e:
            logger.warning(f"频谱质量评估失败: {e}")
            return 0.5
            
    def _generate_processing_report(self, subject_id: str, 
                                  channel_quality: Dict, 
                                  final_quality: Dict) -> Dict:
        """生成处理报告"""
        try:
            report = {
                'subject_id': subject_id,
                'processing_steps': [
                    '数据预处理',
                    '通道质量评估',
                    '坏通道处理',
                    '数字滤波',
                    '伪迹去除',
                    '最终质量检查'
                ],
                'channel_statistics': {
                    'total_channels': channel_quality.get('total_channels', 0),
                    'good_channels': channel_quality.get('good_channels', 0),
                    'bad_channels': len(channel_quality.get('bad_channels', [])),
                    'interpolated_channels': len(channel_quality.get('bad_channels', []))
                },
                'quality_metrics': final_quality,
                'recommendations': self._generate_recommendations(channel_quality, final_quality)
            }
            
            return report
            
        except Exception as e:
            logger.error(f"处理报告生成失败: {e}")
            return {'subject_id': subject_id, 'error': str(e)}
            
    def _generate_recommendations(self, channel_quality: Dict, 
                                final_quality: Dict) -> List[str]:
        """生成处理建议"""
        recommendations = []
        
        try:
            # 基于通道质量的建议
            bad_channel_ratio = len(channel_quality.get('bad_channels', [])) / max(channel_quality.get('total_channels', 1), 1)
            if bad_channel_ratio > 0.2:
                recommendations.append("坏通道比例过高，建议检查数据采集质量")
                
            # 基于最终质量的建议
            overall_score = final_quality.get('overall_score', 0)
            if overall_score < 0.5:
                recommendations.append("数据质量较低，建议重新采集或使用更严格的预处理")
            elif overall_score < 0.7:
                recommendations.append("数据质量中等，建议进一步优化预处理参数")
            else:
                recommendations.append("数据质量良好，可以进行源定位分析")
                
            # 基于SNR的建议
            snr = final_quality.get('snr', 0)
            if snr < 0.3:
                recommendations.append("信噪比较低，建议增加滤波或伪迹去除强度")
                
            return recommendations
            
        except Exception as e:
            logger.warning(f"建议生成失败: {e}")
            return ["数据处理完成，请人工检查质量"]


class Preprocessor:
    """数据预处理器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.preproc_config = config['eeg_processing']['preprocessing']
        
    def preprocess(self, raw_data: mne.io.Raw) -> mne.io.Raw:
        """执行数据预处理"""
        try:
            logger.info("开始EEG数据预处理")
            
            # 复制数据避免修改原始数据
            processed_data = raw_data.copy()
            
            # 去除直流分量
            if self.preproc_config.get('remove_dc', True):
                processed_data = self._remove_dc_offset(processed_data)
                
            # 去趋势
            if self.preproc_config.get('detrend', True):
                processed_data = self._detrend_data(processed_data)
                
            # 基线校正
            if self.preproc_config.get('baseline_correction', True):
                processed_data = self._baseline_correction(processed_data)
                
            logger.info("EEG数据预处理完成")
            return processed_data
            
        except Exception as e:
            logger.error(f"EEG数据预处理失败: {e}")
            raise
            
    def _remove_dc_offset(self, raw_data: mne.io.Raw) -> mne.io.Raw:
        """去除直流分量"""
        try:
            data = raw_data.get_data()
            
            # 计算每个通道的均值并减去
            dc_offset = np.mean(data, axis=1, keepdims=True)
            data_corrected = data - dc_offset
            
            # 创建新的Raw对象
            raw_corrected = mne.io.RawArray(data_corrected, raw_data.info)
            
            logger.info("直流分量去除完成")
            return raw_corrected
            
        except Exception as e:
            logger.error(f"直流分量去除失败: {e}")
            return raw_data
            
    def _detrend_data(self, raw_data: mne.io.Raw) -> mne.io.Raw:
        """去趋势处理"""
        try:
            data = raw_data.get_data()
            
            # 对每个通道进行去趋势
            detrended_data = np.zeros_like(data)
            for i in range(data.shape[0]):
                detrended_data[i] = signal.detrend(data[i])
                
            # 创建新的Raw对象
            raw_detrended = mne.io.RawArray(detrended_data, raw_data.info)
            
            logger.info("去趋势处理完成")
            return raw_detrended
            
        except Exception as e:
            logger.error(f"去趋势处理失败: {e}")
            return raw_data
            
    def _baseline_correction(self, raw_data: mne.io.Raw) -> mne.io.Raw:
        """基线校正"""
        try:
            # 使用前100个样本作为基线
            data = raw_data.get_data()
            baseline_samples = min(100, data.shape[1] // 10)
            
            if baseline_samples > 0:
                baseline = np.mean(data[:, :baseline_samples], axis=1, keepdims=True)
                data_corrected = data - baseline
                
                # 创建新的Raw对象
                raw_corrected = mne.io.RawArray(data_corrected, raw_data.info)
                
                logger.info("基线校正完成")
                return raw_corrected
            else:
                logger.warning("数据长度不足，跳过基线校正")
                return raw_data
                
        except Exception as e:
            logger.error(f"基线校正失败: {e}")
            return raw_data


class FilterBank:
    """数字滤波器组"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.filter_config = config['eeg_processing']['filtering']
        
    def apply_filters(self, raw_data: mne.io.Raw) -> mne.io.Raw:
        """应用数字滤波器"""
        try:
            logger.info("开始应用数字滤波器")
            
            filtered_data = raw_data.copy()
            
            # 高通滤波
            highpass = self.filter_config.get('highpass', 0.5)
            if highpass > 0:
                filtered_data.filter(l_freq=highpass, h_freq=None, fir_design='firwin')
                logger.info(f"高通滤波完成: {highpass} Hz")
                
            # 低通滤波
            lowpass = self.filter_config.get('lowpass', 100)
            if lowpass > 0:
                filtered_data.filter(l_freq=None, h_freq=lowpass, fir_design='firwin')
                logger.info(f"低通滤波完成: {lowpass} Hz")
                
            # 工频滤波
            notch_freq = self.filter_config.get('notch', 50)
            if notch_freq > 0:
                filtered_data.notch_filter(freqs=notch_freq, fir_design='firwin')
                logger.info(f"工频滤波完成: {notch_freq} Hz")
                
            logger.info("数字滤波器应用完成")
            return filtered_data
            
        except Exception as e:
            logger.error(f"数字滤波器应用失败: {e}")
            raise


class ArtifactRemover:
    """伪迹去除器"""

    def __init__(self, config: Dict):
        self.config = config
        self.artifact_config = config['eeg_processing']['artifact_removal']

    def remove_artifacts(self, raw_data: mne.io.Raw) -> mne.io.Raw:
        """去除各种伪迹"""
        try:
            logger.info("开始伪迹去除")

            cleaned_data = raw_data.copy()

            # 眼电伪迹去除
            if self.artifact_config.get('enable_ica', True):
                cleaned_data = self._remove_ocular_artifacts_ica(cleaned_data)

            # 肌电伪迹去除
            cleaned_data = self._remove_muscle_artifacts(cleaned_data)

            # 心电伪迹去除
            cleaned_data = self._remove_cardiac_artifacts(cleaned_data)

            # 运动伪迹去除
            cleaned_data = self._remove_movement_artifacts(cleaned_data)

            logger.info("伪迹去除完成")
            return cleaned_data

        except Exception as e:
            logger.error(f"伪迹去除失败: {e}")
            raise

    def _remove_ocular_artifacts_ica(self, raw_data: mne.io.Raw) -> mne.io.Raw:
        """使用ICA去除眼电伪迹"""
        try:
            logger.info("开始ICA眼电伪迹去除")

            # 设置ICA参数
            n_components = self.artifact_config.get('ica_components', 20)
            n_components = min(n_components, raw_data.info['nchan'])

            # 创建ICA对象
            ica = mne.preprocessing.ICA(
                n_components=n_components,
                random_state=42,
                method='fastica'
            )

            # 拟合ICA
            ica.fit(raw_data)

            # 自动检测眼电成分
            eog_indices, eog_scores = ica.find_bads_eog(raw_data, threshold=3.0)

            if eog_indices:
                logger.info(f"检测到眼电成分: {eog_indices}")
                ica.exclude = eog_indices

                # 应用ICA去除眼电伪迹
                cleaned_data = ica.apply(raw_data.copy())
                logger.info("ICA眼电伪迹去除完成")
                return cleaned_data
            else:
                logger.info("未检测到明显的眼电伪迹")
                return raw_data

        except Exception as e:
            logger.error(f"ICA眼电伪迹去除失败: {e}")
            return raw_data

    def _remove_muscle_artifacts(self, raw_data: mne.io.Raw) -> mne.io.Raw:
        """去除肌电伪迹"""
        try:
            logger.info("开始肌电伪迹去除")

            # 检测肌电伪迹（基于高频功率）
            data = raw_data.get_data()
            sfreq = raw_data.info['sfreq']

            # 计算高频功率（30-100 Hz）
            high_freq_power = self._calculate_band_power(data, sfreq, 30, 100)

            # 检测异常高的肌电活动
            threshold = np.percentile(high_freq_power, 95)  # 95百分位数作为阈值
            artifact_mask = high_freq_power > threshold

            if np.any(artifact_mask):
                # 对检测到的肌电伪迹进行处理
                cleaned_data = self._suppress_muscle_artifacts(data, artifact_mask)

                # 创建新的Raw对象
                raw_cleaned = mne.io.RawArray(cleaned_data, raw_data.info)

                logger.info(f"肌电伪迹去除完成，处理了 {np.sum(artifact_mask)} 个时间点")
                return raw_cleaned
            else:
                logger.info("未检测到明显的肌电伪迹")
                return raw_data

        except Exception as e:
            logger.error(f"肌电伪迹去除失败: {e}")
            return raw_data

    def _calculate_band_power(self, data: np.ndarray, sfreq: float,
                            low_freq: float, high_freq: float) -> np.ndarray:
        """计算特定频段的功率"""
        try:
            # 设计带通滤波器
            nyquist = sfreq / 2
            low = low_freq / nyquist
            high = high_freq / nyquist

            if high >= 1.0:
                high = 0.99

            b, a = signal.butter(4, [low, high], btype='band')

            # 应用滤波器并计算功率
            band_powers = []
            for ch_data in data:
                filtered = signal.filtfilt(b, a, ch_data)
                power = np.mean(filtered ** 2, axis=0)
                band_powers.append(power)

            return np.array(band_powers)

        except Exception as e:
            logger.warning(f"频段功率计算失败: {e}")
            return np.zeros(data.shape[1])

    def _suppress_muscle_artifacts(self, data: np.ndarray,
                                 artifact_mask: np.ndarray) -> np.ndarray:
        """抑制肌电伪迹"""
        try:
            cleaned_data = data.copy()

            # 对检测到的伪迹时间点进行插值
            for ch_idx in range(data.shape[0]):
                if np.any(artifact_mask):
                    # 使用线性插值替换伪迹
                    artifact_indices = np.where(artifact_mask)[0]
                    good_indices = np.where(~artifact_mask)[0]

                    if len(good_indices) > 1:
                        # 线性插值
                        cleaned_data[ch_idx, artifact_indices] = np.interp(
                            artifact_indices, good_indices, data[ch_idx, good_indices]
                        )

            return cleaned_data

        except Exception as e:
            logger.warning(f"肌电伪迹抑制失败: {e}")
            return data

    def _remove_cardiac_artifacts(self, raw_data: mne.io.Raw) -> mne.io.Raw:
        """去除心电伪迹"""
        try:
            logger.info("开始心电伪迹去除")

            # 检测心电伪迹（基于周期性模式）
            data = raw_data.get_data()
            sfreq = raw_data.info['sfreq']

            # 寻找心电模式（通常在1-3 Hz范围内有周期性）
            cardiac_components = self._detect_cardiac_components(data, sfreq)

            if cardiac_components is not None:
                # 去除心电成分
                cleaned_data = data - cardiac_components

                # 创建新的Raw对象
                raw_cleaned = mne.io.RawArray(cleaned_data, raw_data.info)

                logger.info("心电伪迹去除完成")
                return raw_cleaned
            else:
                logger.info("未检测到明显的心电伪迹")
                return raw_data

        except Exception as e:
            logger.error(f"心电伪迹去除失败: {e}")
            return raw_data

    def _detect_cardiac_components(self, data: np.ndarray, sfreq: float) -> Optional[np.ndarray]:
        """检测心电成分"""
        try:
            # 计算心率频段的功率（0.8-2.0 Hz）
            cardiac_power = self._calculate_band_power(data, sfreq, 0.8, 2.0)

            # 检测是否有明显的心电模式
            if np.max(cardiac_power) > 2 * np.mean(cardiac_power):
                # 使用简单的模板匹配去除心电伪迹
                # 这里实现一个简化版本
                return self._estimate_cardiac_template(data, sfreq)
            else:
                return None

        except Exception as e:
            logger.warning(f"心电成分检测失败: {e}")
            return None

    def _estimate_cardiac_template(self, data: np.ndarray, sfreq: float) -> np.ndarray:
        """估计心电模板"""
        try:
            # 简化的心电模板估计
            # 在实际应用中，这里应该实现更复杂的心电检测算法

            # 使用低通滤波提取心电成分
            b, a = signal.butter(4, 3.0 / (sfreq / 2), btype='low')
            cardiac_template = np.zeros_like(data)

            for ch_idx in range(data.shape[0]):
                filtered = signal.filtfilt(b, a, data[ch_idx])
                # 只保留明显的心电成分
                if np.std(filtered) > 0.1 * np.std(data[ch_idx]):
                    cardiac_template[ch_idx] = filtered

            return cardiac_template

        except Exception as e:
            logger.warning(f"心电模板估计失败: {e}")
            return np.zeros_like(data)

    def _remove_movement_artifacts(self, raw_data: mne.io.Raw) -> mne.io.Raw:
        """去除运动伪迹"""
        try:
            logger.info("开始运动伪迹去除")

            data = raw_data.get_data()

            # 检测运动伪迹（基于幅度突变）
            movement_mask = self._detect_movement_artifacts(data)

            if np.any(movement_mask):
                # 处理运动伪迹
                cleaned_data = self._handle_movement_artifacts(data, movement_mask)

                # 创建新的Raw对象
                raw_cleaned = mne.io.RawArray(cleaned_data, raw_data.info)

                logger.info(f"运动伪迹去除完成，处理了 {np.sum(movement_mask)} 个时间点")
                return raw_cleaned
            else:
                logger.info("未检测到明显的运动伪迹")
                return raw_data

        except Exception as e:
            logger.error(f"运动伪迹去除失败: {e}")
            return raw_data

    def _detect_movement_artifacts(self, data: np.ndarray) -> np.ndarray:
        """检测运动伪迹"""
        try:
            # 计算信号的一阶差分（检测突变）
            diff_data = np.abs(np.diff(data, axis=1))

            # 计算每个时间点的总变化量
            total_change = np.sum(diff_data, axis=0)

            # 使用阈值检测运动伪迹
            threshold = np.percentile(total_change, 99)  # 99百分位数作为阈值
            movement_mask = np.zeros(data.shape[1], dtype=bool)
            movement_mask[1:] = total_change > threshold

            return movement_mask

        except Exception as e:
            logger.warning(f"运动伪迹检测失败: {e}")
            return np.zeros(data.shape[1], dtype=bool)

    def _handle_movement_artifacts(self, data: np.ndarray,
                                 movement_mask: np.ndarray) -> np.ndarray:
        """处理运动伪迹"""
        try:
            cleaned_data = data.copy()

            # 对运动伪迹进行插值处理
            artifact_indices = np.where(movement_mask)[0]
            good_indices = np.where(~movement_mask)[0]

            if len(good_indices) > 1:
                for ch_idx in range(data.shape[0]):
                    # 使用三次样条插值
                    from scipy.interpolate import interp1d

                    if len(good_indices) >= 4:  # 三次插值至少需要4个点
                        f = interp1d(good_indices, data[ch_idx, good_indices],
                                   kind='cubic', fill_value='extrapolate')
                        cleaned_data[ch_idx, artifact_indices] = f(artifact_indices)
                    else:
                        # 线性插值
                        cleaned_data[ch_idx, artifact_indices] = np.interp(
                            artifact_indices, good_indices, data[ch_idx, good_indices]
                        )

            return cleaned_data

        except Exception as e:
            logger.warning(f"运动伪迹处理失败: {e}")
            return data


class ChannelQualityAssessor:
    """通道质量评估器"""

    def __init__(self, config: Dict):
        self.config = config
        self.quality_config = config['eeg_processing']['channel_quality']

    def assess_channels(self, raw_data: mne.io.Raw) -> Dict:
        """评估通道质量"""
        try:
            logger.info("开始通道质量评估")

            data = raw_data.get_data()
            ch_names = raw_data.ch_names

            # 各种质量指标
            quality_metrics = {}

            # 1. 信号幅度检查
            amplitude_quality = self._assess_amplitude_quality(data)
            quality_metrics['amplitude'] = amplitude_quality

            # 2. 信号方差检查
            variance_quality = self._assess_variance_quality(data)
            quality_metrics['variance'] = variance_quality

            # 3. 通道间相关性检查
            correlation_quality = self._assess_correlation_quality(data)
            quality_metrics['correlation'] = correlation_quality

            # 4. 频谱质量检查
            spectral_quality = self._assess_spectral_quality(data, raw_data.info['sfreq'])
            quality_metrics['spectral'] = spectral_quality

            # 5. 识别坏通道
            bad_channels = self._identify_bad_channels(
                data, ch_names, quality_metrics
            )

            # 生成质量报告
            quality_report = {
                'total_channels': len(ch_names),
                'good_channels': len(ch_names) - len(bad_channels),
                'bad_channels': bad_channels,
                'quality_metrics': quality_metrics,
                'channel_scores': self._calculate_channel_scores(quality_metrics)
            }

            logger.info(f"通道质量评估完成，坏通道数: {len(bad_channels)}")
            return quality_report

        except Exception as e:
            logger.error(f"通道质量评估失败: {e}")
            raise

    def _assess_amplitude_quality(self, data: np.ndarray) -> Dict:
        """评估信号幅度质量"""
        try:
            # 计算每个通道的RMS幅度
            rms_amplitudes = np.sqrt(np.mean(data**2, axis=1))

            # 检测异常幅度
            median_rms = np.median(rms_amplitudes)
            mad_rms = np.median(np.abs(rms_amplitudes - median_rms))

            # 定义正常范围（基于中位数绝对偏差）
            lower_bound = median_rms - 3 * mad_rms
            upper_bound = median_rms + 3 * mad_rms

            # 标记异常通道
            abnormal_amplitude = (rms_amplitudes < lower_bound) | (rms_amplitudes > upper_bound)

            return {
                'rms_amplitudes': rms_amplitudes,
                'median_rms': median_rms,
                'abnormal_channels': np.where(abnormal_amplitude)[0].tolist()
            }

        except Exception as e:
            logger.warning(f"幅度质量评估失败: {e}")
            return {'abnormal_channels': []}

    def _assess_variance_quality(self, data: np.ndarray) -> Dict:
        """评估信号方差质量"""
        try:
            # 计算每个通道的方差
            variances = np.var(data, axis=1)

            # 检测异常方差
            log_variances = np.log(variances + 1e-10)  # 避免log(0)
            median_log_var = np.median(log_variances)
            mad_log_var = np.median(np.abs(log_variances - median_log_var))

            # 定义正常范围
            threshold = self.quality_config.get('bad_channel_threshold', 3.0)
            lower_bound = median_log_var - threshold * mad_log_var
            upper_bound = median_log_var + threshold * mad_log_var

            # 标记异常通道
            abnormal_variance = (log_variances < lower_bound) | (log_variances > upper_bound)

            return {
                'variances': variances,
                'log_variances': log_variances,
                'abnormal_channels': np.where(abnormal_variance)[0].tolist()
            }

        except Exception as e:
            logger.warning(f"方差质量评估失败: {e}")
            return {'abnormal_channels': []}

    def _assess_correlation_quality(self, data: np.ndarray) -> Dict:
        """评估通道间相关性质量"""
        try:
            # 计算通道间相关矩阵
            correlation_matrix = np.corrcoef(data)

            # 计算每个通道与其他通道的平均相关性
            np.fill_diagonal(correlation_matrix, 0)  # 排除自相关
            mean_correlations = np.mean(np.abs(correlation_matrix), axis=1)

            # 检测相关性异常的通道
            correlation_threshold = self.quality_config.get('correlation_threshold', 0.3)
            low_correlation = mean_correlations < correlation_threshold

            return {
                'correlation_matrix': correlation_matrix,
                'mean_correlations': mean_correlations,
                'low_correlation_channels': np.where(low_correlation)[0].tolist()
            }

        except Exception as e:
            logger.warning(f"相关性质量评估失败: {e}")
            return {'low_correlation_channels': []}

    def _assess_spectral_quality(self, data: np.ndarray, sfreq: float) -> Dict:
        """评估频谱质量"""
        try:
            # 计算功率谱密度
            freqs, psd = signal.welch(data, fs=sfreq, nperseg=min(1024, data.shape[1]//4))

            # 检查各频段的功率分布
            freq_bands = {
                'delta': (1, 4),
                'theta': (4, 8),
                'alpha': (8, 13),
                'beta': (13, 30),
                'gamma': (30, 100)
            }

            band_powers = {}
            for band_name, (low, high) in freq_bands.items():
                band_mask = (freqs >= low) & (freqs <= high)
                if np.any(band_mask):
                    band_power = np.mean(psd[:, band_mask], axis=1)
                    band_powers[band_name] = band_power

            # 检测频谱异常
            abnormal_spectral = []
            for ch_idx in range(data.shape[0]):
                # 检查是否有异常的频谱特征
                total_power = np.sum(psd[ch_idx])
                if total_power == 0 or np.isnan(total_power) or np.isinf(total_power):
                    abnormal_spectral.append(ch_idx)

            return {
                'psd': psd,
                'freqs': freqs,
                'band_powers': band_powers,
                'abnormal_channels': abnormal_spectral
            }

        except Exception as e:
            logger.warning(f"频谱质量评估失败: {e}")
            return {'abnormal_channels': []}

    def _identify_bad_channels(self, data: np.ndarray, ch_names: List[str],
                             quality_metrics: Dict) -> List[str]:
        """识别坏通道"""
        try:
            bad_channel_indices = set()

            # 收集所有异常通道
            for metric_name, metric_data in quality_metrics.items():
                if 'abnormal_channels' in metric_data:
                    bad_channel_indices.update(metric_data['abnormal_channels'])
                elif 'low_correlation_channels' in metric_data:
                    bad_channel_indices.update(metric_data['low_correlation_channels'])

            # 转换为通道名称
            bad_channels = [ch_names[idx] for idx in bad_channel_indices if idx < len(ch_names)]

            return bad_channels

        except Exception as e:
            logger.warning(f"坏通道识别失败: {e}")
            return []

    def _calculate_channel_scores(self, quality_metrics: Dict) -> np.ndarray:
        """计算通道质量评分"""
        try:
            # 这里实现一个简化的评分系统
            # 实际应用中可以根据需要设计更复杂的评分算法

            num_channels = 0
            for metric_data in quality_metrics.values():
                if 'rms_amplitudes' in metric_data:
                    num_channels = len(metric_data['rms_amplitudes'])
                    break

            if num_channels == 0:
                return np.array([])

            # 初始化评分（满分1.0）
            scores = np.ones(num_channels)

            # 根据各种质量指标调整评分
            for metric_name, metric_data in quality_metrics.items():
                abnormal_channels = metric_data.get('abnormal_channels', [])
                low_corr_channels = metric_data.get('low_correlation_channels', [])

                # 降低异常通道的评分
                for ch_idx in abnormal_channels:
                    if ch_idx < num_channels:
                        scores[ch_idx] *= 0.5

                for ch_idx in low_corr_channels:
                    if ch_idx < num_channels:
                        scores[ch_idx] *= 0.7

            return scores

        except Exception as e:
            logger.warning(f"通道评分计算失败: {e}")
            return np.array([])
