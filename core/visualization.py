"""
EEG源定位系统 - 全脑电活动强度图像生成模块

该模块负责将所有通道的源分析结果叠加，生成高分辨率的全脑电活动强度图像。
支持3D可视化、时间序列动画、统计分析和交互式探索。

主要功能：
1. 多通道源定位结果叠加和融合
2. 高分辨率3D脑电活动图像生成
3. 时间序列动画和动态可视化
4. 统计显著性分析和阈值处理
5. 交互式3D可视化和数据探索
6. 多种输出格式支持
"""

import numpy as np
import matplotlib.pyplot as plt
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import nibabel as nib
from scipy import ndimage, stats
from sklearn.cluster import DBSCAN
from typing import Dict, List, Optional, Tuple, Union
import logging
from pathlib import Path
import yaml
import json

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BrainActivityVisualizer:
    """全脑电活动可视化器主类"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化全脑电活动可视化器
        
        参数:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.viz_config = self.config['visualization']
        
        # 初始化子模块
        self.brain_3d_renderer = Brain3DRenderer(self.config)
        self.topography_generator = TopographyGenerator(self.config)
        self.time_frequency_visualizer = TimeFrequencyVisualizer(self.config)
        self.statistical_analyzer = StatisticalAnalyzer(self.config)
        
        logger.info("全脑电活动可视化器初始化完成")
        
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            raise
            
    def generate_brain_activity_images(self, source_results: List[Dict], 
                                     head_model: Dict,
                                     output_dir: str = "results") -> Dict:
        """
        生成全脑电活动强度图像
        
        参数:
            source_results: 多通道源定位结果列表
            head_model: 头部模型数据
            output_dir: 输出目录
            
        返回:
            Dict: 可视化结果和文件路径
        """
        try:
            logger.info("开始生成全脑电活动强度图像")
            
            # 步骤1: 叠加多通道源定位结果
            combined_activity = self._combine_source_results(source_results)
            
            # 步骤2: 统计显著性分析
            statistical_maps = self.statistical_analyzer.analyze_significance(
                combined_activity, source_results
            )
            
            # 步骤3: 生成3D脑图
            brain_3d_results = self.brain_3d_renderer.render_brain_activity(
                combined_activity, head_model, statistical_maps
            )
            
            # 步骤4: 生成拓扑图
            topography_results = self.topography_generator.generate_topography(
                combined_activity, head_model
            )
            
            # 步骤5: 时频可视化
            time_frequency_results = self.time_frequency_visualizer.visualize_time_frequency(
                combined_activity, source_results
            )
            
            # 步骤6: 保存结果
            output_paths = self._save_visualization_results(
                {
                    'brain_3d': brain_3d_results,
                    'topography': topography_results,
                    'time_frequency': time_frequency_results,
                    'statistical_maps': statistical_maps,
                    'combined_activity': combined_activity
                },
                output_dir
            )
            
            # 步骤7: 生成综合报告
            visualization_report = self._generate_visualization_report(
                combined_activity, statistical_maps, output_paths
            )
            
            result = {
                'combined_activity': combined_activity,
                'statistical_maps': statistical_maps,
                'brain_3d_results': brain_3d_results,
                'topography_results': topography_results,
                'time_frequency_results': time_frequency_results,
                'output_paths': output_paths,
                'visualization_report': visualization_report
            }
            
            logger.info("全脑电活动强度图像生成完成")
            return result
            
        except Exception as e:
            logger.error(f"全脑电活动图像生成失败: {e}")
            raise
            
    def _combine_source_results(self, source_results: List[Dict]) -> Dict:
        """叠加多通道源定位结果"""
        try:
            logger.info("开始叠加多通道源定位结果")
            
            if not source_results:
                raise ValueError("源定位结果列表为空")
                
            # 获取第一个结果的维度信息
            first_result = source_results[0]
            source_estimates = first_result['source_estimates']
            num_sources, num_timepoints = source_estimates.shape
            
            # 初始化叠加结果
            combined_estimates = np.zeros((num_sources, num_timepoints))
            combined_power = np.zeros(num_sources)
            combined_weights = np.zeros(len(source_results))
            
            # 叠加所有通道的结果
            for i, result in enumerate(source_results):
                estimates = result['source_estimates']
                quality_score = result.get('quality_metrics', {}).get('overall_score', 1.0)
                
                # 使用质量评分作为权重
                weight = quality_score
                combined_weights[i] = weight
                
                # 加权叠加
                combined_estimates += weight * estimates
                combined_power += weight * np.sum(estimates**2, axis=1)
                
            # 归一化
            total_weight = np.sum(combined_weights)
            if total_weight > 0:
                combined_estimates /= total_weight
                combined_power /= total_weight
                
            # 计算其他统计量
            combined_activity = {
                'source_estimates': combined_estimates,
                'power_distribution': combined_power,
                'weights': combined_weights,
                'num_channels': len(source_results),
                'num_sources': num_sources,
                'num_timepoints': num_timepoints,
                'peak_activity': np.max(np.abs(combined_estimates), axis=1),
                'mean_activity': np.mean(np.abs(combined_estimates), axis=1),
                'temporal_variance': np.var(combined_estimates, axis=1)
            }
            
            logger.info(f"成功叠加 {len(source_results)} 个通道的源定位结果")
            return combined_activity
            
        except Exception as e:
            logger.error(f"源定位结果叠加失败: {e}")
            raise
            
    def _save_visualization_results(self, results: Dict, output_dir: str) -> Dict:
        """保存可视化结果"""
        try:
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            output_paths = {}
            
            # 保存3D脑图
            if 'brain_3d' in results:
                brain_3d_path = output_path / "brain_3d_activity.html"
                results['brain_3d']['figure'].write_html(str(brain_3d_path))
                output_paths['brain_3d'] = str(brain_3d_path)
                
            # 保存拓扑图
            if 'topography' in results:
                topo_path = output_path / "topography.png"
                results['topography']['figure'].savefig(str(topo_path), dpi=300, bbox_inches='tight')
                output_paths['topography'] = str(topo_path)
                
            # 保存时频图
            if 'time_frequency' in results:
                tf_path = output_path / "time_frequency.html"
                results['time_frequency']['figure'].write_html(str(tf_path))
                output_paths['time_frequency'] = str(tf_path)
                
            # 保存统计图
            if 'statistical_maps' in results:
                stats_path = output_path / "statistical_maps.json"
                with open(stats_path, 'w') as f:
                    # 转换numpy数组为列表以便JSON序列化
                    stats_data = {}
                    for key, value in results['statistical_maps'].items():
                        if isinstance(value, np.ndarray):
                            stats_data[key] = value.tolist()
                        else:
                            stats_data[key] = value
                    json.dump(stats_data, f, indent=2)
                output_paths['statistical_maps'] = str(stats_path)
                
            # 保存原始数据
            if 'combined_activity' in results:
                data_path = output_path / "combined_activity.npz"
                activity_data = results['combined_activity']
                np.savez(str(data_path), **activity_data)
                output_paths['combined_activity'] = str(data_path)
                
            logger.info(f"可视化结果已保存到: {output_dir}")
            return output_paths
            
        except Exception as e:
            logger.error(f"保存可视化结果失败: {e}")
            return {}
            
    def _generate_visualization_report(self, combined_activity: Dict, 
                                     statistical_maps: Dict,
                                     output_paths: Dict) -> Dict:
        """生成可视化报告"""
        try:
            report = {
                'summary': {
                    'num_channels': combined_activity['num_channels'],
                    'num_sources': combined_activity['num_sources'],
                    'num_timepoints': combined_activity['num_timepoints'],
                    'max_activity': float(np.max(combined_activity['peak_activity'])),
                    'mean_activity': float(np.mean(combined_activity['mean_activity'])),
                    'active_sources': int(np.sum(combined_activity['peak_activity'] > 
                                                0.1 * np.max(combined_activity['peak_activity'])))
                },
                'statistical_summary': {
                    'significant_sources': int(np.sum(statistical_maps.get('p_values', [1.0]) < 0.05)),
                    'peak_significance': float(np.min(statistical_maps.get('p_values', [1.0]))),
                    'effect_size_mean': float(np.mean(statistical_maps.get('effect_sizes', [0.0])))
                },
                'output_files': output_paths,
                'recommendations': self._generate_interpretation_recommendations(
                    combined_activity, statistical_maps
                )
            }
            
            return report
            
        except Exception as e:
            logger.error(f"可视化报告生成失败: {e}")
            return {}
            
    def _generate_interpretation_recommendations(self, combined_activity: Dict, 
                                               statistical_maps: Dict) -> List[str]:
        """生成解释建议"""
        recommendations = []
        
        try:
            # 基于活动强度的建议
            max_activity = np.max(combined_activity['peak_activity'])
            mean_activity = np.mean(combined_activity['mean_activity'])
            
            if max_activity > 10 * mean_activity:
                recommendations.append("检测到强烈的局部化活动，建议重点关注峰值区域")
            elif max_activity < 3 * mean_activity:
                recommendations.append("活动分布较为弥散，可能需要调整分析参数")
                
            # 基于统计显著性的建议
            p_values = statistical_maps.get('p_values', [1.0])
            significant_ratio = np.sum(np.array(p_values) < 0.05) / len(p_values)
            
            if significant_ratio > 0.1:
                recommendations.append("发现较多统计显著的源，结果可信度较高")
            elif significant_ratio < 0.01:
                recommendations.append("统计显著的源较少，建议检查数据质量或调整阈值")
                
            # 基于时间特征的建议
            temporal_var = combined_activity['temporal_variance']
            high_var_sources = np.sum(temporal_var > np.percentile(temporal_var, 90))
            
            if high_var_sources > combined_activity['num_sources'] * 0.05:
                recommendations.append("检测到时间变化较大的源，建议进行时频分析")
                
            return recommendations
            
        except Exception as e:
            logger.warning(f"解释建议生成失败: {e}")
            return ["数据处理完成，请人工检查结果"]


class Brain3DRenderer:
    """3D脑图渲染器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.brain_3d_config = config['visualization']['brain_3d']
        
    def render_brain_activity(self, combined_activity: Dict, head_model: Dict,
                            statistical_maps: Dict) -> Dict:
        """渲染3D脑电活动图"""
        try:
            logger.info("开始渲染3D脑电活动图")
            
            # 获取源位置（简化版本，实际需要从头部模型获取）
            num_sources = combined_activity['num_sources']
            source_positions = self._generate_source_positions(num_sources)
            
            # 获取活动强度
            activity_values = combined_activity['peak_activity']
            
            # 创建3D散点图
            fig = go.Figure()
            
            # 添加源活动散点
            fig.add_trace(go.Scatter3d(
                x=source_positions[:, 0],
                y=source_positions[:, 1],
                z=source_positions[:, 2],
                mode='markers',
                marker=dict(
                    size=np.clip(activity_values * 20, 2, 20),
                    color=activity_values,
                    colorscale=self.brain_3d_config.get('colormap', 'hot'),
                    opacity=self.brain_3d_config.get('surface_alpha', 0.8),
                    colorbar=dict(title="活动强度")
                ),
                text=[f"源 {i}: {val:.3f}" for i, val in enumerate(activity_values)],
                hovertemplate="<b>%{text}</b><br>位置: (%{x:.1f}, %{y:.1f}, %{z:.1f})<extra></extra>"
            ))
            
            # 添加脑表面（简化版本）
            if 'meshes' in head_model:
                brain_mesh = self._get_brain_surface_mesh(head_model['meshes'])
                if brain_mesh is not None:
                    fig.add_trace(go.Mesh3d(
                        x=brain_mesh['vertices'][:, 0],
                        y=brain_mesh['vertices'][:, 1],
                        z=brain_mesh['vertices'][:, 2],
                        i=brain_mesh['faces'][:, 0],
                        j=brain_mesh['faces'][:, 1],
                        k=brain_mesh['faces'][:, 2],
                        opacity=0.3,
                        color='lightgray',
                        name='脑表面'
                    ))
                    
            # 设置布局
            fig.update_layout(
                title="3D脑电活动分布",
                scene=dict(
                    xaxis_title="X (mm)",
                    yaxis_title="Y (mm)",
                    zaxis_title="Z (mm)",
                    aspectmode='cube'
                ),
                width=800,
                height=600
            )
            
            result = {
                'figure': fig,
                'source_positions': source_positions,
                'activity_values': activity_values,
                'max_activity': np.max(activity_values),
                'num_active_sources': np.sum(activity_values > 0.1 * np.max(activity_values))
            }
            
            logger.info("3D脑电活动图渲染完成")
            return result
            
        except Exception as e:
            logger.error(f"3D脑图渲染失败: {e}")
            raise
            
    def _generate_source_positions(self, num_sources: int) -> np.ndarray:
        """生成源位置（简化版本）"""
        try:
            # 创建球形分布的源位置
            phi = np.random.uniform(0, 2*np.pi, num_sources)
            costheta = np.random.uniform(-1, 1, num_sources)
            theta = np.arccos(costheta)
            
            # 球坐标转直角坐标
            radius = 80  # 大脑半径约80mm
            x = radius * np.sin(theta) * np.cos(phi)
            y = radius * np.sin(theta) * np.sin(phi)
            z = radius * np.cos(theta)
            
            positions = np.column_stack([x, y, z])
            return positions
            
        except Exception as e:
            logger.warning(f"源位置生成失败: {e}")
            # 返回规则网格
            return np.random.randn(num_sources, 3) * 50
            
    def _get_brain_surface_mesh(self, meshes: Dict) -> Optional[Dict]:
        """获取脑表面网格"""
        try:
            # 优先选择灰质表面
            for tissue in ['gray', 'brain', 'white']:
                if tissue in meshes:
                    return meshes[tissue]
            return None
        except Exception as e:
            logger.warning(f"脑表面网格获取失败: {e}")
            return None


class TopographyGenerator:
    """拓扑图生成器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.topo_config = config['visualization']['topography']
        
    def generate_topography(self, combined_activity: Dict, head_model: Dict) -> Dict:
        """生成拓扑图"""
        try:
            logger.info("开始生成拓扑图")
            
            # 创建拓扑图
            fig, axes = plt.subplots(2, 2, figsize=(12, 10))
            
            # 峰值活动拓扑图
            self._plot_activity_topography(
                axes[0, 0], combined_activity['peak_activity'], 
                "峰值活动分布", "hot"
            )
            
            # 平均活动拓扑图
            self._plot_activity_topography(
                axes[0, 1], combined_activity['mean_activity'],
                "平均活动分布", "viridis"
            )
            
            # 时间方差拓扑图
            self._plot_activity_topography(
                axes[1, 0], combined_activity['temporal_variance'],
                "时间变异性分布", "plasma"
            )
            
            # 功率分布拓扑图
            self._plot_activity_topography(
                axes[1, 1], combined_activity['power_distribution'],
                "功率分布", "inferno"
            )
            
            plt.tight_layout()
            
            result = {
                'figure': fig,
                'peak_activity': combined_activity['peak_activity'],
                'mean_activity': combined_activity['mean_activity'],
                'temporal_variance': combined_activity['temporal_variance'],
                'power_distribution': combined_activity['power_distribution']
            }
            
            logger.info("拓扑图生成完成")
            return result
            
        except Exception as e:
            logger.error(f"拓扑图生成失败: {e}")
            raise
            
    def _plot_activity_topography(self, ax, activity_data: np.ndarray, 
                                title: str, colormap: str):
        """绘制活动拓扑图"""
        try:
            # 简化的拓扑图绘制
            # 实际应用中需要根据电极位置进行插值
            
            # 创建2D网格用于显示
            grid_size = int(np.sqrt(len(activity_data)))
            if grid_size * grid_size < len(activity_data):
                grid_size += 1
                
            # 将1D数据重塑为2D网格
            padded_data = np.zeros(grid_size * grid_size)
            padded_data[:len(activity_data)] = activity_data
            grid_data = padded_data.reshape(grid_size, grid_size)
            
            # 绘制热图
            im = ax.imshow(grid_data, cmap=colormap, aspect='equal')
            ax.set_title(title)
            ax.set_xlabel("X位置")
            ax.set_ylabel("Y位置")
            
            # 添加颜色条
            plt.colorbar(im, ax=ax, shrink=0.8)
            
            # 添加等高线
            contour_lines = self.topo_config.get('contour_lines', 10)
            if contour_lines > 0:
                ax.contour(grid_data, levels=contour_lines, colors='white', alpha=0.5, linewidths=0.5)
                
        except Exception as e:
            logger.warning(f"拓扑图绘制失败: {e}")
            ax.text(0.5, 0.5, "绘制失败", ha='center', va='center', transform=ax.transAxes)


class TimeFrequencyVisualizer:
    """时频可视化器"""

    def __init__(self, config: Dict):
        self.config = config

    def visualize_time_frequency(self, combined_activity: Dict,
                               source_results: List[Dict]) -> Dict:
        """可视化时频分析结果"""
        try:
            logger.info("开始时频可视化")

            # 选择最活跃的几个源进行时频分析
            peak_activity = combined_activity['peak_activity']
            top_sources_idx = np.argsort(peak_activity)[-5:]  # 选择前5个最活跃的源

            # 创建子图
            fig = make_subplots(
                rows=len(top_sources_idx), cols=2,
                subplot_titles=[f"源 {idx} 时间序列" for idx in top_sources_idx] +
                              [f"源 {idx} 频谱" for idx in top_sources_idx],
                specs=[[{"secondary_y": False}, {"secondary_y": False}] for _ in top_sources_idx]
            )

            # 为每个选中的源生成时频图
            for i, source_idx in enumerate(top_sources_idx):
                # 获取时间序列
                time_series = combined_activity['source_estimates'][source_idx, :]

                # 时间序列图
                fig.add_trace(
                    go.Scatter(
                        y=time_series,
                        mode='lines',
                        name=f'源 {source_idx}',
                        line=dict(width=2)
                    ),
                    row=i+1, col=1
                )

                # 频谱分析
                freqs, psd = self._compute_power_spectrum(time_series)

                fig.add_trace(
                    go.Scatter(
                        x=freqs,
                        y=psd,
                        mode='lines',
                        name=f'源 {source_idx} 频谱',
                        line=dict(width=2)
                    ),
                    row=i+1, col=2
                )

            # 更新布局
            fig.update_layout(
                title="时频分析结果",
                height=200 * len(top_sources_idx),
                showlegend=False
            )

            # 更新x轴标签
            for i in range(len(top_sources_idx)):
                fig.update_xaxes(title_text="时间点", row=i+1, col=1)
                fig.update_xaxes(title_text="频率 (Hz)", row=i+1, col=2)
                fig.update_yaxes(title_text="幅度", row=i+1, col=1)
                fig.update_yaxes(title_text="功率", row=i+1, col=2)

            result = {
                'figure': fig,
                'analyzed_sources': top_sources_idx.tolist(),
                'time_series_data': {idx: combined_activity['source_estimates'][idx, :]
                                   for idx in top_sources_idx}
            }

            logger.info("时频可视化完成")
            return result

        except Exception as e:
            logger.error(f"时频可视化失败: {e}")
            raise

    def _compute_power_spectrum(self, time_series: np.ndarray,
                              sampling_rate: float = 250.0) -> Tuple[np.ndarray, np.ndarray]:
        """计算功率谱"""
        try:
            from scipy import signal

            # 使用Welch方法计算功率谱密度
            freqs, psd = signal.welch(
                time_series,
                fs=sampling_rate,
                nperseg=min(256, len(time_series)//4),
                noverlap=None
            )

            return freqs, psd

        except Exception as e:
            logger.warning(f"功率谱计算失败: {e}")
            # 返回简单的FFT结果
            fft_result = np.fft.fft(time_series)
            freqs = np.fft.fftfreq(len(time_series), 1/sampling_rate)
            psd = np.abs(fft_result)**2

            # 只返回正频率部分
            positive_freq_idx = freqs >= 0
            return freqs[positive_freq_idx], psd[positive_freq_idx]


class StatisticalAnalyzer:
    """统计分析器"""

    def __init__(self, config: Dict):
        self.config = config

    def analyze_significance(self, combined_activity: Dict,
                           source_results: List[Dict]) -> Dict:
        """分析统计显著性"""
        try:
            logger.info("开始统计显著性分析")

            statistical_maps = {}

            # 1. 计算p值
            p_values = self._calculate_p_values(combined_activity, source_results)
            statistical_maps['p_values'] = p_values

            # 2. 多重比较校正
            corrected_p_values = self._multiple_comparison_correction(p_values)
            statistical_maps['corrected_p_values'] = corrected_p_values

            # 3. 计算效应量
            effect_sizes = self._calculate_effect_sizes(combined_activity, source_results)
            statistical_maps['effect_sizes'] = effect_sizes

            # 4. 置信区间
            confidence_intervals = self._calculate_confidence_intervals(combined_activity)
            statistical_maps['confidence_intervals'] = confidence_intervals

            # 5. 聚类分析
            clusters = self._perform_cluster_analysis(combined_activity)
            statistical_maps['clusters'] = clusters

            # 6. 统计摘要
            statistical_summary = self._generate_statistical_summary(statistical_maps)
            statistical_maps['summary'] = statistical_summary

            logger.info("统计显著性分析完成")
            return statistical_maps

        except Exception as e:
            logger.error(f"统计显著性分析失败: {e}")
            raise

    def _calculate_p_values(self, combined_activity: Dict,
                          source_results: List[Dict]) -> np.ndarray:
        """计算p值"""
        try:
            num_sources = combined_activity['num_sources']
            p_values = np.ones(num_sources)

            # 获取所有通道的源估计
            all_estimates = []
            for result in source_results:
                all_estimates.append(result['source_estimates'])

            if len(all_estimates) < 2:
                logger.warning("通道数量不足，无法进行统计检验")
                return p_values

            # 对每个源位置进行t检验
            for source_idx in range(num_sources):
                # 收集该源位置在所有通道的活动
                source_activities = []
                for estimates in all_estimates:
                    # 使用峰值活动作为统计量
                    peak_activity = np.max(np.abs(estimates[source_idx, :]))
                    source_activities.append(peak_activity)

                # 单样本t检验（检验是否显著大于0）
                if len(source_activities) > 1:
                    t_stat, p_val = stats.ttest_1samp(source_activities, 0)
                    p_values[source_idx] = p_val

            return p_values

        except Exception as e:
            logger.warning(f"p值计算失败: {e}")
            return np.ones(combined_activity['num_sources'])

    def _multiple_comparison_correction(self, p_values: np.ndarray) -> np.ndarray:
        """多重比较校正"""
        try:
            # 使用Benjamini-Hochberg FDR校正
            from scipy.stats import false_discovery_control

            corrected_p_values = false_discovery_control(p_values, method='bh')
            return corrected_p_values

        except Exception as e:
            logger.warning(f"多重比较校正失败: {e}")
            return p_values

    def _calculate_effect_sizes(self, combined_activity: Dict,
                              source_results: List[Dict]) -> np.ndarray:
        """计算效应量（Cohen's d）"""
        try:
            num_sources = combined_activity['num_sources']
            effect_sizes = np.zeros(num_sources)

            # 获取所有通道的源估计
            all_estimates = []
            for result in source_results:
                all_estimates.append(result['source_estimates'])

            if len(all_estimates) < 2:
                return effect_sizes

            # 对每个源位置计算效应量
            for source_idx in range(num_sources):
                source_activities = []
                for estimates in all_estimates:
                    peak_activity = np.max(np.abs(estimates[source_idx, :]))
                    source_activities.append(peak_activity)

                # Cohen's d = (mean - 0) / std
                if len(source_activities) > 1:
                    mean_activity = np.mean(source_activities)
                    std_activity = np.std(source_activities, ddof=1)

                    if std_activity > 0:
                        effect_sizes[source_idx] = mean_activity / std_activity

            return effect_sizes

        except Exception as e:
            logger.warning(f"效应量计算失败: {e}")
            return np.zeros(combined_activity['num_sources'])

    def _calculate_confidence_intervals(self, combined_activity: Dict,
                                      confidence_level: float = 0.95) -> np.ndarray:
        """计算置信区间"""
        try:
            peak_activity = combined_activity['peak_activity']

            # 使用bootstrap方法估计置信区间
            n_bootstrap = 1000
            bootstrap_means = []

            for _ in range(n_bootstrap):
                # 重采样
                bootstrap_sample = np.random.choice(peak_activity, size=len(peak_activity), replace=True)
                bootstrap_means.append(np.mean(bootstrap_sample))

            # 计算置信区间
            alpha = 1 - confidence_level
            lower_percentile = (alpha / 2) * 100
            upper_percentile = (1 - alpha / 2) * 100

            ci_lower = np.percentile(bootstrap_means, lower_percentile)
            ci_upper = np.percentile(bootstrap_means, upper_percentile)

            return np.array([ci_lower, ci_upper])

        except Exception as e:
            logger.warning(f"置信区间计算失败: {e}")
            return np.array([0.0, 1.0])

    def _perform_cluster_analysis(self, combined_activity: Dict) -> Dict:
        """执行聚类分析"""
        try:
            # 使用DBSCAN进行聚类
            peak_activity = combined_activity['peak_activity']

            # 准备聚类数据（使用活动强度和位置信息）
            # 这里简化处理，实际应该使用真实的空间位置
            clustering_data = peak_activity.reshape(-1, 1)

            # DBSCAN聚类
            dbscan = DBSCAN(eps=0.1 * np.std(peak_activity), min_samples=3)
            cluster_labels = dbscan.fit_predict(clustering_data)

            # 分析聚类结果
            unique_labels = np.unique(cluster_labels)
            clusters = {}

            for label in unique_labels:
                if label == -1:  # 噪声点
                    continue

                cluster_mask = cluster_labels == label
                cluster_sources = np.where(cluster_mask)[0]
                cluster_activity = peak_activity[cluster_mask]

                clusters[f'cluster_{label}'] = {
                    'source_indices': cluster_sources.tolist(),
                    'mean_activity': float(np.mean(cluster_activity)),
                    'max_activity': float(np.max(cluster_activity)),
                    'size': len(cluster_sources)
                }

            clusters['num_clusters'] = len([l for l in unique_labels if l != -1])
            clusters['noise_points'] = int(np.sum(cluster_labels == -1))

            return clusters

        except Exception as e:
            logger.warning(f"聚类分析失败: {e}")
            return {'num_clusters': 0, 'noise_points': 0}

    def _generate_statistical_summary(self, statistical_maps: Dict) -> Dict:
        """生成统计摘要"""
        try:
            summary = {}

            # p值统计
            p_values = statistical_maps.get('p_values', [1.0])
            summary['significant_sources_uncorrected'] = int(np.sum(np.array(p_values) < 0.05))
            summary['min_p_value'] = float(np.min(p_values))
            summary['mean_p_value'] = float(np.mean(p_values))

            # 校正后p值统计
            corrected_p_values = statistical_maps.get('corrected_p_values', [1.0])
            summary['significant_sources_corrected'] = int(np.sum(np.array(corrected_p_values) < 0.05))
            summary['min_corrected_p_value'] = float(np.min(corrected_p_values))

            # 效应量统计
            effect_sizes = statistical_maps.get('effect_sizes', [0.0])
            summary['mean_effect_size'] = float(np.mean(effect_sizes))
            summary['max_effect_size'] = float(np.max(effect_sizes))
            summary['large_effect_sources'] = int(np.sum(np.array(effect_sizes) > 0.8))  # Cohen's d > 0.8

            # 聚类统计
            clusters = statistical_maps.get('clusters', {})
            summary['num_clusters'] = clusters.get('num_clusters', 0)
            summary['noise_points'] = clusters.get('noise_points', 0)

            return summary

        except Exception as e:
            logger.warning(f"统计摘要生成失败: {e}")
            return {}
