# 数据管理模块技术文档

## 模块概述

数据管理模块 (`core/data_manager.py`) 是EEG源定位系统的核心基础模块，负责管理所有输入数据的加载、预处理和格式转换。该模块提供统一的数据接口，支持多种EEG和MRI数据格式。

## 核心类设计

### 1. DataManager (数据管理器主类)

```python
class DataManager:
    def __init__(self, config_path: str = "config.yaml")
    def _load_config(self, config_path: str) -> Dict
    def _create_directories(self)
```

**功能描述：**
- 系统的主要数据管理入口
- 负责配置文件加载和系统初始化
- 协调各个子模块的工作
- 创建必要的目录结构

**伪代码实现：**
```
初始化数据管理器():
    加载配置文件(config_path)
    创建EEG加载器实例
    创建MRI处理器实例
    创建数据转换器实例
    创建必要目录结构
```

### 2. EEGLoader (EEG数据加载器)

```python
class EEGLoader:
    def __init__(self, config: Dict)
    def load_eeg_data(self, subject_id: str, data_type: str) -> mne.io.Raw
    def _convert_to_mne_raw(self, eeg_data: pd.DataFrame, subject_id: str) -> mne.io.Raw
    def preprocess_eeg(self, raw: mne.io.Raw) -> mne.io.Raw
```

**功能描述：**
- 支持多种EEG数据格式加载
- 自动识别Guinea-Bissau和Nigeria数据集
- 转换为标准MNE格式
- 实现完整的EEG预处理流程

**伪代码实现：**
```
加载EEG数据(被试ID, 数据类型):
    根据数据类型确定文件路径
    检查文件是否存在
    加载CSV压缩数据
    转换为MNE Raw格式:
        提取通道数据
        创建通道信息
        设置采样率
        应用标准电极布局
    返回Raw对象

预处理EEG数据(原始数据):
    复制原始数据
    应用滤波处理:
        高通滤波(0.5Hz)
        低通滤波(100Hz)
        工频滤波(50Hz)
    执行预处理步骤:
        去除直流分量
        去趋势处理
        基线校正
    返回处理后数据
```

### 3. MRIProcessor (MRI数据处理器)

```python
class MRIProcessor:
    def __init__(self, config: Dict)
    def load_mri_data(self, subject_id: str) -> Dict[str, nib.Nifti1Image]
    def validate_mri_data(self, mri_data: Dict[str, nib.Nifti1Image]) -> bool
```

**功能描述：**
- 加载多种MRI数据类型（T1、T2、mask等）
- 支持NIfTI格式数据处理
- 实现数据质量验证机制
- 提供数据完整性检查

**伪代码实现：**
```
加载MRI数据(被试ID):
    初始化数据字典
    构建数据目录路径
    加载原始mask数据:
        检查MaskInOrig.nii.gz文件
        使用nibabel加载
    加载原始数据mask:
        检查MaskInRawData.nii.gz文件
        使用nibabel加载
    加载T1数据(如果存在):
        检查T1w.nii文件
        使用nibabel加载
    验证数据完整性
    返回数据字典

验证MRI数据(MRI数据字典):
    对每个数据类型执行验证:
        检查数据维度(必须为3D)
        验证体素尺寸(≤2mm)
        检查数据范围(非全零)
        记录验证结果
    返回总体验证状态
```

### 4. DataConverter (数据格式转换器)

```python
class DataConverter:
    def __init__(self, config: Dict)
    def convert_coordinates(self, coords: np.ndarray, from_space: str, to_space: str) -> np.ndarray
    def save_results(self, data: Dict, output_path: str, format_type: str)
```

**功能描述：**
- 实现多种坐标系统间的转换
- 支持多种输出格式保存
- 提供标准化的数据接口
- 确保数据格式兼容性

**伪代码实现：**
```
坐标转换(坐标数组, 源坐标系, 目标坐标系):
    根据坐标系类型选择转换方法:
        MNI152 <-> Talairach
        个体空间 <-> 标准空间
        头部坐标 <-> 设备坐标
    应用相应的变换矩阵
    返回转换后坐标

保存结果(数据, 输出路径, 格式类型):
    创建输出目录
    根据格式类型选择保存方法:
        NIfTI格式: 使用nibabel保存
        JSON格式: 使用json模块保存
        CSV格式: 使用pandas保存
        HDF5格式: 使用h5py保存
    记录保存状态
```

## 数据流程设计

### 1. EEG数据处理流程

```
原始EEG文件 → 格式检测 → 数据加载 → MNE转换 → 预处理 → 质量检查 → 标准化输出
     ↓            ↓         ↓        ↓        ↓        ↓          ↓
  CSV.gz格式   自动识别   pandas   Raw对象   滤波处理  通道验证   统一接口
```

### 2. MRI数据处理流程

```
MRI文件 → 格式验证 → 数据加载 → 质量检查 → 坐标配准 → 标准化 → 输出
   ↓         ↓        ↓        ↓        ↓        ↓       ↓
NIfTI格式  维度检查  nibabel  完整性验证  空间对齐  格式统一  接口输出
```

## 配置参数说明

### EEG处理配置
```yaml
eeg_processing:
  sampling_rate: 250        # 采样率 (Hz)
  filtering:
    highpass: 0.5          # 高通滤波截止频率
    lowpass: 100           # 低通滤波截止频率
    notch: 50              # 工频滤波频率
  preprocessing:
    remove_dc: true        # 去除直流分量
    detrend: true          # 去趋势处理
    baseline_correction: true  # 基线校正
```

### MRI处理配置
```yaml
head_modeling:
  mni_template:
    resolution: 1          # 模板分辨率 (mm)
    template_type: "MNI152_T1_1mm"
  tissue_segmentation:
    method: "freesurfer"   # 分割方法
    accuracy_threshold: 1.0 # 精度阈值 (mm)
```

## 错误处理机制

### 1. 数据加载错误
- 文件不存在：提供详细的文件路径信息
- 格式错误：自动尝试多种解析方法
- 权限问题：提供权限检查和修复建议

### 2. 数据质量错误
- 维度不匹配：自动尝试维度调整
- 数据范围异常：提供统计信息和建议
- 缺失数据：实现智能插值或标记

### 3. 内存管理
- 大文件处理：使用内存映射技术
- 内存不足：实现数据分块处理
- 缓存管理：智能缓存失效机制

## 性能优化策略

### 1. 数据加载优化
- 使用多线程并行加载多个文件
- 实现智能缓存机制避免重复加载
- 采用内存映射处理大型数据集

### 2. 预处理优化
- 利用NumPy向量化操作
- 使用Numba JIT编译加速关键算法
- 实现GPU加速（可选）

### 3. 内存优化
- 及时释放不需要的数据
- 使用数据类型优化减少内存占用
- 实现数据流式处理

## 扩展接口设计

### 1. 自定义数据格式支持
```python
def register_custom_loader(format_name: str, loader_func: callable):
    """注册自定义数据加载器"""
    pass

def register_custom_preprocessor(name: str, processor_func: callable):
    """注册自定义预处理器"""
    pass
```

### 2. 插件架构
- 支持动态加载数据处理插件
- 提供标准化的插件接口
- 实现插件依赖管理

## 测试验证

### 1. 单元测试覆盖
- 数据加载功能测试
- 预处理算法验证
- 格式转换正确性检查
- 错误处理机制测试

### 2. 集成测试
- 端到端数据流程测试
- 多格式数据兼容性测试
- 性能基准测试

### 3. 数据验证
- 使用标准数据集验证
- 与已知结果对比
- 精度和稳定性评估
